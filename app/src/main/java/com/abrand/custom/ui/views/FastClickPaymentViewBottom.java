package com.abrand.custom.ui.views;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.os.CountDownTimer;
import android.os.Handler;
import android.text.Editable;
import android.text.Html;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.abrand.custom.R;
import com.abrand.custom.data.ApoloConfig;
import com.abrand.custom.data.entity.FastClickPaymentSystem;
import com.abrand.custom.tools.GeneralTools;
import com.abrand.custom.ui.activitymain.MainActivity;
import com.squareup.picasso.Picasso;

public class FastClickPaymentViewBottom extends ConstraintLayout {
    private final String                         TAG                            = "FastClickPaymentViewBottom";
    private       Context                        context;
    protected     ViewGroup                      rootView;
    private       EditText                       etFastPaymentAmount;
    private       TextView                       tvFastPaymentCurrency;
    //private       TextView                       tvFastPaymentRequisite;
    private       ImageView                      ivFastPaymentLogo;
    private       Button                         btnFastPayment;
    private       Button                         btnFastPaymentConfirm;
    private       TextView                       btnFastPaymentDismiss;
    private       TextView                       textConfirm;
    private       View                           containerFastPaymentLoader;
    private       ProgressBar                    pbFastPaymentLoader;
    private       TextView                       tvFastPaymentSuccessfulTransaction;
    private       ButtonFastPaymentClickListener buttonFastPaymentClickListener;
    private final int                            SUCCESSFUL_REBILL_MESSAGE_TIME = 3000;
    private       FastClickPaymentSystem         fastClickPaymentSystem;
    private       String                         minDepositWarning;
    private       String                         maxDepositWarning;
    private       LinearLayout                   lay_1click;
    private       ConstraintLayout               lay_confirm;
    private       CountDownTimer                 timer_loader;
    private       CountDownTimer                 timer_success;
    private       TextView                       tv_pay_processing;
    private       boolean                        isPayInProcess;
    private       RelativeLayout                 layResult;
    private       ProgressBar                    pbResult;
    private       ImageView                      ivResultExit;
    private       TextView                       tvResult;
    private       TextView                       tvResultMessage;

    public FastClickPaymentViewBottom(@NonNull Context context) {
        super(context);
        init(context);
    }

    public FastClickPaymentViewBottom(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public FastClickPaymentViewBottom(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        this.context = context;
        rootView = inflate(context, R.layout.view_fast_click_payment_bottom, this).findViewById(R.id.root);
        etFastPaymentAmount = rootView.findViewById(R.id.et_fast_payment_amount);
        tvFastPaymentCurrency = rootView.findViewById(R.id.tv_fast_payment_currency);
        //tvFastPaymentRequisite = rootView.findViewById(R.id.tv_fast_payment_requisite);
        ivFastPaymentLogo = rootView.findViewById(R.id.iv_fast_payment_logo);
        View containerFastPaymentAmount = rootView.findViewById(R.id.container_fast_payment_amount);
        containerFastPaymentAmount.setOnClickListener(v -> {
            etFastPaymentAmount.requestFocus();
            etFastPaymentAmount.setSelection(etFastPaymentAmount.getText().length());
            GeneralTools.showKeyboard(context, etFastPaymentAmount);
        });
        textConfirm = rootView.findViewById(R.id.textConfirm);
        btnFastPayment = rootView.findViewById(R.id.btn_fast_payment);
        btnFastPaymentConfirm = rootView.findViewById(R.id.btn_fast_payment_confirm);
        btnFastPaymentDismiss = rootView.findViewById(R.id.btn_fast_payment_dismiss);
        containerFastPaymentLoader = rootView.findViewById(R.id.container_fast_payment_loader);
        pbFastPaymentLoader = rootView.findViewById(R.id.iv_loader);
        tvFastPaymentSuccessfulTransaction = rootView.findViewById(R.id.tv_fast_payment_successful_transaction);
        lay_1click = rootView.findViewById(R.id.lay_1click);
        lay_confirm = rootView.findViewById(R.id.lay_confirm);
        tv_pay_processing = rootView.findViewById(R.id.tv_pay_processing);

        layResult = rootView.findViewById(R.id.container_fast_payment_result);
        pbResult = rootView.findViewById(R.id.pb_loader_success);
        tvResult = rootView.findViewById(R.id.tv_fast_payment_result);
        tvResultMessage = rootView.findViewById(R.id.tv_fast_payment_result_message);
        ivResultExit = rootView.findViewById(R.id.iv_fast_payment_result_close);
        ivResultExit.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                timer_success.onFinish();
            }
        });
    }


    public void setFastClickPaymentSystem(FastClickPaymentSystem fastClickPaymentSystem) {
        setFastClickPaymentSystem(fastClickPaymentSystem, null);
    }

    /***
     *
     * @param fastClickPaymentSystem
     * @param defaultAmount custom default amount to be filled in editText field,
     *                      must be greater that fastClickPaymentSystem min amount
     *                      and less than fastClickPaymentSystem max amount
     *                      or will be ignored
     */

    public void setFastClickPaymentSystem(FastClickPaymentSystem fastClickPaymentSystem,
                                          @Nullable String defaultAmount) {
        final char space                = ' ';
        final int  REQUISITE_CHARACTERS = 4;
        this.fastClickPaymentSystem = fastClickPaymentSystem;

        try {
            if ( defaultAmount != null ) {
                double customAmount = Double.parseDouble(defaultAmount);
                //noinspection ConstantConditions
                double minAmount = Double.parseDouble(fastClickPaymentSystem.getMinAmount().toString());
                //noinspection ConstantConditions
                double maxAmount = Double.parseDouble(fastClickPaymentSystem.getMaxAmount().toString());

                if ( customAmount < minAmount || customAmount > maxAmount ) {
                    defaultAmount = null;
                } else {
                    defaultAmount = String.valueOf((int) customAmount);
                }
            }
        } catch ( NumberFormatException | NullPointerException e ) {
            // error parse custom amount
            defaultAmount = null;
        }

        String formattedMinAmount = GeneralTools.formatBalance(fastClickPaymentSystem.getCurrency().getCode(),
                Double.parseDouble(fastClickPaymentSystem.getMinAmount().toString()));
        this.minDepositWarning = context.getString(R.string.min_deposit_warning, formattedMinAmount);

        String formattedMaxAmount = GeneralTools.formatBalance(fastClickPaymentSystem.getCurrency().getCode(),
                Double.parseDouble(fastClickPaymentSystem.getMaxAmount().toString()));
        this.maxDepositWarning = context.getString(R.string.max_deposit_warning, formattedMaxAmount);

        etFastPaymentAmount.removeTextChangedListener(fastPaymentAmountTextWatcher);
        etFastPaymentAmount.addTextChangedListener(fastPaymentAmountTextWatcher);
        etFastPaymentAmount.setText(defaultAmount != null ? defaultAmount : fastClickPaymentSystem.getDefaultAmount().toString().split("\\.")[0]);

        tvFastPaymentCurrency.setText(fastClickPaymentSystem.getCurrency().getSymbol());
        String requisite = fastClickPaymentSystem.getRequisite()
                .substring(fastClickPaymentSystem.getRequisite().length() - REQUISITE_CHARACTERS);
        //tvFastPaymentRequisite.setText(requisite);
        if ( !TextUtils.isEmpty(fastClickPaymentSystem.getLogo()) ) {
            Picasso.get().load(ApoloConfig.getFullUrl(fastClickPaymentSystem.getLogo())).into(ivFastPaymentLogo);
        } else {
            Log.e(TAG, "FastClickPaymentSystem logo is empty");
        }

        btnFastPayment.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                hideKeyboard();
                if(!isPayInProcess) {
                    lay_1click.setVisibility(View.GONE);
                    lay_confirm.setVisibility(View.VISIBLE);
                    textConfirm.setText(Html.fromHtml(context.getString(R.string.to_pay_text, "<b>" + etFastPaymentAmount.getText().toString() + " " + tvFastPaymentCurrency.getText().toString() + "</b>")));
                }else{
                    Toast.makeText(context, context.getString(R.string.pay_in_process), Toast.LENGTH_SHORT).show();
                }
            }
        });

        btnFastPaymentDismiss.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                lay_1click.setVisibility(View.VISIBLE);
                lay_confirm.setVisibility(View.GONE);
            }
        });

        btnFastPaymentConfirm.setOnClickListener(v -> {
            String input = GeneralTools.removeSpaces(etFastPaymentAmount.getText().toString());
            Integer amount = Integer.valueOf(input);
            etFastPaymentAmount.clearFocus();
            if ( fastClickPaymentSystem.isRebill() ) {
                GeneralTools.hideKeyboard(context, etFastPaymentAmount);
                isPayInProcess = true;
                //btnFastPayment.setEnabled(false);
            }

            if ( buttonFastPaymentClickListener != null ) {
                buttonFastPaymentClickListener.onClick(fastClickPaymentSystem.isRebill(), amount);
            }
            lay_1click.setVisibility(View.VISIBLE);
            lay_confirm.setVisibility(View.GONE);
        });
    }

    protected TextWatcher fastPaymentAmountTextWatcher = new TextWatcher() {

        @Override
        public void afterTextChanged(Editable s) {
            processFastPaymentAmountInput(etFastPaymentAmount, btnFastPayment, this);
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }
    };

    private void processFastPaymentAmountInput(EditText etFastPaymentAmount, Button btnFastPayment,
                                               TextWatcher textWatcher) {
        final char space          = ' ';
        final int  SPACE_POSITION = 3;
        String     input          = GeneralTools.removeSpaces(etFastPaymentAmount.getText().toString());

        try {
            long amount    = Long.parseLong(input);
            int  minAmount = (int) Double.parseDouble(fastClickPaymentSystem.getMinAmount().toString());
            int  maxAmount = (int) Double.parseDouble(fastClickPaymentSystem.getMaxAmount().toString());

            if ( amount < minAmount ) {
                btnFastPayment.setText(minDepositWarning);
                btnFastPayment.setEnabled(false);
                btnFastPayment.setBackground(ContextCompat.getDrawable(context, R.drawable.btn_bg_fast_payment_disable));
            } else if ( amount > maxAmount) {
                btnFastPayment.setText(maxDepositWarning);
                btnFastPayment.setEnabled(false);
                btnFastPayment.setBackground(ContextCompat.getDrawable(context, R.drawable.btn_bg_fast_payment_disable));
            } else {
                btnFastPayment.setText(context.getString(R.string.to_pay_1click));
                btnFastPayment.setEnabled(true);
                btnFastPayment.setBackground(ContextCompat.getDrawable(context, R.drawable.btn_bg_enable));
            }
        } catch ( NumberFormatException e ) {
            Log.d("FastClickPayment", e.toString());
            btnFastPayment.setText(minDepositWarning); // block refill button if parse error
            btnFastPayment.setEnabled(false);
            btnFastPayment.setBackground(ContextCompat.getDrawable(context, R.drawable.btn_bg_fast_payment_disable));
        }

        StringBuilder inputBuilder = new StringBuilder(input);
        for ( int i = inputBuilder.length() - SPACE_POSITION; i > 0; i -= SPACE_POSITION ) {
            inputBuilder.insert(i, space);
        }

        etFastPaymentAmount.removeTextChangedListener(textWatcher);
        etFastPaymentAmount.setText(inputBuilder.toString());
        etFastPaymentAmount.addTextChangedListener(textWatcher);
        etFastPaymentAmount.setSelection(inputBuilder.length());
    }

    public void setButtonFastPaymentClickListener(ButtonFastPaymentClickListener buttonFastPaymentClickListener) {
        this.buttonFastPaymentClickListener = buttonFastPaymentClickListener;
    }

    public void showLoader() {
        containerFastPaymentLoader.setVisibility(View.VISIBLE);
        timer_loader = new CountDownTimer(15000, 20) {

            public void onTick(long millisUntilFinished) {
                if(pbFastPaymentLoader.getProgress()<99)
                    pbFastPaymentLoader.setProgress(pbFastPaymentLoader.getProgress()+1);
                if(millisUntilFinished<11000){
                    hideLoader();
                }
            }

            public void onFinish() {
                isPayInProcess = false;
                hideLoader();
            }

        }.start();
    }

    public void showResult(Boolean success) {
        hideLoader();

        if ( success ) {
            try {
                timer_loader.cancel();
            } catch ( Exception e ) {}
            isPayInProcess = false;
            tvResult.setText(getResources().getString(R.string.transaction_successful));
            tvResult.setTextColor(Color.parseColor("#279822"));
            tvResultMessage.setText(getResources().getString(R.string.transaction_successful_message));
            pbResult.setProgressDrawable(getResources().getDrawable(R.drawable.progress_bar_success));
        } else {
            tvResult.setText(getResources().getString(R.string.transaction_fail));
            tvResult.setTextColor(Color.parseColor("#c92014"));
            tvResultMessage.setText(getResources().getString(R.string.transaction_fail_message));
            pbResult.setProgressDrawable(getResources().getDrawable(R.drawable.progress_bar_error));
        }
        layResult.setVisibility(VISIBLE);

        timer_success = new CountDownTimer(2500, 25) {

            public void onTick(long millisUntilFinished) {
                pbResult.setProgress(pbResult.getProgress()+1);
            }

            public void onFinish() {
                layResult.setVisibility(View.GONE);
                pbResult.setProgress(0);
            }

        }.start();
    }

    public void hideLoader() {
        containerFastPaymentLoader.setVisibility(View.GONE);
        pbFastPaymentLoader.setProgress(0);
    }

    public void hideKeyboard() {
        GeneralTools.hideKeyboard(context, etFastPaymentAmount);
    }

    public interface ButtonFastPaymentClickListener {
        void onClick(boolean isRebill, Integer amount);
    }
}
