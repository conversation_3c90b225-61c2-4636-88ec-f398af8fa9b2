package com.abrand.custom.ui.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.appcompat.widget.SwitchCompat;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.abrand.custom.R;

public class StateSwitchView extends ConstraintLayout {
    private TextView     textView;
    private SwitchCompat switchView;
    private SwitchListener switchListener;

    public StateSwitchView(Context context) {
        super(context);
        init(context, null, -1);
    }

    public StateSwitchView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, -1);
    }

    public StateSwitchView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        ViewGroup rootView = inflate(context, R.layout.view_state_switch, this).findViewById(R.id.root);
        textView = rootView.findViewById(R.id.text);
        switchView = rootView.findViewById(R.id.switch_view);
        rootView.setOnClickListener(v -> {
            switchView.performClick();
            switchListener.onChecked(switchView.isChecked());
        });
    }

    public void setText(String text) {
        post(() -> textView.setText(text));
    }

    public void setCheckedWithoutListener(boolean checked) {
        post(() -> {
                    SwitchListener currentSwitchListener = this.switchListener;
                    this.switchListener = null;
                    switchView.setChecked(checked);
                    this.switchListener = currentSwitchListener;
                }
        );
    }

    public void setSwitchListener(SwitchListener listener) {
        this.switchListener = listener;
        switchView.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if ( buttonView.isPressed() && switchListener != null ) {
                switchListener.onChecked(isChecked);
            }
        });
    }

    public interface SwitchListener {
        void onChecked(boolean isChecked);
    }
}
