package com.abrand.custom.ui.bonusbalances

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.util.Locale

class BonusBalancesViewModel: ViewModel() {
    private val getBonusesBalancesUseCase: GetBonusesBalancesUseCase = GetBonusesBalancesUseCase()
    private val activateBonusBalanceAndGetUpdatedListUseCase: ActivateBonusBalanceAndGetUpdatedListUseCase = ActivateBonusBalanceAndGetUpdatedListUseCase()
    private val deleteBonusBalanceUseCase: DeleteBonusBalanceUseCase = DeleteBonusBalanceUseCase()
    private val _bonusesState = MutableStateFlow<List<BonusBalanceUi>>(
        emptyList()
    )
    val bonusesState = _bonusesState.asStateFlow()
    private val _sideEffectChannel = Channel<SideEffect>(capacity = Channel.BUFFERED)
    val sideEffectFlow = _sideEffectChannel.receiveAsFlow()

    fun getBonusBalances() {
        viewModelScope.launch {
            val bonuses = getBonusesBalancesUseCase.invoke()

            val bonusesUi = mutableListOf<BonusBalanceUi>()

            for (bonus in bonuses) {
                bonusesUi.add(bonus.mapToUi())
            }

            _bonusesState.emit(bonusesUi.toList())
        }
    }

    fun onEvent(event: BonusBalancesUiEvent) {
        when (event) {
            is BonusBalancesUiEvent.ActivateBonusBalance -> {
                activateBonus(event.bonusBalanceId)
            }

            is BonusBalancesUiEvent.DeleteBonusBalance -> {
                deleteBonus(event.bonusBalanceId)
            }
        }
    }

    fun getActiveBonusAmount() :String? {
        return _bonusesState.value.filter { it.isActive }.getOrNull(0)?.amount
    }

    private fun activateBonus(bonusId: Int) {
        viewModelScope.launch {
            val result = activateBonusBalanceAndGetUpdatedListUseCase.invoke(bonusId)
            val bonusesUi = mutableListOf<BonusBalanceUi>()
            val newList: List<BonusBalance>

            when (result) {
                is BonusActivateResult.Success -> {
                    newList = result.list
                }
                is BonusActivateResult.Error -> {
                    _sideEffectChannel.send(SideEffect.BonusActivateError)
                    newList = result.list
                }
            }

            for (bonus in newList) {
                bonusesUi.add(bonus.mapToUi())
            }

            _bonusesState.emit(bonusesUi.toList())
        }
    }

    private fun deleteBonus(bonusId: Int) {
        viewModelScope.launch {
            val result = deleteBonusBalanceUseCase.invoke(bonusId)
            val bonusesUi = mutableListOf<BonusBalanceUi>()
            val newList: List<BonusBalance>

            when (result) {
                is BonusDeleteResult.Success -> {
                    newList = result.list
                }
                is BonusDeleteResult.Error -> {
                    _sideEffectChannel.send(SideEffect.BonusDeleteError)
                    newList = result.list
                }
            }

            for (bonus in newList) {
                bonusesUi.add(bonus.mapToUi())
            }

            _bonusesState.emit(bonusesUi.toList())
        }
    }
}

sealed interface BonusBalancesUiEvent {
    data class ActivateBonusBalance(val bonusBalanceId: Int): BonusBalancesUiEvent
    data class DeleteBonusBalance(val bonusBalanceId: Int): BonusBalancesUiEvent
}

sealed interface SideEffect {
    data object BonusDeleteError: SideEffect
    data object BonusActivateError: SideEffect
}

fun BonusBalance.mapToUi(): BonusBalanceUi {
    val formatter = DecimalFormat("#,###", DecimalFormatSymbols(Locale.ENGLISH))
    val wageringExpiredAt = this.wageringExpiredAt?.let { dateToInstant(it) }
    val wageringMaxTransferAmount = if (this.wageringMaxTransferAmount != null) {
        this.wageringMaxTransferAmount.convertToUIString(formatter)
    } else {
        null
    }

    return BonusBalanceUi(
        id = this.id,
        wager = this.wager,
        isActive = this.isActive,
        amount = this.amount.convertToUIString(formatter),
        wageringTarget = this.wageringTarget.convertToUIString(formatter),
        wageringCurrent = this.wageringCurrent.convertToUIString(formatter),
        wageringCompletePercent = this.wageringCompletePercent,
        wageringExpiredAt = wageringExpiredAt,
        wageringMaxTransferAmount = wageringMaxTransferAmount
    )
}

fun Float.convertToUIString(formatter: DecimalFormat): String {
    val rubSymbol = " ₽"
    return "${formatter.format(this).replace(',',' ')}$rubSymbol"
}

private fun dateToInstant(dateValue: String): Instant? {
    return runCatching {
        Instant.parse(dateValue)
    }.getOrNull()
}
