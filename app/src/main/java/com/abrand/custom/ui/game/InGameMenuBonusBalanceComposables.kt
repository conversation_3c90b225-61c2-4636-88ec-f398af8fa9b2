package com.abrand.custom.ui.game

import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridState
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.PlainTooltip
import androidx.compose.material3.TooltipBox
import androidx.compose.material3.TooltipDefaults
import androidx.compose.material3.adaptive.currentWindowAdaptiveInfo
import androidx.compose.material3.rememberTooltipState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.window.core.layout.WindowSizeClass
import androidx.window.core.layout.WindowWidthSizeClass
import com.abrand.custom.R
import com.abrand.custom.ui.bonusbalances.AvailablePromotions
import com.abrand.custom.ui.bonusbalances.BonusBalanceCardContainer
import com.abrand.custom.ui.bonusbalances.BonusBalancesViewModel
import com.abrand.custom.ui.bonusbalances.MediumText
import com.abrand.custom.ui.bonusbalances.NoBonusesView
import com.abrand.custom.ui.bonusbalances.RegularText
import com.abrand.custom.ui.bonusbalances.SideEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@Composable
fun BonusBalancesScreen(
    modifier: Modifier = Modifier,
    viewModel: BonusBalancesViewModel = viewModel(),
    windowSizeClass: WindowSizeClass = currentWindowAdaptiveInfo().windowSizeClass,
    openPromotions: () -> Unit = {},
) {
    viewModel.getBonusBalances()
    val bonusesState by viewModel.bonusesState.collectAsState()
    val lifecycle = LocalLifecycleOwner.current.lifecycle
    val context = LocalContext.current

    LaunchedEffect(viewModel, lifecycle) {
        lifecycle.repeatOnLifecycle(state = Lifecycle.State.STARTED) {
            withContext(Dispatchers.Main.immediate) {
                viewModel.sideEffectFlow.collect { sideEffect ->
                    when (sideEffect) {
                        SideEffect.BonusDeleteError -> {
                            Toast.makeText(
                                context,
                                R.string.bonus_balances_deletion_error_text,
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        SideEffect.BonusActivateError -> {
                            Toast.makeText(
                                context,
                                R.string.bonus_balances_activation_error_text,
                                Toast.LENGTH_LONG
                            ).show()
                        }
                    }
                }
            }
        }
    }

    Column(modifier) {
        BonusesTitle()
        Spacer(Modifier.height(16.dp))
        if (bonusesState.isNotEmpty()) {
            val coroutineScope = rememberCoroutineScope()
            val state: LazyStaggeredGridState = rememberLazyStaggeredGridState()
            val scrollToStart = {
                coroutineScope.launch {
                    state.animateScrollToItem(0)
                }
            }
            val columnsCount = when (windowSizeClass.windowWidthSizeClass) {
                WindowWidthSizeClass.EXPANDED -> 3
                WindowWidthSizeClass.MEDIUM -> 2
                WindowWidthSizeClass.COMPACT -> 1
                else -> 1
            }

            LazyVerticalStaggeredGrid(
                columns = StaggeredGridCells.Fixed(columnsCount),
                state = state,
                verticalItemSpacing = 8.dp,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                items(bonusesState.size) { index ->
                    BonusBalanceCardContainer(
                        bonusesState[index],
                        viewModel::onEvent,
                        scrollToStart,
                        viewModel::getActiveBonusAmount,
                        activateBtnOnSeparateRow = windowSizeClass.windowWidthSizeClass != WindowWidthSizeClass.EXPANDED,
                    )
                }
                item(span = StaggeredGridItemSpan.FullLine) {
                    Row(modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Center,
                    ) {
                        AvailablePromotions(
                            modifier = Modifier
                                .padding(top = 24.dp, bottom = 24.dp)
                                .size(width = 188.dp, height = 48.dp),
                            openPromotions = openPromotions,
                        )
                    }
                }
            }
        } else {
            NoBonusesView(
                modifier = Modifier
                    .defaultMinSize(minHeight = 212.dp)
                    .padding(16.dp)
                    .fillMaxWidth(),
                openPromotions = openPromotions,
            )
        }
    }
}

@Composable
@OptIn(ExperimentalMaterial3Api::class)
private fun BonusesTitle() {
    val scope = rememberCoroutineScope()
    val tooltipState = rememberTooltipState()

    Row {
        MediumText(
            text = stringResource(R.string.bonus_balance_title_text),
            fontSize = 16.sp,
            lineHeight = 24.sp,
        )
        TooltipBox(
            positionProvider = TooltipDefaults.rememberPlainTooltipPositionProvider(),
            tooltip = {
                PlainTooltip(
                    caretSize = TooltipDefaults.caretSize,
                    containerColor = Color(0XFF55BEF9),
                ) {
                    RegularText(
                        modifier = Modifier.padding(8.dp),
                        text = stringResource(R.string.bonus_balances_title_hint),
                        fontSize = 12.sp, lineHeight = 20.sp,
                        color = Color(0XFFFFFFFF),
                    )
                }
            },
            state = tooltipState
        ) {
            Image(
                imageVector = ImageVector.vectorResource(R.drawable.ic_lottery_info),
                contentScale = ContentScale.None,
                contentDescription = null,
                modifier = Modifier
                    .size(24.dp)
                    .clickable { scope.launch { tooltipState.show() } }
                    .padding(start = 8.dp)

            )
        }
    }
}


@Preview
@Composable
fun BonusBalancesScreenPreview() {
    BonusBalancesScreen()
}
