package com.abrand.custom.ui.views

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.squareup.picasso.Picasso

class RequisiteView : ConstraintLayout {
    private var ivLogo: ImageView? = null
    private var tvRequisite: TextView? = null
    private val REQUISITE_CHARACTERS = 4

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {
        val rootView = inflate(context, R.layout.view_requisite, this)
        ivLogo = rootView.findViewById(R.id.iv_logo)
        tvRequisite = rootView.findViewById(R.id.tv_requisite)
    }

    fun setLogo(logoUrl: String) {
        if (!TextUtils.isEmpty(logoUrl)) {
            Picasso.get().load(ApoloConfig.getFullUrl(logoUrl)).into(ivLogo)
        }
    }

    fun setRequisite(requisite: String) {
        tvRequisite?.text = requisite.substring(requisite.length - REQUISITE_CHARACTERS)
    }

}
