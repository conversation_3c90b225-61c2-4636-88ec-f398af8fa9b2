package com.abrand.custom.ui.game

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException

class InGameMenuTournamentViewModel : ViewModel() {
    val tournamentLiveData =
        MutableLiveData<Resource<TournamentsItem, ServerError, ApolloException>>()

    fun getTournament(tournamentId: Int) {
        ApolloProcessorKt.loadTournamentById(tournamentId, object: GenericTarget<TournamentsItem> {

            override fun onSuccess(t: TournamentsItem?) {
                t?.apply {
                    tournamentLiveData.postValue(Resource.success(this))
                }
            }

            override fun onFailure(e: ApolloException) {
                tournamentLiveData.postValue(Resource.failure(e))
            }

            override fun onError(errorMessage: String, errorCode: String,
                                 fieldsErrors: FieldsErrorsHolder?) {
                tournamentLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode,
                    fieldsErrors)))
            }
        }, viewModelScope)
    }

    fun joinTournament(tournamentId: Int) {
        ApolloProcessorKt.joinTournament(tournamentId, object : GenericTarget<TournamentsItem> {

            override fun onSuccess(t: TournamentsItem?) {
                tournamentLiveData.postValue(Resource.success(t))
            }

            override fun onFailure(e: ApolloException) {
                tournamentLiveData.postValue(Resource.failure(e))
            }

            override fun onError(errorMessage: String, errorCode: String,
                                 fieldsErrors: FieldsErrorsHolder?) {
                tournamentLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode,
                    fieldsErrors)))
            }
        }, viewModelScope)
    }
}
