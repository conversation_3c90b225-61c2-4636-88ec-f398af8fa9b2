package com.abrand.custom.ui.terms;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.abrand.custom.databinding.FragmentTermsBinding;

public class TermsFragment extends Fragment {
    private FragmentTermsBinding binding;
    private RecyclerView recyclerView = null;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentTermsBinding.inflate(inflater);
        View root = binding.getRoot();

        recyclerView = binding.rvTerms;
        if ( recyclerView != null ) {
            recyclerView.setHasFixedSize(true);
            TermsListAdapter adapter = new TermsListAdapter(getContext());
            LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
            recyclerView.setAdapter(adapter);
            recyclerView.setLayoutManager(layoutManager);
        }

        setupInsets();

        return root;
    }

    private void setupInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(recyclerView, (view, insets) -> {
            view.setPadding(view.getPaddingLeft(), insets.getSystemWindowInsetTop(),
                    view.getPaddingRight(), insets.getSystemWindowInsetBottom());
            return insets;
        });
    }
}
