<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M10.5,15C12.9853,15 15,12.9853 15,10.5C15,8.0147 12.9853,6 10.5,6C8.0147,6 6,8.0147 6,10.5C6,12.9853 8.0147,15 10.5,15ZM15.7489,14.3347L19.704,18.2897C20.0945,18.6803 20.0945,19.3134 19.704,19.704C19.3134,20.0945 18.6803,20.0945 18.2897,19.704L14.3347,15.7489C13.2597,16.5356 11.9341,17 10.5,17C6.9102,17 4,14.0899 4,10.5C4,6.9102 6.9102,4 10.5,4C14.0899,4 17,6.9102 17,10.5C17,11.9341 16.5356,13.2597 15.7489,14.3347Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="19.9969"
          android:startX="19.9969"
          android:endY="4"
          android:endX="19.9969"
          android:type="linear">
        <item android:offset="0" android:color="#FF008E00"/>
        <item android:offset="1" android:color="#FF6ED200"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
