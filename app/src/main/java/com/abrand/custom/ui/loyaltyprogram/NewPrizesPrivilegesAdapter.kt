package com.abrand.custom.ui.loyaltyprogram

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R

class NewPrizesPrivilegesAdapter(private var items: List<String>) :
    RecyclerView.Adapter<NewPrizesPrivilegesAdapter.NewPrizesPrivilegesVH>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NewPrizesPrivilegesVH {
        val inflater = LayoutInflater.from(parent.context)
        return NewPrizesPrivilegesVH(inflater, parent)
    }

    override fun onBindViewHolder(holder: NewPrizesPrivilegesVH, position: Int) {
        val item = items[position]
        holder.tvTitle.text = item
    }

    override fun getItemCount() = items.size

    class NewPrizesPrivilegesVH(inflater: LayoutInflater, parent: ViewGroup) :
        RecyclerView.ViewHolder(inflater.inflate(R.layout.item_new_prizes_privileges, parent, false)) {
        var tvTitle: TextView = itemView.findViewById(R.id.tv_title)
    }

}
