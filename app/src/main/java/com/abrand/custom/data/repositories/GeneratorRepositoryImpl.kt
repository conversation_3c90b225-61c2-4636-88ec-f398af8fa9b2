package com.abrand.custom.data.repositories

import com.abrand.custom.data.datasources.DeviceInfoProvider
import com.abrand.custom.data.datasources.DeviceInfoProviderImpl
import com.abrand.custom.data.datasources.GeneratorRemoteDataSource
import com.abrand.custom.data.datasources.GeneratorRemoteDataSourceImpl
import com.abrand.custom.data.entity.DeviceInfo
import com.abrand.custom.data.entity.GeneratorResponse
import com.abrand.custom.domain.CheckResponse
import com.abrand.custom.domain.GeneratorRepository
import com.abrand.custom.data.entity.AnalyticsDataRequest
import java.net.URI

internal class GeneratorRepositoryImpl( //todo make singleton with DI
    private val generatorRemoteDataSource: GeneratorRemoteDataSource = GeneratorRemoteDataSourceImpl(),
    private val deviceInfoProvider: DeviceInfoProvider = DeviceInfoProviderImpl(),
) : GeneratorRepository {

    override suspend fun initCustomer(
        urlParams: String,
        uuid: String,
        refCode: String,
        downloadCode: String,
        domains: List<String>
    ): String {
        return generatorRemoteDataSource.initCustomer(
            urlParams = urlParams,
            analytics = deviceInfoProvider.getDeviceInfo().buildAnalyticsRequestData(
                uuid, refCode, downloadCode
            ).toString(),
            domains = domains
        ).data
    }

    override suspend fun check(urlParams: String, domains: List<String>): CheckResponse {
        return generatorRemoteDataSource.check(urlParams = urlParams, domains = domains)
            .toCheckResponse()
    }

    override suspend fun getInstalled(urlParams: String, domains: List<String>): String {
        return generatorRemoteDataSource.getInstalled(urlParams = urlParams, domains = domains)
            .data
    }

    override suspend fun sendAnalytics(url: String) {
        return generatorRemoteDataSource.sendInstallAnalytics(url)
    }

    override fun addParametersToUrl(url: String, paramsMap: Map<String, String>): String {
        val uri = URI(url)
        val newQuery = paramsMap.map { (key, value) -> "$key=$value" }.joinToString("&")

        val finalQuery = if (uri.query.isNullOrEmpty()) {
            newQuery
        } else {
            uri.query + "&" + newQuery
        }

        return URI(
            uri.scheme,
            uri.authority,
            uri.path,
            finalQuery,
            uri.fragment
        ).toString()
    }

    private fun DeviceInfo.buildAnalyticsRequestData(
        uuid: String, refCode: String, downloadCode: String,
    ) = AnalyticsDataRequest(
        isEmulator = isEmulator,
        deviceId = deviceId,
        locale = locale,
        diagonalInches = diagonalInches,
        androidApiVersion = androidApiVersion,
        simRegion = simRegion,
        brand = brand,
        deviceUpTimeSeconds = deviceUpTimeSeconds,
        accelerometer = accelerometer,
        fireBaseId = "",
        modelName = modelName,
        operatorName = operatorName,
        refCode = refCode,
        deepLink = "",
        uuid = uuid,
        downloadCode = downloadCode
    )

    private fun GeneratorResponse.toCheckResponse() = CheckResponse(
        data = data,
    )
}
