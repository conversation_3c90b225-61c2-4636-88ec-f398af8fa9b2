package com.abrand.custom.ui.views;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ExpandableListView;

import com.abrand.custom.R;

public class NonScrollExpandableListView extends ExpandableListView {
    private boolean navBarShow = true;

    public NonScrollExpandableListView(Context context) {
        super(context);
    }

    public NonScrollExpandableListView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public NonScrollExpandableListView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int heightMeasureSpec_custom_start = MeasureSpec.makeMeasureSpec(Integer.MAX_VALUE >> 2, MeasureSpec.AT_MOST);
        super.onMeasure(widthMeasureSpec, heightMeasureSpec_custom_start);

        if ( navBarShow ) {
            int heightEnd = getMeasuredHeight() + getContext().getResources().getDimensionPixelSize(R.dimen.navigation_menu_bottom_padding);
            int heightMeasureSpec_custom_end = MeasureSpec.makeMeasureSpec(heightEnd, MeasureSpec.AT_MOST);
            super.onMeasure(widthMeasureSpec, heightMeasureSpec_custom_end);
        }
    }

    public void setNavBarShow(boolean show) {
        this.navBarShow = show;
    }
}
