package com.abrand.custom.data.entity

import androidx.annotation.DrawableRes
import com.abrand.custom.R

enum class SocialNetwork(val value: String, @DrawableRes val icon: Int) {
    VKONTAKTE("vkontakte", R.drawable.ic_social_vkontakte),
    MAILRU("mailru", R.drawable.ic_social_mail_ru),
    ODNOKLASSNIKI("odnoklassniki", R.drawable.ic_social_ok_ru),
    YANDEX("yandex", R.drawable.ic_social_yandex),
    FACEBOOK("facebook", R.drawable.ic_social_facebook);
//    TWITTER("twitter", R.drawable.ic_social_twitter);

    companion object {
        fun from(value: String): SocialNetwork? = values().find { it.value == value }
    }
}
