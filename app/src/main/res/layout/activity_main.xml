<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:openDrawer="start">

    <include
        android:id="@+id/app_bar"
        layout="@layout/app_bar_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view_root"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start">

        <androidx.core.widget.NestedScrollView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:orientation="vertical">

                <com.google.android.material.navigation.NavigationView
                    android:id="@+id/nav_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    app:elevation="0dp">

                    <com.abrand.custom.ui.views.NonScrollExpandableListView
                        android:id="@+id/navigation_menu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/white"
                        android:clipToPadding="false"
                        android:groupIndicator="@null"
                        android:scrollbars="none" />

                </com.google.android.material.navigation.NavigationView>

                <LinearLayout
                    android:id="@+id/spacer_to_bottom"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="vertical" />

                <include
                    android:id="@+id/nav_footer_view"
                    layout="@layout/nav_footer" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </com.google.android.material.navigation.NavigationView>

</androidx.drawerlayout.widget.DrawerLayout>
