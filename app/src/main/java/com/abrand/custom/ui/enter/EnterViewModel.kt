package com.abrand.custom.ui.enter

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.GetReCaptchaQuery
import com.abrand.custom.data.Resource
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.AuthResponse
import com.abrand.custom.data.entity.AvailableSocialNetworksResponse
import com.abrand.custom.data.entity.ResponseException
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.domain.CaptchaType
import com.abrand.custom.domain.GetRecaptchaUseCase
import com.abrand.custom.domain.RecaptchaResponse
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.interfaces.GetRegisterBonusesTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.type.CaptchaForm
import com.apollographql.apollo3.exception.ApolloException
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class EnterViewModel : ViewModel() {
    private val getRecaptchaUseCase = GetRecaptchaUseCase<Unit>()
    private val _socialNetworksLiveData =
        MutableLiveData<Resource<AvailableSocialNetworksResponse, ServerError, ApolloException>>()
    val socialNetworksLiveData: LiveData<Resource<AvailableSocialNetworksResponse, ServerError, ApolloException>> =
        _socialNetworksLiveData

    private val _eventChannel = Channel<EnterEffects>(capacity = Channel.BUFFERED)
    val eventFlow: Flow<EnterEffects> = _eventChannel.receiveAsFlow()

    fun getReCaptcha(captchaForm: CaptchaForm, target: GenericTarget<GetReCaptchaQuery.ReCaptcha>) {
        ApolloProcessorKt.getReCaptcha(captchaForm, target, viewModelScope)
    }

    fun getRegisterBonuses(target: GetRegisterBonusesTarget) {
        ApolloProcessorKt.getRegisterBonuses(target, viewModelScope)
    }

    fun register(
        email: String,
        password: String,
        language: String,
        registrationBonusId: Int,
        adjustId: String?,
        pushSubscriptionData: String?,
        target: GenericTarget<AuthResponse>
    ) {
        viewModelScope.launch {
            val result = getRecaptchaUseCase(CaptchaType.REGISTER) { solvedCaptcha ->
                ApolloProcessorKt.register(
                    email, password,
                    language,
                    registrationBonusId,
                    solvedCaptcha?.gCaptchaResponse,
                    adjustId,
                    pushSubscriptionData,
                    Settings.get().refCode,
                    target, viewModelScope
                )
            }

            when (result) {
                is RecaptchaResponse.Executed<Unit> -> {
                    //NOP event processed via target: GenericTarget<AuthResponse> callback
                }

                is RecaptchaResponse.CaptchaNotSolved -> {
                    _eventChannel.send(EnterEffects.CaptchaStatusError(result.message))
                }

                is RecaptchaResponse.NetworkError -> {
                    _eventChannel.send(EnterEffects.CaptchaStatusNetworkError(result.e))
                }
            }
        }
    }

    fun login(
        email: String,
        password: String,
        adjustId: String?,
        pushSubscriptionData: String?,
        target: GenericTarget<AuthResponse>
    ) {
        viewModelScope.launch {
            val result = getRecaptchaUseCase(CaptchaType.LOGIN) { solvedCaptcha ->
                ApolloProcessorKt.login(
                    email, password,
                    solvedCaptcha?.gCaptchaResponse ?: "",
                    adjustId,
                    pushSubscriptionData,
                    Settings.get().refCode,
                    target, viewModelScope
                )
            }

            when (result) {
                is RecaptchaResponse.Executed<Unit> -> {
                    //NOP event processed via target: GenericTarget<AuthResponse> callback
                }

                is RecaptchaResponse.CaptchaNotSolved -> {
                    _eventChannel.send(EnterEffects.CaptchaStatusError(result.message))
                }

                is RecaptchaResponse.NetworkError -> {
                    _eventChannel.send(EnterEffects.CaptchaStatusNetworkError(result.e))
                }
            }
        }
    }

    fun getSocialNetworks() {
        ApolloProcessorKt.getSocialNetworks(object : GenericTarget<AvailableSocialNetworksResponse> {
            override fun onSuccess(t: AvailableSocialNetworksResponse?) {
                _socialNetworksLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _socialNetworksLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _socialNetworksLiveData.postValue(Resource.failure(e))
            }

        })
    }
}

sealed interface EnterEffects {
    data object EmptyReCaptchaID : EnterEffects
    data class CaptchaStatusError(val message: String) : EnterEffects
    data class CaptchaStatusNetworkError(val e: ResponseException) : EnterEffects
}
