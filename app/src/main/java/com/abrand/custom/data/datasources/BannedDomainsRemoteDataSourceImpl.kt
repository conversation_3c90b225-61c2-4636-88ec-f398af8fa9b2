package com.abrand.custom.data.datasources

import com.abrand.custom.BuildConfig
import com.abrand.custom.data.entity.BannedDomainDTO
import com.abrand.custom.data.entity.BannedDomainDataDTO
import com.abrand.custom.data.entity.BannedDomainResponseDTO
import com.abrand.custom.data.entity.BannedDomainsData
import com.abrand.custom.data.entity.BannedDomainsRequestDTO
import com.abrand.custom.data.entity.appconfig.LocalConfig
import com.abrand.custom.tools.Utils.validateUrl
import retrofit2.Response

internal class BannedDomainsRemoteDataSourceImpl(
    private val apiService: BannedDomainsApiService = BannedDomainsApiService.provideBannedDomainsService()
) : BannedDomainsRemoteDataSource {

    override suspend fun sendBannedDomains(
        appConfig: LocalConfig,
        bannedDomainsList: List<BannedDomainDTO>,
        deviceId: String,
        country: String?
    ): BannedDomainResponseDTO {
        return processRequest(
            request = { url ->
                apiService.sendBannedDomains(
                    url = url,
                    body = BannedDomainsRequestDTO(
                        applicationToken = appConfig.token,
                        customerToken = appConfig.customerToken ?: "",
                        data = bannedDomainsList.map { banned ->
                            BannedDomainDataDTO(
                                domain = banned.domain,
                                actionName = banned.action,
                                reason = banned.reason,
                                statusCode = banned.code,
                                errorDatetime = banned.time,
                                device = deviceId,
                                country = country,
                                packageId = BuildConfig.APPLICATION_ID
                            )
                        }
                    ))
            },
            domains = appConfig.domains
        )
    }

    private suspend fun processRequest(
        request: suspend (String) -> Response<BannedDomainResponseDTO>,
        domains: List<String>
    ): BannedDomainResponseDTO {
        var lastException: Exception? = null

        for (domain in domains) {
            try {
                val url = validateUrl(domain) + BANNED_DOMAINS_URL_PATH
                val response = request(url)
                if (response.isSuccessful) {
                    val serverResponse = response.body()
                    if (serverResponse != null && serverResponse.status) {
                        return serverResponse
                    }
                }
            } catch (e: Exception) {
                lastException = e
            }
        }

        return BannedDomainResponseDTO(
            false,
            lastException?.message.orEmpty(),
            BannedDomainsData(emptyList(), emptyList())
        )
    }

    companion object {
        private const val BANNED_DOMAINS_URL_PATH = "domain-stat-v2"
    }
}
