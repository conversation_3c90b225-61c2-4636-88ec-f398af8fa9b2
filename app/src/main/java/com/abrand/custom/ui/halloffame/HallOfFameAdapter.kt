package com.abrand.custom.ui.halloffame

import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.HallOfFamePlace
import com.abrand.custom.databinding.ItemHallOfFameBinding
import com.abrand.custom.tools.GeneralTools
import com.squareup.picasso.Picasso
import com.squareup.picasso.Target

class HallOfFameAdapter : RecyclerView.Adapter<HallOfFameAdapter.ViewHolder>() {
    var items = mutableListOf<HallOfFamePlace>()
    var listener: Listener? = null

    companion object {
        const val CURRENT_USER_COLOR = "#f8b523"
        const val OTHER_USER_NUMBER_COLOR = "#40ffffff"
        const val OTHER_USER_NAME_COLOR = "#ffffff"
        const val DEFAULT_THUMBNAIL_PATH = "assets/img/mobile/games/defaultThumb.jpg"
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = ItemHallOfFameBinding.inflate(inflater, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.bind(item)
    }

    fun setList(newList: MutableList<HallOfFamePlace>) {
        this.items = newList
        notifyDataSetChanged()
    }

    inner class ViewHolder(val view: ItemHallOfFameBinding) : RecyclerView.ViewHolder(view.root) {

        fun bind(hallOfFamePlace: HallOfFamePlace) {
            view.tvNumber.text = hallOfFamePlace.position.toString()
            setGame(hallOfFamePlace.gameItem)

            if (hallOfFamePlace.user.id != Settings.get().userId) {
                view.tvUser.text = hallOfFamePlace.user.formattedUserName
                view.tvNumber.setTextColor(Color.parseColor(OTHER_USER_NUMBER_COLOR))
                view.tvUser.setTextColor(Color.parseColor(OTHER_USER_NAME_COLOR))
            } else {
                if (Settings.get().userName.isNullOrEmpty()) {
                    view.tvUser.text = Settings.get().loggedUserEmail
                } else {
                    view.tvUser.text = Settings.get().userName
                }
                view.tvNumber.setTextColor(Color.parseColor(CURRENT_USER_COLOR))
                view.tvUser.setTextColor(Color.parseColor(CURRENT_USER_COLOR))
            }

            view.tvLoyaltyStatus.text = hallOfFamePlace.user.loyaltyStatus
            view.tvPrize.text = GeneralTools.formatBalance(Settings.get().userCurrencyCode, hallOfFamePlace.maxWinMoneyAmount)

            if (adapterPosition % 2 == 0) {
                view.root.setBackgroundColor(Color.parseColor("#1455BEF9"))
            } else {
                view.root.setBackgroundColor(Color.TRANSPARENT)
            }

            view.llGameUser.setOnClickListener {
                hallOfFamePlace.gameItem.url?.let { url -> listener?.onGameClicked(url) }
            }
        }

        private fun setGame(gameItem: HallOfFamePlace.GameItem?) {
            val imageUrl = gameItem?.iconMob?.takeIf { !TextUtils.isEmpty(it) }
                ?: DEFAULT_THUMBNAIL_PATH

            Picasso.get()
                .load(ApoloConfig.getFullUrl(imageUrl))
                .into(target)
        }

        private val target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: Picasso.LoadedFrom) {
                view.ivGame.setImageBitmap(bitmap)
            }

            override fun onBitmapFailed(e: Exception, errorDrawable: Drawable?) { }

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) { }
        }
    }

    interface Listener {
        fun onGameClicked(gameUrl: String)
    }
}
