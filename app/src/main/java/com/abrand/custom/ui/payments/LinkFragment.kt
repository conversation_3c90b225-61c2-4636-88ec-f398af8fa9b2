package com.abrand.custom.ui.payments

import android.annotation.SuppressLint
import android.content.ActivityNotFoundException
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.*
import android.text.TextUtils
import android.util.Patterns
import android.view.View
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.webkit.CookieManager
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Constants
import com.abrand.custom.data.Settings
import com.abrand.custom.databinding.FragmentLinkBinding
import com.abrand.custom.interfaces.GetUrlTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.network.UserAgentInterceptor
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.type.PaymentUrlDirection
import com.abrand.custom.ui.activitymain.MainActivity
import com.apollographql.apollo3.exception.ApolloException
import java.net.MalformedURLException
import java.net.URL

class LinkFragment : Fragment(R.layout.fragment_link) {
    private lateinit var animation: Animation
    private lateinit var loaderHandler : Handler
    private val HIDE_LOADER_DELAY : Long = 500
    private val PAYMENTS_HISTORY_TAG = "popup-payments-his"
    private val PAYMENTS_TAG = "popup-payments"
    private val TERMS_PATH = "terms"
    private var link: String? = null
    private var paymentUrlDirection: PaymentUrlDirection? = null
    private var insetBottom: Int = 0
    var linkFragmentListener : Listener? = null
    private var binding: FragmentLinkBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        arguments?.let {
            link = it.getString(KEY_URL)
            if (!TextUtils.isEmpty(it.getString(KEY_PAYMENT_URL_DIRECTION))) {
                paymentUrlDirection = PaymentUrlDirection.valueOf(it.getString(KEY_PAYMENT_URL_DIRECTION)!!)
            }
        }

        loaderHandler = Handler(Looper.getMainLooper())
        animation = AnimationUtils.loadAnimation(context, R.anim.rotation_loader)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentLinkBinding.bind(view)
        showLoader()
        initWw()

        if (!TextUtils.isEmpty(link)) {
            link?.let { binding?.ww?.loadUrl(it) }
        } else if (paymentUrlDirection != null) {
            getLinkDirectly(paymentUrlDirection!!)
        } else {
            Toast.makeText(context, getString(R.string.something_goes_wrong), Toast.LENGTH_LONG).show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    fun setInsetBottom(bottom: Int) {
        binding?.apply {
            LinkFragment@insetBottom = bottom
            if (root != null) {
                root.updatePadding(bottom = insetBottom)
            }
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWw() {
        binding?.ww?.let { webView ->
            webView.settings.javaScriptEnabled = true
            webView.settings.domStorageEnabled = true
            webView.settings.userAgentString += UserAgentInterceptor.webViewAgentSuffix
            //In Lower than LOLLIPOP(including LOLLIPOP) Third party cookies are enabled by default
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.LOLLIPOP) {
                CookieManager.getInstance().setAcceptThirdPartyCookies(webView, true)
            }
            webView.webViewClient = object : WebViewClient() {
                override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                    return tryProcessBankUrl(url)
                }

                override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                    val newUrl = request?.url?.toString()
                    val isBankUrl = tryProcessBankUrl(newUrl)

                    loaderHandler.removeCallbacks(hideLoaderWorker)

                    return isBankUrl
                }

                override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                    super.onPageStarted(view, url, favicon)
                    showLoader()
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    url?.let {
                        if ( Patterns.WEB_URL.matcher(url).matches() ) {
                            loaderHandler.postDelayed(hideLoaderWorker, HIDE_LOADER_DELAY)
                        }
                    }
                }

                override fun doUpdateVisitedHistory(view: WebView?, url: String?, isReload: Boolean) {
                    super.doUpdateVisitedHistory(view, url, isReload)
                    try {
                        val aURL = URL(url)

                        if (ApoloConfig.BASE_URL == url) {
                            linkFragmentListener?.paymentsCompleted()
                        } else if (!TextUtils.isEmpty(aURL.ref)) {
                            if (PAYMENTS_HISTORY_TAG == aURL.ref) {
                                linkFragmentListener?.paymentsCompleted()
                            } else if (PAYMENTS_TAG == aURL.ref) {
                                proceedCashboxClick()
                            }
                        } else if (!TextUtils.isEmpty(aURL.path)) {
                            if (aURL.path.contains(TERMS_PATH)) {
                                linkFragmentListener?.termsClicked()
                            } else if (aURL.path.contains(Constants.DEEP_LINK_GAME)) {
                                url?.let { linkFragmentListener?.gameClicked(it) }
                            } else if (aURL.path.contains(Constants.PATH_PAYMENTS_SUCCESS)) {
                                linkFragmentListener?.paymentsCompleted()
                            }
                        }
                    } catch ( e: MalformedURLException ) {
                        // NOP cause incorrect URL
                    }
                }

                fun tryProcessBankUrl(url: String?): Boolean {
                    if (url?.startsWith("bank") == true) { // To process "bank100000000004://qr.nspk.ru/..." type deep links
                        try {
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                            activity?.startActivity(intent)

                            showInitCashboxPage()
                            linkFragmentListener?.openHistory()
                        } catch (e: ActivityNotFoundException) {
                            Toast.makeText(
                                activity,
                                "Ошибка, приложение банка не установлено.",
                                Toast.LENGTH_SHORT
                            ).show()
                        }

                        return true
                    }

                    return false
                }
            }
        }
    }

    private fun proceedCashboxClick() {
        showInitCashboxPage()
        updateHistory()
    }

    private fun showInitCashboxPage() {
        paymentUrlDirection?.let {
            showLoader()
            getLinkDirectly(it)
        }
    }

    private fun updateHistory() {
        linkFragmentListener?.updateHistory()
    }

    private var hideLoaderWorker: Runnable = Runnable {
        hideLoader()
    }

    private fun showLoader() {
        binding?.apply {
            ww.visibility = View.GONE
            if (lProgress != null) {
                lProgress.visibility = View.VISIBLE
                ivLoader.startAnimation(animation)
            }
        }
    }

    private fun hideLoader() {
        binding?.apply {
            ww.visibility = View.VISIBLE
            if (ivLoader != null) {
                ivLoader.clearAnimation()
                lProgress.visibility = View.GONE
            }
        }
    }

    private fun getLinkDirectly(direction: PaymentUrlDirection) {
        ApolloProcessorKt.getPaymentUrl(direction, Settings.get().refCode, object : GetUrlTarget {
            override fun onSuccess(url: String) {
                if (isAdded) {
                    if (url.isNotEmpty()) {
                        activity?.runOnUiThread {
                            binding?.ww?.loadUrl(url)
                        }
                    } else {
                        hideLoader()
                    }
                }
            }

            override fun onFailure(exception: ApolloException) {
                if (isAdded) {
                    (activity as? MainActivity)?.showConnectionIssueMessage(exception)
                }
            }

            override fun onError(errorMessage: String?, errorCode: String?, fieldsErrors: FieldsErrorsHolder?) {
                if (isAdded) {
                    activity?.runOnUiThread {
                        binding?.ww?.visibility = View.INVISIBLE
                        hideLoader()
                        binding?.tvInfo?.text = getString(R.string.something_goes_wrong)
                    }
                }
            }

            override fun onViewerNull() {
                linkFragmentListener?.onViewerNull()
            }
        })
    }

    fun openUrl(url: String) {
        binding?.ww?.loadUrl(url)
    }

    interface Listener {
        fun paymentsCompleted()
        fun onViewerNull()
        fun termsClicked()
        fun gameClicked(gameUrl: String)
        fun updateHistory()
        fun openHistory()
    }

    companion object {
        const val KEY_URL = "url"
        const val KEY_PAYMENT_URL_DIRECTION = "paymentUrlDirection"
        const val REQUEST_CODE = 0

        @JvmStatic
        fun newInstance(link: String?, paymentUrlDirection: PaymentUrlDirection) =
            LinkFragment().apply {
                arguments = Bundle().apply {
                    putString(KEY_URL, link)
                    putString(KEY_PAYMENT_URL_DIRECTION, paymentUrlDirection.name)
                }
            }
    }
}
