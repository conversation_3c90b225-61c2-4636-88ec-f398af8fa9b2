package com.abrand.custom;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.testing.FragmentScenario;

import com.abrand.custom.data.entity.AuthResponse;
import com.abrand.custom.data.entity.Banner;
import com.abrand.custom.data.entity.LocalGameItem;
import com.abrand.custom.fragment.GameThumb;
import com.abrand.custom.interfaces.GenericTarget;
import com.abrand.custom.interfaces.GetRegisterBonusesTarget;
import com.abrand.custom.interfaces.GetRegistrationBannerTarget;
import com.abrand.custom.interfaces.GetSlidesTarget;
import com.abrand.custom.interfaces.LoadTarget;
import com.abrand.custom.network.ApolloProcessor;
import com.abrand.custom.network.ApolloProcessorKt;
import com.abrand.custom.presenter.FieldsErrorsHolder;
//import com.abrand.custom.type.GameAttributeType;
//import com.abrand.custom.type.GameTypeField;
import com.abrand.custom.ui.enter.EnterFragment;
import com.apollographql.apollo3.exception.ApolloException;

import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

public class NotLoggedTest {
    private final String[] error         = {null};
    private final int      RESPONSE_TIME = 5_000;

    @Before
    public void setUp() {
//        FragmentScenario.launch(EnterFragment.class, null, R.style.AppTheme, null);
    }

    @Test
    public void getDrawerRegistrationBannerTest() {
        // TODO: 20.10.2022 rewrite to Kt version
//        ApolloProcessor.getDrawerRegistrationBanner("", new GetRegistrationBannerTarget() {
//            @Override
//            public void onSuccess(@Nullable String banner) {
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    @Test
    public void getSlidesTest() {
        // TODO: 28.10.2022 rewrite to KtVersion
//        ApolloProcessor.getSlides(new GetSlidesTarget() {
//            @Override
//            public void onSuccess(@NonNull List<Banner> banners) {
//                if ( banners.isEmpty() ) {
//                    error[0] = "banners is empty";
//                } else {
//                    for ( Banner banner : banners ) {
//                        if ( TextUtils.isEmpty(banner.getImageUrl()) ) {
//                            error[0] = "at least one banner image is empty";
//                            break;
//                        }
//                    }
//                }
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    @Test
    public void getRegisterBonuses() {
        // TODO: 28.10.2022 rewrite to Kt version
//        ApolloProcessor.getRegisterBonuses(new GetRegisterBonusesTarget() {
//            @Override
//            public void onSuccess(@NotNull List<? extends GetRegisterBonusesQuery.RegisterBonuse> registerBonuses) {
//                if ( !registerBonuses.isEmpty() ) {
//                    for ( GetRegisterBonusesQuery.RegisterBonuse registerBonus : registerBonuses ) {
//                        if ( registerBonus.id == null ) {
//                            error[0] = "at least one register bonus id is null";
//                        } else if ( TextUtils.isEmpty(registerBonus.popupRegistrationChoiceAbout) ) {
//                            error[0] = "at least one register bonus text is empty";
//                        } else if ( TextUtils.isEmpty(registerBonus.popupRegistrationChoiceImage) ) {
//                            error[0] = "at least one register bonus image is empty";
//                        }
//                    }
//                }
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    @Test
    public void getCurrentBonusesTest() {
//        ApolloProcessorKt.getCurrentBonuses(new GenericTarget<List<GetCurrentBonusesQuery.CurrentBonuse>>() {
//            @Override
//            public void onSuccess(@Nullable List<GetCurrentBonusesQuery.CurrentBonuse> currentBonuses) {
//                if ( currentBonuses.isEmpty() ) {
//                    error[0] = "current bonuses is empty";
//                } else {
//                    for ( GetCurrentBonusesQuery.CurrentBonuse currentBonus : currentBonuses ) {
//                        if ( TextUtils.isEmpty(currentBonus.getName()) ) {
//                            error[0] = "at least one current bonus name is empty";
//                        } else if ( TextUtils.isEmpty(currentBonus.getButtonName()) ) {
//                            error[0] = "at least one current bonus buttonName is empty";
//                        } else if ( currentBonus.getDateEnd() == null ) {
//                            error[0] = "at least one current bonus dateEnd is empty";
//                        }
//                    }
//                }
//            }
//
//            @Override
//            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    @Test
    public void restorePasswordByMailTest() {
        ApolloProcessorKt.restorePasswordByMail("<EMAIL>", new GenericTarget<Boolean>() {
            @Override
            public void onSuccess(Boolean isSuccess) {
                if ( !isSuccess ) {
                    error[0] = "restore password by mail not success";
                }
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }
        });

        waitResponseAndAssertFailIfError();
    }

    @Test
    public void getCaptchas() {
//        ApolloProcessor.getCaptchas(false, new GetCaptchaTarget() {
//            @Override
//            public void onSuccess(boolean isCaptchaEnabled, String captchaId, boolean isReCaptchaEnabled, String reCaptchaId) {
//                if ( isCaptchaEnabled && TextUtils.isEmpty(captchaId) ) {
//                    error[0] = "Captcha enabled but empty";
//                } else if ( isReCaptchaEnabled && TextUtils.isEmpty(reCaptchaId) ) {
//                    error[0] = "ReCaptcha enabled but empty";
//                }
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = "getCaptchas: " + e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    @Test
    public void registerWithEmptyCaptchaTest() {
//        ApolloProcessor.getCaptchas(false, new GetCaptchaTarget() {
//            @Override
//            public void onSuccess(boolean isCaptchaEnabled, String captchaId, boolean isReCaptchaEnabled, String reCaptchaId) {
//                if ( isCaptchaEnabled ) {
//                    error[0] = "Captcha enabled, can't test register";
//                } else if ( isReCaptchaEnabled ) {
//                    error[0] = "ReCaptcha enabled, can't test register";
//                } else {
//                    registerWithEmptyCaptcha();
//                }
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = "getCaptchas: " + e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    private void registerWithEmptyCaptcha() {
        // TODO: 28.10.2022 rewrite to Kt version
//        String email    = System.currentTimeMillis() + "@email.com";
//        String password = "qweqwe1";
//
//        ApolloProcessor.register(email, password, "ru", 0, "",
//                "", "", new GenericTarget<AuthResponse>() {
//                    @Override
//                    public void onSuccess(@org.jetbrains.annotations.Nullable AuthResponse authResponse) {
//                        if ( TextUtils.isEmpty(userId) ) {
//                            error[0] = "success response but empty userId";
//                        }
//                    }
//
//                    @Override
//                    public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                        error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//                    }
//
//                    @Override
//                    public void onFailure(ApolloException e) {
//                        error[0] = e.toString();
//                    }
//                });
//
//        waitResponseAndAssertFailIfError();
    }

    //removed ad unused network call
    // TODO: 19.10.2022 remove after release
//    @Test
//    public void loadGamesByType() {
//        int loadAmount = 20;
//        ApolloProcessor.loadGamesByType(GameTypeField.slots, loadAmount, 0,
//                null, null, new LoadTarget() {
//                    @Override
//                    public void onSuccess(List<LocalGameItem> localGameItems, int total, int offset) {
//                        if ( loadAmount != localGameItems.size() ) {
//                            error[0] = "loadAmount not equal to the number of games that the server returned";
//                        } else if ( localGameItems.size() > total ) {
//                            error[0] = "the number of games that the server returned more than total";
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(ApolloException e) {
//                        error[0] = e.toString();
//                    }
//                });
//
//        waitResponseAndAssertFailIfError();
//    }

    @Test
    public void loadGamesByAttribute() {
        int loadAmount = 20;
        // TODO: 19.10.2022 refactor to proper solution
        //removed as unused network call
//        ApolloProcessor.loadByAttribute(GameAttributeType.POPULAR, 20, 0,
//                null, null, new LoadTarget() {
//                    @Override
//                    public void onSuccess(List<LocalGameItem> gameThumbs, int total, int offset) {
//                        if ( loadAmount != gameThumbs.size() ) {
//                            error[0] = "loadAmount not equal to the number of games that the server returned";
//                        } else if ( gameThumbs.size() > total ) {
//                            error[0] = "the number of games that the server returned more than total";
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(ApolloException e) {
//                        error[0] = e.toString();
//                    }
//                });
//
//        waitResponseAndAssertFailIfError();
    }

    @Test
    public void loadGamesByName() {
        String partName = "of";
        // TODO: 20.10.2022 rewrite to Kt version
//        ApolloProcessor.loadGamesByName(partName, 21, 0, new LoadTarget() {
//            @Override
//            public void onSuccess(List<LocalGameItem> localGameItems, int total, int offset) {
//                if ( gameThumbs.isEmpty() ) {
//                    error[0] = "game list is empty";
//                } else if ( gameThumbs.size() != total ) {
//                    error[0] = "game list size is not equal total";
//                } else {
//                    for ( GameThumb gameThumb : gameThumbs ) {
//                        if ( !gameThumb.getName().contains(partName) ) {
//                            error[0] = "one of the games does not contain the search part \"of\" in the name";
//                        }
//                    }
//                }
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    private void waitResponseAndAssertFailIfError() {
        try {
            Thread.sleep(RESPONSE_TIME);
            if ( !TextUtils.isEmpty(error[0]) ) {
                Assert.fail(error[0]);
            }
        } catch ( InterruptedException e ) {
            e.printStackTrace();
        }
    }
}
