package com.abrand.custom.ui.shop

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.LocalBonusProductForPoints
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.data.entity.ShopItem
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException

class ShopViewModel : ViewModel() {
    private val _bonusProductsForPointsLiveData =
        MutableLiveData<Resource<List<LocalBonusProductForPoints>, ServerError, ApolloException>>()
    val bonusProductsForPointsLiveData: LiveData<Resource<List<LocalBonusProductForPoints>, ServerError, ApolloException>> =
        _bonusProductsForPointsLiveData
    private val _buyProductLiveData =
        MutableLiveData<Resource<LocalBonusProductForPoints, ServerError, ApolloException>>()
    val buyProductLiveData: LiveData<Resource<LocalBonusProductForPoints, ServerError, ApolloException>> =
        _buyProductLiveData

    init {
        getBonusStoreProducts()
    }

    private fun getBonusStoreProducts() {
        ApolloProcessorKt.getBonusStoreProducts(object : GenericTarget<List<LocalBonusProductForPoints>> {
            override fun onSuccess(t: List<LocalBonusProductForPoints>?) {
                _bonusProductsForPointsLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _bonusProductsForPointsLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _bonusProductsForPointsLiveData.postValue(Resource.failure(e))
            }
        })
    }

    fun listLocalBonusProductForPointsToShopItems(listBonusProducts: List<LocalBonusProductForPoints>): List<ShopItem> {
        val result = mutableListOf<ShopItem>()
        for (product in listBonusProducts) {
            result.add(ShopItem(ShopItem.ItemType.PRODUCT, product))
        }
        return result
    }

    fun buyProduct(productId: Int) {
        ApolloProcessorKt.bonusStoreBuyProduct(productId, object : GenericTarget<LocalBonusProductForPoints> {
            override fun onSuccess(t: LocalBonusProductForPoints?) {
                _buyProductLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _buyProductLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _buyProductLiveData.postValue(Resource.failure(e))
            }
        })
    }
}
