package com.abrand.custom.data.datasources

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import com.abrand.custom.data.entity.BannedDomainEntity

@Dao
interface BannedDomainDao {

    @Query("SELECT * FROM BannedDomainEntity")
    suspend fun getBannedDomainList(): List<BannedDomainEntity>

    @Insert
    suspend fun insertBannedDomain(vararg domains: BannedDomainEntity)

    @Query("DELETE FROM BannedDomainEntity WHERE id = :id")
    suspend fun deleteBannedDomainById(id: Long)

    @Query("DELETE FROM BannedDomainEntity")
    suspend fun deleteAllBannedDomains()
}
