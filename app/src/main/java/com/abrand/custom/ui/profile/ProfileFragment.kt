package com.abrand.custom.ui.profile

import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.content.DialogInterface
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.text.InputType
import android.text.TextUtils
import android.util.Log
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.animation.AccelerateInterpolator
import android.view.animation.Animation
import android.view.inputmethod.EditorInfo
import android.widget.TextView
import android.widget.Toast
import androidx.biometric.BiometricManager
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.Navigation
import com.abrand.custom.R
import com.abrand.custom.data.Constants
import com.abrand.custom.data.JsonDataGenerator
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.AnalyticsEvent
import com.abrand.custom.data.entity.BiometricCredentials
import com.abrand.custom.data.entity.Country
import com.abrand.custom.data.entity.Profile
import com.abrand.custom.data.entity.SaveProfileRequestsState
import com.abrand.custom.data.entity.SaveProfileRequestsState.ProfileRequest
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.databinding.FragmentProfileBinding
import com.abrand.custom.network.OkHttpProcessor.sendAnalytics
import com.abrand.custom.presenter.Fields
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.BiometricUtil.canAuthenticate
import com.abrand.custom.tools.BiometricUtil.hasFeatureFingerPrint
import com.abrand.custom.tools.BiometricUtil.launchBiometricSettings
import com.abrand.custom.tools.CryptographyUtil.getInitializedCipherForEncryption
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.SlideAnimation
import com.abrand.custom.type.Gender
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.bonusbalances.BonusBalancesViewModel
import com.abrand.custom.ui.views.StateSwitchView.SwitchListener
import com.abrand.custom.ui.views.Switcher.ItemChangeListener
import com.abrand.custom.ui.views.textfield.TextField.FocusChangeListener
import com.abrand.custom.ui.views.textfield.TextField.UpdateListener
import com.apollographql.apollo3.exception.ApolloException
import kotlinx.coroutines.launch
import javax.crypto.Cipher

class ProfileFragment : Fragment() {
    private val GENDER_DEFAULT_POSITION: Int = 0
    private val GENDER_MALE_POSITION: Int = 1
    private val GENDER_FEMALE_POSITION: Int = 2
    private val RESEND_EMAIL_CONFIRMATION_ANIMATION_DURATION: Long = 300
    private val CHANGE_PASSWORD_CONTAINER_ANIMATION_DURATION: Long = 300
    private var profileViewModel: ProfileViewModel? = null
    private var profileViewModelKt: ProfileViewModelKt? = null
    private var bonusBalancesViewModel: BonusBalancesViewModel? = null
    private var binding: FragmentProfileBinding? = null
    private val saveProfileRequestsState: SaveProfileRequestsState = SaveProfileRequestsState()
    private var changePasswordContainerVisible: Boolean = false
    private val TAG: String = "ProfileFragment"
    private var authenticationStatus: Int = 0

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentProfileBinding.inflate(
            inflater,
            null,
            false
        ) //Container is null to fix error with bottom padding of save button

        if (profileViewModel == null) {
            profileViewModel = ViewModelProviders.of(this).get(
                ProfileViewModel::class.java
            )
        }
        if (profileViewModelKt == null) {
            profileViewModelKt = ViewModelProvider(this).get(
                ProfileViewModelKt::class.java
            )
        }
        if (bonusBalancesViewModel == null) {
            bonusBalancesViewModel = ViewModelProvider(this).get(
                BonusBalancesViewModel::class.java
            )
        }
        profileViewModel!!.fetchProfile()

        binding?.composeView?.apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                bonusBalancesViewModel?.getBonusBalances()

                MaterialTheme {
                    BonusesBalancesSection(
                        openPromotions = {
                            view?.let { fragmentView ->
                                Navigation.findNavController(fragmentView)
                                    .navigate(R.id.nav_promotions, null)
                            }
                        }
                    )
                }
            }
        }

        binding!!.etName.setInputType(InputType.TYPE_CLASS_TEXT)
        binding!!.etName.setImeOptions(EditorInfo.IME_ACTION_SEND)
        binding!!.etPhone.setInputType(InputType.TYPE_CLASS_PHONE)
        binding!!.etPhone.setCountryChangeListener({ countryISO: String? ->
            profileViewModel!!.updateCountryIso(countryISO)
        })

        val genders: ArrayList<String> = ArrayList()
        genders.add(GENDER_DEFAULT_POSITION, getString(R.string._gender_default))
        genders.add(GENDER_MALE_POSITION, getString(R.string._gender_male))
        genders.add(GENDER_FEMALE_POSITION, getString(R.string._gender_female))
        binding!!.gender.setVariants(genders)
        binding!!.gender.setItemChangeListener(genderChangeListener)

        initBiometricSwitch()

        if (!Settings.get().isSocialAuth) {
            binding!!.btnChangePassword.setOnClickListener(btnChangePasswordClickListener)
            binding!!.etOldPassword.setInputType(InputType.TYPE_TEXT_VARIATION_WEB_PASSWORD)
            binding!!.etOldPassword.setOnFocusChangeListener(oldPasswordFocusChangeListener)
            binding!!.etOldPassword.subscribeToUpdates(oldPasswordUpdateListener)
            binding!!.etNewPassword.setInputType(InputType.TYPE_TEXT_VARIATION_WEB_PASSWORD)
            binding!!.etNewPassword.setOnFocusChangeListener(newPasswordFocusChangeListener)
            binding!!.etNewPassword.subscribeToUpdates(newPasswordUpdateListener)
            binding!!.etPasswordConfirmation.setInputType(InputType.TYPE_TEXT_VARIATION_WEB_PASSWORD)
            binding!!.etPasswordConfirmation.setOnFocusChangeListener(
                passwordConfirmationFocusChangeListener
            )
            binding!!.etPasswordConfirmation.subscribeToUpdates(passwordConfirmationUpdateListener)
        } else {
            binding!!.btnChangePassword.visibility = View.GONE
            binding!!.containerChangePassword.visibility = View.GONE
        }

        profileViewModel!!.profileLiveData.observe(viewLifecycleOwner, profileObserver)
        profileViewModel!!.profileChangedLiveData.observe(viewLifecycleOwner, profileChangeObserver)
        profileViewModel!!.passwordChangedLiveData.observe(
            viewLifecycleOwner,
            passwordChangeObserver
        )
        profileViewModel!!.emailConfirmationRequestedLiveData.observe(
            viewLifecycleOwner,
            emailConfirmationRequestedObserver
        )
        profileViewModel!!.viewerEmptyLiveData.observe(viewLifecycleOwner, viewerEmptyObserver)
        profileViewModel!!.serverErrorLiveData.observe(viewLifecycleOwner, serverErrorObserver)
        profileViewModel!!.apolloExceptionLiveData.observe(
            viewLifecycleOwner,
            apolloExceptionObserver
        )

        setupInsets()

        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                profileViewModelKt?.eventFlow?.collect { event ->
                    when (event) {
                        is ProfileEffects.CaptchaStatusError -> {
                            Toast.makeText(
                                <EMAIL>,
                                event.message,
                                Toast.LENGTH_LONG
                            ).show()
                        }

                        is ProfileEffects.CaptchaStatusNetworkError -> {
                            (activity as MainActivity).showConnectionIssueMessage(event.e)
                        }
                    }
                }
            }
        }

        return binding!!.root
    }

    override fun onResume() {
        super.onResume()
        updateBiometricAuthenticationStatus()
    }

    override fun onStop() {
        super.onStop()
        if (activity != null) {
            GeneralTools.hideKeyboard(activity)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun initBiometricSwitch() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.get().isSocialAuth) {
            val hasFeatureFingerPrint: Boolean = hasFeatureFingerPrint(requireContext())

            if (hasFeatureFingerPrint) {
                binding!!.switchBiometric.setText(getString(R.string.profile_biometric_auth))

                authenticationStatus = canAuthenticate(requireContext())
                val biometricCredentials: BiometricCredentials? =
                    Settings.get().biometricCredentials
                val cipherForEncryption: Cipher? = cipherForEncryption
                binding!!.switchBiometric.setCheckedWithoutListener(
                    BiometricManager.BIOMETRIC_SUCCESS == authenticationStatus && biometricCredentials != null && biometricCredentials.login != null &&
                            biometricCredentials.login == Settings.get().loggedUserEmail && cipherForEncryption != null
                )

                binding!!.switchBiometric.setSwitchListener(biometricSwitchListener)
            } else {
                binding!!.switchBiometric.visibility = View.GONE
            }
        } else {
            binding!!.switchBiometric.visibility = View.GONE
        }
    }

    @get:SuppressLint("NewApi")
    private val cipherForEncryption: Cipher?
        get() {
            try {
                return getInitializedCipherForEncryption()
            } catch (exception: Exception) {
                Settings.get().biometricCredentials = null
                return null
            }
        }

    private val biometricSwitchListener: SwitchListener = object : SwitchListener {
        override fun onChecked(isChecked: Boolean) {
            if (isChecked) {
                binding!!.switchBiometric.setCheckedWithoutListener(false)
                if (BiometricManager.BIOMETRIC_SUCCESS == authenticationStatus) {
                    (activity as MainActivity).openBiometricInputPasswordScreen()
                } else {
                    showAlertToSetupBiometric()
                }
            } else {
                Settings.get().biometricCredentials = null
                val activity: Activity? = activity
                if (activity != null) {
                    val biometryAuthDataJson: String = JsonDataGenerator().getBiometryAuthDataJson(
                        false, Settings.get().userId
                    )
                    sendAnalytics(
                        activity, AnalyticsEvent.BIOMETRY_AUTH,
                        biometryAuthDataJson, null
                    )
                }
            }
        }
    }

    private fun showAlertToSetupBiometric() {
        AlertDialog.Builder(context)
            .setMessage(R.string.setup_biometric)
            .setPositiveButton(android.R.string.ok) { _: DialogInterface?, _: Int ->
                launchBiometricSettings(requireActivity())
            }
            .setNegativeButton(R.string.cancel, null)
            .setCancelable(false)
            .show()
    }

    private fun setupInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(
            binding!!.containerBtnSave,
            { view: View?, insets: WindowInsetsCompat ->
                val layoutParams: ViewGroup.LayoutParams = binding!!.containerBtnSave.layoutParams
                if (layoutParams is MarginLayoutParams) {
                    val systemInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
                    val baseMargin = resources.getDimensionPixelSize(R.dimen.size_16)
                    val bottomMargin = maxOf(
                        systemInsets.bottom + baseMargin,
                        baseMargin
                    )

                    layoutParams.bottomMargin = bottomMargin
                }
                insets
            })

        ViewCompat.setOnApplyWindowInsetsListener(
            binding!!.scrollViewContainer
        ) { view: View, insets: WindowInsetsCompat ->
            val systemInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            val topPadding: Int = GeneralTools.getActionBarHeight(context) + systemInsets.top

            view.setPadding(view.paddingLeft, topPadding, view.paddingRight, view.paddingBottom)
            insets
        }
    }

    private val nameUpdateListener: UpdateListener =
        UpdateListener { _: String?, _: Boolean ->
            setSaveButtonState(isAllDataSaved)
        }

    private val phoneUpdateListener: UpdateListener = object : UpdateListener {
        override fun onUpdate(data: String, valid: Boolean) {
            binding!!.etPhone.unsubscribeFromUpdates(this)

            val formattedNumber: String? = profileViewModel!!.getFormattedPhone(data)
            if (formattedNumber != null) {
                binding!!.etPhone.setText(formattedNumber)
                binding!!.etPhone.setSelection(formattedNumber.length)
            }

            binding!!.etPhone.subscribeToUpdates(this)
            setSaveButtonState(isAllDataSaved)
        }
    }

    private val genderChangeListener: ItemChangeListener =
        ItemChangeListener { index ->
            val currentGender: Gender = profileViewModel!!.currentGender
            if (GENDER_DEFAULT_POSITION == index && Gender.MALE == currentGender) {
                binding!!.gender.selectChild(GENDER_MALE_POSITION)
            } else if (GENDER_DEFAULT_POSITION == index && Gender.FEMALE == currentGender) {
                binding!!.gender.selectChild(GENDER_FEMALE_POSITION)
            } else {
                setSaveButtonState(isAllDataSaved)
            }
        }

    private val birthdayUpdateListener: UpdateListener =
        UpdateListener { _: String?, _: Boolean ->
            setSaveButtonState(isAllDataSaved)
        }

    private val oldPasswordUpdateListener: UpdateListener =
        UpdateListener { data, _ ->
            setTvPasswordRuleNewOldShouldBeDifferentMode(binding!!.etNewPassword.text, data)
            setSaveButtonState(isAllDataSaved)
        }

    private val newPasswordUpdateListener: UpdateListener =
        UpdateListener { data, _ ->
            setTvPasswordRuleCharactersCountMode(data)
            setTvPasswordRuleNewOldShouldBeDifferentMode(data, binding!!.etOldPassword.text)
            setTvPasswordRuleEqualsPasswordMode(data, binding!!.etPasswordConfirmation.text)

            setSaveButtonState(isAllDataSaved)
        }

    private val passwordConfirmationUpdateListener: UpdateListener =
        UpdateListener { data, _ ->
            setTvPasswordRuleEqualsPasswordMode(binding!!.etNewPassword.text, data)
            setSaveButtonState(isAllDataSaved)
        }

    private fun setTvPasswordRuleCharactersCountMode(newPassword: String) {
        if (newPassword.length >= profileViewModel!!.minPasswordLength) {
            binding!!.tvPasswordRuleCharactersCount.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.password_rule_observed
                )
            )
            binding!!.tvPasswordRuleCharactersCount.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_done,
                0,
                0,
                0
            )
        } else {
            binding!!.tvPasswordRuleCharactersCount.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.password_rule_not_observed
                )
            )
            binding!!.tvPasswordRuleCharactersCount.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_done_gray,
                0,
                0,
                0
            )
        }
    }

    private fun setTvPasswordRuleNewOldShouldBeDifferentMode(
        newPassword: String,
        oldPassword: String
    ) {
        if (!TextUtils.isEmpty(newPassword) && !TextUtils.isEmpty(oldPassword) && newPassword != oldPassword) {
            binding!!.tvPasswordRuleNewOldDifferent.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.password_rule_observed
                )
            )
            binding!!.tvPasswordRuleNewOldDifferent.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_done,
                0,
                0,
                0
            )
        } else {
            binding!!.tvPasswordRuleNewOldDifferent.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.password_rule_not_observed
                )
            )
            binding!!.tvPasswordRuleNewOldDifferent.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_done_gray,
                0,
                0,
                0
            )
        }
    }

    private fun setTvPasswordRuleEqualsPasswordMode(
        newPassword: String,
        passwordConfirmation: String
    ) {
        if (!TextUtils.isEmpty(newPassword) && !TextUtils.isEmpty(passwordConfirmation) &&
            newPassword == passwordConfirmation
        ) {
            binding!!.tvPasswordRuleEqualsPasswords.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.password_rule_observed
                )
            )
            binding!!.tvPasswordRuleEqualsPasswords.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_done,
                0,
                0,
                0
            )
        } else {
            binding!!.tvPasswordRuleEqualsPasswords.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    R.color.password_rule_not_observed
                )
            )
            binding!!.tvPasswordRuleEqualsPasswords.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_done_gray,
                0,
                0,
                0
            )
        }
    }

    private val btnChangePasswordClickListener: View.OnClickListener =
        object : View.OnClickListener {
            override fun onClick(v: View) {
                if (!changePasswordContainerVisible) {
                    binding!!.btnChangePassword.text = getString(R.string.cancel_change_password)
                    binding!!.btnChangePassword.setCompoundDrawablesWithIntrinsicBounds(
                        0,
                        0,
                        R.drawable.ic_close,
                        0
                    )
                    showContainerChangePassword()
                } else {
                    binding!!.btnChangePassword.text = getString(R.string.change_password)
                    binding!!.btnChangePassword.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                    hideContainerChangePassword()
                    GeneralTools.hideKeyboard(activity)
                }
                changePasswordContainerVisible = !changePasswordContainerVisible
            }
        }

    private fun showContainerChangePassword() {
        val containerHeight: Int =
            requireContext().resources.getDimensionPixelSize(R.dimen.profile_container_change_password_height)
        val animation: Animation =
            SlideAnimation(binding!!.containerChangePassword, 0, containerHeight)
        animation.interpolator = AccelerateInterpolator()
        animation.duration = CHANGE_PASSWORD_CONTAINER_ANIMATION_DURATION
        binding!!.containerChangePassword.startAnimation(animation)

        Handler(Looper.getMainLooper()).postDelayed({
            binding!!.scrollView.post {
                binding!!.scrollView.fullScroll(
                    View.FOCUS_DOWN
                )
            }
        }, CHANGE_PASSWORD_CONTAINER_ANIMATION_DURATION)
    }

    private fun hideContainerChangePassword() {
        val containerHeight: Int =
            requireContext().resources.getDimensionPixelSize(R.dimen.profile_container_change_password_height)
        val animation: Animation =
            SlideAnimation(binding!!.containerChangePassword, containerHeight, 0)
        animation.interpolator = AccelerateInterpolator()
        animation.duration = CHANGE_PASSWORD_CONTAINER_ANIMATION_DURATION
        binding!!.containerChangePassword.startAnimation(animation)
    }

    private val oldPasswordFocusChangeListener: FocusChangeListener =
        FocusChangeListener { isFocused ->
            val handler = Handler(Looper.getMainLooper())
            handler.postDelayed({
                if (isFocused) {
                    binding!!.scrollView.scrollBy(0, binding!!.etOldPassword.bottom)
                }
            }, 400)
        }

    private val newPasswordFocusChangeListener: FocusChangeListener =
        FocusChangeListener { isFocused ->
            val handler = Handler(Looper.getMainLooper())
            handler.postDelayed({
                if (isFocused) {
                    binding!!.scrollView.scrollBy(0, binding!!.tvPasswordRuleEqualsPasswords.bottom)
                }
            }, 400)
        }

    private val passwordConfirmationFocusChangeListener: FocusChangeListener =
        FocusChangeListener { isFocused ->
            val handler = Handler(Looper.getMainLooper())
            handler.postDelayed({
                if (isFocused) {
                    binding!!.scrollView.scrollBy(
                        0,
                        binding!!.tvPasswordRuleEqualsPasswords.bottom
                    )
                }
            }, 400)
        }

    private fun setSaveButtonState(allDataSaved: Boolean) {
        if (allDataSaved) {
            binding!!.containerBtnSave.isClickable = false
            binding!!.containerBtnSave.background =
                ContextCompat.getDrawable(requireContext(), R.drawable.btn_bg_disable)
            binding!!.tvBtnSave.text = getString(R.string.changes_saved)
            binding!!.ivBtnSave.setImageDrawable(
                ContextCompat.getDrawable(
                    requireContext(),
                    R.drawable.ic_done
                )
            )
        } else {
            binding!!.containerBtnSave.isClickable = true
            binding!!.containerBtnSave.background =
                ContextCompat.getDrawable(requireContext(), R.drawable.btn_bg_enable)
            binding!!.tvBtnSave.text = getString(R.string.save_changes)
            binding!!.ivBtnSave.setImageDrawable(null)
            binding!!.containerBtnSave.setOnClickListener({ v: View? ->
                val activity: Activity? = <EMAIL>
                if (activity != null) {
                    GeneralTools.hideKeyboard(activity)
                }

                if (isProfileChanged) {
                    binding!!.etName.hideErrorMessage()
                    binding!!.etPhone.hideErrorMessage()
                    binding!!.etBirthdate.hideErrorMessage()
                    if (profileViewModel!!.isValidUserName(binding!!.etName.text) && binding!!.etPhone.isPhoneNumberValid) {
                        checkCaptchaAndSaveProfile()
                    } else {
                        if (!profileViewModel!!.isValidUserName(binding!!.etName.text)) {
                            binding!!.etName.showErrorMessage(
                                context?.let { context ->
                                    profileViewModel!!.getNotValidUserNameMessage(
                                        context, binding!!.etName.text
                                    )
                                }
                            )
                        }
                        if (!binding!!.etPhone.isPhoneNumberValid) {
                            binding!!.etPhone.showErrorMessage(getString(R.string.phone_number_not_valid_message))
                        }
                    }
                }
                if (isPasswordChanged) {
                    binding!!.etOldPassword.hideErrorMessage()
                    binding!!.etNewPassword.hideErrorMessage()

                    profileViewModel!!.changePassword(
                        binding!!.etOldPassword.text,
                        binding!!.etNewPassword.text
                    )
                    saveProfileRequestsState.passwordRequest.isRequestSend = true
                }
            }
            )
        }
    }

    private fun checkCaptchaAndSaveProfile() {
        // Start saving profile flow
        profileViewModelKt!!.saveProfile(
            binding!!.etName.text,
            binding!!.etPhone.phone, gender,
            binding!!.etBirthdate.birthDate,
            profileViewModel!!
        )
        saveProfileRequestsState.generalDataRequest.isRequestSend = true
    }

    private fun showResendEmailConfirmationTextView() {
        binding!!.tvResendEmailConfirmation.setText(
            Html.fromHtml(getString(R.string.resend_email_confirmation)),
            TextView.BufferType.SPANNABLE
        )
        binding!!.tvResendEmailConfirmation.setOnClickListener {
            profileViewModel!!.resendEmailConfirmation()
        }

        val tvHeight: Int = emailConfirmationTextViewHeight
        val animation: Animation =
            SlideAnimation(binding!!.containerResendEmailConfirmation, 0, tvHeight)
        animation.interpolator = AccelerateInterpolator()
        animation.duration = RESEND_EMAIL_CONFIRMATION_ANIMATION_DURATION
        binding!!.tvResendEmailConfirmation.startAnimation(animation)
    }

    private fun blockResendEmailConfirmationTextView() {
        binding!!.tvResendEmailConfirmation.text =
            getString(R.string.email_confirmation_already_sent, binding!!.etEmail.text)
        binding!!.tvResendEmailConfirmation.setOnClickListener(null)
    }

    private val emailConfirmationTextViewHeight: Int
        get() {
            val textView = TextView(context)
            textView.setTextSize(
                TypedValue.COMPLEX_UNIT_PX,
                requireContext().resources
                    .getDimension(R.dimen.profile_email_confirmation_text_size)
            )
            textView.setText(
                Html.fromHtml(getString(R.string.resend_email_confirmation)),
                TextView.BufferType.SPANNABLE
            )

            val screenWidth: Int = GeneralTools.getScreenWidth(context)
            val horizontalMargin: Int = requireContext().resources
                .getDimensionPixelSize(R.dimen.activity_horizontal_margin)
            val icWarningWidth: Int =
                requireContext().resources.getDimensionPixelSize(R.dimen.profile_ic_warning_size)
            val textViewWidth: Int = screenWidth - 4 * horizontalMargin - icWarningWidth

            val widthMeasureSpec: Int = View.MeasureSpec.makeMeasureSpec(
                textViewWidth,
                View.MeasureSpec.EXACTLY
            )
            textView.measure(widthMeasureSpec, 0)
            return textView.measuredHeight
        }

    private val profileObserver: Observer<Profile?> =
        Observer<Profile?> { value ->
            binding!!.root.post {
                binding!!.etName.setText(value?.name)
                if (!TextUtils.isEmpty(value?.email)) {
                    binding!!.etEmail.setFieldNotEditable()
                    binding!!.etEmail.setText(value?.email)
                    if (value?.emailConfirmed != null && !value.emailConfirmed!!) {
                        showResendEmailConfirmationTextView()
                    }
                }

                value?.phone
                    ?.takeIf { !TextUtils.isEmpty(it) }
                    ?.let { phone ->
                        val userCountry: Country? = context?.let { context ->
                            profileViewModel!!.getUserCountry(context, phone)
                        }
                        if (userCountry != null) {
                            binding!!.etPhone.setUserCountry(context, userCountry)
                            profileViewModel!!.updateCountryIso(userCountry.isoCode)
                            val localPhoneNumber: String =
                                phone.substring(userCountry.phoneCode.length)
                            val formattedLocalPhoneNumber: String? =
                                profileViewModel?.getFormattedPhone(localPhoneNumber)
                            binding!!.etPhone.setPhone(
                                userCountry.phoneCode,
                                formattedLocalPhoneNumber ?: localPhoneNumber
                            )
                        } else {
                            binding!!.etPhone.setText(
                                profileViewModel!!.getFormattedPhone(phone)
                            )
                        }
                        binding!!.etPhone.setFieldNotEditable()
                    }

                if (value?.gender == null) {
                    binding!!.gender.selectChild(GENDER_DEFAULT_POSITION)
                } else if (value.gender == Gender.MALE) {
                    binding!!.gender.selectChild(GENDER_MALE_POSITION)
                } else {
                    binding!!.gender.selectChild(GENDER_FEMALE_POSITION)
                }
                if (!TextUtils.isEmpty(value?.birthday)) {
                    binding!!.etBirthdate.birthDate = value?.birthday
                    binding!!.etBirthdate.setFieldNotEditable()
                }

                binding!!.etName.subscribeToUpdates(nameUpdateListener)
                binding!!.etPhone.subscribeToUpdates(phoneUpdateListener)
                binding!!.etBirthdate.subscribeToUpdates(birthdayUpdateListener)
                setSaveButtonState(isAllDataSaved)
            }
        }

    private val profileChangeObserver: Observer<Boolean?> = object : Observer<Boolean?> {
        override fun onChanged(isSuccess: Boolean?) {
            val passwordRequest: ProfileRequest = saveProfileRequestsState.passwordRequest
            if (isSuccess == true) {
                updateLocalProfile()
                if (!passwordRequest.isRequestSend || passwordRequest.isResponseSuccess) {
                    binding!!.root.post({ setSaveButtonState(true) })
                    saveProfileRequestsState.generalDataRequest.clearState()
                    saveProfileRequestsState.passwordRequest.clearState()
                } else {
                    saveProfileRequestsState.generalDataRequest.isResponseSuccess = true
                }

                binding!!.root.post({
                    if (!TextUtils.isEmpty(binding!!.etPhone.text)) {
                        binding!!.etPhone.setFieldNotEditable()
                    }
                    if (!TextUtils.isEmpty(binding!!.etBirthdate.text)) {
                        binding!!.etBirthdate.setFieldNotEditable()
                    }
                })
            }
        }
    }

    private val passwordChangeObserver: Observer<Boolean?> = object : Observer<Boolean?> {
        override fun onChanged(isSuccess: Boolean?) {
            val generalDataRequest: ProfileRequest = saveProfileRequestsState.generalDataRequest
            if (isSuccess == true) {
                if (!generalDataRequest.isRequestSend || generalDataRequest.isResponseSuccess) {
                    binding!!.root.post({ setSaveButtonState(true) })
                    saveProfileRequestsState.generalDataRequest.clearState()
                    saveProfileRequestsState.passwordRequest.clearState()
                } else {
                    saveProfileRequestsState.passwordRequest.isResponseSuccess = true
                }
                binding!!.etOldPassword.clear()
                binding!!.etNewPassword.clear()
                binding!!.etPasswordConfirmation.clear()
            }
        }
    }

    private val serverErrorObserver: Observer<ServerError> =
        Observer { serverError: ServerError ->
            if (serverError.errorCode == Constants.SERVER_EMAIL_CONFIRMATION_ALREADY_REQUESTED) {
                blockResendEmailConfirmationTextView()
                return@Observer
            } else if (serverError.errorCode == Constants.SERVER_UNAUTHORIZED) {
                val activity: MainActivity? = activity as MainActivity?
                if (activity != null) {
                    activity.applyNotLoggedState()
                }
                navigateUp()
                return@Observer
            }
            val fieldsErrorsHolder: FieldsErrorsHolder? = serverError.fieldsErrors
            if (fieldsErrorsHolder != null) {
                val errorsMap: Map<Fields, List<String>> = fieldsErrorsHolder.errorsMap
                if (errorsMap.containsKey(Fields.USER_NAME) || errorsMap.containsKey(Fields.PHONE) ||
                    errorsMap.containsKey(Fields.BIRTHDAY)
                ) {
                    binding!!.etName.showErrorIfNeeded(fieldsErrorsHolder, Fields.USER_NAME)
                    binding!!.etPhone.showErrorIfNeeded(fieldsErrorsHolder, Fields.PHONE)
                    binding!!.etBirthdate.showErrorIfNeeded(fieldsErrorsHolder, Fields.BIRTHDAY)
                } else if (errorsMap.containsKey(Fields.CURRENT_PASSWORD) || errorsMap.containsKey(
                        Fields.NEW_PASSWORD
                    )
                ) {
                    binding!!.etOldPassword.showErrorIfNeeded(
                        fieldsErrorsHolder,
                        Fields.CURRENT_PASSWORD
                    )
                    binding!!.etNewPassword.showErrorIfNeeded(
                        fieldsErrorsHolder,
                        Fields.NEW_PASSWORD
                    )
                }
            } else {
                Toast.makeText(
                    <EMAIL>,
                    serverError.errorMessage,
                    Toast.LENGTH_LONG
                ).show()
            }
        }

    private val apolloExceptionObserver: Observer<ApolloException> =
        Observer { exception: ApolloException? ->
            (activity as MainActivity).showConnectionIssueMessage(
                exception
            )
        }

    private val emailConfirmationRequestedObserver: Observer<Boolean> =
        Observer { isSuccess: Boolean ->
            if (isSuccess) {
                Toast.makeText(
                    <EMAIL>,
                    R.string.email_confirmation_was_sent,
                    Toast.LENGTH_LONG
                ).show()
            }
        }

    private val viewerEmptyObserver: Observer<Boolean> =
        Observer { isViewerEmpty: Boolean? ->
            navigateUp()
        }

    private val isProfileChanged: Boolean
        get() = profileViewModel!!.isProfileChanged(
            binding!!.etName.text,
            binding!!.etPhone.phone, gender,
            binding!!.etBirthdate.birthDate
        )

    private val isPasswordChanged: Boolean
        get() {
            if (!changePasswordContainerVisible) {
                return false
            } else {
                return profileViewModel!!.isPasswordChanged(
                    binding!!.etOldPassword.text,
                    binding!!.etNewPassword.text, binding!!.etPasswordConfirmation.text
                )
            }
        }

    private val isAllDataSaved: Boolean
        get() {
            return !isProfileChanged && !isPasswordChanged
        }

    private val gender: Gender
        get() {
            when (binding!!.gender.selectedPos) {
                GENDER_MALE_POSITION -> return Gender.MALE
                GENDER_FEMALE_POSITION -> return Gender.FEMALE
                else -> return Gender.UNKNOWN__
            }
        }

    private fun updateLocalProfile() {
        Settings.get().userName = binding!!.etName.text
        profileViewModel!!.updateLocalProfile(
            binding!!.etName.text, binding!!.etPhone.text,
            gender, binding!!.etBirthdate.birthDate
        )
    }

    private fun navigateUp() {
        val fragmentView: View? = view
        if (fragmentView != null) {
            Navigation.findNavController(fragmentView).navigateUp()
        }
    }

    fun updateBiometricAuthenticationStatus() {
        authenticationStatus = canAuthenticate(requireContext())
    }
}
