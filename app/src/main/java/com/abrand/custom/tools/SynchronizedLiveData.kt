package com.abrand.custom.tools

import androidx.annotation.MainThread
import androidx.lifecycle.MutableLiveData
import java.util.*

class SynchronizedLiveData<T> : MutableLiveData<T>() {
    private val queuedValues: Queue<T> = LinkedList<T>()

    @Synchronized
    override fun postValue(value: T) {
        queuedValues.offer(value)
        super.postValue(value)
    }

    @MainThread
    @Synchronized
    override fun setValue(value: T) {
        queuedValues.remove(value)
        queuedValues.offer(value)
        while (queuedValues.isNotEmpty())
            super.setValue(queuedValues.poll())
    }
}
