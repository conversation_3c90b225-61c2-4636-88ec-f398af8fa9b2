<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:app="http://schemas.android.com/apk/res-auto"
android:id="@+id/card_root"
android:layout_width="match_parent"
android:layout_height="200dp"
android:layout_marginLeft="@dimen/tournaments_card_side_margin"
android:layout_marginTop="@dimen/size_16"
android:layout_marginRight="@dimen/tournaments_card_side_margin">


    <ImageView
        android:id="@+id/iv_item_tournament_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
         />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_gradient_black" />

    <TextView
        android:id="@+id/tv_item_tournament_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginStart="16dp"
        android:layout_marginLeft="16dp"
        android:layout_marginEnd="@dimen/size_16"
        android:layout_marginRight="@dimen/size_16"
        android:textStyle="bold"
        android:gravity="start"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_item_tournament_prize"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="16dp"
        android:layout_marginTop="28dp"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_text" />

    <TextView
        android:id="@+id/tv_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="@dimen/size_16"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:alpha="0.6"
        android:text="@string/tournaments_fund"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_item_tournament_day" />

    <TextView
        android:id="@+id/tv_item_tournament_day"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:layout_marginStart="16dp"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/tv_text1"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_text1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:alpha="0.6"
        android:text="@string/tournaments_toend"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:layout_marginBottom="@dimen/size_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

<Button
    android:id="@+id/btn_about"
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    android:layout_marginEnd="16dp"
    android:textSize="14sp"
    android:layout_gravity="center_horizontal"
    android:minHeight="32dp"
    android:textStyle="bold"
    android:textAllCaps="false"
    android:scaleType="center"
    android:background="@drawable/btn_bg_disable"
    android:textColor="@color/white"
    android:text="@string/tournaments_about"
 />
</androidx.constraintlayout.widget.ConstraintLayout>