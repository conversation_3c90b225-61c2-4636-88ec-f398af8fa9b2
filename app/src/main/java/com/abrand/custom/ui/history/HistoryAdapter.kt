package com.abrand.custom.ui.history

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.abrand.custom.R
import com.abrand.custom.data.entity.HistoryItem
import com.abrand.custom.data.entity.Transaction
import com.abrand.custom.databinding.ItemHistoryBinding
import com.abrand.custom.databinding.ItemHistoryFilterBinding
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.setHtmlText
import com.abrand.custom.type.TransactionDirectionType
import com.abrand.custom.type.TransactionOperationType
import com.abrand.custom.type.TransactionStatus
import java.util.*

class HistoryAdapter(val context: Context, val historyListener: HistoryListener) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private val TAG = "HistoryAdapter"
    private var items = mutableListOf<HistoryItem>()
    private var isExpanded: MutableList<Boolean>? = null
    private var counter: Int = 0

    object ViewTypes {
        const val FILTER = 1
        const val TRANSACTION = 2
    }

    init {
        this.items.add(HistoryItem(HistoryItem.ItemViewType.FILTER))
        notifyItemChanged(0)
    }

    fun setList(transactions: List<Transaction>?) {
        this.items.clear()
        this.items.add(HistoryItem(HistoryItem.ItemViewType.FILTER))

        if (!transactions.isNullOrEmpty()) {
            for (transaction in transactions) {
                this.items.add(HistoryItem(HistoryItem.ItemViewType.TRANSACTION, transaction))
            }
            isExpanded = ArrayList(Collections.nCopies(transactions.size + 1, false))
        }

        notifyDataSetChanged()
    }

    fun updateTransaction(transaction: Transaction) {
        var updatePosition = 0
        items.forEachIndexed { index, historyItem ->
            if (historyItem.transaction?.baseTransaction?.id == transaction.baseTransaction?.id) {
                updatePosition = index
            }
        }

        val updateHistoryItem = HistoryItem(HistoryItem.ItemViewType.TRANSACTION, transaction)
        this.items[updatePosition] = updateHistoryItem

        notifyItemChanged(updatePosition)
    }

    fun setCounter(filtersSelected: Int) {
        this.counter = filtersSelected
        notifyItemChanged(0)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            ViewTypes.FILTER -> {
                val view = ItemHistoryFilterBinding.inflate(inflater, parent, false)
                FilterViewHolder(view)
            }
            else -> {
                val view = ItemHistoryBinding.inflate(inflater, parent, false)
                HistoryViewHolder(view)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = items[position]

        if (holder is FilterViewHolder) {
            holder.bind()
        } else if (holder is HistoryViewHolder) {
            val isExpandedItem = isExpanded?.get(position)
            if (isExpandedItem == true) {
                holder.view.ivArrowDropdown.setBackgroundResource(R.drawable.ic_history_arrow_to_top)
                holder.view.transactionMain.setBackgroundColor(ContextCompat.getColor(context, R.color.transaction_main_bg))
                holder.view.transactionHide.visibility = View.VISIBLE
                holder.view.transactionHide.setOnClickListener {  }
                holder.view.divider.visibility = View.GONE
            } else {
                holder.view.ivArrowDropdown.setBackgroundResource(R.drawable.ic_history_arrow_to_bottom)
                holder.view.transactionMain.setBackgroundColor(Color.TRANSPARENT)
                holder.view.transactionHide.visibility = View.GONE
                if (position != itemCount - 1) {
                    holder.view.divider.visibility = View.VISIBLE
                } else {
                    holder.view.divider.visibility = View.GONE
                }
            }
            holder.itemView.isActivated = isExpandedItem ?: false
            holder.itemView.setOnClickListener {
                collapsePreviousTransaction(position)
                changeTransactionState(position)
            }

            val baseTransaction = item.transaction?.baseTransaction
            if (baseTransaction != null) {
                val children = item.transaction?.children

                setAskQuestionButton(holder, baseTransaction, !children.isNullOrEmpty())
                setPaymentButton(holder, baseTransaction)
                setAmount(holder, baseTransaction)
                setOpenStatus(holder, baseTransaction, children)
                setTransactionIcon(holder, baseTransaction)
                val operationType = baseTransaction.operationType?.let { getTypeTransactionText(it) }
                holder.view.tvOperationType.text = operationType

                setTransactionId(holder, baseTransaction.id, !children.isNullOrEmpty())
                setStatus(holder, baseTransaction.status, !children.isNullOrEmpty())
                setDate(holder, baseTransaction.date)
                holder.view.tvTransactionPaymentSystem.text = baseTransaction.paymentSystemName
                setUserComment(holder, baseTransaction)

                if (children.isNullOrEmpty()) {
                    holder.view.rvChildren.visibility = View.GONE
                } else {
                    initChildrenTransactions(holder, children)
                }

            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == 0) {
            ViewTypes.FILTER
        } else {
            ViewTypes.TRANSACTION
        }
    }

    private fun collapsePreviousTransaction(position: Int) {
        val expandedIndex = getExpandedIndex()
        if (expandedIndex >= 0 && expandedIndex != position) {
            isExpanded?.set(expandedIndex, false)
            notifyItemChanged(expandedIndex)
        }
    }

    private fun getExpandedIndex(): Int {
        isExpanded?.forEachIndexed { index, element ->
            if (element) {
                return index
            }
        }
        return -1
    }

    private fun changeTransactionState(position: Int) {
        val isExpandedItem = isExpanded?.get(position)
        if (isExpandedItem != null) {
            isExpanded?.set(position, !isExpandedItem)
        }
        notifyItemChanged(position)
    }

    private fun initChildrenTransactions(holder: HistoryViewHolder, children: List<Transaction.LocalBaseTransaction>) {
        holder.view.rvChildren.visibility = View.VISIBLE
        holder.view.rvChildren.layoutManager = LinearLayoutManager(context)
        holder.view.rvChildren.adapter =
            ChildTransactionAdapter(context, children, object : ChildTransactionAdapter.Listener {
                override fun onOpenSupportChat(baseTransaction: Transaction.LocalBaseTransaction) {
                    holder.showSupportChatDialog(getSupportChatFirstMessage(holder, baseTransaction))
                }
            })

        val dividerItemDecoration: ItemDecoration =
            ChildTransactionDividerItemDecorator(ContextCompat.getDrawable(context, R.drawable.child_transaction_divider))
        holder.view.rvChildren.addItemDecoration(dividerItemDecoration)
    }

    override fun getItemCount() = items.size

    private fun setAskQuestionButton(holder: HistoryViewHolder, baseTransaction: Transaction.LocalBaseTransaction,
                                     hasChildrenTransactions: Boolean) {
        if ((TransactionOperationType.IN == baseTransaction.operationType || TransactionOperationType.OUT == baseTransaction.operationType) &&
            !hasChildrenTransactions
        ) {
            holder.view.btnQuestion.visibility = View.VISIBLE
            holder.view.btnQuestion.setOnClickListener {
                holder.showSupportChatDialog(getSupportChatFirstMessage(holder, baseTransaction))
            }
        } else {
            holder.view.btnQuestion.visibility = View.GONE
        }
    }

    private fun getSupportChatFirstMessage(holder: HistoryViewHolder,
                                           baseTransaction: Transaction.LocalBaseTransaction): String {
        val transactionId = baseTransaction.id.toString()
        val transactionDate = getFormattedDate(baseTransaction.date, holder.itemView.context)
        val typeTransaction = baseTransaction.operationType?.let { getTypeTransactionText(it) }
        val transactionAmount = getFormattedAmount(baseTransaction)
        val transactionStatus = getStatusText(baseTransaction.status)

        return holder.itemView.context.getString(R.string.transaction_support_chat_message,
            transactionId, transactionDate, typeTransaction, transactionAmount, transactionStatus)
    }

    private fun setPaymentButton(holder: HistoryViewHolder, baseTransaction: Transaction.LocalBaseTransaction) {
        if (baseTransaction.rejectionEnable == true) {
            holder.view.btnPayment.visibility = View.VISIBLE
            holder.view.spaceBeforePaymentBtn.visibility = View.VISIBLE
            holder.view.btnPayment.text = context.getString(R.string.cancel)
            holder.view.btnPayment.setBackgroundResource(R.drawable.bg_btn_transaction_cancel)
            holder.view.btnPayment.setOnClickListener {
                baseTransaction.id?.let { transactionId ->
                    holder.showCancelPayoutDialog(transactionId)
                }
            }
        } else if (TransactionOperationType.IN == baseTransaction.operationType &&
            (TransactionStatus.SUCCESS == baseTransaction.status || TransactionStatus.FAIL == baseTransaction.status)) {
            holder.view.btnPayment.visibility = View.VISIBLE
            holder.view.spaceBeforePaymentBtn.visibility = View.VISIBLE
            holder.view.btnPayment.text = context.getString(R.string.repeat_payment)
            holder.view.btnPayment.setBackgroundResource(R.drawable.bg_btn_transaction_repeat)
            holder.view.btnPayment.setOnClickListener {
                baseTransaction.id?.let { transactionId ->
                    historyListener.onRetryPayment(transactionId)
                }
            }
        } else {
            holder.view.btnPayment.visibility = View.GONE
            holder.view.spaceBeforePaymentBtn.visibility = View.GONE
        }
    }

    private fun setAmount(holder: HistoryViewHolder, baseTransaction: Transaction.LocalBaseTransaction) {
        holder.view.tvAmount.text = getFormattedAmount(baseTransaction)
    }

    private fun getFormattedAmount(baseTransaction: Transaction.LocalBaseTransaction): String {
        val amount: Double = baseTransaction.amount.toString().toDouble()
        return GeneralTools.formatBalance(baseTransaction.currency?.code, amount, 2, true)
    }

    private fun setOpenStatus(holder: HistoryViewHolder, baseTransaction: Transaction.LocalBaseTransaction,
                              children: MutableList<Transaction.LocalBaseTransaction>?) {
        if (!children.isNullOrEmpty()) {
            holder.view.ivOpenStatus.visibility = View.GONE
            holder.view.llOpenStatus.visibility = View.VISIBLE
            showStatusNumberTransactions(holder, children)
        } else {
            holder.view.ivOpenStatus.visibility = View.VISIBLE
            holder.view.llOpenStatus.visibility = View.GONE

            when (baseTransaction.status) {
                TransactionStatus.SUCCESS -> holder.view.ivOpenStatus.setBackgroundResource(R.drawable.ic_transaction_status_success)

                TransactionStatus.NEW,
                TransactionStatus.PROCESS,
                TransactionStatus.PARTIAL,
                TransactionStatus.MANUAL,
                TransactionStatus.USER_AGREEMENT ->
                    holder.view.ivOpenStatus.setBackgroundResource(R.drawable.ic_transaction_status_new)

                TransactionStatus.FAIL,
                TransactionStatus.USER_CANCELLED ->
                    holder.view.ivOpenStatus.setBackgroundResource(R.drawable.ic_transaction_status_fail)

                else -> holder.view.ivOpenStatus.setBackgroundResource(R.drawable.ic_transaction_status_fail)
            }
        }
    }

    private fun showStatusNumberTransactions(holder: HistoryViewHolder,
                                          children: MutableList<Transaction.LocalBaseTransaction>) {
        var numberSuccess = 0
        var numberNew = 0
        var numberFail = 0
        for (item in children) {
            when (item.status) {
                TransactionStatus.SUCCESS -> numberSuccess++

                TransactionStatus.NEW,
                TransactionStatus.PROCESS,
                TransactionStatus.PARTIAL,
                TransactionStatus.MANUAL,
                TransactionStatus.USER_AGREEMENT -> numberNew++

                TransactionStatus.FAIL,
                TransactionStatus.USER_CANCELLED -> numberFail++
                else -> {}
            }
        }

        if (numberSuccess > 0) {
            holder.view.ivOpenStatusSuccess.visibility = View.VISIBLE
            holder.view.tvOpenStatusSuccess.visibility = View.VISIBLE
            holder.view.tvOpenStatusSuccess.text = numberSuccess.toString()
        } else {
            holder.view.ivOpenStatusSuccess.visibility = View.GONE
            holder.view.tvOpenStatusSuccess.visibility = View.GONE
        }

        if (numberNew > 0) {
            holder.view.ivOpenStatusNew.visibility = View.VISIBLE
            holder.view.tvOpenStatusNew.visibility = View.VISIBLE
            holder.view.tvOpenStatusNew.text = numberNew.toString()
        } else {
            holder.view.ivOpenStatusNew.visibility = View.GONE
            holder.view.tvOpenStatusNew.visibility = View.GONE
        }

        if (numberFail > 0) {
            holder.view.ivOpenStatusFail.visibility = View.VISIBLE
            holder.view.tvOpenStatusFail.visibility = View.VISIBLE
            holder.view.tvOpenStatusFail.text = numberFail.toString()
        } else {
            holder.view.ivOpenStatusFail.visibility = View.GONE
            holder.view.tvOpenStatusFail.visibility = View.GONE
        }
    }

    private fun setTransactionIcon(holder: HistoryViewHolder, baseTransaction: Transaction.LocalBaseTransaction) {
        if (TransactionOperationType.EXCHANGE_POINT == baseTransaction.operationType) {
            holder.view.ivTransaction.setBackgroundResource(R.drawable.ic_transaction_exchange_point)
        } else if (TransactionDirectionType.IN == baseTransaction.direction) {
            holder.view.ivTransaction.setBackgroundResource(R.drawable.ic_transaction_in)
        } else {
            holder.view.ivTransaction.setBackgroundResource(R.drawable.ic_transaction_out)
        }
    }

    private fun setTransactionId(holder: HistoryViewHolder, transactionId: Int?, hasChildrenTransactions: Boolean) {
        if (hasChildrenTransactions) {
            holder.view.tvTransactionId.visibility = View.GONE
        } else {
            holder.view.tvTransactionId.text = context.getString(R.string.transaction_id, transactionId)
            holder.view.tvTransactionId.visibility = View.VISIBLE
        }
    }

    private fun setStatus(holder: HistoryViewHolder, transactionStatus: TransactionStatus?, hasChildrenTransactions: Boolean) {
        if (hasChildrenTransactions) {
            holder.view.tvTransactionStatus.visibility = View.GONE
            holder.view.ivTransactionStatus.visibility = View.GONE
            return
        } else {
            holder.view.tvTransactionStatus.visibility = View.VISIBLE
            holder.view.ivTransactionStatus.visibility = View.VISIBLE
        }

        holder.view.tvTransactionStatus.text = getStatusText(transactionStatus)

        when (transactionStatus) {
            TransactionStatus.SUCCESS -> holder.view.ivTransactionStatus.setBackgroundResource(R.drawable.ic_transaction_status_success)

            TransactionStatus.NEW,
            TransactionStatus.PROCESS,
            TransactionStatus.PARTIAL,
            TransactionStatus.MANUAL,
            TransactionStatus.USER_AGREEMENT ->
                holder.view.ivTransactionStatus.setBackgroundResource(R.drawable.ic_transaction_status_new)

            TransactionStatus.FAIL,
            TransactionStatus.USER_CANCELLED ->
                holder.view.ivTransactionStatus.setBackgroundResource(R.drawable.ic_transaction_status_fail)

            else -> holder.view.ivTransactionStatus.setBackgroundResource(R.drawable.ic_transaction_status_fail)
        }
    }

    private fun getStatusText(transactionStatus: TransactionStatus?) : String {
        return when (transactionStatus) {
            TransactionStatus.SUCCESS -> context.getString(R.string.transaction_status_success)
            TransactionStatus.NEW -> context.getString(R.string.transaction_status_new)
            TransactionStatus.PROCESS -> context.getString(R.string.transaction_status_in_process)
            TransactionStatus.PARTIAL -> context.getString(R.string.transaction_status_partial)
            TransactionStatus.MANUAL -> context.getString(R.string.transaction_status_manual)
            TransactionStatus.USER_AGREEMENT -> context.getString(R.string.transaction_status_user_agreement)
            TransactionStatus.FAIL -> context.getString(R.string.transaction_status_fail)
            TransactionStatus.USER_CANCELLED -> context.getString(R.string.transaction_status_user_cancelled)
            else -> context.getString(R.string.something_goes_wrong)
        }
    }

    private fun setDate(holder: HistoryViewHolder, date: Any?) {
        holder.view.tvTransactionDate.text = getFormattedDate(date, holder.itemView.context)
        if (holder.view.tvTransactionStatus.visibility == View.VISIBLE) {
            val params = holder.view.tvTransactionDate.layoutParams as ViewGroup.MarginLayoutParams
            params.topMargin = context.resources.getDimensionPixelSize(R.dimen.transaction_date_margin_top)
            holder.view.tvTransactionDate.layoutParams = params
        } else {
            val params = holder.view.tvTransactionDate.layoutParams as ViewGroup.MarginLayoutParams
            params.topMargin = 0
            holder.view.tvTransactionDate.layoutParams = params
        }
    }

    private fun getFormattedDate(date: Any?, context: Context) : String? {
        return date?.let {
            DateTools.getFormattedDate(context, it, DateTools.FormatDateType.TRANSACTION) }
    }

    private fun setUserComment(holder: HistoryViewHolder, baseTransaction: Transaction.LocalBaseTransaction) {
        val transactionStatus = getStatusText(baseTransaction.status)
        if (baseTransaction.userComment.isNullOrEmpty() || (baseTransaction.userComment == transactionStatus)) {
            holder.view.tvTransactionUserComment.visibility = View.GONE
        } else {
            holder.view.tvTransactionUserComment.visibility = View.VISIBLE
            holder.view.tvTransactionUserComment.setHtmlText(baseTransaction.userComment)
        }
    }

    private fun getTypeTransactionText(operationType: TransactionOperationType): String {
        return when(operationType) {
            TransactionOperationType.BS_BONUS_PERSONAL_ASSIGN -> context.getString(R.string.transaction_BS_BONUS_PERSONAL_ASSIGN)
            TransactionOperationType.STRIP_BONUS -> context.getString(R.string.transaction_STRIP_BONUS)
            TransactionOperationType.PAYMENT_BONUS -> context.getString(R.string.transaction_PAYMENT_BONUS)
            TransactionOperationType.LOTTERY_EXTRA_ATTEMPT -> context.getString(R.string.transaction_LOTTERY_EXTRA_ATTEMPT)
            TransactionOperationType.INSURANCE_PAY -> context.getString(R.string.transaction_INSURANCE_PAY)
            TransactionOperationType.DAILY_CASH_BACK_PRIZE -> context.getString(R.string.transaction_DAILY_CASH_BACK_PRIZE)
            TransactionOperationType.BUY_TALISMAN -> context.getString(R.string.transaction_BUY_TALISMAN)
            TransactionOperationType.BUY_BENEFITS -> context.getString(R.string.transaction_BUY_BENEFITS)
            TransactionOperationType.BS_REAL_WHITE_LIST -> context.getString(R.string.transaction_BS_REAL_WHITE_LIST)
            TransactionOperationType.BS_REAL_SINGLE -> context.getString(R.string.transaction_BS_REAL_SINGLE)
            TransactionOperationType.BS_REAL_REGISTER -> context.getString(R.string.transaction_BS_REAL_REGISTER)
            TransactionOperationType.BS_REAL_PROGRESSIVE -> context.getString(R.string.transaction_BS_REAL_PROGRESSIVE)
            TransactionOperationType.BS_REAL_MANUAL_ACTIVATION -> context.getString(R.string.transaction_BS_REAL_MANUAL_ACTIVATION)
            TransactionOperationType.BS_REAL_LOYALTY -> context.getString(R.string.transaction_BS_REAL_LOYALTY)
            TransactionOperationType.BS_BONUS_WHITE_LIST -> context.getString(R.string.transaction_BS_BONUS_WHITE_LIST)
            TransactionOperationType.BS_BONUS_SINGLE -> context.getString(R.string.transaction_BS_BONUS_SINGLE)
            TransactionOperationType.BS_BONUS_REGISTER -> context.getString(R.string.transaction_BS_BONUS_REGISTER)
            TransactionOperationType.BS_BONUS_PROGRESSIVE -> context.getString(R.string.transaction_BS_BONUS_PROGRESSIVE)
            TransactionOperationType.BS_BONUS_MANUAL_ACTIVATION -> context.getString(R.string.transaction_BS_BONUS_MANUAL_ACTIVATION)
            TransactionOperationType.BS_BONUS_LOYALTY -> context.getString(R.string.transaction_BS_BONUS_LOYALTY)
            TransactionOperationType.OUT_BUY_LOTTERY_TICKETS -> context.getString(R.string.transaction_OUT_BUY_LOTTERY_TICKETS)
            TransactionOperationType.BS_BONUS_MANUAL_ASSIGN -> context.getString(R.string.transaction_BS_BONUS_MANUAL_ASSIGN)
            TransactionOperationType.CANCEL_BONUS_BALANCE -> context.getString(R.string.transaction_CANCEL_BONUS_BALANCE)
            TransactionOperationType.BS_REAL_MANUAL_ASSIGN -> context.getString(R.string.transaction_BS_REAL_MANUAL_ASSIGN)
            TransactionOperationType.WHEEL_FORTUNE_SPIN -> context.getString(R.string.transaction_WHEEL_FORTUNE_SPIN)
            TransactionOperationType.BONUS_BALANCE_PRIZE -> context.getString(R.string.transaction_BONUS_BALANCE_PRIZE)
            TransactionOperationType.REAL_BALANCE_PRIZE -> context.getString(R.string.transaction_REAL_BALANCE_PRIZE)
            TransactionOperationType.EXCHANGE_POINT -> context.getString(R.string.transaction_EXCHANGE_POINT)
            TransactionOperationType.ADMIN_OUT_MANUAL -> context.getString(R.string.transaction_ADMIN_OUT_MANUAL)
            TransactionOperationType.ADMIN_IN_COMPENSATION -> context.getString(R.string.transaction_ADMIN_IN_COMPENSATION)
            TransactionOperationType.ADMIN_IN_PRIZE -> context.getString(R.string.transaction_ADMIN_IN_PRIZE)
            TransactionOperationType.OUT -> context.getString(R.string.transaction_OUT)
            TransactionOperationType.IN -> context.getString(R.string.transaction_IN)
            TransactionOperationType.BS_BONUS_PROMO_CODE -> context.getString(R.string.transaction_BS_BONUS_PROMO_CODE)
            TransactionOperationType.BS_REAL_PERSONAL_ASSIGN -> context.getString(R.string.transaction_BS_REAL_PERSONAL_ASSIGN)
            TransactionOperationType.REFUND -> context.getString(R.string.transaction_REFUND)
            else -> {
                Log.d(TAG, "unknown TransactionOperationType: ${operationType.name}")
                ""
            }
        }
    }

    inner class HistoryViewHolder(val view: ItemHistoryBinding) : RecyclerView.ViewHolder(view.root) {

        fun showSupportChatDialog(message: String) {
            val dialog = Dialog(itemView.context)
            dialog.setContentView(R.layout.dialog_transaction_support_chat)
            dialog.findViewById<View>(R.id.ib_close)
                .setOnClickListener { v: View? -> dialog.cancel() }
            dialog.findViewById<View>(R.id.btn_no).setOnClickListener { v: View? ->
                dialog.cancel()
            }

            dialog.findViewById<View>(R.id.btn_yes).setOnClickListener { v: View? ->
                dialog.cancel()
                historyListener.onOpenSupportChat(message)
            }

            val dialogView = dialog.window?.decorView
            dialogView?.setBackgroundResource(android.R.color.transparent)

            val lp = dialog.window?.attributes
            lp?.dimAmount = 0.8f
            dialog.window?.attributes = lp
            dialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)

            dialog.show()
        }

        fun showCancelPayoutDialog(transactionId: Int) {
            val dialog = Dialog(itemView.context)
            dialog.setContentView(R.layout.dialog_transaction_cancel)
            dialog.findViewById<View>(R.id.ib_close)
                .setOnClickListener { v: View? -> dialog.cancel() }
            dialog.findViewById<View>(R.id.btn_no).setOnClickListener { v: View? ->
                dialog.cancel()
            }

            dialog.findViewById<View>(R.id.btn_yes).setOnClickListener { v: View? ->
                dialog.cancel()
                historyListener.onCancelPayout(transactionId)
            }

            val dialogView = dialog.window?.decorView
            dialogView?.setBackgroundResource(android.R.color.transparent)

            val lp = dialog.window?.attributes
            lp?.dimAmount = 0.8f
            dialog.window?.attributes = lp
            dialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)

            dialog.show()
        }

    }

    inner class FilterViewHolder(val view: ItemHistoryFilterBinding) : RecyclerView.ViewHolder(view.root) {

        fun bind() {
            view.btnFilter.setOnClickListener {
                historyListener.onOpenFilter()
            }

            if (counter == 0) {
                hideCounter()
            } else {
                showCounter(counter)
            }
        }

        private fun showCounter(counter: Int) {
            view.tvCounter.text = counter.toString()
            view.tvCounter.visibility = View.VISIBLE
        }

        private fun hideCounter() {
            view.tvCounter.visibility = View.INVISIBLE
        }

    }

    interface HistoryListener {
        fun onOpenSupportChat(messageText: String)
        fun onCancelPayout(transactionId: Int)
        fun onRetryPayment(transactionId: Int)
        fun onOpenFilter()
    }
}
