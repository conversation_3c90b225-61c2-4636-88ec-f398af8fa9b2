package com.abrand.custom.ui.news

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.entity.NewsItem
import com.abrand.custom.data.repositories.YearRepository
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.squareup.picasso.Picasso

class NewsAdapter(val newsCardWidth: Int, val newsCardHeight: Int) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    var items = mutableListOf<NewsItem>()
    var newsClickListener: NewsClickListener? = null
    var footerListener: FooterListener? = null
    var newsLoadVH: NewsLoadVH? = null
    var currentTotalNews = 0
    var isGamesLoading = false

    init {
        items.add(NewsItem(NewsItem.ItemType.TITLE))
        items.add(NewsItem(NewsItem.ItemType.FOOTER))
    }

    fun setList(newList: MutableList<NewsItem>) {
        newList.add(0, NewsItem(NewsItem.ItemType.TITLE))
        newList.add(NewsItem(NewsItem.ItemType.NEWS_LOAD))
        newList.add(NewsItem(NewsItem.ItemType.FOOTER))
        this.items = newList
        notifyDataSetChanged()
    }

    fun appendList(newItems: MutableList<NewsItem>) {
        //Add news items before NEWS_LOAD and FOOTER item
        items.addAll(items.size - 2, newItems)
        notifyDataSetChanged()
    }

    fun setGameLoadingState(isLoading: Boolean) {
        this.isGamesLoading = isLoading
        newsLoadVH?.setGameLoadingState(isLoading)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            NewsItem.ItemType.TITLE.id -> TitleVH(inflater, parent)
            NewsItem.ItemType.CARD_NEWS.id -> CardNewsVH(inflater, parent)
            NewsItem.ItemType.SMALL_NEWS.id -> SmallNewsVH(inflater, parent)
            NewsItem.ItemType.NEWS_LOAD.id -> NewsLoadVH(inflater, parent)
            else -> FooterVH(inflater, parent)
        }
    }

    override fun getItemCount() = items.size

    override fun getItemViewType(position: Int): Int {
        return items[position].itemType.id
    }

    fun getNewsCount(): Int {
        return items.count {
            NewsItem.ItemType.CARD_NEWS == it.itemType ||
                    NewsItem.ItemType.SMALL_NEWS == it.itemType
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val newsItem = items[position]
        if (holder is SmallNewsVH) {
            holder.bind(newsItem, position)
        } else if (holder is CardNewsVH) {
            holder.bind(newsItem)
        } else if (holder is NewsLoadVH) {
            newsLoadVH = holder
            holder.bind()
        } else if (holder is FooterVH) {
            holder.bind()
        }

        setFirstItemAppBarPadding(position, holder)
    }

    private fun setFirstItemAppBarPadding(position: Int, holder: RecyclerView.ViewHolder) {
        if (position == 0) {
            val padding: Int = GeneralTools.getActionBarHeight(holder.itemView.context)
            holder.itemView.setPadding(
                    holder.itemView.paddingLeft,
                    padding,
                    holder.itemView.paddingRight,
                    holder.itemView.paddingBottom
            )
        } else {
            holder.itemView.setPadding(
                    holder.itemView.paddingLeft,
                    0,
                    holder.itemView.paddingRight,
                    holder.itemView.paddingBottom
            )
        }
    }

    class TitleVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_news_title, parent, false))

    inner class CardNewsVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_card_news, parent, false)) {
        private var cardRoot: ConstraintLayout = itemView.findViewById(R.id.card_root)
        private val tvTitle = itemView.findViewById<TextView>(R.id.tv_title)
        private val ivBg = itemView.findViewById<ImageView>(R.id.iv_bg)
        private val tvMonthDay = itemView.findViewById<TextView>(R.id.tv_month_day)
        private val tvMonthName = itemView.findViewById<TextView>(R.id.tv_month_name)

        fun bind(newsItem: NewsItem) {
            tvTitle.text = newsItem.title
            setNewsBackground(newsItem.mobileIcon, itemView.context)
            tvMonthDay.text = DateTools.getDay(itemView.context, newsItem.createdAt)
            tvMonthName.text = DateTools.getMonth(itemView.context, newsItem.createdAt)

            itemView.setOnClickListener {
                newsClickListener?.onNewsClicked(newsItem)
            }

        }

        private fun setNewsBackground(mobileIcon: String?, context: Context) {
            if (!TextUtils.isEmpty(mobileIcon)) {
                Picasso.get()
                        .load(ApoloConfig.getFullUrl(mobileIcon))
                        .resize(newsCardWidth, newsCardHeight)
                        .centerCrop()
                        .into(object : com.squareup.picasso.Target {
                            override fun onPrepareLoad(placeHolderDrawable: Drawable?) {

                            }

                            override fun onBitmapFailed(e: Exception?, errorDrawable: Drawable?) {}

                            override fun onBitmapLoaded(bitmap: Bitmap, from: Picasso.LoadedFrom?) {
                                val roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(context.resources, bitmap)
                                roundedBitmapDrawable.cornerRadius = context.resources.getDimensionPixelSize(R.dimen.news_card_corner_radius).toFloat()
                                ivBg.background = roundedBitmapDrawable
                            }
                        })
            } else {
                ivBg.background = null
            }
        }
    }

    inner class SmallNewsVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_small_news, parent, false)) {
        private val tvCreatedAt = itemView.findViewById<TextView>(R.id.tv_created_at)
        private val tvTitle = itemView.findViewById<TextView>(R.id.tv_title)

        fun bind(newsItem: NewsItem, position: Int) {
            tvTitle.text = newsItem.title
            setCreatedAt(newsItem)
            setTopMargin(position)

            itemView.setOnClickListener {
                newsClickListener?.onNewsClicked(newsItem)
            }
        }

        private fun setCreatedAt(newsItem: NewsItem) {
            val createdAt = newsItem.createdAt
            if (!TextUtils.isEmpty(createdAt as String)) {
                val formattedDate = DateTools.getFormattedDate(itemView.context, createdAt, DateTools.FormatDateType.NEWS)
                if (!TextUtils.isEmpty(formattedDate)) {
                    tvCreatedAt.text = formattedDate
                } else {
                    tvCreatedAt.text = createdAt
                }
            }
        }

        private fun setTopMargin(position: Int) {
            if (NewsFragment.CARD_NEWS_NUM == position - 1) {
                val layoutParams = itemView.layoutParams
                if (layoutParams is ViewGroup.MarginLayoutParams) {
                    layoutParams.topMargin = itemView.context.resources.getDimensionPixelSize(R.dimen.news_small_first_top_margin)
                }
            } else {
                val layoutParams = itemView.layoutParams
                if (layoutParams is ViewGroup.MarginLayoutParams) {
                    layoutParams.topMargin = itemView.context.resources.getDimensionPixelSize(R.dimen.news_small_top_margin)
                }
            }
        }
    }

    inner class NewsLoadVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_news_load, parent, false)) {
        private val btnLoadMore = itemView.findViewById<TextView>(R.id.btn_load_more)
        private val ivLoader = itemView.findViewById<ImageView>(R.id.iv_loader)

        init {
            setLoadMoreView()
        }

        fun bind() {
            btnLoadMore.setOnClickListener {
                newsClickListener?.onLoadMoreClicked()
            }
        }

        fun setGameLoadingState(isLoading: Boolean) {
            if (isLoading) {
                ivLoader.visibility = View.VISIBLE
                val animation = AnimationUtils.loadAnimation(itemView.context, R.anim.rotation_loader)
                ivLoader.startAnimation(animation)
                btnLoadMore.visibility = View.INVISIBLE
            } else {
                ivLoader.clearAnimation()
                ivLoader.visibility = View.GONE
                setLoadMoreView()
            }
        }

        private fun setLoadMoreView() {
            if (currentTotalNews > getNewsCount()) {
                if (currentTotalNews - getNewsCount() >= NewsFragment.LOAD_STEP) {
                    btnLoadMore.text = itemView.context.resources.getQuantityString(R.plurals.more_news, NewsFragment.LOAD_STEP, NewsFragment.LOAD_STEP)
                } else {
                    val btnNewsCount = currentTotalNews - getNewsCount()
                    btnLoadMore.text = itemView.context.resources.getQuantityString(R.plurals.more_news, btnNewsCount, btnNewsCount)
                }
                btnLoadMore.visibility = View.VISIBLE
            } else {
                btnLoadMore.visibility = View.GONE
            }
        }
    }

    inner class FooterVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.view_footer, parent, false)) {

        fun bind() {
            val paddingBottom: Int = itemView.context.resources.getDimensionPixelSize(R.dimen.not_organic_user_footer_padding_bottom)
            itemView.setPadding(0, 0, 0, paddingBottom)
            itemView.findViewById<View>(R.id.gl_payment_systems_view).setOnClickListener {
                footerListener?.onClickPaymentSystems()
            }
            itemView.findViewById<TextView>(R.id.tv_copyright_year)?.let {
                it.text = itemView.context.getString(
                    R.string.app_copyright_year,
                    YearRepository.getCurrentYear()
                )
            }
        }
    }

    interface NewsClickListener {
        fun onNewsClicked(newsItem: NewsItem)
        fun onLoadMoreClicked()
    }

}
