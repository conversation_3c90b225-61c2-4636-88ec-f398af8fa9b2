package com.abrand.custom.ui.game

import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.NavHostFragment.findNavController
import com.abrand.custom.R

class InGameMenuBonusBalanceFragment : Fragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                MaterialTheme {
                    val configuration = LocalConfiguration.current
                    val paddingStart = when (configuration.orientation) {
                        Configuration.ORIENTATION_LANDSCAPE -> 128.dp
                        else -> 80.dp
                    }
                    val modifier = Modifier.padding(start = paddingStart, end = 16.dp)
                    // In Compose world call composables
                    BonusBalancesScreen(modifier, openPromotions = {
                        findNavController(this@InGameMenuBonusBalanceFragment)
                            .navigate(R.id.nav_promotions, null)
                    })
                }
            }
        }
    }
}
