<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient"
    tools:context=".ui.newpassword.NewPasswordFragment">

    <TextView
        android:id="@+id/tv_enter_password"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="80dp"
        android:text="@string/input_password"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.abrand.custom.ui.views.textfield.TextField
        android:id="@+id/et_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        app:hint="@string/password"
        app:layout_constraintTop_toBottomOf="@+id/tv_enter_password" />

    <Button
        android:id="@+id/btn_save"
        style="@style/ButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:text="@string/proceed"
        app:layout_constraintTop_toBottomOf="@+id/et_password" />

    <Button
        android:id="@+id/btn_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:background="@drawable/btn_bg_disable"
        android:text="@string/cancellation"
        android:textAllCaps="false"
        app:layout_constraintTop_toBottomOf="@+id/btn_save" />

</androidx.constraintlayout.widget.ConstraintLayout>
