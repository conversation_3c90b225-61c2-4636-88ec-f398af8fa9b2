package com.abrand.custom.data.mappers

import com.abrand.custom.GetHomeAdditionalBlocksQuery
import com.abrand.custom.GetNewsQuery
import com.abrand.custom.data.entity.LocalNews
import com.abrand.custom.fragment.FragmentNewsItem

fun List<GetHomeAdditionalBlocksQuery.Item2?>.mapToFirstLocalNews(): LocalNews? {
    return if (this.isNotEmpty()) {
        this[0]?.fragments?.fragmentNewsItem?.mapToLocalNews()
    } else {
        null
    }
}

fun FragmentNewsItem.mapToLocalNews(): LocalNews {
    return LocalNews(
        name = this.name,
        createdAt = this.publishDate,
        mobileIcon = this.mobileIcon,
        content = this.content)
}

fun List<GetNewsQuery.Item?>?.mapToLocalNewsList() : List<LocalNews> {
    val result = arrayListOf<LocalNews>()

    if (this != null) {
        for (item in this) {
            if (item != null) {
                result.add(item.fragments.fragmentNewsItem.mapToLocalNews())
            }
        }
    }

    return result
}
