<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="54dp"
        android:paddingStart="16dp"
        android:paddingTop="8dp"
        android:paddingEnd="16dp"
        android:paddingBottom="8dp"
        tools:background="@drawable/bg_text_field">

        <TextView
            android:id="@+id/hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/hint_editable_field"
            android:textSize="10sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0"
            tools:text="Hint" />

        <LinearLayout
            android:id="@+id/country_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/hint">

            <ImageView
                android:id="@+id/iv_country_flag"
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:src="@drawable/ic_flag_ru" />

            <ImageView
                android:id="@+id/iv_arrow_dropdown"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:src="@drawable/ic_arrow_dropdown_black" />

        </LinearLayout>

        <TextView
            android:id="@+id/country_phone_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:includeFontPadding="false"
            android:text="@string/russia_country_code"
            android:textColor="#000"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@+id/country_container"
            app:layout_constraintStart_toEndOf="@+id/country_container"
            app:layout_constraintTop_toTopOf="@+id/country_container"
            tools:text="@string/russia_country_code" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginLeft="8dp"
            android:background="@null"
            android:includeFontPadding="false"
            android:textColor="#000"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@+id/country_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/country_phone_code"
            app:layout_constraintTop_toTopOf="@+id/country_container"
            tools:text="Phone number"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        android:textAlignment="center"
        android:textColor="#ffffff"
        android:textFontWeight="400"
        android:textSize="11sp"
        android:visibility="gone"
        tools:text="error message"
        tools:visibility="visible" />
</LinearLayout>