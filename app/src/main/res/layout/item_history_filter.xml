<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/size_16"
    android:layout_marginTop="28dp"
    android:layout_marginBottom="18dp">

    <Button
        android:id="@+id/btn_filter"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="6dp"
        android:background="@drawable/bg_btn_history_filter"
        android:minWidth="42dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="@string/history_filter"
        android:textAllCaps="true"
        android:textSize="10sp" />

    <TextView
        android:id="@+id/tv_counter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|end"
        android:background="@drawable/bg_history_filter_counter"
        android:gravity="center"
        android:textColor="#FFF"
        android:textSize="9sp"
        android:visibility="gone" />

</FrameLayout>
