package com.abrand.custom.network

import android.util.Log
import com.abrand.custom.*
import com.abrand.custom.data.AffDataGenerator.Companion.getAffData
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Constants
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.*
import com.abrand.custom.data.entity.Banner
import com.abrand.custom.data.entity.Talisman
import com.abrand.custom.data.entity.User
import com.abrand.custom.data.entity.WheelSector
import com.abrand.custom.data.entity.lottery.LocalLottery
import com.abrand.custom.data.entity.lottery.LocalLotteryTicket
import com.abrand.custom.data.entity.lottery.LotteriesResponse
import com.abrand.custom.data.mappers.*
import com.abrand.custom.domain.interfaces.CaptchaRepository
import com.abrand.custom.domain.interfaces.CaptchaRepository.ReCaptchaResult
import com.abrand.custom.fragment.GameThumb
import com.abrand.custom.interfaces.*
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.mapEnum
import com.abrand.custom.type.*
import com.abrand.custom.type.Locale.Companion.safeValueOf
import com.apollographql.apollo3.api.ApolloResponse
import com.apollographql.apollo3.api.Optional
import com.apollographql.apollo3.exception.ApolloException
import com.apollographql.apollo3.exception.ApolloNetworkException
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.retryWhen

suspend fun <T> Flow<T>.retrySubscription(
    limit: Long = Long.MAX_VALUE,
    delay: Long = 10_000L
): Flow<T> {
    return this.retryWhen(predicate = { cause, attempt ->
        Log.d("WS", "retrySubscription: invoked" +
                "\n with attempt: $attempt " +
                "\n and cause $cause")
        if ( attempt == limit || attempt % 100L == 0L ) { //second condition to get stat, increase if too many reports
            FirebaseCrashlytics.getInstance()
                .log("subscription fail\n with attempt: $attempt\n and cause $cause")
        }
        (cause is ApolloNetworkException && attempt < limit)
            .also { if (it) delay(delay) }
    })
}

class ApolloProcessorKt {
    companion object {
        @JvmStatic
        fun subscribeTournament(tournamentId: String):
                Flow<ApolloResponse<SubscribeTournamentResultSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeTournamentResultSubscription(tournamentId))
                .toFlow()
        }

        @JvmStatic
        fun subscribeBalance(): Flow<ApolloResponse<SubscribeBalanceSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeBalanceSubscription()).toFlow()
        }

        @JvmStatic
        fun subscribeLoyaltyPoints(): Flow<ApolloResponse<SubscribeLoyaltyPointsSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeLoyaltyPointsSubscription()).toFlow()
        }

        @JvmStatic
        fun subscribeLoyaltyProgress():
                Flow<ApolloResponse<SubscribeLoyaltyProgressSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeLoyaltyProgressSubscription())
                .toFlow()
        }

        @JvmStatic
        fun subscribeLoyaltyStatus(): Flow<ApolloResponse<SubscribeLoyaltyStatusSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeLoyaltyStatusSubscription())
                .toFlow()
        }

        @JvmStatic
        fun subscribeLoyaltyXOnPoints(): Flow<ApolloResponse<SubscribeLoyaltyXOnPointsSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeLoyaltyXOnPointsSubscription())
                .toFlow()
        }

        @JvmStatic
        fun subscribeRealtimeMessages():
                Flow<ApolloResponse<SubscribeRealTimeNotificationSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeRealTimeNotificationSubscription())
                .toFlow()
        }

        @JvmStatic
        fun subscribeOnlineUsers(): Flow<ApolloResponse<OnlineUsersSubscription.Data>> {
            return AApp.getApolloClient().subscription(OnlineUsersSubscription()).toFlow()
        }

        @JvmStatic
        fun subscribeMessages(): Flow<ApolloResponse<SubscribeMessagesSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeMessagesSubscription()).toFlow()
        }

        fun subscribeFreeSpins(): Flow<ApolloResponse<SubscribeFreeSpinsSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeFreeSpinsSubscription()).toFlow()
        }

        fun subscribePromotionsCount(): Flow<ApolloResponse<SubscribeViewerBonusesCountSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeViewerBonusesCountSubscription()).toFlow()
        }

        fun subscribeTournamentsCount(): Flow<ApolloResponse<SubscribeTournamentsCountChangeSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeTournamentsCountChangeSubscription()).toFlow()
        }

        fun subscribeLotteriesCount(): Flow<ApolloResponse<SubscribeLotteriesCountChangeSubscription.Data>> {
            return AApp.getApolloClient().subscription(SubscribeLotteriesCountChangeSubscription()).toFlow()
        }

        fun hello(target: GenericTarget<String?>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(HelloQuery()).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.hello)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun loadTournamentsByStatus(status: List<TournamentStatus?>?, offset: Int, loadAmount: Int,
                                    loadTarget: LoadTournament, scope: CoroutineScope) {
            var loadAmountInt: Int? = null
            var offsetInt: Int? = null
            if (loadAmount > 0) {
                loadAmountInt = loadAmount
            }
            if (offset > 0) {
                offsetInt = offset
            }
            val query = GetTournamentsByStatusQuery(status!!, Optional.presentIfNotNull(offsetInt),
                Optional.presentIfNotNull(loadAmountInt))
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(query).execute()
                    if (response.data?.tournaments != null) {
                        val tournaments: GetTournamentsByStatusQuery.Tournaments? =
                            response.data?.tournaments

                        if ( tournaments != null ) {
                            val tournamentsItems = mutableListOf<TournamentsItem>()
                            for (item in tournaments.items) {
                                if (item != null) {
                                    tournamentsItems.add(item.fragments.fragmentTournamentsItem.mapToTournamentsItem())
                                }
                            }

                            loadTarget.onSuccess(
                                tournamentsItems,
                                tournaments.totalCount,
                                offset)
                        } else {
                            loadTarget.onSuccess(ArrayList(), 0, offset)
                        }
                    } else {
                        response.errors
                        ApolloProcessor.processError(response, loadTarget)
                    }
                } catch (e: ApolloException) {
                    loadTarget.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun loadTournamentById(id: Int, loadTarget: GenericTarget<TournamentsItem>, scope: CoroutineScope) {
            val query = GetTournamentByIdQuery(id)

            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(query).execute()

                    if (response.data != null && response.data?.tournament != null) {
                        val tournament = response.data?.tournament

                        if ( tournament != null ) {
                            loadTarget.onSuccess(tournament.fragments.fragmentFullTournamentsItem.mapToTournamentsItem())
                        } else {
                            loadTarget.onSuccess(null)
                        }
                    } else {
                        ApolloProcessor.processError(response, loadTarget)
                    }
                } catch (e: ApolloException) {
                    loadTarget.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun joinTournament(tournamentId: Int, loadTarget: GenericTarget<TournamentsItem>, scope: CoroutineScope) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(JoinTournamentMutation(tournamentId)).execute()

                    if (response.data != null && !response.hasErrors()) {
                        loadTarget.onSuccess(response.data?.joinTournament
                            ?.fragments?.fragmentFullTournamentsItem?.mapToTournamentsItem())
                    } else {
                        ApolloProcessor.processError(response, loadTarget)
                    }
                } catch (e: ApolloException) {
                    loadTarget.onFailure(e)
                }
            }
        }

        //unused temporary left as example
//        fun loadGamesByType(
//            type: GameTypeField?, loadAmount: Int, offset: Int,
//            gameOrderField: GameOrderField?, orderDirection: OrderDirection?,
//            loadTarget: LoadTarget, scope: CoroutineScope
//        ) {
//            var loadAmountInt: Int? = null
//            var gameOrder: GameOrder? = null
//            if (loadAmount > 0) {
//                loadAmountInt = loadAmount
//            }
//            if (gameOrderField != null && orderDirection != null) {
//                gameOrder = GameOrder(gameOrderField, orderDirection)
//            }
//            val query = GetGamesByTypeQuery(
//                type!!, Optional.presentIfNotNull(loadAmountInt),
//                Optional.Present(offset), Optional.presentIfNotNull(gameOrder)
//            )
//            scope.launch {
//                try {
//                    val response = AApp.getApolloClient().query(query).execute()
//
//                    if ( response.data?.games != null ) {
//                        response.data?.games?.fragments?.games?.let {
//                            ApolloProcessor.processLoad(
//                                it, offset, loadTarget)
//                        }
//                    } else {
//                        throw ApolloException("Illegal server response")
//                    }
//                } catch (e: ApolloException) {
//                    loadTarget.onFailure(e)
//                }
//            }
//        }

        fun loadNewGames(
            loadAmount: Int, offset: Int, gameOrderField: GameListOrderField?,
            orderDirection: OrderDirection?, loadTarget: LoadTarget, scope: CoroutineScope
        ) {
            var loadAmountInt: Int? = null
            var gameOrder: GameListOrder? = null
            if (loadAmount > 0) {
                loadAmountInt = loadAmount
            }
            if (gameOrderField != null && orderDirection != null) {
                gameOrder = GameListOrder(gameOrderField, orderDirection)
            }
            val query = GetNewGamesQuery(loadAmountInt!!, offset, Optional.presentIfNotNull(gameOrder))
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(query).execute()

                    if (response.data != null && response.data?.gameListNew != null) {
                        response.data?.gameListNew?.fragments?.gameList?.let {
                            ApolloProcessor.processLoad(it, offset, loadTarget)
                        }
                    } else {
                        throw  ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    loadTarget.onFailure(e)
                }
            }
        }

        fun loadSlotGames(
            loadAmount: Int, offset: Int, gameOrderField: GameListOrderField?,
            orderDirection: OrderDirection?, loadTarget: LoadTarget, scope: CoroutineScope
        ) {
            var loadAmountInt: Int? = null
            var gameOrder: GameListOrder? = null
            if (loadAmount > 0) {
                loadAmountInt = loadAmount
            }
            if (gameOrderField != null && orderDirection != null) {
                gameOrder = GameListOrder(gameOrderField, orderDirection)
            }
            val query = GetSlotGamesQuery(loadAmountInt!!, offset, Optional.presentIfNotNull(gameOrder))
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(query).execute()

                    if (response.data != null && response.data?.gameListSlots != null) {
                        response.data?.gameListSlots?.fragments?.gameList?.let {
                            ApolloProcessor.processLoad(it, offset, loadTarget)
                        }
                    } else {
                        throw ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    loadTarget.onFailure(e)
                }
            }
        }

        fun loadTableGames(
            loadAmount: Int, offset: Int, gameOrderField: GameListOrderField?,
            orderDirection: OrderDirection?, loadTarget: LoadTarget, scope: CoroutineScope
        ) {
            var loadAmountInt: Int? = null
            var gameOrder: GameListOrder? = null
            if (loadAmount > 0) {
                loadAmountInt = loadAmount
            }
            if (gameOrderField != null && orderDirection != null) {
                gameOrder = GameListOrder(gameOrderField, orderDirection)
            }
            val query = GetTableGamesQuery(loadAmountInt!!, offset, Optional.presentIfNotNull(gameOrder))
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(query).execute()

                    if (response.data != null && response.data?.gameListTable != null) {
                        response.data?.gameListTable?.fragments?.gameList?.let {
                            ApolloProcessor.processLoad(it, offset, loadTarget)
                        }
                    } else {
                        throw ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    loadTarget.onFailure(e)
                }
            }
        }

        fun loadPopularGames(
            loadAmount: Int, offset: Int, gameOrderField: GameListOrderField?,
            orderDirection: OrderDirection?, loadTarget: LoadTarget, scope: CoroutineScope
        ) {
            var loadAmountInt: Int? = null
            var gameOrder: GameListOrder? = null
            if (loadAmount > 0) {
                loadAmountInt = loadAmount
            }
            if (gameOrderField != null && orderDirection != null) {
                gameOrder = GameListOrder(gameOrderField, orderDirection)
            }
            val query = GetPopularGamesQuery(loadAmountInt!!, offset, Optional.presentIfNotNull(gameOrder))
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(query).execute()

                    if (response.data?.gameListPopular != null) {
                        response.data?.gameListPopular?.fragments?.gameList?.let {
                            ApolloProcessor.processLoad(it, offset, loadTarget)
                        }
                    } else {
                        throw ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    loadTarget.onFailure(e)
                }
            }
        }

        fun loadFavouriteGames(
            gameOrderField: GameListOrderField?, orderDirection: OrderDirection?,
            loadAmount: Int, offset: Int,
            loadTarget: LoadTarget, scope: CoroutineScope
        ) {
            var gameOrder: GameListOrder? = null

            if (gameOrderField != null && orderDirection != null) {
                gameOrder = GameListOrder(gameOrderField, orderDirection)
            }

            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(
                        GetFavouriteGamesQuery(
                            loadAmount, offset, Optional.presentIfNotNull(gameOrder),
                        )
                    ).execute()

                    if (response.data?.gameListFavourites != null) {
                        ApolloProcessor.processLoad(
                            response.data?.gameListFavourites!!.fragments.gameList,
                            offset,
                            loadTarget
                        )
                    } else {
                        throw ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    loadTarget.onFailure(e)
                }
            }
        }

        fun loadGamesByType(typeSlug: String, limit: Int, offset: Int, loadTarget: LoadTarget, scope: CoroutineScope) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(
                        GetGameListByTypeQuery(typeSlug, limit, offset,)
                    ).execute()

                    val gameListByType = response.data?.gameListByType
                    if (gameListByType != null) {
                        ApolloProcessor.processLoad(gameListByType.fragments.gameList, offset, loadTarget)
                    } else {
                        throw ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    loadTarget.onFailure(e)
                }
            }
        }

        //this is replacement method to isGameFavourite(int gameId, GenericTarget<Boolean> target)
        @JvmStatic
        fun getGameById(gameId: Int, target: GenericTarget<LocalGameItem>, scope: CoroutineScope) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(GetGameByIdQuery(gameId)).execute()

                    if (response.data?.gameById != null && !response.hasErrors()) {
                        target.onSuccess(LocalGameItem(response.data?.gameById!!))
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun loadGamesByName(name: String, loadAmount: Int, offset: Int, loadTarget: LoadTarget,
                            scope: CoroutineScope) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(
                        GetGamesByNameQuery(name, loadAmount, offset)
                    ).execute()

                    if (response.data?.gameListAll != null) {
                        ApolloProcessor.processLoad(
                            response.data?.gameListAll!!.fragments.gameList,
                            offset,
                            loadTarget
                        )
                    } else {
                        throw ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    loadTarget.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun getGameByUrl(gameUrl: String?, target: GenericTarget<GameThumb?>, scope: CoroutineScope) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(GetGameByUrlQuery(gameUrl!!))
                        .execute()

                    if (response.data != null && !response.hasErrors()) {
                        val gameByUrl: GetGameByUrlQuery.GameByUrl? =
                            response.data?.gameByUrl

                        if (gameByUrl != null) {
                            target.onSuccess(gameByUrl.fragments.gameThumb)
                        } else {
                            target.onSuccess(null)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun hasGameFreeSpins(gameUrl: String?, target: GenericTarget<Boolean?>, scope: CoroutineScope) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(HasGameFreeSpinsQuery(gameUrl!!))
                        .execute()

                    if (response.data != null && !response.hasErrors()) {
                        val hasGameFreeSpins: HasGameFreeSpinsQuery.HasGameFreeSpins? =
                            response.data!!.hasGameFreeSpins

                        if (hasGameFreeSpins != null) {
                            target.onSuccess(hasGameFreeSpins.free_spins)
                        } else {
                            target.onSuccess(null)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun getDrawerRegistrationBanner(target: GetRegistrationBannerTarget, scope: CoroutineScope) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(GetDrawerBannerQuery()).execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.banners)
                    } else {
                        throw ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getSlides(target: GetSlidesTarget, scope: CoroutineScope) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(GetSlidesQuery()).execute()

                    if (response.data != null && !response.hasErrors()
                            && response.data?.slides != null) {
                        response.data?.slides?.let { slidesList ->
                            val banners = slidesList.mapNotNull { slide ->
                                slide?.image?.let { image ->
                                    Banner(image, slide.html ?: "", slide.url)
                                }
                            }

                            target.onSuccess(banners)
                        }
                    } else {
                        throw ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun obtainDemoGameUrl(shortUrl: String, target: GenericTarget<String>,
                              scope: CoroutineScope) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient()
                        .query(GetDemoGameUrlQuery(shortUrl, ApoloConfig.HOST))
                        .execute()

                    if (response.data?.demoGameUrl != null) {
                        target.onSuccess(response.data?.demoGameUrl?.url)
                    } else {
                        throw ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun obtainGameUrl(
            shortUrl: String,
            refCode: String?,
            target: GenericTarget<String>,
            scope: CoroutineScope
        ) {
            scope.launch {
                try {
                    val userState = Settings.get().userState
                    var referrer = refCode

                    if (User.State.ORGANIC == userState) {
                        return@launch
                    }

                    if (referrer.isNullOrBlank()) {
                        referrer = null
                    }

                    val response = AApp.getApolloClient()
                        .query(GetGameUrlQuery(shortUrl,
                            ApoloConfig.HOST, Optional.presentIfNotNull(referrer)))
                        .execute()

                    if (response.data?.gameUrl != null) {
                        response.data?.gameUrl?.url?.let {
                            target.onSuccess(it)
                        }
                    } else {
                        throw ApolloException("Illegal server response")
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun getReCaptcha(
            captchaForm: CaptchaForm,
            target: GenericTarget<GetReCaptchaQuery.ReCaptcha>,
            scope: CoroutineScope
        ) {
            scope.launch {
                try {
                    val query = GetReCaptchaQuery(
                        captchaForm,
                        Optional.presentIfNotNull(ApoloConfig.RECAPTCHA_HOST)
                    )

                    val response = AApp.getApolloClient().query(query).execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.reCaptcha)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        suspend fun getRecaptcha(
            captchaForm: CaptchaForm,
            host: String = ApoloConfig.RECAPTCHA_HOST,
            refCode: String? = null,
        ): ReCaptchaResult {
            try {
                val query = GetReCaptchaQuery(
                    captchaForm,
                    Optional.presentIfNotNull(host),
                    Optional.presentIfNotNull(refCode)
                )
                val response = AApp.getApolloClient().query(query).execute()

                if (response.data != null && !response.hasErrors()) {
                    return ReCaptchaResult.Success(
                        CaptchaRepository.CaptchaStatus(
                            enabled = response.data?.reCaptcha?.enabled == true,
                            provider = response.data?.reCaptcha?.provider
                                .mapEnum(CaptchaRepository.CaptchaProvider.UNKNOWN__) { f, s ->
                                    f.rawValue == s?.rawValue
                                },
                            captchaId = response.data?.reCaptcha?.captchaId
                        )
                    )
                } else {
                    val responseErrors = processResponseErrors(response)

                    return ReCaptchaResult.ResponseError(
                        ServerError(
                            errorMessage = responseErrors.first,
                            errorCode = responseErrors.second,
                            fieldsErrors = responseErrors.third
                        )
                    )
                }
            } catch (exception: ApolloException) {
                return ReCaptchaResult.NetworkError(exception.mapToResponseException())
            }
        }

        @JvmStatic
        fun getRegisterBonuses(target: GetRegisterBonusesTarget, scope: CoroutineScope) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient().query(GetRegisterBonusesQuery()).execute()

                    if (response.data != null) {
                        target.onSuccess(response.data?.registerBonuses ?: emptyList())
                    } else {
                        throw ApolloException("Incorrect server response")
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun register(
            email: String,
            password: String,
            language: String,
            registrationBonusId: Int,
            gCaptchaResponse: String?,
            adjustId: String?,
            pushSubscriptionData: String?,
            refCode: String?,
            target: GenericTarget<AuthResponse>,
            scope: CoroutineScope
        ) {
            val mutation: RegistrationMutation
            val gCaptchaHost = ApoloConfig.RECAPTCHA_HOST
            var gCaptchaCode = gCaptchaResponse
            var registrationBonusIdInt: Int? = null

            var locale = safeValueOf(language)
            // TODO: it's possibly a bug, need to check tech task
            if (locale != Locale.EN || locale != Locale.ZH) {
                locale = Locale.RU
            }

            if (gCaptchaCode.isNullOrBlank()) {
                gCaptchaCode = null
            }

            if (registrationBonusId != -1 && registrationBonusId != 0) {
                registrationBonusIdInt = registrationBonusId
            }

            mutation = RegistrationMutation(
                email, password,
                locale,
                Optional.presentIfNotNull(gCaptchaCode), Optional.presentIfNotNull(gCaptchaHost),
                Optional.presentIfNotNull(registrationBonusIdInt),
                Optional.presentIfNotNull(
                    RegistrationPayload(
                        Optional.presentIfNotNull(refCode),
                        Optional.presentIfNotNull(getAffData(adjustId)),
                        Optional.Absent,
                        Optional.Absent,
                        Optional.presentIfNotNull(pushSubscriptionData)
                    ),
                ),
            )

            scope.launch {
                try {
                    val response = AApp.getApolloClient().mutation(mutation).execute()

                    if (!response.hasErrors()
                        && response.data?.registerUserByEmail?.viewer?.id != null
                    ) {
                        response.data?.registerUserByEmail?.viewer?.id?.let {
                            target.onSuccess(AuthResponse(it, email))
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun login(
            email: String,
            password: String,
            gCaptchaResponse: String,
            adjustId: String?,
            pushSubscriptionData: String?,
            refCode: String?,
            target: GenericTarget<AuthResponse>,
            scope: CoroutineScope
        ) {
            val gCaptchaHost = ApoloConfig.RECAPTCHA_HOST
            var gCaptchaCode: String? = gCaptchaResponse
            val mutation: AuthMutation

            if (gCaptchaCode.isNullOrBlank()) {
                gCaptchaCode = null
            }

            mutation = AuthMutation(
                email, password,
                Optional.presentIfNotNull(gCaptchaCode),
                Optional.presentIfNotNull(gCaptchaHost),
                Optional.presentIfNotNull(getAffData(adjustId)),
                Optional.presentIfNotNull(pushSubscriptionData),
                Optional.presentIfNotNull(refCode),
            )

            scope.launch {
                try {
                    val response = AApp.getApolloClient().mutation(mutation).execute()

                    if (!response.hasErrors() && response.data?.auth?.viewer?.id != null) {
                        response.data?.auth?.viewer?.id?.let {
                            target.onSuccess(AuthResponse(it, email))
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun socialLogin(
            token: String,
            language: String,
            target: GenericTarget<AuthResponse>,
            scope: CoroutineScope
        ) {
            var locale = safeValueOf(language)
            // TODO: it's possibly a bug, need to check tech task
            if (locale !== Locale.EN || locale !== Locale.ZH) {
                locale = Locale.RU
            }

            val mutation = SocialAuthMutation(ApoloConfig.HOST, token, locale)

            scope.launch {
                try {
                    val response = AApp.getApolloClient().mutation(mutation).execute()

                    if (response.data?.login4playAuth?.viewer?.id != null
                        && !response.hasErrors()
                    ) {
                        response.data?.login4playAuth?.viewer?.let { viewer ->
                            viewer.id?.let { id ->
                                target.onSuccess(AuthResponse(id, viewer.profile?.email))
                            }
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun autoLogin(
            loginToken: String,
            refCode: String?,
            adjustId: String?,
            target: GenericTarget<AutologinMutation.Viewer>,
            scope: CoroutineScope
        ) {
            var referrer :String? = refCode

            if (referrer.isNullOrBlank()) {
                referrer = null
            }

            scope.launch {
                try {
                    val response = AApp.getApolloClient().mutation(
                        AutologinMutation(
                            loginToken,
                            Optional.presentIfNotNull(referrer),
                            Optional.presentIfNotNull(getAffData(adjustId))
                        ))
                        .execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.autologin?.viewer)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }


        fun updateMobileAppSubscription(
            pushSubscriptionData: String,
            target: GenericTarget<Boolean>,
            scope: CoroutineScope
        ) {
            scope.launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(UpdateMobileAppSubscriptionMutation(pushSubscriptionData))
                        .execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.updateMobileAppSubscription)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun logout(target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(UserLogoutMutation()).execute()

                    if ( response.data != null && !response.hasErrors() ) {
                        val userLogout = response.data?.userLogout ?: true

                        if (userLogout) {
                            Settings.get().setUserId("")
                            Settings.get().userName = ""
                            FirebaseCrashlytics.getInstance().setUserId("")
                        }

                        target.onSuccess(userLogout)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun restorePasswordByMail(email: String, target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(ResetPassMutation(email)).execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.userSendResetPasswordEmail)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun getInitialViewerData(target: GetInitialViewerDataTarget) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetInitialViewerDataQuery())
                        .execute()

                    if (response.data != null && !response.hasErrors()) {
                        val viewer = response.data?.viewer

                        if (viewer != null) {
                            target.onSuccess(viewer)
                        } else {
                            ApolloProcessor.processEmptyViewer(target)
                        }
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun getPaymentUrl(direction: PaymentUrlDirection, refCode: String?, target: GetUrlTarget) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    var referrer = refCode

                    if (referrer.isNullOrBlank()) {
                        referrer = null
                    }

                    val response = AApp.getApolloClient().query(
                        GetPaymentUrlQuery(
                            ApoloConfig.HOST,
                            direction,
                            Optional.presentIfNotNull(referrer)
                        )
                    ).execute()

                    if (response.data != null && !response.hasErrors()) {
                        if (response.data?.viewer?.paymentUrl != null) {
                            target.onSuccess(response.data?.viewer?.paymentUrl?.url)
                        } else {
                            ApolloProcessor.processEmptyViewer(target)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun getProfile(target: GetProfileTarget) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetProfileQuery()).execute()

                    if (response.data != null && !response.hasErrors()) {
                        val viewer = response.data?.viewer

                        if (viewer != null) {
                            target.onSuccess(viewer.profile)
                        } else {
                            ApolloProcessor.processEmptyViewer(target)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getTransactions(operations: List<PaymentCompoundOperation>?, statuses: List<TransactionStatus>?,
                            dateFrom: String?, dateTo: String?, target: TransactionsTarget) {
            CoroutineScope(Dispatchers.IO).launch {
                try {

                    val dateFromTo = if (!dateFrom.isNullOrEmpty() && !dateTo.isNullOrEmpty()) {
                        DateFromTo(dateFrom, dateTo)
                    } else {
                        null
                    }

                    val response = AApp.getApolloClient().query(
                        GetTransactionsQuery(
                            Optional.presentIfNotNull(operations),
                            Optional.presentIfNotNull(statuses),
                            Optional.presentIfNotNull(dateFromTo)),
                    ).execute()

                    if (response.data != null && !response.hasErrors()) {
                        val viewer = response.data?.viewer

                        if (viewer != null) {
                            target.onSuccess(viewer.transactions.mapToLocalTransactions())
                        } else {
                            ApolloProcessor.processEmptyViewer(target)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun cancelPayout(paymentId: Int, target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(CancelPayoutMutation(paymentId)).execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.cancelPayout)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }

                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getTransaction(transactionId: Int, target: TransactionTarget) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetTransactionQuery(transactionId)).execute()

                    if (response.data != null && !response.hasErrors()) {
                        val viewer = response.data?.viewer

                        if (viewer != null) {
                            val transaction = viewer.transactions?.get(0)
                            if (transaction != null) {
                                target.onSuccess(transaction.mapToLocalTransaction())
                            }
                        } else {
                            ApolloProcessor.processEmptyViewer(target)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getRetryPaymentUrl(transactionId: Int, refCode: String, target: RetryPaymentUrlTarget) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(RetryPaymentUrlQuery(transactionId, ApoloConfig.HOST, refCode)).execute()

                    if (response.data != null && !response.hasErrors()) {
                        val viewer = response.data?.viewer
                        if (viewer != null) {
                            target.onSuccess(viewer.retryPaymentUrl)
                        } else {
                            ApolloProcessor.processEmptyViewer(target)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun userChangePassword(
            oldPassword: String,
            newPassword: String,
            target: GenericTarget<Boolean>
        ) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(
                        ChangePasswordMutation(oldPassword, newPassword)
                    ).execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.userChangePassword)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun userChangeProfile(
            pUserName: String?,
            pPhone: String?,
            pGender: Gender?,
            pBirthday: String?,
            pGCaptchaResponse: String?,
            target: GenericTarget<Boolean>
        ) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val gCaptchaHost = ApoloConfig.RECAPTCHA_HOST
                    var userName = pUserName
                    var phone = pPhone
                    var gender = pGender
                    var birthday = pBirthday
                    var gCaptchaResponse = pGCaptchaResponse

                    if (userName.isNullOrBlank()) {
                        userName = null
                    }
                    if (phone.isNullOrBlank()) {
                        phone = null
                    }
                    if (gender == Gender.UNKNOWN__) {
                        gender = null
                    }
                    if (birthday.isNullOrBlank()) {
                        birthday = null
                    }
                    if (gCaptchaResponse.isNullOrBlank()) {
                        gCaptchaResponse = null
                    }

                    val mutation = ChangeProfileMutation(
                        Optional.presentIfNotNull(userName), Optional.presentIfNotNull(phone),
                        Optional.presentIfNotNull(gender), Optional.presentIfNotNull(birthday),
                        Optional.presentIfNotNull(gCaptchaResponse),
                        Optional.presentIfNotNull(gCaptchaHost)
                    )
                    val response = AApp.getApolloClient().mutation(mutation).execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.userChangeProfile)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun requestEmailConfirmation(target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(RequestEmailConfirmationMutation()).execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.requestEmailConfirmation)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun makeRebill(sum: Int, target: GenericTarget<String>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(
                            MakeRebillMutation(Optional.presentIfNotNull(sum),
                            ApoloConfig.HOST
                        )).execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.makeRebill)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun getFastPaymentUrl(sum: Int, refCode: String?, target: GetFastPaymentUrlTarget) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    var referrer = refCode

                    if (referrer.isNullOrBlank()) {
                        referrer = null
                    }

                    val response = AApp.getApolloClient().query(
                        GetFastPaymentUrlQuery(
                            Optional.presentIfNotNull(sum),
                            ApoloConfig.HOST,
                            Optional.presentIfNotNull(referrer)
                        )).execute()

                    if (response.data != null && !response.hasErrors()) {
                        if (response.data?.viewer?.oneClickUrl != null) {
                            response.data?.viewer?.oneClickUrl?.let {
                                target.onSuccess(it)
                            }
                        } else {
                            ApolloProcessor.processEmptyViewer(target)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun getFastClickPaymentSystem(target: FastClickPaymentSystemTarget) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetFastClickPaymentSystemQuery())
                        .execute()

                    if (response.data != null && !response.hasErrors()) {
                        if (response.data?.viewer != null) {
                            target.onSuccess(response.data?.viewer?.fastClickPaymentSystem)
                        } else {
                            ApolloProcessor.processEmptyViewer(target)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun getBonuses(target: GenericTarget<BonusesResponse> ) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetBonusesQuery()).execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.mapToBonusesResponse())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun bonusActivate(bonusId: String, pPromoCode: String?, target: GenericTarget<Int>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    var promoCode = pPromoCode

                    if (promoCode.isNullOrBlank()) {
                        promoCode = null
                    }

                    val mutation = BonusActivateMutation(
                        bonusId, Optional.presentIfNotNull(promoCode)
                    )
                    val  response = AApp.getApolloClient().mutation(mutation).execute()

                    if (!response.hasErrors() && response.data?.bonusActivate != null) {
                        target.onSuccess(response.data?.bonusActivate!!.id)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun bonusDeactivate(bonusId: String, target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(BonusDeactivateMutation(bonusId))
                        .execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.bonusDeactivate)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun bonusActivateWithHash(
            bonusId: String,
            userId: String,
            hash: String,
            target: GenericTarget<Boolean>
        ) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(BonusActivateWithHashMutation(
                            bonusId, userId, hash
                        )).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.bonusActivateWithHash)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun bonusReceiveWithHash(
            bonusId: String,
            userId: String,
            hash: String,
            target: GenericTarget<Boolean>
        ) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(BonusReceiveWithHashMutation(
                        bonusId, userId, hash
                    )).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.bonusReceiveWithHash)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun tryBonusPromoCode(promoCode: String, target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(TryBonusPromoCodeMutation(promoCode)).execute()
                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(true)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun addGameToFavourites(gameId: Int, target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(AddGameToFavouritesMutation(gameId)).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.addGameToFavourites?.isFavourite)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun removeGameFromFavourites(gameId: Int, target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(RemoveGameFromFavouritesMutation(gameId)).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.removeGameFromFavourites?.isFavourite)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getSupportConfig(target: GenericTarget<GetSupportConfigQuery.YhelperConfig>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .query(GetSupportConfigQuery(ApoloConfig.HOST)).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.yhelperConfig)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getLoyaltyStatuses(target: GenericTarget<List<GetLoyaltyStatusesQuery.LoyaltyStatus>?>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetLoyaltyStatusesQuery()).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.loyaltyStatuses?.filterNotNull())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getExchangePointsData(target: GenericTarget<GetExchangePointsDataQuery.Viewer>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetExchangePointsDataQuery())
                        .execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.viewer)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun loyaltyPointsExchange(points: Int, target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(LoyaltyPointsExchangeMutation(points)).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.loyaltyPointsExchange)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getNews(offset: Int, pLoadAmount: Int, target: GetNewsTarget) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    var loadAmount: Int? = null

                    if (pLoadAmount > 0) {
                        loadAmount = pLoadAmount
                    }

                    val response = AApp.getApolloClient().query(GetNewsQuery(
                        Optional.presentIfNotNull(offset),
                        Optional.presentIfNotNull(loadAmount)
                    )).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(
                            response.data?.news?.items?.mapToLocalNewsList() ?: emptyList(),
                            response.data?.news?.totalCount ?: 0,
                            offset
                        )
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun getNewsItemByUrl(newsUrl: String, target: GenericTarget<LocalNews>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetNewsItemByUrlQuery(newsUrl))
                        .execute()

                    if (!response.hasErrors() && response.data?.newsItemByUrl != null) {
                        target.onSuccess(response.data?.newsItemByUrl?.fragments?.fragmentNewsItem?.mapToLocalNews())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun resetUserPasswordByToken(
            resetToken: String,
            newPassword: String,
            refCode: String?,
            affData: String?,
            target: GenericTarget<String>
        ) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val mutation = ResetUserPasswordByTokenMutation(
                        resetToken, newPassword,
                        Optional.presentIfNotNull(refCode),
                        Optional.presentIfNotNull(affData)
                    )
                    val response = AApp.getApolloClient().mutation(mutation).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.resetUserPasswordByToken?.viewer?.id)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun confirmEmail(confirmationCode: String?, target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(UserConfirmEmailMutation(
                        Optional.presentIfNotNull(confirmationCode)
                    )).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.userConfirmEmail)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        @JvmStatic
        fun userCheckPasswordResetToken(token: String, target: GenericTarget<String>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .query(UserCheckPasswordResetTokenQuery(token)).execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(token)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun checkPassword(password: String, target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(CheckPasswordQuery(password))
                        .execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.userCheckPassword)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getMessages(target: MessagesTarget) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetMessagesQuery()).execute()

                    if (!response.hasErrors() && response.data != null) {
                        val viewer = response.data?.viewer

                        if (viewer != null) {
                            target.onSuccess(viewer.messages)
                        } else {
                            ApolloProcessor.processEmptyViewer(target)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun deleteSeveralMessages(ids: ArrayList<String>?, target: GenericTarget<Int>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(DeleteSeveralMessagesMutation(Optional.presentIfNotNull(ids)))
                        .execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.deleteSeveralNotifications)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun readSeveralMessages(ids: ArrayList<String>?, target: GenericTarget<Int>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(ReadSeveralMessagesMutation(Optional.presentIfNotNull(ids)))
                        .execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.readSeveralNotifications)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun readAllMessages(target: GenericTarget<Int>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(ReadAllMessagesMutation())
                        .execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.readAllNotifications)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun resetBonusBalance(target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(ResetBonusBalanceMutation())
                        .execute()

                    if (!response.hasErrors() && response.data != null) {
                        target.onSuccess(response.data?.resetUserBonusBalance)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getBonusRefund(target: GenericTarget<BonusRefund>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetBonusRefundQuery()).execute()

                    if (!response.hasErrors() && response.data != null) {
                        val viewer = response.data?.viewer
                        if (viewer != null) {
                            target.onSuccess(viewer.wallet?.mapToBonusRefund())
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getTalisman(target: GenericTarget<Talisman>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetTalismanQuery()).execute()

                    if (!response.hasErrors() && response.data != null) {
                        val viewer = response.data?.viewer

                        if (viewer != null) {
                            val talisman = Talisman(viewer.talismanV2?.name, viewer.talismanV2?.img)
                            target.onSuccess(talisman)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        suspend fun getSupportPhone(): String? =
            withContext(Dispatchers.IO) {
                val supportPhone: String? = try {
                    val response = AApp.getApolloClient().query(GetSupportPhoneQuery(ApoloConfig.HOST)).execute()

                    response.data?.siteMirror?.supportPhone
                } catch (e: ApolloException) {
                    null
                }

                supportPhone
            }

        fun getWheel(currencyCode: String, denomination: Int, target: GenericTarget<LocalWheel>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .query(GetWheelQuery(currencyCode, denomination))
                        .execute()

                    if (response.data != null && !response.hasErrors()) {
                        val wheel = response.data?.wheelByDenomination
                        target.onSuccess(wheel?.mapToLocalWheel())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getFreeSpins(target: GenericTarget<Int>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetFreeSpinsQuery()).execute()
                    if (response.data != null && !response.hasErrors()) {
                        val wheelUserFreeSpins = response.data?.wheelUserFreeSpins
                        target.onSuccess(wheelUserFreeSpins.mapToLocalWheelUserFreeSpin())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getWheelRules(textId: String, target: GenericTarget<String>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetWheelFortuneRulesQuery(textId)).execute()
                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.textBlocks.mapToWheelRules())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun wheelSpin(wheelId : Int, target: GenericTarget<WheelSector>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(WheelSpinMutation(wheelId)).execute()

                    if (response.data != null && !response.hasErrors()) {
                        target.onSuccess(response.data?.wheelGameSpin?.mapToWheelSector())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }

                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getPage(path: String, target: GenericTarget<String>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetPageQuery(path)).execute()
                    if (response.data != null && !response.hasErrors()) {
                        val pageContent = response.data?.page?.contents?.get(0)
                        target.onSuccess(pageContent)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getLotteries(offset: Int,  loadAmount: Int, target: GenericTarget<LotteriesResponse>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(
                        GetLotteriesQuery(
                            Optional.presentIfNotNull(offset),
                            Optional.presentIfNotNull(loadAmount)
                        )).execute()

                    val lotteries = response.data?.lotteries
                    if (lotteries != null && !response.hasErrors()) {
                        val items = lotteries.items.mapToListLottery()
                        val totalCount = lotteries.totalCount
                        val lotteriesResponse = LotteriesResponse(items, totalCount, offset)
                        target.onSuccess(lotteriesResponse)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getLottery(isUserAuthorized: Boolean, id: Int, target: GenericTarget<LocalLottery>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetLotteryQuery(id, isUserAuthorized)).execute()
                    if (!response.hasErrors()) {
                        val fragmentFullLotteryItem = response.data?.lottery?.asStandardLottery?.fragments?.fragmentFullLotteryItem
                        target.onSuccess(fragmentFullLotteryItem?.mapToLocalLottery())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun buyLotteryTickets(lotteryId: Int, ticketsCount: Int,
            target: GenericTarget<MutableList<LocalLotteryTicket>>) {

            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(BuyLotteryTicketsMutation(lotteryId, ticketsCount)).execute()
                    if (!response.hasErrors()) {
                        target.onSuccess(response.data?.buyLotteryTickets?.mapToLocalLotteryTickets())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun buyLotteryTicketsPackage(lotteryId: Int, ticketsCount: Int,
                              target: GenericTarget<MutableList<LocalLotteryTicket>>) {

            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient()
                        .mutation(BuyLotteryTicketsPackageMutation(lotteryId, ticketsCount)).execute()
                    if (!response.hasErrors()) {
                        target.onSuccess(response.data?.buyLotteryTicketsPackage?.mapToLocalTickets())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getHallOfFame(year: Int, month:Int, itemsLimit: Int, target: GenericTargetV2<MutableList<HallOfFamePlace>>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(
                        GetHallOfFameQuery(
                            Optional.presentIfNotNull(year),
                            Optional.presentIfNotNull(month),
                            Optional.presentIfNotNull(itemsLimit)
                        )).execute()
                    if (!response.hasErrors()) {
                        target.onSuccess(response.data?.hotHallOfFameV2?.mapToListHallOfFamePlace())
                    } else {
                        processErrorV2(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e.mapToResponseException())
                }
            }
        }

        fun processErrorV2(response: ApolloResponse<*>, target: BaseTargetOnErrorV2) {
            val errorMessage = ApolloProcessor.getErrorMessage(response)
            val errorCode = ApolloProcessor.getErrorCode(response)
            val errorsHolder = FieldsErrorsHolder.parce(response.errors)
            target.onError(errorMessage, errorCode, errorsHolder)
        }

        fun processResponseErrors(
            response: ApolloResponse<*>
        ): Triple<String, String, FieldsErrorsHolder?> {
            val errorMessage = getErrorMessage(response)
            val errorCode = getErrorCode(response)
            val errorsHolder = FieldsErrorsHolder.parce(response.errors)

            return Triple(errorMessage, errorCode, errorsHolder)
        }

        private fun getErrorMessage(response: ApolloResponse<*>): String {
            response.errors?.let { errors ->
                if (errors.isNotEmpty()) {
                    return errors[0].message
                }
            }
            return "Empty error message"
        }

        fun getErrorCode(response: ApolloResponse<*>): String {
            var customAttributes: MutableMap<String, Any?>? = null
            val errors = response.errors
            if (errors?.isNotEmpty() == true) {
                customAttributes = errors[0].nonStandardFields?.toMutableMap()
            }
            return if (customAttributes != null) {
                customAttributes[Constants.SERVER_CODE].toString()
            } else {
                ""
            }
        }

        fun getLotteryPrize(winnerId: Int, target: GenericTarget<Int>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(GetLotteryPrizeMutation(winnerId)).execute()
                    if (!response.hasErrors()) {
                        target.onSuccess(response.data?.getLotteryPrize?.id)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getUnvisitedCount(target: GenericTarget<UnvisitedCount>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetUnvisitedCountQuery()).execute()
                    if (!response.hasErrors()) {
                        response.data?.let {
                            val unvisitedCount = UnvisitedCount(
                                bonusesUnvisitedCount = it.bonuses?.unvisitedCount,
                                tournamentsUnvisitedCount = it.tournaments?.viewerCount,
                                lotteriesUnvisitedCount = it.lotteries?.viewerCount)
                            target.onSuccess(unvisitedCount)
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun markBonusesVisited(target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(MarkBonusesVisitedMutation()).execute()
                    if (!response.hasErrors()) {
                        target.onSuccess(response.data?.markBonusesVisited)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun markTournamentsVisited(target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(MarkTournamentsVisitedMutation()).execute()
                    if (!response.hasErrors()) {
                        target.onSuccess(response.data?.markTournamentsVisitedV2)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun markLotteriesVisited(target: GenericTarget<Boolean>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(MarkLotteriesVisitedMutation()).execute()
                    if (!response.hasErrors()) {
                        target.onSuccess(response.data?.markLotteriesVisited)
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getBonusStoreProducts(target: GenericTarget<List<LocalBonusProductForPoints>>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetBonusStoreProductsQuery()).execute()
                    if (!response.hasErrors()) {
                        target.onSuccess(response.data?.bonusStoreProducts.mapToListLocalBonusProductForPoints())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun bonusStoreBuyProduct(productId: Int, target: GenericTarget<LocalBonusProductForPoints>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().mutation(BonusStoreBuyProductMutation(productId)).execute()
                    if (!response.hasErrors()) {
                        target.onSuccess(
                            response.data?.bonusStoreBuyProduct?.fragments?.storeProductFragment?.mapToLocalBonusProductForPoints())
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getHomeAdditionalBlocks(target: GenericTarget<HomeAdditionalBlocks>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetHomeAdditionalBlocksQuery()).execute()
                    if (!response.hasErrors()) {
                        response.data?.let {
                            val tournamentItem = it.tournaments?.items?.mapToFirstTournamentItem()
                            val localLottery = it.lotteries?.items?.mapToFirstLocalLottery()
                            val localNews = it.news?.items?.mapToFirstLocalNews()
                            target.onSuccess(HomeAdditionalBlocks(tournamentItem, localLottery, localNews))
                        }
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }

        fun getSocialNetworks(target: GenericTarget<AvailableSocialNetworksResponse>) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val response = AApp.getApolloClient().query(GetSocialNetworksQuery(ApoloConfig.HOST)).execute()
                    if (!response.hasErrors()) {
                        target.onSuccess(AvailableSocialNetworksResponse(
                                response.data?.siteMirror?.socialNetworkForLogin,
                                response.data?.siteMirror?.socialNetworkForRegistration))
                    } else {
                        ApolloProcessor.processError(response, target)
                    }
                } catch (e: ApolloException) {
                    target.onFailure(e)
                }
            }
        }
    }
}
