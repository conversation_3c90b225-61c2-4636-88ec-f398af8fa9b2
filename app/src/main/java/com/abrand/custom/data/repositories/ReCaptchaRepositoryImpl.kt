package com.abrand.custom.data.repositories

import android.app.Application
import android.util.Log
import com.abrand.custom.BuildConfig
import com.abrand.custom.data.entity.ResponseException
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.domain.CaptchaType
import com.abrand.custom.domain.interfaces.CaptchaRepository
import com.abrand.custom.domain.interfaces.CaptchaRepository.ReCaptchaResult
import com.abrand.custom.domain.interfaces.ReCaptchaEnterpriseRepository
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.tools.mapEnum
import com.abrand.custom.type.CaptchaForm
import com.google.android.recaptcha.Recaptcha
import com.google.android.recaptcha.RecaptchaAction
import com.google.android.recaptcha.RecaptchaClient
import com.google.android.recaptcha.RecaptchaException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object ReCaptchaRepositoryImpl : CaptchaRepository, ReCaptchaEnterpriseRepository {
    const val TAG = "EnterpriseCaptcha"

    private var recaptchaClient: RecaptchaClient? = null
    private val applicationScope = CoroutineScope(Dispatchers.Default)

    override fun initialize(application: Application) {
        applicationScope.launch {
            try {
                recaptchaClient = Recaptcha.fetchClient(application, BuildConfig.CAPTCHA_KEY)
            } catch (e: RecaptchaException) {
                // TODO Log exceptions into crashlytics
                Log.e(TAG, "initializeRecaptchaClient: $e")
            }
        }
    }

    override suspend fun solve(type: CaptchaType): Result<String> {
        if (recaptchaClient == null) {
            Log.w(TAG, "Recaptcha client is not initialized")
            return Result.failure(Exception("Recaptcha client is not initialized"))
        }

        return recaptchaClient?.execute(type.mapToEnterpriseCaptchaAction())
            ?: Result.failure(Exception("Recaptcha client error"))
    }

    private fun CaptchaType.mapToEnterpriseCaptchaAction(): RecaptchaAction {
        return when (this) {
            CaptchaType.LOGIN -> RecaptchaAction.LOGIN
            CaptchaType.REGISTER -> RecaptchaAction.SIGNUP
            else -> {
                RecaptchaAction.custom(this.rawValue)
            }
        }
    }

    override suspend fun getRecaptcha(
        type: CaptchaType,
        appId: String
    ): ReCaptchaResult {
        return ApolloProcessorKt.getRecaptcha(type.toNetworkCaptchaForm())
        // Stub for tests
//        return ReCaptchaResult.Success(
//            CaptchaRepository.CaptchaStatus(
//                enabled = true,
//                provider = CaptchaRepository.CaptchaProvider.GOOGLE_ENTERPRISE,
//                captchaId = "test_captcha_id"
//            )
//        )
//        return ReCaptchaResult.ResponseError(
//            ServerError("message", "code", null)
//        )
//        return ReCaptchaResult.NetworkError(
//            ResponseException(403, ResponseException.ExceptionType.HTTP)
//        )
    }

    private fun CaptchaType.toNetworkCaptchaForm(): CaptchaForm {
        return this.mapEnum(CaptchaForm.UNKNOWN__) { f, s ->
            f.rawValue == s.rawValue
        }
    }
}
