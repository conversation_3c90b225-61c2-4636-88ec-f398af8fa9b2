package com.abrand.custom.ui.game

import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.databinding.FragmentInGameMenuTournamentBinding
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import java.util.*

class InGameMenuTournamentFragment : Fragment() {
    private var binding: FragmentInGameMenuTournamentBinding? = null
    private val viewModel: InGameMenuTournamentViewModel by viewModels()
    private var tournamentId: Int? = 0;

    companion object {
        const val TAG = "TournamentFrDialog"
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        val localBinding = FragmentInGameMenuTournamentBinding.inflate(inflater, container, false)
        binding = localBinding

        observeViewModel()

        binding?.btnJoin?.isClickable = false
        binding?.rvParticipants?.layoutManager = LinearLayoutManager(context)
        tournamentId = (activity as? GameFragment)?.tournamentId
        tournamentId?.let {
            if (it > 0) {
                viewModel.getTournament(it)
            }
        }

        localBinding.ivTournamentInfo.setOnClickListener {
            (activity as? GameFragment)?.openCurrentTournament()
        }

        return localBinding.root
    }

    override fun onDestroyView() {
        binding = null
        super.onDestroyView()
    }

    private fun observeViewModel() {
        viewModel.tournamentLiveData.observe(
            viewLifecycleOwner
        ) { (status, data, error, failure) ->
            when (status) {
                Status.SUCCESS -> {
                    data?.let { fillTournament(it) }
                }
                Status.ERROR -> {
                    Toast.makeText(context, error?.errorMessage, Toast.LENGTH_SHORT).show()
                }
                Status.FAILURE -> {
                    binding?.btnJoin?.isClickable = true
                    Toast.makeText(context, failure.toString(), Toast.LENGTH_SHORT).show()
//                    (activity as MainActivity).showConnectionIssueMessage(failure)
                }
                else -> {
                    Log.e(TAG, "tournamentLiveData else branch")
                }
            }
        }
    }

    private fun fillTournament(data: TournamentsItem) {
        binding?.tvTitle?.text = data.title
        val prizeFund =
            GeneralTools.formatBalance(Settings.get().userCurrencyCode, data.prizeFund.toDouble())
        binding?.tvPrizeFund?.text = prizeFund
        showDateEnd(data.dateEnd)

        val participants = data.participants
        if (participants != null && participants.isNotEmpty()) {
            binding?.rvParticipants?.adapter = InGameTournamentParticipantsAdapter(participants)
        }

        val tournamentViewerProgress = data.tournamentViewerProgress
        if (tournamentViewerProgress != null) {
            binding?.tvMinbetTitle?.visibility = View.VISIBLE
            binding?.tvMinbet?.visibility = View.VISIBLE
            binding?.tvJoinbetTitle?.visibility = View.VISIBLE
            binding?.tvJoinbet?.visibility = View.VISIBLE
            binding?.tvResultTitle?.visibility = View.VISIBLE
            binding?.tvPlace?.visibility = View.VISIBLE
            binding?.tvResult?.visibility = View.VISIBLE

            binding?.tvMinbet?.text = data.minBetLimit.toString().split(".")[0]

            val totalBets = tournamentViewerProgress.totalBets
            val bets = data.qualificationRounds - totalBets
            if (bets > 0) {
                binding?.tvJoinbet?.text = bets.toString()
                binding?.tvJoinbet?.visibility = View.VISIBLE
                binding?.tvJoinbetTitle?.visibility = View.VISIBLE
            } else {
                binding?.tvJoinbet?.visibility = View.GONE
                binding?.tvJoinbetTitle?.visibility = View.GONE
            }

            binding?.tvPlace?.text = getString(R.string.tournament_place, tournamentViewerProgress.place)
            try {
                val decimalFormat = GeneralTools.getTournamentResultDecimalFormat()
                binding?.tvResult?.text =
                    GeneralTools.formatDoubleNumber(decimalFormat.format(tournamentViewerProgress.score))
            } catch (e: Exception) {
                binding?.tvResult?.text = "0"
            }
            
            binding?.btnJoin?.visibility = View.GONE
        } else {
            binding?.tvMinbetTitle?.visibility = View.GONE
            binding?.tvMinbet?.visibility = View.GONE
            binding?.tvJoinbetTitle?.visibility = View.GONE
            binding?.tvJoinbet?.visibility = View.GONE
            binding?.tvResultTitle?.visibility = View.GONE
            binding?.tvPlace?.visibility = View.GONE
            binding?.tvResult?.visibility = View.GONE

            binding?.btnJoin?.visibility = View.VISIBLE
            binding?.btnJoin?.isClickable = true
            binding?.btnJoin?.setOnClickListener {
                tournamentId?.let {
                    if (it > 0) {
                        viewModel.joinTournament(it)
                        binding?.btnJoin?.isClickable = false
                    }
                }
            }
        }
    }

    private fun showDateEnd(dateEnd: Any) {
        val endTime = DateTools.getServerDate(context, dateEnd).time
        val currentTime = Calendar.getInstance().timeInMillis
        val days = (endTime - currentTime) / (DateTools.MILLIS_IN_DAY)
        if (days < 2) {
            val timer = object : CountDownTimer(endTime - currentTime,
                DateTools.MILLIS_IN_SECOND.toLong()
            ) {
                override fun onTick(millisUntilFinished: Long) {
                    val formattedTime = DateTools.formatTime(millisUntilFinished)
                    binding?.tvDate?.text = formattedTime
                }

                override fun onFinish() {
                }
            }
            timer.start()
        } else {
            binding?.tvDate?.text = this.resources.getQuantityString(R.plurals.days, days.toInt(),
                days.toInt())
        }
    }
}
