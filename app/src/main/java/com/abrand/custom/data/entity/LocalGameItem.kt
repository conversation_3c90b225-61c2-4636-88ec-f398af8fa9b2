package com.abrand.custom.data.entity

import com.abrand.custom.GetGameByIdQuery
import com.abrand.custom.fragment.GameItem
import com.abrand.custom.fragment.GameThumb
import java.io.Serializable

class LocalGameItem : Serializable {
    var id: Int = 0
    var name: String? = ""
    var url: String = ""
    var isHasDemo: Boolean = false
    var mobileIcon: String? = ""
    var isFavorite: Boolean = false
    var tournamentId: Int? = 0

    constructor(gameId: Int, gameName: String) {
        this.id = gameId
        this.name = gameName
    }

    constructor(gameItem: GameItem) {
        this.id = gameItem.gameId
        this.name = gameItem.name
        this.url = gameItem.url
        this.isHasDemo = gameItem.isHasDemo
        this.mobileIcon = gameItem.iconMob
        this.isFavorite = gameItem.userRelatedProperties.isFavorite
    }

    constructor(gameThumb: GameThumb) {
        this.id = gameThumb.id
        this.name = gameThumb.name
        this.url = gameThumb.url
        this.isHasDemo = gameThumb.isHasDemo
        this.mobileIcon = gameThumb.mobileIcon
        this.isFavorite = gameThumb.isFavourite
        this.tournamentId = gameThumb.tournament?.id
    }

    constructor(gameById: GetGameByIdQuery.GameById) {
        this.isFavorite = gameById.isFavourite
        this.tournamentId = gameById.tournament?.id
    }
}
