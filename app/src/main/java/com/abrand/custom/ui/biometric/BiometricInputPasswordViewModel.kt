package com.abrand.custom.ui.biometric

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessor
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException

class BiometricInputPasswordViewModel : ViewModel() {
    val checkPasswordLiveData = MutableLiveData<Resource<Boolean?, ServerError, ApolloException>>()

    fun checkPassword(password: String) {
        ApolloProcessorKt.checkPassword(password, object : GenericTarget<Boolean> {
                override fun onSuccess(success: Boolean?) {
                    checkPasswordLiveData.postValue(Resource.success(success))
                }

                override fun onFailure(e: ApolloException) {
                    checkPasswordLiveData.postValue(Resource.failure(e))
                }

                override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                    checkPasswordLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
                }
            })
    }
}
