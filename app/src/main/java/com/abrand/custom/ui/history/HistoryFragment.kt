package com.abrand.custom.ui.history

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.Navigation
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.HistoryFilter
import com.abrand.custom.data.entity.Transaction
import com.abrand.custom.databinding.FragmentHistoryBinding
import com.abrand.custom.ui.activitymain.MainActivity

class HistoryFragment : Fragment(R.layout.fragment_history) {
    private val TAG = "HistoryFragment"
    private var binding: FragmentHistoryBinding? = null
    private var historyAdapter: HistoryAdapter? = null
    private val viewModel: HistoryViewModel by viewModels()
    private var insetBottom: Int = 0
    var historyFragmentListener : Listener? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val localBinding = FragmentHistoryBinding.inflate(inflater, container, false)
        binding = localBinding
        return localBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showLoader()

        observeViewModel()
        setInsetBottom(insetBottom)

        historyAdapter = HistoryAdapter(requireContext(), historyListener)
        binding?.apply {
            rvHistory.layoutManager = LinearLayoutManager(context)
            rvHistory.adapter = historyAdapter

            btnReplenishAccount.setOnClickListener {
                historyFragmentListener?.openDeposit(url = "")
            }
        }

        viewModel.getTransactions()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    companion object {
        @JvmStatic
        fun newInstance() =
                HistoryFragment().apply {
                    arguments = Bundle().apply { }
                }
    }

    private fun observeViewModel() {
        viewModel.transactionsLiveData.observe(viewLifecycleOwner, Observer { (status, data, error, failure) ->
                when (status) {
                    Status.SUCCESS -> {
                        if (data.isNullOrEmpty()) {
                            setTransactions(null)
                        } else {
                            setTransactions(data)
                        }
                    }
                    Status.FAILURE -> {
                        Log.e(TAG, "Failed to load transactions: $failure")
                        activity?.runOnUiThread {
                            (activity as MainActivity).showConnectionIssueMessage(failure)
                        }
                    }
                    Status.ERROR -> {
                        (activity as MainActivity).showMessage(error?.errorMessage ?: "")
                    }
                    Status.VIEWER_EMPTY -> {
                        navigateUp()
                    }
                }
            })

        viewModel.cancelPayoutLiveData.observe(viewLifecycleOwner) { (status, data, error, failure) ->
            when (status) {
                Status.SUCCESS -> {
                    if (data != null) {
                        viewModel.getTransaction(data)
                    }
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(failure)
                    }
                }
                Status.VIEWER_EMPTY -> {
                    navigateUp()
                }
            }
        }

        viewModel.oneTransactionLiveData.observe(viewLifecycleOwner) { (status, data, error, failure) ->
            when (status) {
                Status.SUCCESS -> {
                    if (data != null) {
                        historyAdapter?.updateTransaction(data)
                    }
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(failure)
                    }
                }
                Status.VIEWER_EMPTY -> {
                    navigateUp()
                }
            }
        }

        viewModel.retryPaymentUrlLiveData.observe(viewLifecycleOwner) { (status, data, error, failure) ->
            when (status) {
                Status.SUCCESS -> {
                    if (data != null) {
                        historyFragmentListener?.openDeposit(data)
                    }
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(failure)
                    }
                }
                Status.VIEWER_EMPTY -> {
                    navigateUp()
                }
            }
        }
    }

    fun setInsetBottom(bottom: Int) {
        HistoryFragment@insetBottom = bottom
        binding?.rvHistory?.updatePadding(bottom = insetBottom)
    }

    private fun setTransactions(transactions: List<Transaction>?) {
        hideLoader()
        if (transactions.isNullOrEmpty()) {
            binding?.llHistoryEmpty?.visibility = View.VISIBLE
        } else {
            binding?.llHistoryEmpty?.visibility = View.GONE
        }

        historyAdapter?.apply {
            setList(transactions)
        }
    }

    private fun showLoader() {
        binding?.apply {
            ivLoader.visibility = View.VISIBLE
            val animation = AnimationUtils.loadAnimation(context, R.anim.rotation_loader)
            ivLoader.startAnimation(animation)
        }
    }

    fun hideLoader() {
        binding?.apply {
            ivLoader.clearAnimation()
            ivLoader.visibility = View.GONE
        }
    }

    val historyListener = object : HistoryAdapter.HistoryListener {

        override fun onOpenSupportChat(messageText: String) {
            (activity as MainActivity).openSupportChat(messageText)
        }

        override fun onCancelPayout(transactionId: Int) {
            viewModel.cancelPayout(transactionId)
        }

        override fun onRetryPayment(transactionId: Int) {
            viewModel.onRetryPaymentUrl(transactionId)
        }

        override fun onOpenFilter() {
            historyFragmentListener?.onOpenFilter()
        }

    }

    fun getTransactions(operations: List<HistoryFilter.Operation>? = null,
        statuses: List<HistoryFilter.Status>? = null, dateFrom: String? = null, dateTo: String? = null) {
        historyAdapter?.setList(null)
        showLoader()
        viewModel.getTransactions(operations, statuses, dateFrom, dateTo)
    }

    fun setAdapterCounter(filtersSelected: Int) {
        historyAdapter?.setCounter(filtersSelected)
    }

    fun updateTransactions() {
        viewModel.getTransactions()
    }

    private fun navigateUp() {
        view?.let {
            activity?.runOnUiThread { Navigation.findNavController(it).navigateUp() }
        }
    }

    interface Listener {
        fun openDeposit(url: String)
        fun onOpenFilter()
    }
}
