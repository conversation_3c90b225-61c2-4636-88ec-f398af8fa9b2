package com.abrand.custom.data.entity.appconfig

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName

data class LocalConfig(
    @SerializedName("token") val token: String,
    @SerializedName("hash") val hash: String? = null,
    @SerializedName("domains") val domains: List<String>,
    @SerializedName("updater_domains") val updaterDomains: List<String>?,
    @SerializedName("download_code") val downloadCode: String,
    @SerializedName("uuid") val uuid: String?,
    @SerializedName("app_install_uuid") val appInstallUuid: String?,
    @SerializedName("ref") val ref: String?,
    @SerializedName("salt") val salt: String?,
    @SerializedName("affdata") val affData: Map<String, String>?,
    @SerializedName("customer_token") val customerToken: String? = null,
)

fun LocalConfig.affdataToJson(): String {
    return Gson().toJson(this.affData)
}
