package com.abrand.custom.ui.search

import android.graphics.drawable.BitmapDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.navigation.Navigation
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
import com.abrand.custom.R
import com.abrand.custom.data.entity.*
import com.abrand.custom.databinding.FragmentGameSearchBinding
import com.abrand.custom.tools.EditTextDebounce
import com.abrand.custom.tools.GameSpacingItemDecoration
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.afterTextChanged
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.home.GamesAdapter
import com.abrand.custom.ui.interfaces.GameClickListener
import com.apollographql.apollo3.exception.ApolloException

class GameSearchFragment : Fragment(), GameClickListener {
    private lateinit var mainActivity: MainActivity
    private val viewModel: GameSearchViewModel by viewModels()
    private lateinit var gamesAdapter: GamesAdapter
    private val MIN_SEARCH_LENGHT = 2
    private val COLS_COUNT = 3
    private val SEARCH_DELAY_MILLIS = 1000
    private var currentSearch = ""
    private var binding: FragmentGameSearchBinding? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        mainActivity = activity as MainActivity
        binding = FragmentGameSearchBinding.inflate(inflater, container, false)

        return binding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initEditTextSearch()
        binding?.ibCloseSearch?.setOnClickListener { navigateUp() }

        viewModel.gamesByNameLiveData.observe(viewLifecycleOwner, gamesByNameObserver)
        viewModel.popularGamesLiveData.observe(viewLifecycleOwner, popularGamesObserver)
        viewModel.apolloExceptionLiveData.observe(viewLifecycleOwner, apolloExceptionObserver)

        gamesAdapter = GamesAdapter(
            GeneralTools.getGameIconSize(activity, Screen.GAME_SEARCH),
            this, null, loadMoreClickListener,
            Screen.GAME_SEARCH, context,
            LOAD_STEP
        )

        val layoutManager = GridLayoutManager(context, COLS_COUNT)
        layoutManager.spanSizeLookup = object : SpanSizeLookup() {
            override fun getSpanSize(position: Int): Int {
                return when (gamesAdapter.getItemViewType(position)) {
                    GameListItem.ItemType.GAME_LOAD.id -> COLS_COUNT
                    else -> 1
                }
            }
        }
        binding?.apply {
            rvGames.layoutManager = layoutManager

            rvGames.adapter = gamesAdapter

            val gameIconStartEndMargin = requireContext().resources.getDimensionPixelSize(R.dimen.game_icon_start_end_margin)
            val gameIconLeftRightSpacing = requireContext().resources.getDimensionPixelSize(R.dimen.game_icon_left_right_spacing)
            val gameIconBottomSpacing = requireContext().resources.getDimensionPixelSize(R.dimen.game_icon_bottom_spacing)
            rvGames.addItemDecoration(GameSpacingItemDecoration(COLS_COUNT,
                gameIconStartEndMargin, gameIconLeftRightSpacing, gameIconBottomSpacing, Screen.GAME_SEARCH))

            val layoutParams: ViewGroup.LayoutParams = ivSearch.layoutParams
            if (layoutParams is MarginLayoutParams) {
                layoutParams.topMargin = GeneralTools.getStatusBarHeight(context) +
                        resources.getDimensionPixelSize(R.dimen.size_16)
            }
        }

        gamesAdapter.setGameLoadingState(true)
        if (currentSearch.isEmpty()) {
            viewModel.loadPopularGames()
        }

        setupInsets()
    }

    override fun onStart() {
        super.onStart()
        mainActivity.hideToolbar()
    }

    private fun setupInsets() {
        binding?.apply {
            ViewCompat.setOnApplyWindowInsetsListener(rvGames) { view: View, insets: WindowInsetsCompat ->
                view.updatePadding(bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }

    override fun onDestroyView() {
        mainActivity.showToolbar()
        super.onDestroyView()
        binding = null
    }

    override fun onGameClick(game: LocalGameItem?, view: View?) {
        GeneralTools.hideKeyboard(context, binding?.etSearch)
        val dataHolder = SessionDataHolder.getInstance()
        dataHolder.currentGameItem = game
        val gameIcon = view?.findViewById<ImageView>(R.id.iv_item_game_image)
        if (gameIcon?.drawable is BitmapDrawable) {
            dataHolder.currentGameImage = (gameIcon.drawable as BitmapDrawable).bitmap
        }
        (activity as MainActivity).showPregame(game, view)
    }

    private val gamesByNameObserver = Observer<LoadedGames> {
        gamesAdapter.setCurrentTotalGames(it.total)

        if (it.localGameItems.isEmpty()) {
            binding?.tvNoGamesFound?.visibility = View.VISIBLE
            showPopularGames()
        } else if (it.offset == 0) {
            val newList = mutableListOf<GameListItem>()
            for (game in it.localGameItems) {
                newList.add(GameListItem(GameListItem.ItemType.GAME, game))
            }
            gamesAdapter.setList(newList)
            binding?.rvGames?.smoothScrollToPosition(0)
        } else {
            val newList = mutableListOf<GameListItem>()
            for (game in it.localGameItems) {
                newList.add(GameListItem(GameListItem.ItemType.GAME, game))
            }
            gamesAdapter.appendList(newList)
        }

        gamesAdapter.setGameLoadingState(false)
    }

    private val popularGamesObserver = Observer<LoadedGames> {
        showPopularGames(it.localGameItems)
    }

    private val apolloExceptionObserver = Observer<ApolloException?> {
        GeneralTools.hideKeyboard(activity)
        gamesAdapter.setGameLoadingState(false)
        (activity as MainActivity).showConnectionIssueMessage(it)
    }

    private fun initEditTextSearch() {
        binding?.apply {
            etSearch.post { initSearchInput() }
            etSearch.afterTextChanged {
                tvNoGamesFound.visibility = View.GONE
                if ((it.length < MIN_SEARCH_LENGHT && isPopularGamesShowing())) {
                    return@afterTextChanged
                }
                gamesAdapter.setList(ArrayList<GameListItem>(0))
                tvPopularGame.visibility = View.GONE
                gamesAdapter.setGameLoadingState(true)
            }
            EditTextDebounce.create(etSearch, SEARCH_DELAY_MILLIS).watch { result ->
                if (result.length >= MIN_SEARCH_LENGHT) {
                    viewModel.loadGamesByName(result, 0)
                } else {
                    showPopularGames()
                }
                currentSearch = result
            }
        }
    }

    private fun initSearchInput() {
        binding?.apply {
            // not use requestFocusFromTouch() because NoConnection's (screen) update button in focus after screen open
            etSearch.requestFocus()
            GeneralTools.showKeyboard(context, etSearch)
        }
    }

    private fun showPopularGames() {
        val loadedGames = viewModel.popularGamesLiveData.value
        if (loadedGames != null) {
            showPopularGames(loadedGames.localGameItems)
        } else {
            viewModel.loadPopularGames()
        }
    }

    private fun showPopularGames(localGameItems: List<LocalGameItem>) {
        gamesAdapter.setCurrentTotalGames(viewModel.POPULAR_GAMES_LIMIT)
        binding?.tvPopularGame?.visibility = View.VISIBLE
        val newList = mutableListOf<GameListItem>()
        for (game in localGameItems) {
            newList.add(GameListItem(GameListItem.ItemType.GAME, game))
        }
        gamesAdapter.setList(newList)
        gamesAdapter.setGameLoadingState(false)
        binding?.rvGames?.smoothScrollToPosition(0)
    }

    private fun isPopularGamesShowing(): Boolean {
        return binding?.tvPopularGame?.visibility == View.VISIBLE
    }

    private fun navigateUp() {
        view?.let { fragmentView ->
            GeneralTools.hideKeyboard(context, binding?.etSearch)
            Navigation.findNavController(fragmentView).navigateUp()
        }
    }

    private val loadMoreClickListener = GamesAdapter.LoadMoreClickListener {
        viewModel.loadGamesByName(currentSearch, gamesAdapter.gameCount)
    }

    companion object {
        private const val LOAD_STEP = 21
    }
}
