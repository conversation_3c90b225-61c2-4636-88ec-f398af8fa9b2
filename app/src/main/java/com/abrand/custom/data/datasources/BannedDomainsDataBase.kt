package com.abrand.custom.data.datasources

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.abrand.custom.AApp
import com.abrand.custom.data.entity.BannedDomainEntity

@Database(entities = [BannedDomainEntity::class], version = 1)
abstract class BannedDomainsDataBase : RoomDatabase() {
    abstract fun bannedDomainDao(): BannedDomainDao
}

object BannedDomainDatabaseProvider {
    private const val BANNED_DOMAINS_DB_NAME = "BannedDomainsDataBase.db"
    private val bannedDomainsDataBase = Room.databaseBuilder(
        AApp.getContext(),
        BannedDomainsDataBase::class.java,
        BANNED_DOMAINS_DB_NAME
    ).build()

    fun getDao() = bannedDomainsDataBase.bannedDomainDao()
}
