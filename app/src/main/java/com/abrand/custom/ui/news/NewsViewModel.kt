package com.abrand.custom.ui.news

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.data.entity.LocalNews
import com.abrand.custom.data.entity.NewsResponse
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GetNewsTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException

class NewsViewModel : ViewModel() {
    val newsLiveData = MutableLiveData<NewsResponse>()
    val serverErrorLiveData = MutableLiveData<ServerError>()
    val apolloExceptionLiveData = SingleLiveEvent<ApolloException?>()

    fun getNews(offset: Int, loadAmount: Int) {
        ApolloProcessorKt.getNews(offset, loadAmount, object : GetNewsTarget {

            override fun onSuccess(newsList: List<LocalNews>, total: Int, offset: Int) {
                newsLiveData.postValue(NewsResponse(newsList, total, offset))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                serverErrorLiveData.postValue(ServerError(errorMessage, errorCode, fieldsErrors))
            }

            override fun onFailure(e: ApolloException?) {
                apolloExceptionLiveData.postValue(e)
            }

        })
    }
}
