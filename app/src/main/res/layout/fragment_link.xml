<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <WebView
        android:id="@+id/ww"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#000" />

    <TextView
        android:id="@+id/tv_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:textColor="@color/white"
        android:textSize="20sp" />

    <FrameLayout
        android:id="@+id/l_progress"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#000">

        <ImageView
            android:id="@+id/iv_loader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:src="@drawable/ic_preload"
            tools:ignore="ContentDescription" />

    </FrameLayout>

</LinearLayout>
