package com.abrand.custom.ui.halloffame

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.HallOfFamePlace
import com.abrand.custom.data.entity.ResponseException
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GenericTargetV2
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import java.text.SimpleDateFormat
import java.util.*

class HallOfFameViewModel : ViewModel() {
    private val _hallOfFameLiveData = MutableLiveData<Resource<MutableList<HallOfFamePlace>, ServerError, ResponseException>>()
    val hallOfFameLiveData: LiveData<Resource<MutableList<HallOfFamePlace>, ServerError, ResponseException>> = _hallOfFameLiveData

    fun getHallOfFame(year: Int, month: Int) {
        ApolloProcessorKt.getHallOfFame(year, month, HALL_OF_FAME_LIMIT,
            object : GenericTargetV2<MutableList<HallOfFamePlace>> {
                override fun onSuccess(t: MutableList<HallOfFamePlace>?) {
                    _hallOfFameLiveData.postValue(Resource.success(t))
                }

                override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                    _hallOfFameLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
                }

                override fun onFailure(e: ResponseException) {
                    _hallOfFameLiveData.postValue(Resource.failure(e))
                }
            })
    }

    fun getMonthList(): MutableList<String> {
        val result = mutableListOf<String>()
        val cal: Calendar = Calendar.getInstance()
        val monthDate = SimpleDateFormat("LLLL yyyy", Locale("ru"))
        repeat(12) {
            var monthName: String = monthDate.format(cal.time)
            monthName = monthName.uppercase().substring(0, 1) + monthName.substring(1)
            result.add(monthName)
            cal.add(Calendar.MONTH, -1)
        }
        return result
    }

    companion object {
        const val HALL_OF_FAME_LIMIT = 30
    }
}
