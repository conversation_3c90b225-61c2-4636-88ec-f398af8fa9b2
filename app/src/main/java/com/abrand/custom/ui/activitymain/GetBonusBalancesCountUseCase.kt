package com.abrand.custom.ui.activitymain

import com.abrand.custom.data.repositories.BonusBalancesRepository
import com.abrand.custom.ui.bonusbalances.IBonusBalancesRepo
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class GetBonusBalancesCountUseCase(
    private val bonusBalanceRepo: IBonusBalancesRepo = BonusBalancesRepository,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) {
    suspend operator fun invoke(): Int = withContext(dispatcher) {
            val bonuses = bonusBalanceRepo.obtainBonusBalancesCount()

            return@withContext bonuses
    }
}
