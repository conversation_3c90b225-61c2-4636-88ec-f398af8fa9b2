package com.abrand.custom.ui.tournaments

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.data.entity.TournamentsResponse
import com.abrand.custom.interfaces.LoadTournament
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.abrand.custom.type.*
import com.apollographql.apollo3.exception.ApolloException


class TournamentsViewModel : ViewModel() {

    val tournamentsLiveData = MutableLiveData<TournamentsResponse>()
    val tournamentsLiveDataError = SingleLiveEvent<String?>()
    val tournamentsLoadApolloExceptionLiveData = SingleLiveEvent<ApolloException?>()

    fun loadByStatus(status: List<TournamentStatus>, offset: Int, loadAmount: Int) {
        ApolloProcessorKt.loadTournamentsByStatus(status, offset, loadAmount, object : LoadTournament {
            override fun onSuccess(tournamentsList: List<TournamentsItem>, total: Int, offset: Int) {
                tournamentsLiveData.postValue(TournamentsResponse(tournamentsList, total, offset))
            }

            override fun onFailure(e: ApolloException?) {
                tournamentsLoadApolloExceptionLiveData.postValue(e)
            }

            override fun onError(errorMessage: String?, errorCode: String?, fieldsErrors: FieldsErrorsHolder?
            ) {
                tournamentsLiveDataError.postValue(errorMessage ?: "Load error")
            }

        }, viewModelScope)
    }

}
