package com.abrand.custom.ui.views;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.interpolator.view.animation.FastOutSlowInInterpolator;

import com.abrand.custom.R;

import java.util.List;

public class Switcher extends FrameLayout {

    private LinearLayout variantsView = null;
    private View selector = null;
    private int variantsCount = 0;
    private List<String> variantsTitles = null;
    private int selectedPos = -1;
    private ItemChangeListener itemChangeListener;

    public Switcher(Context context) {
        super(context);
        init(context, null, -1);
    }

    public Switcher(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, -1);
    }

    public Switcher(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    public void setVariants(List<String> variants) {
        variantsView.removeAllViews();
        variantsTitles = variants;
        variantsCount = variants.size();
        initChildren();
        initSelector();
        post(this::setLayoutParamsSelector);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        initVariantsView();
        initChildren();
    }

    private void initVariantsView() {
        variantsView = new LinearLayout(getContext());
        LayoutParams params = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT, Gravity.CENTER);
        variantsView.setLayoutParams(params);
        variantsView.setOrientation(LinearLayout.HORIZONTAL);
        addView(variantsView);
    }

    private void initChildren() {
        for ( int i = 0; i < variantsCount; i++ ) {
            String text = "";
            if ( variantsTitles != null && variantsTitles.size() > i && variantsTitles.get(i) != null ) {
                text = variantsTitles.get(i);
            }
            TextView child = buildChild(text);
            int finalI = i;
            child.setOnClickListener((view) -> {
                        selectChild(finalI);
                        if (itemChangeListener != null) {
                            itemChangeListener.onItemSelected(finalI);
                        }
                    }
            );
            variantsView.addView(child);
        }
    }

    private TextView buildChild(String text) {
        TextView child = new TextView(getContext());
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT, 1);
        child.setLayoutParams(params);
        child.setText(text);
        child.setGravity(Gravity.CENTER);
        child.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        child.setTextColor(Color.BLACK);
        return child;
    }

    private void initSelector() {
        if ( selector != null ) {
            removeView(selector);
            selector = null;
        }

        selector = new View(getContext());
        addView(selector, 0);
    }

    private void setLayoutParamsSelector() {
        int width = 0;
        if ( variantsCount > 0 ) width = getWidth() / variantsCount;
        LayoutParams params = new LayoutParams(width, LayoutParams.MATCH_PARENT);
        selector.setLayoutParams(params);
    }

    public void selectChild(int position) {
        if ( position != -1 && selector.getBackground() == null ) {
            selector.setBackgroundResource(R.drawable.btn_bg_enable);
        }

        TextView selected = (TextView) variantsView.getChildAt(selectedPos);
        if ( selected != null ) {
            selected.setTextColor(Color.BLACK);
        }

        TextView child = (TextView) variantsView.getChildAt(position);
        if ( child != null ) {
            int animationDuration = (selectedPos != -1) ? 303 : 0;
            child.setTextColor(Color.WHITE);
            selector.animate()
                    .translationX(child.getX())
                    .x(child.getX())
                    .setInterpolator(new FastOutSlowInInterpolator())
                    .setDuration(animationDuration);
            selectedPos = position;
        }
    }

    public int getSelectedPos() {
        return selectedPos;
    }

    public void setItemChangeListener(ItemChangeListener itemChangeListener) {
        this.itemChangeListener = itemChangeListener;
    }

    public interface ItemChangeListener {
        void onItemSelected(int index);
    }

}
