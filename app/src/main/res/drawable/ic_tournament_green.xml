<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <path
      android:pathData="M13,16.9C14.9591,16.5023 16.5023,14.9591 16.9,13H17C18.6569,13 20,11.6569 20,10V8C20,7.4477 19.5523,7 19,7H17V5C17,4.4477 16.5523,4 16,4H8C7.4477,4 7,4.4477 7,5V7H5C4.4477,7 4,7.4477 4,8V10C4,11.6569 5.3432,13 7,13H7.1C7.4977,14.9591 9.0409,16.5023 11,16.9V19H9C8.4477,19 8,19.4477 8,20C8,20.5523 8.4477,21 9,21H15C15.5523,21 16,20.5523 16,20C16,19.4477 15.5523,19 15,19H13V16.9ZM9,12V6H15V12C15,13.6569 13.6569,15 12,15C10.3431,15 9,13.6569 9,12ZM7,8H5V10C5,11.1046 5.8954,12 7,12V8ZM19,10C19,11.1046 18.1046,12 17,12V8H19V10Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="21"
          android:startX="20"
          android:endY="4"
          android:endX="20"
          android:type="linear">
        <item android:offset="0" android:color="#FF008E00"/>
        <item android:offset="1" android:color="#FF6ED200"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
