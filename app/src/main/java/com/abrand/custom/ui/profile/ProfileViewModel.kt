package com.abrand.custom.ui.profile

import android.content.Context
import android.os.Build
import android.telephony.PhoneNumberUtils
import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.R
import com.abrand.custom.data.Constants
import com.abrand.custom.data.entity.Country
import com.abrand.custom.data.entity.Profile
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.network.ProfileRepository
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.type.Gender
import com.apollographql.apollo3.exception.ApolloException

class ProfileViewModel : ViewModel() {

    private var _countryIso: String = "RU"

    private val repository = ProfileRepository()

    val profileLiveData: LiveData<Profile> = repository.profileLiveData
    val profileChangedLiveData: LiveData<Boolean> = repository.profileChangedLiveData
    val passwordChangedLiveData: LiveData<Boolean> = repository.passwordChangedLiveData
    val serverErrorLiveData: LiveData<ServerError> = repository.serverErrorLiveData
    val apolloExceptionLiveData: LiveData<ApolloException> = repository.apolloExceptionLiveData
    val emailConfirmationRequestedLiveData: LiveData<Boolean> =
        repository.emailConfirmationRequestedLiveData
    val viewerEmptyLiveData: LiveData<Boolean> = repository.viewerEmptyLiveData

    val currentGender: Gender
        get() = profileLiveData.value?.gender ?: Gender.UNKNOWN__

    val minPasswordLength: Int
        get() = Constants.MIN_PASSWORD_LENGTH

    fun updateCountryIso(newCountryIso: String?) {
        newCountryIso?.let {
            _countryIso = it
        }
    }

    fun resendEmailConfirmation() {
        repository.requestEmailConfirmation()
    }

    fun fetchProfile() {
        repository.fetchProfile()
    }

    fun isProfileChanged(name: String, phone: String, gender: Gender, birthday: String): Boolean {
        val profile = profileLiveData.value ?: return false

        return when {
            name != profile.name -> true
            TextUtils.isEmpty(profile.phone) && phone.isNotEmpty() -> true
            (profile.gender == null && gender != Gender.UNKNOWN__) || (profile.gender != null && profile.gender != gender) -> true
            TextUtils.isEmpty(profile.birthday) && birthday.isNotEmpty() -> true
            else -> false
        }
    }

    fun saveProfile(
        userNameInput: String,
        phoneInput: String,
        genderInput: Gender,
        birthdayInput: String,
        gCaptchaResponse: String?
    ) {
        val profile = profileLiveData.value ?: return

        val userName = if (profile.name == userNameInput) null else userNameInput
        val phone = if (!TextUtils.isEmpty(profile.phone)) null else phoneInput
        val gender =
            if (profile.gender == genderInput || genderInput == Gender.UNKNOWN__) null else genderInput
        val birthday = if (!TextUtils.isEmpty(profile.birthday)) null else birthdayInput

        repository.saveProfile(userName, phone, gender, birthday, gCaptchaResponse)
    }

    fun updateLocalProfile(userName: String, phone: String, gender: Gender, birthday: String) {
        profileLiveData.value?.apply {
            name = userName
            this.phone = phone
            this.gender = gender
            this.birthday = birthday
        }
    }

    fun changePassword(oldPassword: String, newPassword: String) {
        repository.changePassword(oldPassword, newPassword)
    }

    fun isPasswordChanged(
        oldPassword: String,
        newPassword: String,
        passwordConfirmation: String
    ): Boolean {
        return oldPassword.length >= Constants.MIN_PASSWORD_LENGTH
                && newPassword.length >= Constants.MIN_PASSWORD_LENGTH
                && oldPassword != newPassword
                && newPassword == passwordConfirmation
    }

    fun getFormattedPhone(phone: String): String {
        return PhoneNumberUtils.formatNumber(phone, _countryIso) ?: phone
    }

    fun getUserCountry(context: Context, phoneNumber: String): Country? {
        val countries = GeneralTools.getCountries(context)
        return countries.find { phoneNumber.startsWith(it.phoneCode) }
    }

    fun isValidUserName(userName: String): Boolean {
        return userName.length >= MIN_USER_NAME_LENGTH
    }

    fun getNotValidUserNameMessage(context: Context, userName: String): String {
        return when {
            TextUtils.isEmpty(userName) -> context.getString(R.string.required_filed_message)
            userName.length < MIN_USER_NAME_LENGTH -> context.getString(
                R.string.min_symbols_validation_message,
                MIN_USER_NAME_LENGTH
            )

            else -> ""
        }
    }

    companion object {
        private const val MIN_USER_NAME_LENGTH = 4
    }
}
