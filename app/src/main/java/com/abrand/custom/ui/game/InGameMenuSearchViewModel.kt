package com.abrand.custom.ui.game

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.data.entity.LoadedGames
import com.abrand.custom.network.GameSearchRepository
import com.apollographql.apollo3.exception.ApolloException

class InGameMenuSearchViewModel : ViewModel() {
    private val repository = GameSearchRepository()
    val gamesByNameLiveData: LiveData<LoadedGames>
    val popularGamesLiveData: LiveData<LoadedGames>
    val apolloExceptionLiveData: LiveData<ApolloException?>

    init {
        gamesByNameLiveData = repository.gamesByNameLiveData
        popularGamesLiveData = repository.popularGamesLiveData
        apolloExceptionLiveData = repository.apolloExceptionLiveData
    }

    fun loadGamesByName(name: String, offset: Int) {
        repository.loadGamesByName(name, GAMES_BY_NAME_LIMIT, offset, viewModelScope)
    }

    fun loadPopularGames() {
        repository.loadPopularGames(POPULAR_GAMES_LIMIT, viewModelScope)
    }

    companion object {
        const val GAMES_BY_NAME_LIMIT = 20
        const val POPULAR_GAMES_LIMIT = 6
    }
}
