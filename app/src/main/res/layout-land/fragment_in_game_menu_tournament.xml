<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="128dp"
        android:layout_marginTop="16dp"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_tournament_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:src="@drawable/ic_tournament_info"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintStart_toEndOf="@+id/tv_title"
        app:layout_constraintTop_toTopOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_prize_fund"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="#6ED200"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_prize_fund_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.4"
        android:text="@string/tournaments_fund_ingame_menu"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_prize_fund" />

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_prize_fund_title" />

    <TextView
        android:id="@+id/tv_date_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.4"
        android:text="@string/tournaments_toend_ingame_menu"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@id/tv_date" />

    <ImageView
        android:id="@+id/iv_loader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dp"
        android:src="@drawable/ic_preload"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_participant_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginTop="8dp"
        android:alpha="0.4"
        android:text="@string/tournament_name_ingame_menu"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_date_title" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:alpha="0.4"
        android:text="@string/tournament_result_ingame_menu"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        app:layout_constraintEnd_toEndOf="@+id/rv_participants"
        app:layout_constraintTop_toBottomOf="@+id/tv_date_title" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_participants"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/container_results"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_participant_name" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container_results"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="48dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rv_participants"
        app:layout_constraintTop_toTopOf="@+id/tv_prize_fund">

        <TextView
            android:id="@+id/tv_result_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="@string/tournament_my_result"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_place"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="25 место" />

        <TextView
            android:id="@+id/tv_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_place"
            tools:text="1231231" />

        <TextView
            android:id="@+id/tv_minbet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:alpha="0.8"
            android:textColor="@color/white"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_minbet_title" />

        <TextView
            android:id="@+id/tv_minbet_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:alpha="0.4"
            android:text="@string/tournament_ingame_menu_min_bet"
            android:textColor="#FFFFFF"
            android:textSize="10sp"
            app:layout_constraintStart_toStartOf="@+id/tv_result_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_result_title" />

        <TextView
            android:id="@+id/tv_joinbet"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:alpha="0.8"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_joinbet_title" />

        <TextView
            android:id="@+id/tv_joinbet_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:alpha="0.4"
            android:text="@string/tournament_join_bet"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@+id/tv_result_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_minbet_title" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <Button
        android:id="@+id/btn_join"
        style="@style/ButtonBlueStyle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="48dp"
        android:text="@string/participate"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rv_participants"
        app:layout_constraintTop_toTopOf="@+id/tv_prize_fund" />

</androidx.constraintlayout.widget.ConstraintLayout>

