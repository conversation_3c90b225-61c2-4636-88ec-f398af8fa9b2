package com.abrand.custom.presenter

import androidx.recyclerview.widget.DiffUtil
import com.abrand.custom.data.entity.PromotionItem

class PromotionItemDiffCallback(private val oldList: List<PromotionItem>,
                                private val newList: List<PromotionItem>) : DiffUtil.Callback() {

    override fun getOldListSize() = oldList.size

    override fun getNewListSize() = newList.size

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition].id == newList[newItemPosition].id
    }

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Bo<PERSON>an {
        return oldList[oldItemPosition] == newList[newItemPosition]
    }
}