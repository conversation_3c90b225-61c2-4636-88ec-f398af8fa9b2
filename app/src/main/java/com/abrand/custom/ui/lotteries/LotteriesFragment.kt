package com.abrand.custom.ui.lotteries

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.User
import com.abrand.custom.data.entity.lottery.LocalLottery
import com.abrand.custom.databinding.FragmentLotteryListBinding
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment

class LotteriesFragment : Fragment() {
    private var binding: FragmentLotteryListBinding? = null
    private val viewModel: LotteriesViewModel by viewModels()
    private var adapter: LotteriesAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentLotteryListBinding.inflate(inflater, container, false)

        binding?.rvLottery?.layoutManager = LinearLayoutManager(context)
        adapter = LotteriesAdapter(getLotteryWidth(), getLotteryActiveHeight(), getLotteryCompletedHeight())
        setLotteryAdapterListener()
        binding?.rvLottery?.adapter = adapter

        setupInsets()
        observeViewModel()

        return binding?.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun setLotteryAdapterListener() {
        adapter?.listener = object : LotteriesAdapter.Listener {
            override fun onLotteryClicked(lottery: LocalLottery) {
                showLottery(lottery)
            }

            override fun onLoadMoreClicked() {
                adapter?.setLotteriesLoadingState(true)
                adapter?.let {
                    viewModel.getLotteries(it.getLotteriesCount(), LotteriesViewModel.LOAD_STEP)
                }
            }

            override fun onRefresh() {
                refreshLotteries()
            }
        }

        adapter?.footerListener = object : FooterListener {
            override fun onClickPaymentSystems() {
                if (User.State.PLAYER == Settings.get().userState) {
                    (activity as? MainActivity)?.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
                } else {
                    (activity as? MainActivity)?.openEnterScreen(false)
                }
            }
        }
    }

    private fun setupInsets() {
        binding?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it.rvLottery) { view, insets ->
                view.updatePadding(bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }

    private fun observeViewModel() {
        viewModel.lotteriesLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    it.data?.apply {
                        adapter?.currentTotalLotteries = total
                        adapter?.setList(activeLotteryItems, completedLotteryItems)
                    }
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(it.failure)
                    }
                }
                else -> {}
            }
            adapter?.setLotteriesLoadingState(false)
        }
    }

    private fun showLottery(lottery: LocalLottery) {
        if (activity != null) {
            (activity as? MainActivity)?.showLotteryInternal(lottery)
        }
    }

    private fun refreshLotteries() {
        adapter?.setLotteriesLoadingState(true)
        viewModel.getLotteries(0, LotteriesViewModel.INIT_AMOUNT)
    }

    private fun getLotteryWidth(): Int {
        val screenWidth = GeneralTools.getScreenWidth(context)
        return screenWidth - 2 * (context?.resources?.getDimensionPixelSize(R.dimen.lottery_card_side_margin)
            ?: 0)
    }

    private fun getLotteryActiveHeight() : Int {
        return context?.resources?.getDimensionPixelSize(R.dimen.lottery_active_card_height) ?: 0
    }

    private fun getLotteryCompletedHeight() : Int {
        return context?.resources?.getDimensionPixelSize(R.dimen.lottery_completed_card_height) ?: 0
    }

}
