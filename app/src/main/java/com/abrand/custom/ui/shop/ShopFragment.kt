package com.abrand.custom.ui.shop

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.User
import com.abrand.custom.databinding.FragmentShopBinding
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment

class ShopFragment : Fragment() {
    private var binding: FragmentShopBinding? = null
    private val viewModel: ShopViewModel by viewModels()
    private var adapter: ShopAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentShopBinding.inflate(inflater, container, false)

        binding?.rvShop?.layoutManager = LinearLayoutManager(context)
        adapter = ShopAdapter()
        binding?.rvShop?.adapter = adapter
        adapter?.loyaltyPoints = (activity as MainActivity).getLoyaltyPoints()
        adapter?.balance = (activity as MainActivity).balance
        adapter?.buttonsClickListener = object : ShopAdapter.ButtonsClickListener {
            override fun onBuyClicked(productId: Int) {
                viewModel.buyProduct(productId)
            }

            override fun onActionButtonClicked() {
                if ((activity as MainActivity).balance > 0) {
                    (activity as MainActivity).openHomeScreen()
                } else {
                    (activity as MainActivity).openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
                }
            }
        }
        adapter?.footerListener = footerListener

        observeViewModel()

        return binding?.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    fun observeViewModel() {
        viewModel.bonusProductsForPointsLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    it.data?.let { products ->
                        val shopItems = viewModel.listLocalBonusProductForPointsToShopItems(products)
                        adapter?.setList(shopItems)
                    }
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(it.failure)
                    }
                }
                else -> {}
            }
        }

        viewModel.buyProductLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    it.data?.let { shopProduct -> adapter?.updateShopProduct(shopProduct) }
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(it.failure)
                    }
                }
                else -> {}
            }
        }
    }

    private val footerListener = object : FooterListener {
        override fun onClickPaymentSystems() {
            if (User.State.PLAYER == Settings.get().userState) {
                (activity as? MainActivity)?.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
            } else {
                (activity as? MainActivity)?.openEnterScreen(false)
            }
        }
    }

    fun setLoyaltyPoints(loyaltyPoints: Int) {
        adapter?.loyaltyPoints = loyaltyPoints
    }

    fun setBalance(balance: Double) {
        adapter?.balance = balance
    }
}
