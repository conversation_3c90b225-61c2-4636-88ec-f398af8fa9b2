package com.abrand.custom.ui.loyaltyprogram

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.GetLoyaltyStatusesQuery
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessor
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException

class LoyaltyProgramViewModel : ViewModel() {
    val loyaltyStatusesLiveData = MutableLiveData<List<GetLoyaltyStatusesQuery.LoyaltyStatus>?>()
    val serverErrorLiveData = MutableLiveData<ServerError>()
    val apolloExceptionLiveData = SingleLiveEvent<ApolloException?>()

    fun getLoyaltyStatuses() {
        ApolloProcessorKt.getLoyaltyStatuses(object : GenericTarget<List<GetLoyaltyStatusesQuery.LoyaltyStatus>?> {
            override fun onSuccess(loyaltyStatuses: List<GetLoyaltyStatusesQuery.LoyaltyStatus>?) {
                loyaltyStatusesLiveData.postValue(loyaltyStatuses)
            }

            override fun onFailure(e: ApolloException?) {
                apolloExceptionLiveData.postValue(e)
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                serverErrorLiveData.postValue(ServerError(errorMessage, errorCode, fieldsErrors))
            }
        })
    }
}
