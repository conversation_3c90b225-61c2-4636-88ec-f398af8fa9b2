package com.abrand.custom.ui.lotteries

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.CountDownTimer
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.TextView
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.lottery.LocalLottery
import com.abrand.custom.data.entity.lottery.LotteryItem
import com.abrand.custom.data.repositories.YearRepository
import com.abrand.custom.databinding.ItemLotteryActiveBinding
import com.abrand.custom.databinding.ItemLotteryCompletedBinding
import com.abrand.custom.databinding.ItemNewsLoadBinding
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.presenter.LotteryItemDiffCallback
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.squareup.picasso.Picasso
import com.squareup.picasso.Target
import java.util.*
import java.util.concurrent.TimeUnit

class LotteriesAdapter() : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    var listener: Listener? = null
    var footerListener: FooterListener? = null
    var items = mutableListOf<LotteryItem>()
    var loadLotteryVH: LoadLotteryVH? = null
    var isLotteriesLoading = false
    var lotteryWidth = 0
    var lotteryActiveHeight = 0
    var lotteryCompletedHeight = 0
    var currentTotalLotteries = 0

    init {
        items.add(LotteryItem(LotteryItem.ItemType.FOOTER))
    }

    constructor(lotteryWidth: Int, lotteryActiveHeight: Int, lotteryCompletedHeight: Int) : this() {
        this.lotteryWidth = lotteryWidth
        this.lotteryActiveHeight = lotteryActiveHeight
        this.lotteryCompletedHeight = lotteryCompletedHeight
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            LotteryItem.ItemType.TITLE_ACTIVE_LOTTERY.id -> TitleActiveVH(inflater, parent)
            LotteryItem.ItemType.ACTIVE_LOTTERY.id -> {
                val view = ItemLotteryActiveBinding.inflate(inflater, parent, false)
                ActiveLotteryVH(view)
            }
            LotteryItem.ItemType.TITLE_COMPLETED_LOTTERY.id -> TitleCompletedVH(inflater, parent)
            LotteryItem.ItemType.COMPLETED_LOTTERY.id -> {
                val view = ItemLotteryCompletedBinding.inflate(inflater, parent, false)
                CompletedLotteryVH(view)
            }
            LotteryItem.ItemType.LOTTERY_LOAD.id -> {
                val view = ItemNewsLoadBinding.inflate(inflater, parent, false)
                LoadLotteryVH(view)
            }
            else -> FooterVH(inflater, parent)
        }
    }

    override fun getItemCount() = items.size

    override fun getItemViewType(position: Int): Int {
        return items[position].itemType.id
    }

    fun getLotteriesCount() = items.count {
        LotteryItem.ItemType.ACTIVE_LOTTERY == it.itemType ||
                LotteryItem.ItemType.COMPLETED_LOTTERY == it.itemType
    }

    fun setLotteriesLoadingState(isLotteriesLoading: Boolean) {
        this.isLotteriesLoading = isLotteriesLoading
        loadLotteryVH?.setLotteriesLoadingState(isLotteriesLoading)
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = items[position]
        when (holder) {
            is ActiveLotteryVH -> {
                item.lottery?.let { holder.bind(it) }
            }
            is CompletedLotteryVH -> {
                item.lottery?.let { holder.bind(it) }
            }
            is LoadLotteryVH -> {
                loadLotteryVH = holder
            }
        }
    }

    fun setList(activeLotteries: List<LotteryItem>, completedLotteries: List<LotteryItem>) {
        val newList = mutableListOf<LotteryItem>()
        if (activeLotteries.isNotEmpty()) {
            newList.add(LotteryItem(LotteryItem.ItemType.TITLE_ACTIVE_LOTTERY))
            newList.addAll(activeLotteries)
        }
        if (completedLotteries.isNotEmpty()) {
            newList.add(LotteryItem(LotteryItem.ItemType.TITLE_COMPLETED_LOTTERY))
            newList.addAll(completedLotteries)
        }
        newList.add(LotteryItem(LotteryItem.ItemType.LOTTERY_LOAD))
        newList.add(LotteryItem(LotteryItem.ItemType.FOOTER))
        
        if (this.items.size > 1) {
            val diffResult = DiffUtil.calculateDiff(LotteryItemDiffCallback(items, newList))
            this.items = newList
            diffResult.dispatchUpdatesTo(this)
        } else {
            this.items = newList
            notifyDataSetChanged()
        }
    }

    class TitleActiveVH(inflater: LayoutInflater, parent: ViewGroup) :
        RecyclerView.ViewHolder(inflater.inflate(R.layout.item_active_lottery_title, parent, false))

    class TitleCompletedVH(inflater: LayoutInflater, parent: ViewGroup) :
        RecyclerView.ViewHolder(inflater.inflate(R.layout.item_completed_lottery_title, parent, false))

    inner class ActiveLotteryVH(val view: ItemLotteryActiveBinding) :
        RecyclerView.ViewHolder(view.root) {

        fun bind(lottery: LocalLottery) {
            view.tvLotteryName.text = lottery.name

            val prizeFund = if (lottery.prizeFundByString.isNullOrEmpty()) {
                GeneralTools.formatBalance(Settings.get().userCurrencyCode, lottery.prizesSum)
            } else {
                lottery.prizeFundByString
            }
            view.tvLotteryPrize.text = prizeFund

            setTimeToEnd(lottery)
            setLotteryBackground(lottery.bannerMobile, view.root.context)

            view.btnAbout.setOnClickListener {
                listener?.onLotteryClicked(lottery)
            }
        }

        private fun setTimeToEnd(lottery: LocalLottery) {
            val endTime = DateTools.getServerDate(itemView.context, lottery.finishAt).time
            val currentTime = Calendar.getInstance().timeInMillis
            val days = (endTime - currentTime) / (DateTools.MILLIS_IN_DAY)
            if (days < 2) {
                val timer = object : CountDownTimer(endTime - currentTime, 1000) {
                    var hms: String = ""
                    override fun onTick(millisUntilFinished: Long) {
                        hms = String.format(
                            "%02d:%02d:%02d", TimeUnit.MILLISECONDS.toHours(millisUntilFinished),
                            TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished) - TimeUnit.HOURS.toMinutes(
                                TimeUnit.MILLISECONDS.toHours(millisUntilFinished)
                            ),
                            TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) - TimeUnit.MINUTES.toSeconds(
                                TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)
                            )
                        )
                        view.tvToEnd.text = hms
                    }

                    override fun onFinish() {
                        listener?.onRefresh()
                    }
                }
                timer.start()
            } else {
                view.tvToEnd.text = itemView.context.resources.getQuantityString(
                    R.plurals.days,
                    days.toInt(),
                    days.toInt()
                )
            }
        }

        private fun setLotteryBackground(bannerMobile: String?, context: Context) {
            if (!TextUtils.isEmpty(bannerMobile)) {
                Picasso.get()
                    .load(ApoloConfig.getFullUrl(bannerMobile))
                    .resize(lotteryWidth, lotteryActiveHeight)
                    .centerCrop()
                    .into(target)
            } else {
                view.ivLotteryImage.background = null
            }
        }

        private val target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: Picasso.LoadedFrom) {
                val roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(view.root.context.resources, bitmap)
                roundedBitmapDrawable.cornerRadius =
                    view.root.context.resources.getDimensionPixelSize(R.dimen.news_card_corner_radius).toFloat()
                view.ivLotteryImage.background = roundedBitmapDrawable
            }

            override fun onBitmapFailed(e: Exception, errorDrawable: Drawable?) { }

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) { }
        }
    }

    inner class CompletedLotteryVH(val view: ItemLotteryCompletedBinding) :
        RecyclerView.ViewHolder(view.root) {

        fun bind(lottery: LocalLottery) {
            view.tvLotteryName.text = lottery.name

            val prizeFund = if (lottery.prizeFundByString.isNullOrEmpty()) {
                GeneralTools.formatBalance(Settings.get().userCurrencyCode, lottery.prizesSum)
            } else {
                lottery.prizeFundByString
            }
            view.tvLotteryPrize.text = prizeFund

            val finishDay = DateTools.getDay(itemView.context, lottery.finishAt)
            val finishMonth = DateTools.getMonth(itemView.context, lottery.finishAt)
            view.tvLotteryDay.text = finishDay
            view.tvLotteryMonth.text = finishMonth

            setLotteryBackground(lottery.bannerMobile, view.root.context)

            itemView.setOnClickListener {
                listener?.onLotteryClicked(lottery)
            }
        }

        private fun setLotteryBackground(bannerMobile: String?, context: Context) {
            if (!TextUtils.isEmpty(bannerMobile)) {
                Picasso.get()
                    .load(ApoloConfig.getFullUrl(bannerMobile))
                    .resize(lotteryWidth, lotteryCompletedHeight)
                    .centerCrop()
                    .into(target)
            } else {
                view.ivLotteryImage.background = null
            }
        }

        private val target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: Picasso.LoadedFrom) {
                val roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(view.root.context.resources, bitmap)
                roundedBitmapDrawable.cornerRadius =
                    view.root.context.resources.getDimensionPixelSize(R.dimen.news_card_corner_radius).toFloat()
                view.ivLotteryImage.background = roundedBitmapDrawable
            }

            override fun onBitmapFailed(e: Exception, errorDrawable: Drawable?) { }

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) { }
        }
    }

    inner class LoadLotteryVH(val view: ItemNewsLoadBinding) : RecyclerView.ViewHolder(view.root) {
        init {
            setLoadMoreView()
            bind()
        }

        fun bind() {
            view.btnLoadMore.setOnClickListener {
                listener?.onLoadMoreClicked()
            }
        }

        private fun setLoadMoreView() {
            if (currentTotalLotteries > getLotteriesCount()) {
                if (currentTotalLotteries - getLotteriesCount() >= LotteriesViewModel.LOAD_STEP) {
                    view.btnLoadMore.text = itemView.context.resources.getQuantityString(R.plurals.more_lotteries, LotteriesViewModel.LOAD_STEP, LotteriesViewModel.LOAD_STEP)
                } else {
                    val diffLotteriesCount = currentTotalLotteries - getLotteriesCount()
                    view.btnLoadMore.text = itemView.context.resources.getQuantityString(R.plurals.more_lotteries, diffLotteriesCount, diffLotteriesCount)
                }
                view.btnLoadMore.visibility = View.VISIBLE
            } else {
                view.btnLoadMore.visibility = View.GONE
            }
        }

        fun setLotteriesLoadingState(isLoading: Boolean) {
            if (isLoading) {
                view.ivLoader.visibility = View.VISIBLE
                val animation = AnimationUtils.loadAnimation(itemView.context, R.anim.rotation_loader)
                view.ivLoader.startAnimation(animation)
                view.btnLoadMore.visibility = View.INVISIBLE
            } else {
                view.ivLoader.clearAnimation()
                view.ivLoader.visibility = View.GONE
                setLoadMoreView()
            }
        }
    }

    inner class FooterVH(inflater: LayoutInflater, parent: ViewGroup) :
        RecyclerView.ViewHolder(inflater.inflate(R.layout.view_footer, parent, false)) {

        init {
            bind()
        }

        fun bind() {
            val paddingBottom: Int = itemView.context.resources.getDimensionPixelSize(R.dimen.not_organic_user_footer_padding_bottom)
            itemView.setPadding(0, 0, 0, paddingBottom)
            itemView.findViewById<View>(R.id.gl_payment_systems_view).setOnClickListener {
                footerListener?.onClickPaymentSystems()
            }
            itemView.findViewById<TextView>(R.id.tv_copyright_year)?.let {
                it.text = itemView.context.getString(
                    R.string.app_copyright_year,
                    YearRepository.getCurrentYear()
                )
            }
        }
    }

    interface Listener {
        fun onLotteryClicked(lottery: LocalLottery)
        fun onLoadMoreClicked()
        fun onRefresh()
    }
}
