package com.abrand.custom.data.entity.lottery

import java.io.Serializable

data class LocalLottery(
    val id: Int,
    val name: String,
    val status: LocalLotteryStatus,
    val prizesSum: Double?,
    val prizeFundByString: String?,
    val startAt: Any,
    val finishAt: Any,
    val bannerMobile: String?,
    val textMobile: String? = null,
    val redemptionType: RedemptionType? = null,
    val ticketPrice: Double? = null,
    val prizes: List<LocalLotteryPrize>? = null,
    val winners: List<LocalLotteryWinner>? = null,
    var userTickets: List<LocalLotteryTicket>? = null,
    val ticketPackages: List<LocalLotteryTicketPackage>? = null
) : Serializable {

    enum class RedemptionType {
        NO_REDEMPTION, REDEMPTION
    }
}
