package com.abrand.custom.ui.interfaces;

import com.abrand.custom.data.entity.User;

public interface UserStateListener {
    default void applyState(User.State userState) {
        switch (userState) {
            case NOT_LOGGED:
                applyNotLoggedState();
                break;
            case PLAYER:
                applyLoggedState();
                break;
            case ORGANIC:
                applyOrganicState();
                break;
        }
    }

    void applyLoggedState();

    void applyNotLoggedState();

    void applyOrganicState();
}
