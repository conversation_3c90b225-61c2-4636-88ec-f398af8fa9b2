<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/bg_system_message"
        android:orientation="vertical"
        android:paddingBottom="16dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="42dp"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent" />

        <WebView
            android:id="@+id/wv_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="42dp"
            android:layout_marginBottom="16dp"
            android:paddingBottom="@dimen/size_16"
            android:scrollbars="none"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <ImageButton
            android:id="@+id/ib_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@null"
            android:padding="@dimen/size_16"
            android:src="@drawable/ic_promotion_info_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>
