package com.abrand.custom.ui.game

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.databinding.ItemIngameTournamentParticipantBinding
import com.abrand.custom.tools.GeneralTools

class InGameTournamentParticipantsAdapter(private val data: List<TournamentsItem.TournamentParticipant>) :
    RecyclerView.Adapter<InGameTournamentParticipantsAdapter.ParticipantViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ParticipantViewHolder {
        val binding: ItemIngameTournamentParticipantBinding =
            ItemIngameTournamentParticipantBinding.inflate(
                LayoutInflater.from(parent.context),
                parent, false
            )
        return ParticipantViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ParticipantViewHolder, position: Int) {
        data[position].let {
            holder.binding.tvNumber.text = it.place.toString()
            if (it.user?.id != Settings.get().userId) {
                holder.binding.tvName.text = it.user?.formattedUserName
            } else {
                holder.binding.tvName.text = Settings.get().loggedUserEmail
            }

            try {
                val decimalFormat = GeneralTools.getTournamentResultDecimalFormat()
                holder.binding.tvScore.text = GeneralTools.formatDoubleNumber(decimalFormat.format(it.score))
            } catch (e: Exception) {
                holder.binding.tvScore.text = "0"
            }
        }
    }

    override fun getItemCount() = data.size

    class ParticipantViewHolder(val binding: ItemIngameTournamentParticipantBinding) :
        RecyclerView.ViewHolder(binding.root)
}
