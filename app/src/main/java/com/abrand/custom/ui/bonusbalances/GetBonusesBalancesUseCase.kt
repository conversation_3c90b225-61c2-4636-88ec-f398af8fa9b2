package com.abrand.custom.ui.bonusbalances

import com.abrand.custom.data.repositories.BonusBalancesRepository
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class GetBonusesBalancesUseCase(
    private val bonusBalanceRepo: IBonusBalancesRepo = BonusBalancesRepository,
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO
) {
    suspend fun invoke(): List<BonusBalance> = withContext(dispatcher) {
            var bonuses = bonusBalanceRepo.obtainBonusBalances()

            ensureOnlyOneIsActive(bonuses)
            bonuses = moveActiveToFirstPlace(bonuses)

            return@withContext bonuses
    }

    private fun moveActiveToFirstPlace(bonuses: List<BonusBalance>): List<BonusBalance> {
        val mutableBonuses = bonuses.toMutableList()

        mutableBonuses.sortBy { bonusBalance ->
            !bonusBalance.isActive
        }

        return mutableBonuses.toList()
    }

    private fun ensureOnlyOneIsActive(bonuses: List<BonusBalance>) {
        var activeFound = false

        for (bonus in bonuses) {
            if (bonus.isActive) {
                if (activeFound) {
                    Firebase.crashlytics.recordException(
                        IllegalStateException("More than one bonus balance is active")
                    )
                    println("Warning: More than one bonus is active")
                }

                activeFound = true
            }
        }
    }
}

data class BonusBalance(
    val id: Int,
    val wager: Int?,
    val isActive: Boolean,
    val amount: Float,
    val wageringTarget: Float,
    val wageringCurrent: Float,
    val wageringCompletePercent: Double,
    val wageringExpiredAt: String?,
    val wageringMaxTransferAmount: Float? = null
)
