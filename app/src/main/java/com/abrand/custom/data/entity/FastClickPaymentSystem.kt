package com.abrand.custom.data.entity

import android.os.Parcelable
import com.abrand.custom.GetInitialViewerDataQuery
import com.abrand.custom.GetFastClickPaymentSystemQuery
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import kotlinx.android.parcel.RawValue

@Parcelize
class FastClickPaymentSystem(
    @SerializedName("logo") var logo: String?,
    @SerializedName("code") var code: String?,
    @SerializedName("minAmount") var minAmount: @RawValue Any?,
    @SerializedName("maxAmount") var maxAmount: @RawValue Any?,
    @SerializedName("requisite") var requisite: String?,
    @SerializedName("isRebill") var isRebill: Boolean?,
    @SerializedName("defaultAmount") var defaultAmount: @RawValue Any?,
    @SerializedName("currency") var currency: @RawValue Currency?
) : Parcelable {

    constructor(fastClickPaymentSystem: GetFastClickPaymentSystemQuery.FastClickPaymentSystem) :
            this(
                fastClickPaymentSystem.logo, fastClickPaymentSystem.code,
                fastClickPaymentSystem.minAmount, fastClickPaymentSystem.maxAmount,
                fastClickPaymentSystem.requisite, fastClickPaymentSystem.isRebill,
                fastClickPaymentSystem.defaultAmount,
                Currency(
                    fastClickPaymentSystem.currency?.code,
                    fastClickPaymentSystem.currency?.symbol
                )
            )

    constructor(fastClickPaymentSystem: GetInitialViewerDataQuery.FastClickPaymentSystem) :
            this(
                fastClickPaymentSystem.logo, fastClickPaymentSystem.code,
                fastClickPaymentSystem.minAmount, fastClickPaymentSystem.maxAmount,
                fastClickPaymentSystem.requisite, fastClickPaymentSystem.isRebill,
                fastClickPaymentSystem.defaultAmount,
                Currency(
                    fastClickPaymentSystem.currency?.code,
                    fastClickPaymentSystem.currency?.symbol
                )
            )

    @Parcelize
    data class Currency(@SerializedName("code") var code: String?,
                        @SerializedName("symbol") var symbol: String?) : Parcelable
}
