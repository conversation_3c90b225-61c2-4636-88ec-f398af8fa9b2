package com.abrand.custom.ui.profile

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlin.time.Duration

@Composable
fun CountdownTimer(targetTime: Instant?, content: @Composable (Duration?) -> Unit) {
    if (targetTime == null) {
        content(null)

        return
    }

    var remainingTime by remember { mutableStateOf(targetTime - Clock.System.now()) }

    if (remainingTime.isNegative()) {
        content(remainingTime)

        return
    }

    LaunchedEffect(targetTime) {
        while (remainingTime > Duration.ZERO && isActive) {
            val now = Clock.System.now()

            remainingTime = targetTime - now

            delay(1_000L - (now.toEpochMilliseconds() % 1_000L))
        }
    }

    content(remainingTime.coerceAtLeast(Duration.ZERO))
}
