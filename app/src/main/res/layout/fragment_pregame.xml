<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <include
        android:id="@+id/l_item_game"
        layout="@layout/item_game"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <LinearLayout
        android:id="@+id/lay_buttons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="48dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/btn_game"
            style="@style/ButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/size_16"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:enabled="false"
            android:text="@string/_registration" />

        <Button
            android:id="@+id/btn_tournament"
            style="@style/ButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:enabled="false"
            android:visibility="gone"
            android:text="@string/tournament_join" />

    </LinearLayout>

    <TextView
        android:id="@+id/tv_demo"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="center_vertical"
        android:lineSpacingExtra="6sp"
        android:paddingTop="16dp"
        android:paddingBottom="16dp"
        android:text="@string/demo_game"
        android:textColor="#fff"
        android:textFontWeight="400"
        android:textSize="14sp"
        android:visibility="gone"
        tools:ignore="UnusedAttribute"
        tools:visibility="visible"
        app:layout_constraintBottom_toTopOf="@id/lay_buttons"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/container_switch_favourite" />

    <LinearLayout
        android:id="@+id/container_switch_favourite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/size_16"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toTopOf="@id/lay_buttons"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/to_favorites"
            android:textColor="#ffffff"
            android:textFontWeight="400"
            android:textSize="14sp" />

        <com.abrand.custom.ui.views.FavouriteSwitcher
            android:id="@+id/switch_favourite"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>