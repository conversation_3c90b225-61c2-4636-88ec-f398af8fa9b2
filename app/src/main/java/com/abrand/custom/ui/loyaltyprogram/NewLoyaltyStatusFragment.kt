package com.abrand.custom.ui.loyaltyprogram

import android.os.Bundle
import android.text.Html
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.navigation.Navigation
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.entity.LoyaltyStatus
import com.abrand.custom.databinding.FragmentNewLoyaltyStatusBinding
import com.abrand.custom.tools.GeneralTools

class NewLoyaltyStatusFragment : Fragment() {
    private var binding: FragmentNewLoyaltyStatusBinding? = null
    private var loyaltyStatus: LoyaltyStatus? = null

    companion object {
        const val KEY_LOYALTY_STATUS = "loyaltyStatus"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments.let {
            loyaltyStatus = it?.getParcelable(KEY_LOYALTY_STATUS)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val localBinding = FragmentNewLoyaltyStatusBinding.inflate(inflater, container, false)
        binding = localBinding

        setupInsets()

        val privileges = when (loyaltyStatus?.statusId) {
            1 -> context?.resources?.getStringArray(R.array.loyalty_privileges_1)
            2 -> context?.resources?.getStringArray(R.array.loyalty_privileges_2)
            3 -> context?.resources?.getStringArray(R.array.loyalty_privileges_3)
            4 -> context?.resources?.getStringArray(R.array.loyalty_privileges_4)
            5 -> context?.resources?.getStringArray(R.array.loyalty_privileges_5)
            6 -> context?.resources?.getStringArray(R.array.loyalty_privileges_6)
            else -> context?.resources?.getStringArray(R.array.loyalty_privileges_7)
        }
        if (!privileges.isNullOrEmpty()) {
            privileges[0] = context?.getString(R.string.loyalty_rate, privileges[0])
        }
        val privilegesList = privileges?.toCollection(ArrayList())
        val prizesTitle = getPrizesTitle()
        if (prizesTitle.isNotEmpty()) {
            privilegesList?.add(0, prizesTitle)
        }

        val localLoyaltyStatus = loyaltyStatus
        if (localLoyaltyStatus != null) {
            localBinding.ivStatus.setImageDrawable(localLoyaltyStatus.statusId?.let {
                GeneralTools.getLoyaltyStatusDrawable(context,
                    it, true)
            })
            localBinding.tvStatus.text = localLoyaltyStatus.title

            localBinding.rvNewPrizesPrivileges.layoutManager = LinearLayoutManager(context)
            if (privilegesList != null) {
                localBinding.rvNewPrizesPrivileges.adapter = NewPrizesPrivilegesAdapter(privilegesList)
            }
        }

        localBinding.btnHome.setOnClickListener {
            navigateUp()
        }

        return localBinding.root
    }

    fun setupInsets() {
        binding?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it.tvCongratulations) { view, insets ->
                view.updatePadding(top = insets.systemWindowInsetTop)
                insets
            }

            ViewCompat.setOnApplyWindowInsetsListener(it.rootContainer) { view, insets ->
                view.updatePadding(bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }

    private fun navigateUp() {
        view?.let {
            activity?.runOnUiThread { Navigation.findNavController(it).navigateUp() }
        }
    }

    private fun getPrizesTitle() :String {
        return if (loyaltyStatus?.prizePoints?.isNotEmpty() == true && loyaltyStatus?.prizeMoney != 0.0) {
            Html.fromHtml(context?.resources?.getString(R.string.prize_points_money,
                loyaltyStatus?.prizePoints, loyaltyStatus?.prizeMoney?.toInt(),
                loyaltyStatus?.prizeCurrencySymbol)).toString()
        } else if (loyaltyStatus?.prizePoints?.isNotEmpty() == true) {
            Html.fromHtml(context?.resources?.getString(R.string.prize_points,
                loyaltyStatus?.prizePoints)).toString()
        } else if (loyaltyStatus?.prizeMoney != 0.0) {
            Html.fromHtml(context?.resources?.getString(R.string.prize_money, loyaltyStatus?.prizeMoney?.toInt(),
                loyaltyStatus?.prizeCurrencySymbol)).toString()
        } else {
            ""
        }
    }
}
