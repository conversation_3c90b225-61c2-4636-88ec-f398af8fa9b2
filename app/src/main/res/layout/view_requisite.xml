<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="48dp"
    android:layout_height="48dp"
    android:background="@drawable/bg_requisite">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="10dp"
        android:layout_marginTop="10dp"
        android:scaleType="fitCenter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:id="@+id/view_circle_fast_payment_requisite"
            android:layout_width="2dp"
            android:layout_height="2dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/circle_fast_payment_requisite" />

        <TextView
            android:id="@+id/tv_requisite"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:textColor="#406080"
            android:textSize="10sp" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
