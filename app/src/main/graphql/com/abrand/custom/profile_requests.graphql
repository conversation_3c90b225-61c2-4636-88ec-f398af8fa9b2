#import "./fragments.graphql"

query getReCaptcha (
    $type: CaptchaForm!,
    $appId: String,
    $refCode: String
) {
    reCaptcha(
        type: $type,
        appId: $appId,
        refCode: $refCode
    ) {
        captchaId,
        enabled,
        provider
    }
}

mutation registrationMutation(
    $email: String!,
    $plainPassword: String!,
    $locale: Locale!,
    $gCaptcha: String,
    $appId: String,
    $registrationBonusId: Int,
    $payload: RegistrationPayload
) {
    registerUserByEmail(
        email: $email,
        locale: $locale,
        plainPassword: $plainPassword,
        registrationBonusId: $registrationBonusId,
        subscribeOnEmailNotifications: true,
        gCaptchaResponse: $gCaptcha,
        appId: $appId,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        payload: $payload
    ) {
        viewer {
            id
        }
    }
}

mutation authMutation(
    $name: String!,
    $password: String!,
    $gCaptcha: String,
    $appId: String,
    $affData: String,
    $pushSubscriptionData: String,
    $refCode: String,
) {
    auth(
        userName: $name,
        password: $password,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
        gCaptchaResponse: $gCaptcha
        appId: $appId,
        affData: $affData,
        pushSubscriptionData: $pushSubscriptionData,
        refCode: $refCode,
    ) {
        viewer {
            id
        }
    }
}

mutation socialAuth(
    $domain: String!,
    $token: String!,
    $locale: Locale!
) {
    login4playAuth(
        domain: $domain,
        token: $token,
        locale: $locale,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    ) {
        viewer {
            id,
            profile {
                email
            }
        }
    }
}

mutation userLogout {
    userLogout
}

mutation resetPass (
    $email: String!
) {
    userSendResetPasswordEmail(
        email: $email
    )
}

query getProfile {
    viewer {
        profile {
            userName,
            email,
            phone,
            emailConfirmed,
            birthday,
            gender
        }
    }
}

query getInitialViewerData {
    viewer {
        wallet {
            balance,
            realBalance,
            bonusBalance,
            currency {
                code,
                symbol
            }
        },
        loyaltyProgress {
            status {
                id
                title
                prizes {
                    ... on LoyaltyMoneyPrizeType {
                        amount
                        currency {
                            code
                            symbol
                        }
                        type
                    }
                    ... on LoyaltyPointsPrizeType {
                        count
                        type
                    }
                }
            }
            points {
                current
            }
            percent
            xOnPoints {
                ... FragmentXOnPoints
            }
        },
        profile {
            email
            userName
        },
        fastClickPaymentSystem {
            logo,
            code,
            minAmount,
            maxAmount,
            requisite,
            isRebill,
            defaultAmount,
            currency {
                code,
                symbol
            }
        },
        cashbacks {
            status {
                amount
            }
            store {
                amount
            }
        },
        messages {
            unreadCount
        }
    }
}

query getPaymentUrl (
    $host: String!,
    $direction: PaymentUrlDirection!,
    $refCode: String) {
    viewer {
        paymentUrl (
            direction: $direction,
            deviceInfo: {
                platformType: MOB,
                platform: NATIVE_APP,
                os: ANDROID
            },
            host: $host,
            refCode: $refCode) {
            url
        }
    }
}

query retryPaymentUrl(
    $paymentId: Int!,
    $host: String!,
    $refCode: String!) {
    viewer {
        retryPaymentUrl(
            paymentId: $paymentId,
            deviceInfo: {
                platformType: MOB,
                platform: NATIVE_APP,
                os: ANDROID
            },
            host: $host,
            refCode: $refCode)
    }
}

query getFastClickPaymentSystem {
    viewer {
        fastClickPaymentSystem {
            logo,
            code,
            minAmount,
            maxAmount,
            requisite,
            isRebill,
            defaultAmount,
            currency {
                code,
                symbol
            }
        }
    }
}

query getTransactions($operations: [PaymentCompoundOperation],
    $statuses: [TransactionStatus],
    $dateFromTo: DateFromTo) {
    viewer {
        transactions(operations: $operations,
            statuses: $statuses,
            dateFromTo: $dateFromTo) {
            ... on Transaction {
                ...BaseTransaction
            }
            ... on CompoundTransaction {
                children {
                    ...BaseTransaction
                }
                ...BaseTransaction
            }
        }
    }
}

query getTransaction($transactionId: Int!) {
    viewer {
        transactions (id: $transactionId) {
            ... on Transaction {
                ...BaseTransaction
            }
            ... on CompoundTransaction {
                children {
                    ...BaseTransaction
                }
                ...BaseTransaction
            }
        }
    }
}

mutation cancelPayout($paymentId: Int!) {
    cancelPayout (paymentId: $paymentId)
}

subscription subscribeBalance {
    viewerWallet {
        balance,
        realBalance,
        bonusBalance,
        bonusBetSum,
        bonusRefundSum,
        bonusPercentToRefund
    }
}

subscription subscribeLoyaltyPoints {
    viewerLoyaltyPoints {
        current
    }
}

subscription subscribeLoyaltyProgress {
    viewerLoyaltyPercent
}

subscription subscribeLoyaltyStatus {
    viewerLoyaltyStatus {
        id
        title
        prizes {
            ... on LoyaltyMoneyPrizeType {
                amount
                currency {
                    code
                    symbol
                }
                type
            }
            ... on LoyaltyPointsPrizeType {
                count
                type
            }
        }
    }
}

subscription subscribeLoyaltyXOnPoints {
    viewerLoyaltyXOnPoints {
        ...FragmentXOnPoints
    }
}

subscription subscribeRealTimeNotification {
    viewerRealTimeNotification {
        title
        content
        buttonText
    }
}

subscription subscribeMessages {
    viewerMessagesV2 {
        id
        imgMob
        date
        title
        message
        buttonUrl
        buttonText
        buttonUrl2
        buttonText2
        isRead
    }
}

subscription onlineUsers {
    onlineUsers (
        platformType: MOB
    )
}

mutation changeProfile (
    $userName: String,
    $phone: String,
    $gender: Gender,
    $birthday: String,
    $gCaptcha: String,
    $appId: String
) {
    userChangeProfile(
        userName: $userName,
        phone: $phone,
        gender: $gender,
        birthday: $birthday
        gCaptchaResponse: $gCaptcha
        appId: $appId
    )
}

mutation changePassword (
    $currentPassword: String!,
    $newPassword: String!
) {
    userChangePassword(
        currentPassword: $currentPassword,
        newPassword: $newPassword
    )
}

mutation requestEmailConfirmation {
    requestEmailConfirmation
}

mutation makeRebill (
    $amount: GraphQLMoneyType,
    $host: String!
) {
    makeRebill(
        amount: $amount,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        host: $host
    )
}

query getFastPaymentUrl (
    $amount: GraphQLMoneyType,
    $host: String!,
    $refCode: String
) {
    viewer {
        oneClickUrl(
            amount: $amount,
            deviceInfo: {
                platformType: MOB,
                platform: NATIVE_APP,
                os: ANDROID
            },
            host: $host,
            refCode: $refCode
        )
    }
}

query getNews(
    $offset: Int,
    $limitPagination: PaginationAmount
) {
    news(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        offset: $offset
        limitPagination: $limitPagination
    ) {
        items {
            ... on News {
                ...FragmentNewsItem
            }
        },
        totalCount
    }
}

query getNewsItemByUrl(
    $url: String!
) {
    newsItemByUrl(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        url: $url
    ) {
        ... on News {
            ...FragmentNewsItem
        }
    }
}

query getSupportConfig(
    $host: String!
) {
    yhelperConfig(
        host: $host,
    ) {
        widgetId,
        signature,
        host,
    }
}

query getLoyaltyStatuses {
    loyaltyStatuses {
        id
        title
        exchange_rate
        startPoints
        prizes {
            ... on LoyaltyMoneyPrizeType {
                amount
                currency {
                    code
                    symbol
                }
                type
            }
            ... on LoyaltyPointsPrizeType {
                count
                type
            }
        }
        privileges {
            description
        }
    }
}

query getExchangePointsData {
    viewer {
        profile {
            emailConfirmed
        }
        loyaltyProgress {
            status {
                title
                exchange_rate
            }
        }
    }
}

mutation loyaltyPointsExchangeMutation(
    $points: Int!
) {
    loyaltyPointsExchange(
        points: $points
    )
}

mutation autologin(
    $token: String!,
    $refCode: String,
    $affData: String
) {
    autologin(
        token: $token,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        refCode: $refCode,
        affData: $affData
    ) {
        viewer {
            id
            profile {
                email
                userName
            }
        }
    }
}

mutation updateMobileAppSubscription(
    $pushSubscriptionData: String!
) {
    updateMobileAppSubscription(
        pushSubscriptionData: $pushSubscriptionData
    )
}

mutation resetUserPasswordByToken(
    $token: String!,
    $newPassword: String!,
    $refCode: String,
    $affData: String
) {
    resetUserPasswordByToken(
        token: $token,
        newPassword: $newPassword,
        refCode: $refCode,
        affData: $affData,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    ) {
        viewer {
            id
        }
    }
}

mutation userConfirmEmail(
    $confirmationCode: String
) {
    userConfirmEmail(
        confirmationCode: $confirmationCode,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    )
}

query userCheckPasswordResetToken(
    $token: String!
) {
    userCheckPasswordResetToken(
        token: $token
    )
}

query checkPassword(
    $password: String!
) {
    userCheckPassword(
        password: $password
    )
}

query getMessages {
    viewer {
        messages {
            unreadCount
            items {
                id
                imgMob
                date
                title
                message
                buttonUrl
                buttonText
                buttonUrl2
                buttonText2
                isRead
            }
        }
    }
}

mutation deleteSeveralMessages(
    $ids: [ID]
) {
    deleteSeveralNotifications (
        ids: $ids
    )
}

mutation readSeveralMessages(
    $ids: [ID]
) {
    readSeveralNotifications (
        ids: $ids
    )
}

mutation readAllMessages {
    readAllNotifications
}

mutation resetBonusBalance {
    resetUserBonusBalance
}

query getBonusRefund {
    viewer {
        wallet {
            bonusBetSum
            bonusRefundSum
            bonusPercentToRefund
        }
    }
}

query getTalisman {
    viewer {
        talismanV2 {
            name
            img
        }
    }
}

query getHomeAdditionalBlocks {
    tournaments (
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        statusList: IN_PROCESS
        offset: 0
        limitPagination: 1
    ) {
        items {
            ... on Tournament {
                ...FragmentTournamentsItem
            }
        }
    }

    lotteries (
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        status: IN_PROCESS
        offset: 0
        limitPagination: 1
    ) {
        items {
            ...on StandardLottery {
                ...FragmentLotteriesItem
            }
        }
    }

    news(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        offset: 0
        limitPagination: 1
    ) {
        items {
            ... on News {
                ...FragmentNewsItem
            }
        }
    }
}

query getUnvisitedCount {
    bonuses(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    ) {
        unvisitedCount
    },
    tournaments(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        statusList: [IN_PROCESS, COMPLETED],
    ){
        viewerCount
    },
    lotteries(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    ){
        viewerCount
    }
}

mutation markBonusesVisited {
    markBonusesVisited(deviceInfo: {
        platformType: MOB,
        platform: NATIVE_APP,
        os: ANDROID
    })
}

mutation markTournamentsVisited {
    markTournamentsVisitedV2(deviceInfo: {
        platformType: MOB,
        platform: NATIVE_APP,
        os: ANDROID
    })
}

mutation markLotteriesVisited {
    markLotteriesVisited(deviceInfo: {
        platformType: MOB,
        platform: NATIVE_APP,
        os: ANDROID
    })
}

subscription subscribeViewerBonusesCount {
    viewerBonusesCount
}

subscription subscribeTournamentsCountChange {
    tournamentsCountChange
}

subscription subscribeLotteriesCountChange {
    lotteriesCountChange
}

fragment BaseTransaction on TransactionInterface {
    id
    direction
    amount
    currency {
        code
    }
    date
    status
    operationType
    paymentSystemName
    userComment
    rejectionEnable
}

fragment FragmentNewsItem on News {
    name
    publishDate
    mobileIcon
    content
}

fragment FragmentXOnPoints on XOnPoints {
    name
    enabled
    endDate
}
