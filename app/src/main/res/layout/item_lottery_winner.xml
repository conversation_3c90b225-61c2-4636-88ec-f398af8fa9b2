<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/parent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingStart="@dimen/size_16"
    android:paddingTop="8dp"
    android:paddingEnd="@dimen/size_16"
    android:paddingBottom="8dp"
    android:weightSum="100">

    <TextView
        android:id="@+id/tv_number"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="10"
        android:gravity="center_vertical"
        android:textColor="#40ffffff"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="35"
        android:ellipsize="end"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="#ffffff"
        android:textSize="14sp" />

    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="25">

        <ImageView
            android:id="@+id/iv_bg_ticket"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:scaleType="fitCenter" />

        <TextView
            android:id="@+id/tv_ticket_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:maxLines="1"
            android:textColor="#ffffff"
            android:textSize="12sp" />
    </FrameLayout>

    <TextView
        android:id="@+id/tv_prize"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="30"
        android:maxLines="1"
        android:textAlignment="viewEnd"
        android:ellipsize="end"
        android:textColor="@color/lottery_prize_highlighted_text"
        android:textSize="14sp" />

</LinearLayout>
