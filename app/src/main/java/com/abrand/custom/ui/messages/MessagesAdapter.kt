package com.abrand.custom.ui.messages

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Build
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.entity.MessageItem
import com.abrand.custom.data.entity.Screen
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.RoundedCornersTransformation
import com.abrand.custom.tools.readTextFromAsset
import com.squareup.picasso.Picasso

class MessagesAdapter(val screen: Screen, var items: MutableList<MessageItem>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    var listener: Listener? = null

    init {
        items.add(0, MessageItem(MessageItem.ItemType.TITLE))
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            MessageItem.ItemType.TITLE.id -> TitleVH(inflater, parent)
            else -> MessageVH(inflater, parent)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val messageItem = items[position]
        if (holder is MessageVH) {
            holder.bind(messageItem)
        }

        if (Screen.MESSAGES_MAIN == screen) {
            setFirstItemAppBarPadding(position, holder)
        }
    }

    override fun getItemCount() = items.size

    override fun getItemViewType(position: Int): Int {
        return items[position].itemType.id
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setList(messageList: MutableList<MessageItem>) {
        messageList.add(0, MessageItem(MessageItem.ItemType.TITLE))
        this.items = messageList
        notifyDataSetChanged()
    }

    fun appendList(newItem: MessageItem) {
        items.add(1, newItem)
        notifyItemInserted(1)
    }

    fun removeItem(itemId: String, itemPosition: Int) {
        items.removeAll { it.id == itemId }
        if (items.size == 1) {
            listener?.onMessagesListEmpty()
        }
        notifyItemRemoved(itemPosition)
    }

    fun markItemAsRead(itemId: String, itemPosition: Int) {
        for (item in items) {
            if (item.id == itemId) {
                item.isRead = true
                break
            }
        }
        notifyItemChanged(itemPosition)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun markAllAsRead() {
        for (item in items) {
            item.isRead = true
        }
        notifyDataSetChanged()
    }

    private fun setFirstItemAppBarPadding(position: Int, holder: RecyclerView.ViewHolder) {
        if (position == 0) {
            val padding: Int = GeneralTools.getActionBarHeight(holder.itemView.context)
            holder.itemView.setPadding(
                holder.itemView.paddingLeft,
                padding,
                holder.itemView.paddingRight,
                holder.itemView.paddingBottom
            )
        } else {
            holder.itemView.setPadding(
                holder.itemView.paddingLeft,
                0,
                holder.itemView.paddingRight,
                holder.itemView.paddingBottom
            )
        }
    }

    class TitleVH(inflater: LayoutInflater, parent: ViewGroup) :
        RecyclerView.ViewHolder(inflater.inflate(R.layout.item_messages_title, parent, false))

    inner class MessageVH(inflater: LayoutInflater, parent: ViewGroup) :
        RecyclerView.ViewHolder(inflater.inflate(R.layout.item_message, parent, false)) {
        private val tvDate = itemView.findViewById<TextView>(R.id.tv_date)
        private val tvTitle = itemView.findViewById<TextView>(R.id.tv_title)
        private val ivDelete = itemView.findViewById<ImageView>(R.id.iv_delete)
        private val ivBanner = itemView.findViewById<ImageView>(R.id.iv_banner)
        private val wvText = itemView.findViewById<WebView>(R.id.wv_text)
        private val btnBottom1 = itemView.findViewById<Button>(R.id.btn_bottom_1)
        private val btnBottom2 = itemView.findViewById<Button>(R.id.btn_bottom_2)
        private val btnBottomMargin = itemView.findViewById<View>(R.id.btn_bottom_margin)
        private val messageReadMask = itemView.findViewById<View>(R.id.message_read_mask)

        fun bind(message: MessageItem) {
            tvDate.text = message.date
            tvTitle.text = message.title
            wvText.setBackgroundColor(Color.TRANSPARENT)
            message.text?.let {
                setHtmlMessage(it)
            }

            if (!message.image.isNullOrEmpty()) {
                val imageCornerRadius = itemView.context.resources.getDimensionPixelSize(R.dimen.message_card_corner_radius)
                val marginStartEnd = itemView.context.resources.getDimensionPixelSize(R.dimen.activity_horizontal_margin)
                Picasso.get().load(ApoloConfig.getFullUrl(message.image))
                    .transform(RoundedCornersTransformation(imageCornerRadius, 0,
                        RoundedCornersTransformation.CornerType.TOP))
                    .resize(GeneralTools.getScreenWidth(itemView.context) - marginStartEnd * 2, 0)
                    .centerCrop()
                    .into(ivBanner)
            } else {
                ivBanner.background = null
            }

            if (message.buttonText.isNullOrEmpty()) {
                btnBottom1.visibility = View.GONE
                btnBottomMargin.visibility = View.GONE
            } else {
                btnBottom1.text = message.buttonText
                btnBottom1.visibility = View.VISIBLE
                btnBottomMargin.visibility = View.VISIBLE
                btnBottom1.setOnClickListener {
                    message.buttonUrl?.let { buttonUrl -> listener?.onDeepLinkClicked(buttonUrl) }
                }
            }

            if (message.buttonText2.isNullOrEmpty()) {
                btnBottom2.visibility = View.GONE
            } else {
                btnBottom2.text = message.buttonText2
                btnBottom2.visibility = View.VISIBLE
                btnBottom2.setOnClickListener {
                    message.buttonUrl2?.let { buttonUrl -> listener?.onDeepLinkClicked(buttonUrl) }
                }
            }

            ivDelete.setOnClickListener {
                listener?.onItemDelete(message.id, adapterPosition)
            }

            if (message.isRead == true) {
                messageReadMask.visibility = View.VISIBLE
            } else {
                messageReadMask.visibility = View.INVISIBLE
            }

            itemView.setOnClickListener {
                if (message.isRead != true) {
                    listener?.onMarkItemAsRead(message.id, adapterPosition)
                }
            }

        }

        @SuppressLint("SetJavaScriptEnabled")
        private fun setHtmlMessage(htmlMessage: String) {
            val CSS_ASSET_FILE_NAME = "messageItem.css"
            wvText.setBackgroundColor(Color.TRANSPARENT)
            wvText.settings.javaScriptEnabled = true
            wvText.webViewClient = object : WebViewClient() {
                override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                    listener?.onDeepLinkClicked(url)
                    return true
                }

                @RequiresApi(api = Build.VERSION_CODES.KITKAT)
                override fun onPageFinished(view: WebView, url: String) {
                    val css: String = itemView.context.readTextFromAsset(CSS_ASSET_FILE_NAME)
                    val js =
                        "var style = document.createElement('style'); style.innerHTML = `$css`; document.head.appendChild(style);"
                    wvText.evaluateJavascript(js, null)
                    super.onPageFinished(view, url)
                }
            }
            wvText.loadDataWithBaseURL(
                ApoloConfig.BASE_URL,
                htmlMessage,
                "text/html",
                "UTF-8",
                ""
            )
        }
    }

    interface Listener {
        fun onItemDelete(id: String?, itemPosition: Int)
        fun onMarkItemAsRead(id: String?, itemPosition: Int)
        fun onDeepLinkClicked(url: String)
        fun onMessagesListEmpty()
    }
}
