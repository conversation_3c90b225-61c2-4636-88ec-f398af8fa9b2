query getWheel(
    $currencyCode: String!,
    $denomination: Int!) {
    wheelByDenomination(currencyCode: $currencyCode,
        denomination: $denomination) {
        id
        spinPrice
        sectors {
            position
            prize {
                title
                type {
                    alias
                }
                amount
            }
        }
    }
}

query getFreeSpins {
    wheelUserFreeSpins {
        spinCount
    }
}

query getWheelFortuneRules($textIds: String!) {
    textBlocks(textIds: $textIds) {
        text
    }
}

mutation wheelSpin (
    $wofId: Int!
) {
    wheelGameSpin(
        wofId: $wofId
        platform: MOB
    ) {
        position
        prize {
            title
            type {
                alias
            }
        }
    }
}

subscription subscribeFreeSpins {
    wofUserFreeSpinsUpdated {
        ... on UserFreeSpin {
            spinCount
        }
    }
}
