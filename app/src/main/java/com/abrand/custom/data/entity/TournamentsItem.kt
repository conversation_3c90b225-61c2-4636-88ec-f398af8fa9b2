package com.abrand.custom.data.entity

import java.io.Serializable

data class TournamentsItem(
    var itemType: ItemType = ItemType.TITLE_CURRENT,
    var id: Int = 0,
    var title: String? = null,
    var dateStart: Any? = null,
    var dateEnd: Any = "",
    var prizeFund: String = "",
    var bannerMob: String? = null,
    var status: String? = null,
    var mobText: String? = null,
    var minBaseLoyaltyStatusId: Int? = null,
    var maxBaseLoyaltyStatusId: Int? = null,
    var tournamentViewerProgress: TournamentViewerProgress? = null,
    var tournamentPrizes: List<TournamentPrize> = mutableListOf(),
    var isDynamicPrizeFund: Boolean = true,
    var participants: List<TournamentParticipant> = mutableListOf(),
    var gameList: List<LocalGameItem> = mutableListOf(),
    var minBetLimit: Any? = null,
    var qualificationRounds: Int = 0
) : Serializable {


    companion object {
        val STATUS_COMPLETED = "completed"
    }

    enum class ItemType(val id: Int) {
        TITLE_CURRENT(0),
        TOURNAMENT_CURRENT(1),
        TITLE_ENDED(2),
        TOURNAMENT_ENDED(3),
        TOURNAMENTS_LOAD(4),
        FOOTER(5)
    }

    data class TournamentViewerProgress(
        val place: Int?,
        val score: Double?,
        val totalBets: Int
    ) : Serializable

    data class TournamentPrize(
        val placeNumber: Int,
        val sum: String
    ) : Serializable

    data class TournamentParticipant(
        val user: TournamentUser?,
        val place: Int?,
        val score: Double?
    ) : Serializable

    data class TournamentUser(
        val id: String?,
        val formattedUserName: String?
    ) : Serializable

}
