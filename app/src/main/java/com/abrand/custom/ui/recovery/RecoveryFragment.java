package com.abrand.custom.ui.recovery;

import android.app.Activity;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Html;
import android.text.InputType;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.annotation.NonNull;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;
import androidx.navigation.Navigation;

import com.abrand.custom.R;
import com.abrand.custom.databinding.FragmentRecoveryBinding;
import com.abrand.custom.interfaces.GenericTarget;
import com.abrand.custom.network.ApolloProcessorKt;
import com.abrand.custom.presenter.Fields;
import com.abrand.custom.presenter.FieldsErrorsHolder;
import com.abrand.custom.tools.GeneralTools;
import com.abrand.custom.ui.views.textfield.TextField;
import com.apollographql.apollo3.exception.ApolloException;

import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Map;

public class RecoveryFragment extends Fragment {

    private static final String TAG = "RecoveryFragment";

    private FragmentRecoveryBinding binding;
    private String                  email;
    private boolean                 isEmailValid;
    private CountDownTimer          timer;
    private OnBackPressedCallback   backPressedCallback;
    private boolean                 isRequestProcessing = false;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentRecoveryBinding.inflate(inflater);
        initEmailField(binding.etEmail);
        setAppBarVisibility(false);
        binding.ivClose.setOnClickListener(view -> {
            close();
        });
        binding.btnRecover.setOnClickListener(view -> {
            onRecoverClick();
        });
        initBackPressedCallback();
        setupInsets();
        return binding.getRoot();
    }

    private void setupInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.cardRecovery, (view, insets) -> {

            int                    insetTop       = insets.getSystemWindowInsetTop();
            ViewGroup.LayoutParams cardRecoveryLp = binding.cardRecovery.getLayoutParams();
            if ( cardRecoveryLp instanceof ViewGroup.MarginLayoutParams ) {
                ((ViewGroup.MarginLayoutParams) cardRecoveryLp).topMargin = insetTop +
                        getResources().getDimensionPixelSize(R.dimen.size_16);
            }

            return insets;
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        setAppBarVisibility(false);
    }

    @Override
    public void onStop() {
        setAppBarVisibility(true);
        super.onStop();
    }

    @Override
    public void onDestroyView() {
        if ( timer != null ) {
            timer.cancel();
        }
        backPressedCallback.setEnabled(false);
        super.onDestroyView();
    }

    private void initBackPressedCallback() {
        backPressedCallback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                close();
            }
        };
        getActivity().getOnBackPressedDispatcher().addCallback(backPressedCallback);
    }

    private void onRecoverClick() {
        if ( email == null || email.isEmpty() ) {
            binding.etEmail.showErrorMessage(getResources().getString(R.string._required_field));
            return;
        }

        if ( isEmailValid && !isRequestProcessing ) {
            isRequestProcessing = true;
            ApolloProcessorKt.restorePasswordByMail(email, new GenericTarget<Boolean>() {
                @Override
                public void onSuccess(Boolean isSuccess) {
                    Log.wtf(TAG, "onSuccess");
                    emailSuccessSent();
                    isRequestProcessing = false;
                }

                @Override
                public void onError(String errorMessage, String errorCode, FieldsErrorsHolder errors) {
                    Log.wtf(TAG, "onRestoreError " + errors);
                    if (errors != null) {
                        Map<Fields, List<String>> errorsMap = errors.getErrorsMap();
                        if ( errorsMap != null && errorsMap.containsKey(Fields.EMAIL) ) {
                            binding.getRoot().post(() -> binding.etEmail.showErrorIfNeeded(errors, Fields.EMAIL));
                        }
                    }

                    error(getString(R.string.password_recovery_error));

                    isRequestProcessing = false;
                }

                @Override
                public void onFailure(@NonNull ApolloException e) {
                    Log.wtf(TAG, "onFailedRequest " + e);
                    error(e);
                    isRequestProcessing = false;
                }
            });
        }
    }

    private void onSecondRecoverClick() {
        if ( !isRequestProcessing ) {
            isRequestProcessing = true;
            ApolloProcessorKt.restorePasswordByMail(email, new GenericTarget<Boolean>() {
                @Override
                public void onSuccess(@Nullable Boolean aBoolean) {
                    Activity activity = getActivity();
                    if ( activity != null ) {
                        activity.runOnUiThread(() -> {
                            binding.tvRecoveryInfo.setText(getString(R.string.email_sent));
                            binding.tvResendEmailQuestion.setVisibility(View.GONE);
                            binding.tvResendEmail.setVisibility(View.GONE);

                        });
                    }

                    isRequestProcessing = false;
                }

                @Override
                public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                    error(errorMessage);
                    isRequestProcessing = false;
                }

                @Override
                public void onFailure(ApolloException e) {
                    error(e);
                    isRequestProcessing = false;
                }
            });
        }
    }

    private void emailSuccessSent() {
        Activity activity = getActivity();
        if ( activity != null ) {
            activity.runOnUiThread(() -> {
                GeneralTools.hideKeyboard(getActivity());
                binding.tvRecoveryInfo.setText(Html.fromHtml(getString(R.string.recovery_email_sent, email)), TextView.BufferType.SPANNABLE);
                binding.etEmail.setVisibility(View.GONE);
                binding.btnRecover.setBackgroundResource(R.drawable.btn_blue_bg);
                binding.btnRecover.setText(getString(R.string._close));
                binding.btnRecover.setOnClickListener(view -> close());
                binding.tvResendEmailQuestion.setVisibility(View.VISIBLE);
                binding.tvResendEmail.setVisibility(View.VISIBLE);
                binding.tvResendEmail.setOnClickListener(v -> onSecondRecoverClick());
            });
        }
    }

    private void error(String errorMessage) {
        if ( getActivity() != null ) {
            getActivity().runOnUiThread(() -> Toast.makeText(getContext(), errorMessage, Toast.LENGTH_LONG).show());
        }
    }

    private void error(ApolloException e) {
        if ( getActivity() != null ) {
            getActivity().runOnUiThread(() -> Toast.makeText(getContext(), e.getMessage(), Toast.LENGTH_LONG).show());
        }
    }

    private void initEmailField(TextField etEmail) {
        etEmail.setInputType(InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS);
        etEmail.subscribeToUpdates((data, valid) -> {
            if ( data.isEmpty() ) {
                etEmail.showErrorMessage(getResources().getString(R.string._required_field));
                etEmail.setForeground(null);
                isEmailValid = false;
            } else if ( !valid ) {
                etEmail.showErrorMessage(getResources().getString(R.string._incorrect_email));
                etEmail.setForeground(null);
                isEmailValid = false;
            } else {
                etEmail.hideErrorMessage();
                etEmail.setForeground(getResources().getDrawable(R.drawable.fg_text_field_transparent));
                email = data.trim();
                isEmailValid = true;
            }
        });
    }

    private void close() {
        if ( getView() != null && getActivity() != null ) {
            GeneralTools.hideKeyboard(getActivity());
            Navigation.findNavController(getView()).popBackStack(R.id.nav_home, false);
        }
    }

    private void setAppBarVisibility(boolean visible) {
        Activity activity = getActivity();
        if ( activity != null ) {
            View appBar = activity.findViewById(R.id.abl_appbar);
            if ( appBar != null ) {
                if ( visible ) {
                    appBar.setVisibility(View.VISIBLE);
                } else {
                    appBar.setVisibility(View.GONE);
                }
            }
        }
    }

}
