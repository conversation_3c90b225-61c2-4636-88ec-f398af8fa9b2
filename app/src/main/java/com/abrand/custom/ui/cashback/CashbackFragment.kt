package com.abrand.custom.ui.cashback

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Status
import com.abrand.custom.databinding.FragmentCashbackBinding
import com.abrand.custom.tools.readTextFromAsset
import com.abrand.custom.ui.activitymain.MainActivity

class CashbackFragment : Fragment() {
    private var binding: FragmentCashbackBinding? = null
    private val viewModel: CashbackViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?): View? {
        binding = FragmentCashbackBinding.inflate(inflater, container, false)

        setupInsets()
        observeViewModel()
        initWebView()

        return binding?.root
    }

    private fun setupInsets() {
        binding?.apply {
            ViewCompat.setOnApplyWindowInsetsListener(ll) { view, insets ->
                view.updatePadding(
                    top = insets.systemWindowInsetTop,
                    bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }

    private fun observeViewModel() {
        viewModel.cashbackPageLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    val html = it.data
                    html?.let {
                        binding?.webView?.loadDataWithBaseURL(ApoloConfig.BASE_URL, it,
                            "text/html", "UTF-8", "")
                    }
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(it.failure)
                    }
                }
                else -> {}
            }
        }
    }

    private fun initWebView() {
        val CSS_ASSET_FILE_NAME = "cashback.css"
        binding?.webView?.setBackgroundColor(Color.TRANSPARENT)
        binding?.webView?.settings?.javaScriptEnabled = true

        binding?.webView?.webViewClient = object : WebViewClient() {

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                val css = context?.readTextFromAsset(CSS_ASSET_FILE_NAME)
                val js = "var style = document.createElement('style'); style.innerHTML = `$css`; document.head.appendChild(style);"
                binding?.webView?.evaluateJavascript(js, null)
                binding?.webView?.visibility = View.VISIBLE
            }
        }
    }
}
