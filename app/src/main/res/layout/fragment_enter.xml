<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_gradient"
        android:clickable="true"
        android:focusable="true"
        android:focusableInTouchMode="true">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_title_bot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_begin="@dimen/enter_title_guideline_begin" />

        <TextView
            android:id="@+id/tv_choose_registration_bonus_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:text="@string/choose_registration_bonus"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@+id/gl_title_bot"
            app:layout_constraintLeft_toLeftOf="parent" />

        <LinearLayout
            android:id="@+id/container_registration_bonus_first"
            android:layout_width="96dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="38dp"
            android:orientation="vertical"
            android:paddingTop="18dp"
            android:paddingBottom="7dp"
            app:layout_constraintEnd_toStartOf="@+id/container_registration_bonus_second"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_choose_registration_bonus_title">

            <ImageView
                android:id="@+id/iv_registration_bonus_first"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center_horizontal" />

            <TextView
                android:id="@+id/tv_registration_bonus_first"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:gravity="center_horizontal"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/container_registration_bonus_second"
            android:layout_width="96dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="18dp"
            android:paddingBottom="7dp"
            app:layout_constraintEnd_toStartOf="@+id/container_registration_bonus_third"
            app:layout_constraintStart_toEndOf="@+id/container_registration_bonus_first"
            app:layout_constraintTop_toTopOf="@+id/container_registration_bonus_first">

            <ImageView
                android:id="@+id/iv_registration_bonus_second"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center_horizontal" />

            <TextView
                android:id="@+id/tv_registration_bonus_second"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:gravity="center_horizontal"
                android:textSize="12sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/container_registration_bonus_third"
            android:layout_width="96dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="18dp"
            android:paddingBottom="7dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/container_registration_bonus_second"
            app:layout_constraintTop_toTopOf="@+id/container_registration_bonus_first">

            <ImageView
                android:id="@+id/iv_registration_bonus_third"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center_horizontal" />

            <TextView
                android:id="@+id/tv_registration_bonus_third"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:gravity="center_horizontal"
                android:textSize="12sp" />
        </LinearLayout>

        <CheckBox
            android:id="@+id/cb_registration_bonus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/registration_without_bonus"
            android:textColor="#8DA1BD"
            android:textSize="12sp"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/container_registration_bonus_first" />

        <TextView
            android:id="@+id/tv_enter_registration_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:text="@string/_registration"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/gl_title_bot"
            app:layout_constraintLeft_toLeftOf="parent" />

        <TextView
            android:id="@+id/tv_enter_login_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:text="@string/_login"
            android:textColor="@color/white"
            android:textSize="24sp"
            app:layout_constraintBottom_toBottomOf="@+id/gl_title_bot"
            app:layout_constraintLeft_toLeftOf="parent" />

        <com.abrand.custom.ui.views.textfield.TextField
            android:id="@+id/et_enter_login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="24dp"
            android:layout_marginRight="16dp"
            app:hint="@string/_your_email"
            app:layout_constraintTop_toBottomOf="@+id/tv_enter_registration_title" />

        <com.abrand.custom.ui.views.textfield.TextField
            android:id="@+id/et_enter_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginRight="16dp"
            app:hint="@string/_password"
            app:layout_constraintTop_toBottomOf="@+id/et_enter_login" />

        <Button
            android:id="@+id/btn_biometric"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="16dp"
            android:background="@null"
            android:drawableStart="@drawable/ic_fingerprint"
            android:drawablePadding="8dp"
            android:text="@string/use_biometrics"
            android:textAllCaps="false"
            android:textColor="#0097EC"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/et_enter_password" />

        <TextView
            android:id="@+id/tv_enter_forget_pass"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="@string/forget_pass_"
            android:textColor="#0097EC"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_biometric" />

        <View
            android:id="@+id/v_padding"
            android:layout_width="match_parent"
            android:layout_height="16dp"
            android:visibility="gone"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_enter_forget_pass" />

        <Button
            android:id="@+id/btn_enter_go"
            style="@style/ButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:text="@string/to_register"
            app:layout_constraintTop_toBottomOf="@+id/v_padding" />

        <TextView
            android:id="@+id/tv_or_social_auth"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="@string/or_social_auth"
            android:textColor="#998DA1BD"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_enter_go" />

        <com.abrand.custom.ui.views.SocialLoginView
            android:id="@+id/view_social_login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="32dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_or_social_auth" />

        <View
            android:id="@+id/v_bottom_padding"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view_social_login" />

        <TextView
            android:id="@+id/tv_registration_rules"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@color/black"
            android:gravity="center"
            android:text="@string/registration_rules"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:ignore="SmallSp"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
