package com.abrand.custom.ui.tournamentinternal

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.data.entity.TournamentSingleResponse
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.network.retrySubscription
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException
import com.apollographql.apollo3.exception.ApolloNetworkException
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class TournamentsInternalViewModel : ViewModel() {

    val tournamentLiveData = MutableLiveData<TournamentSingleResponse>()
    val tournamentErrorLiveData = SingleLiveEvent<String>()
    val joinLiveData = MutableLiveData<TournamentSingleResponse>()
    val joinErrorLiveData = SingleLiveEvent<String?>()
    val tournamentLoadApolloExceptionLiveData = SingleLiveEvent<ApolloException?>()
    private val exceptionHandler = CoroutineExceptionHandler { _, e -> Log.e(TAG, e.toString()) }
    private val dispatcher = Dispatchers.Default + exceptionHandler

    fun loadById(id: Int) {
        ApolloProcessorKt.loadTournamentById(id, object: GenericTarget<TournamentsItem> {
            override fun onSuccess(t: TournamentsItem?) {
                t?.apply {
                    tournamentLiveData.postValue(TournamentSingleResponse(this))
                }
            }

            override fun onFailure(e: ApolloException?) {
                tournamentLoadApolloExceptionLiveData.postValue(e)
            }

            override fun onError(errorMessage: String?, errorCode: String?, fieldsErrors: FieldsErrorsHolder?) {
                tournamentErrorLiveData.postValue(errorMessage ?: "Load error")
            }

        }, viewModelScope)
    }

    fun joinTournament(id: Int){
        ApolloProcessorKt.joinTournament(id, object : GenericTarget<TournamentsItem> {

            override fun onSuccess(t: TournamentsItem?) {
                t?.apply {
                    joinLiveData.postValue(TournamentSingleResponse(this))
                }
            }

            override fun onFailure(e: ApolloException?) {
                tournamentLoadApolloExceptionLiveData.postValue(e)
            }

            override fun onError(errorMessage: String?, errorCode: String?, fieldsErrors: FieldsErrorsHolder?) {
                joinErrorLiveData.postValue(errorMessage)
            }

        }, viewModelScope)
        viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeTournament(id.toString()).retrySubscription().collect {
                    Log.d("ApolloSubscription", "Tournament subscription")
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "$TAG balanceSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    companion object {
        const val TAG = "TournamentsInternalVM"
    }
}
