package com.abrand.custom.presenter;

import androidx.recyclerview.widget.DiffUtil;

import com.abrand.custom.data.entity.GameListItem;

import java.util.List;
import java.util.Objects;

public class GameItemDiffCallback extends DiffUtil.Callback {
    private final List<GameListItem> oldList;
    private final List<GameListItem> newList;

    public GameItemDiffCallback(List<GameListItem> oldList, List<GameListItem> newList) {
        this.oldList = oldList;
        this.newList = newList;
    }

    @Override
    public int getOldListSize() {
        return oldList.size();
    }

    @Override
    public int getNewListSize() {
        return newList.size();
    }

    @Override
    public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
        return oldList.get(oldItemPosition).getId() == newList.get(newItemPosition).getId();
    }

    @Override
    public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
        GameListItem oldItem = oldList.get(oldItemPosition);
        GameListItem newItem = newList.get(newItemPosition);
        if ( oldItem.getItemType() == GameListItem.ItemType.GAME && newItem.getItemType() == GameListItem.ItemType.GAME ) {
            if ( oldItem.getGameItem() != null && newItem.getGameItem() != null ) {
                return Objects.equals(oldItem.getId(), newItem.getId()) && oldItem.getGameItem().isFavorite() == newItem.getGameItem().isFavorite();
            } else {
                return false;
            }
        } else {
            return Objects.equals(oldItem.getId(), newItem.getId());
        }
    }
}
