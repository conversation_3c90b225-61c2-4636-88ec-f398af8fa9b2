<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/root_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="48dp"
        android:paddingStart="16dp"
        android:paddingTop="8dp"
        android:paddingEnd="16dp"
        android:paddingBottom="8dp"
        tools:background="@drawable/bg_text_field">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@+id/iv_date_picker"
                android:layout_toLeftOf="@+id/iv_date_picker"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/hint"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#000"
                    android:textSize="14sp"
                    tools:text="Hint" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:textColor="#000"
                    android:textSize="14sp"
                    android:visibility="gone"
                    tools:text="Input data" />

            </LinearLayout>

            <ImageView
                android:id="@+id/iv_date_picker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:src="@drawable/ic_date_picker" />

        </RelativeLayout>

    </FrameLayout>

    <TextView
        android:id="@+id/error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        android:textAlignment="center"
        android:textColor="#ffffff"
        android:textFontWeight="400"
        android:textSize="11sp"
        android:visibility="gone"
        tools:text="error message"
        tools:visibility="visible" />
</LinearLayout>