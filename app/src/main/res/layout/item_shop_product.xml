<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/size_16"
    android:layout_marginTop="8dp"
    android:layout_marginEnd="@dimen/size_16"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/product_main"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/size_16"
        android:paddingBottom="@dimen/size_16">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="@dimen/size_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintEnd_toStartOf="@+id/iv_arrow_dropdown"
            app:layout_constraintStart_toEndOf="@+id/iv_icon"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_points"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:src="@drawable/ic_points"
            app:layout_constraintStart_toStartOf="@+id/tv_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_name" />

        <TextView
            android:id="@+id/tv_points"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            app:layout_constraintBottom_toBottomOf="@+id/iv_points"
            app:layout_constraintStart_toEndOf="@+id/iv_points"
            app:layout_constraintTop_toTopOf="@+id/iv_points" />

        <ImageView
            android:id="@+id/iv_arrow_dropdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/size_16"
            app:layout_constraintBottom_toBottomOf="@+id/tv_points"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_name" />

        <Button
            android:id="@+id/btn_buy"
            style="@style/ButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="@dimen/size_16"
            android:text="@string/shop_buy_with_points"
            app:layout_constraintTop_toBottomOf="@+id/tv_points" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btn_open_screen_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="@dimen/size_16"
            android:layout_marginEnd="@dimen/size_16"
            android:background="@drawable/bg_btn_product_info_container"
            app:layout_constraintTop_toBottomOf="@+id/tv_points">

            <ImageView
                android:id="@+id/iv_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_16"
                android:layout_marginTop="@dimen/size_16"
                android:src="@drawable/ic_shop_product_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_info_description"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginTop="@dimen/size_16"
                android:paddingEnd="@dimen/size_16"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/iv_info"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/btn_open_screen"
                style="@style/ButtonGreyStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_16"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="@dimen/size_16"
                app:layout_constraintTop_toBottomOf="@+id/tv_info_description" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/product_hide"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shop_card_hide_bg">

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
