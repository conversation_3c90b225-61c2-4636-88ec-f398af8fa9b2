<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/container_btn_save"
        android:layout_alignParentTop="true">

        <LinearLayout
            android:id="@+id/scroll_view_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gradient"
            android:orientation="vertical"
            android:paddingTop="?attr/actionBarSize">

            <androidx.compose.ui.platform.ComposeView
                android:id="@+id/compose_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="@dimen/activity_horizontal_margin"
                android:paddingEnd="@dimen/activity_horizontal_margin">

                <TextView
                    android:id="@+id/tv_profile"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="top"
                    android:lineSpacingExtra="4sp"
                    android:paddingBottom="24dp"
                    android:text="@string/_profile"
                    android:textAppearance="@style/CategoryTitle" />

                <com.abrand.custom.ui.views.textfield.TextField
                    android:id="@+id/et_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:hint="@string/_your_name" />

                <com.abrand.custom.ui.views.textfield.TextField
                    android:id="@+id/et_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:hint="@string/_your_email" />

                <LinearLayout
                    android:id="@+id/container_resend_email_confirmation"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_marginTop="21dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="@dimen/profile_ic_warning_size"
                        android:layout_height="@dimen/profile_ic_warning_size"
                        android:src="@drawable/ic_warning" />

                    <TextView
                        android:id="@+id/tv_resend_email_confirmation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/activity_horizontal_margin"
                        android:textColor="#8DA1BD"
                        android:textSize="@dimen/profile_email_confirmation_text_size" />

                </LinearLayout>

                <com.abrand.custom.ui.views.phonefield.PhoneField
                    android:id="@+id/et_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="30dp"
                    app:hint="@string/_phone_number" />

                <com.abrand.custom.ui.views.Switcher
                    android:id="@+id/gender"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginTop="16dp"
                    android:background="@drawable/bg_text_field" />

                <com.abrand.custom.ui.views.BirthDateField
                    android:id="@+id/et_birthdate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:hint="@string/_birthdate" />

                <com.abrand.custom.ui.views.StateSwitchView
                    android:id="@+id/switch_biometric"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp" />

                <Button
                    android:id="@+id/btn_change_password"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginLeft="10dp"
                    android:background="@null"
                    android:drawablePadding="10dp"
                    android:text="@string/change_password"
                    android:textAllCaps="false"
                    android:textColor="#0097EC"
                    android:textSize="16sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/container_change_password"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="#1A8DA1BD"
                android:orientation="vertical"
                android:paddingStart="@dimen/activity_horizontal_margin"
                android:paddingEnd="@dimen/activity_horizontal_margin">

                <com.abrand.custom.ui.views.textfield.TextField
                    android:id="@+id/et_old_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:hint="@string/profile_old_password" />

                <com.abrand.custom.ui.views.textfield.TextField
                    android:id="@+id/et_new_password"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:hint="@string/profile_new_password" />

                <com.abrand.custom.ui.views.textfield.TextField
                    android:id="@+id/et_password_confirmation"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:hint="@string/profile_password_confirmation" />

                <TextView
                    android:id="@+id/tv_password_rule_characters_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawableStart="@drawable/ic_done_gray"
                    android:drawableLeft="@drawable/ic_done_gray"
                    android:drawablePadding="15dp"
                    android:text="@string/password_rule_characters_count"
                    android:textColor="@color/password_rule_not_observed" />

                <TextView
                    android:id="@+id/tv_password_rule_new_old_different"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawableStart="@drawable/ic_done_gray"
                    android:drawableLeft="@drawable/ic_done_gray"
                    android:drawablePadding="15dp"
                    android:text="@string/password_rule_new_old_different"
                    android:textColor="@color/password_rule_not_observed" />

                <TextView
                    android:id="@+id/tv_password_rule_equals_passwords"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:drawableStart="@drawable/ic_done_gray"
                    android:drawableLeft="@drawable/ic_done_gray"
                    android:drawablePadding="15dp"
                    android:text="@string/password_rule_equals_passwords"
                    android:textColor="@color/password_rule_not_observed" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/container_btn_save"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_alignParentBottom="true"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/btn_bg_disable"
        android:clickable="false"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackground"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_btn_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_marginRight="10dp"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_done" />

        <TextView
            android:id="@+id/tv_btn_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/changes_saved"
            android:textColor="#FFFFFF" />

    </LinearLayout>

</RelativeLayout>
