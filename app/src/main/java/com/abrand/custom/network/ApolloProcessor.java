package com.abrand.custom.network;

import androidx.annotation.NonNull;

import com.abrand.custom.data.Constants;
import com.abrand.custom.data.Settings;
import com.abrand.custom.data.entity.LocalGameItem;
import com.abrand.custom.fragment.GameList;
import com.abrand.custom.interfaces.BaseTargetOnError;
import com.abrand.custom.interfaces.LoadTarget;
import com.abrand.custom.interfaces.ViewerQuery;
import com.abrand.custom.presenter.FieldsErrorsHolder;
import com.apollographql.apollo3.api.ApolloResponse;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ApolloProcessor {
    private static final String TAG = ApolloProcessor.class.getSimpleName();

    //todo remove after rewrite NotLoggedTest.java to Kt
    //unused temporary left as example
//    public static void loadGamesByType(GameTypeField type, int loadAmount, int offset,
//                                       GameOrderField gameOrderField, OrderDirection orderDirection,
//                                       LoadTarget loadTarget) {
//        Integer loadAmountInt = null;
//        GameOrder gameOrder = null;
//
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//
//        if ( gameOrderField != null && orderDirection != null ) {
//            gameOrder = new GameOrder(gameOrderField, orderDirection);
//        }
//
//        GetGamesByTypeQuery query = new GetGamesByTypeQuery(type, Input.optional(loadAmountInt),
//                Input.fromNullable(offset), Input.optional(gameOrder));
//
//        AApp.getApolloClient().query(query)
//                .enqueue(new ApolloCall.Callback<GetGamesByTypeQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetGamesByTypeQuery.Data> response) {
//                        if ( response.getData() != null
//                                && response.getData().getGames() != null ) {
//                            //noinspection ConstantConditions
//                            processLoad(response.getData().getGames().getFragments().getGames(), offset, loadTarget);
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

    //todo remove after rewrite *Test.java to Kt
//    public static void loadByAttribute(GameAttributeType attribute, int loadAmount, int offset,
//                                       GameOrderField gameOrderField, OrderDirection orderDirection,
//                                       LoadTarget loadTarget) {
//        Integer loadAmountInt = null;
//        GameOrder gameOrder = null;
//
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//
//        if ( gameOrderField != null && orderDirection != null ) {
//            gameOrder = new GameOrder(gameOrderField, orderDirection);
//        }
//
//        GetGamesByAttributeQuery query = new GetGamesByAttributeQuery(attribute, Input.optional(loadAmountInt),
//                Input.fromNullable(offset), Input.optional(gameOrder));
//
//        AApp.getApolloClient().query(query)
//                .enqueue(new ApolloCall.Callback<GetGamesByAttributeQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetGamesByAttributeQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getGames() != null ) {
//                            //noinspection ConstantConditions
//                            processLoad(response.getData().getGames().getFragments().getGames(), offset, loadTarget);
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

    //todo remove after rewrite *Test.java to Kt
//    public static void loadFavouriteGames(GameOrderField gameOrderField, OrderDirection orderDirection,
//                                          int loadAmount, int offset,
//                                          LoadTarget loadTarget) {
//        GameListOrder gameOrder = null;
//        if ( gameOrderField != null && orderDirection != null ) {
//            gameOrder = new GameListOrder(gameOrderField, orderDirection);
//        }
//
//        Integer loadAmountInt = null;
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//
//        AApp.getApolloClient().query(new GetFavouriteGamesQuery(loadAmountInt, offset, Input.optional(gameOrder)))
//                .enqueue(new ApolloCall.Callback<GetFavouriteGamesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetFavouriteGamesQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getGameListFavourites() != null ) {
//                            //noinspection ConstantConditions
//                            processLoad(response.getData().getGameListFavourites().getFragments().getGameList(), offset, loadTarget);
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

//    public static void loadTournamentsByStatus(List<TournamentStatus> status,int offset, int loadAmount, LoadTournament loadTarget) {
//        Integer loadAmountInt = null;
//        Integer offsetInt = null;
//
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//        if ( offset > 0 ) {
//            offsetInt = offset;
//        }
//
//        GetTournamentsByStatusQuery query = new GetTournamentsByStatusQuery(status,Input.optional(offsetInt),Input.optional(loadAmountInt));
//        AApp.getApolloClient().query(query)
//                .enqueue(new ApolloCall.Callback<GetTournamentsByStatusQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetTournamentsByStatusQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getTournaments() != null ) {
//                            GetTournamentsByStatusQuery.Tournaments tournaments = response.getData().getTournaments();
//                            if ( tournaments != null ) {
//                                loadTarget.onSuccess(tournaments.getItems(), tournaments.getTotalCount(), offset);
//                            } else {
//                                loadTarget.onSuccess(new ArrayList<>(), 0, offset);
//                            }
//
//                        } else {
//                            processError(response, loadTarget);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

//    public static void loadTournamentById(int id, LoadSingleTournament loadTarget) {
//
//        GetTournamentByIdQuery query = new GetTournamentByIdQuery(id);
//        AApp.getApolloClient().query(query)
//                .enqueue(new ApolloCall.Callback<GetTournamentByIdQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetTournamentByIdQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getTournament() != null ) {
//                            GetTournamentByIdQuery.Tournament tournament = response.getData().getTournament();
//                            if ( tournament != null ) {
//                                loadTarget.onSuccess(tournament.getFragments().getFragmentFullTournamentsItem());
//                            } else {
//                                loadTarget.onSuccess(null);
//                            }
//
//                        } else {
//                            processError(response, loadTarget);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

//    public static void joinTournament(int tournamentId, LoadSingleTournament loadTarget) {
//        AApp.getApolloClient().mutate(new JoinTournamentMutation(tournamentId))
//                .enqueue(new ApolloCall.Callback<JoinTournamentMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<JoinTournamentMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            loadTarget.onSuccess(response.getData().getJoinTournament().getFragments().getFragmentFullTournamentsItem());
//                        } else {
//                            processError(response, loadTarget);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

//    public static void loadSlotGames(int loadAmount, int offset, GameListOrderField gameOrderField,
//                                        OrderDirection orderDirection, LoadTarget loadTarget) {
//        Integer loadAmountInt = null;
//        GameListOrder gameOrder = null;
//
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//
//        if ( gameOrderField != null && orderDirection != null ) {
//            gameOrder = new GameListOrder(gameOrderField, orderDirection);
//        }
//
//        GetSlotGamesQuery query = new GetSlotGamesQuery(loadAmountInt, offset, Input.optional(gameOrder));
//
//        AApp.getApolloClient().query(query)
//                .enqueue(new ApolloCall.Callback<GetSlotGamesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetSlotGamesQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getGameListSlots() != null ) {
//                            //noinspection ConstantConditions
//                            processLoad(response.getData().getGameListSlots().getFragments().getGameList(), offset, loadTarget);
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

//    public static void loadTableGames(int loadAmount, int offset, GameListOrderField gameOrderField,
//                                     OrderDirection orderDirection, LoadTarget loadTarget) {
//        Integer loadAmountInt = null;
//        GameListOrder gameOrder = null;
//
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//
//        if ( gameOrderField != null && orderDirection != null ) {
//            gameOrder = new GameListOrder(gameOrderField, orderDirection);
//        }
//
//        GetTableGamesQuery query = new GetTableGamesQuery(loadAmountInt, offset, Input.optional(gameOrder));
//
//        AApp.getApolloClient().query(query)
//                .enqueue(new ApolloCall.Callback<GetTableGamesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetTableGamesQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getGameListTable() != null ) {
//                            //noinspection ConstantConditions
//                            processLoad(response.getData().getGameListTable().getFragments().getGameList(), offset, loadTarget);
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

    // TODO: 20.09.2022 remove after check usages 
//    public static void loadPopularGames(int loadAmount, int offset, GameListOrderField gameOrderField,
//                                        OrderDirection orderDirection, LoadTarget loadTarget) {
//        Integer loadAmountInt = null;
//        GameListOrder gameOrder = null;
//
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//
//        if ( gameOrderField != null && orderDirection != null ) {
//            gameOrder = new GameListOrder(gameOrderField, orderDirection);
//        }
//
//        GetPopularGamesQuery query = new GetPopularGamesQuery(loadAmountInt, offset, Input.optional(gameOrder));
//
//        AApp.getApolloClient().query(query)
//                .enqueue(new ApolloCall.Callback<GetPopularGamesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetPopularGamesQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getGameListPopular() != null ) {
//                            //noinspection ConstantConditions
//                            processLoad(response.getData().getGameListPopular().getFragments().getGameList(), offset, loadTarget);
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

//    public static void loadNewGames(int loadAmount, int offset, GameListOrderField gameOrderField,
//                                        OrderDirection orderDirection, LoadTarget loadTarget) {
//        Integer loadAmountInt = null;
//        GameListOrder gameOrder = null;
//
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//
//        if ( gameOrderField != null && orderDirection != null ) {
//            gameOrder = new GameListOrder(gameOrderField, orderDirection);
//        }
//
//        GetNewGamesQuery query = new GetNewGamesQuery(loadAmountInt, offset, Input.optional(gameOrder));
//
//        AApp.getApolloClient().query(query)
//                .enqueue(new ApolloCall.Callback<GetNewGamesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetNewGamesQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getGameListNew() != null ) {
//                            //noinspection ConstantConditions
//                            processLoad(response.getData().getGameListNew().getFragments().getGameList(), offset, loadTarget);
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

    //this is replacement method to isGameFavourite(int gameId, GenericTarget<Boolean> target)
    //todo remove after rewrite *Test.java to Kt
//    public static void getGameById(int gameId, GenericTarget<LocalGameItem> target) {
//        AApp.getApolloClient().query(new GetGameByIdQuery(gameId))
//                .enqueue(new ApolloCall.Callback<GetGameByIdQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetGameByIdQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors()
//                                && response.getData().getGameById() != null ) {
//                            target.onSuccess(new LocalGameItem(response.getData().getGameById()));
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

    //todo remove after rewrite *Test.java to Kt
//    public static void loadFavouriteGames(GameListOrderField gameOrderField, OrderDirection orderDirection,
//                                          int loadAmount, int offset,
//                                          LoadTarget loadTarget) {
//
//        GameListOrder gameOrder = null;
//        if ( gameOrderField != null && orderDirection != null ) {
//            gameOrder = new GameListOrder(gameOrderField, orderDirection);
//        }
//
//        Integer loadAmountInt = null;
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//
//        AApp.getApolloClient().query(new GetFavouriteGamesQuery(loadAmountInt, offset, Input.optional(gameOrder)))
//                .enqueue(new ApolloCall.Callback<GetFavouriteGamesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetFavouriteGamesQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getGameListFavourites() != null ) {
//                            //noinspection ConstantConditions
//                            processLoad(response.getData().getGameListFavourites().getFragments().getGameList(), offset, loadTarget);
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

//    public static void loadGamesByName(String name, int loadAmount, int offset, LoadTarget loadTarget) {
//        Integer loadAmountInt = null;
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//
//        AApp.getApolloClient().query(new GetGamesByNameQuery(name, loadAmountInt, offset))
//                .enqueue(new ApolloCall.Callback<GetGamesByNameQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetGamesByNameQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getGameListByName() != null ) {
//                            //noinspection ConstantConditions
//                            processLoad(response.getData().getGameListByName().getFragments().getGameList(), offset, loadTarget);
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        loadTarget.onFailure(e);
//                    }
//                });
//    }

//    public static void getGameByUrl(String gameUrl, GenericTarget<GameThumb> target) {
//        AApp.getApolloClient().query(new GetGameByUrlQuery(gameUrl))
//                .enqueue(new ApolloCall.Callback<GetGameByUrlQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetGameByUrlQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors()) {
//                            GetGameByUrlQuery.GameByUrl gameByUrl = response.getData().getGameByUrl();
//                            if ( gameByUrl != null ) {
//                                target.onSuccess(gameByUrl.getFragments().getGameThumb());
//                            } else {
//                                target.onSuccess(null);
//                            }
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void hasGameFreeSpins(String gameUrl, GenericTarget<Boolean> target) {
//        AApp.getApolloClient().query(new HasGameFreeSpinsQuery(gameUrl))
//                .enqueue(new ApolloCall.Callback<HasGameFreeSpinsQuery.Data>() {
//                    @Override
//                    public void onResponse(@NonNull Response<HasGameFreeSpinsQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors()) {
//                            HasGameFreeSpinsQuery.HasGameFreeSpins hasGameFreeSpins = response.getData().getHasGameFreeSpins();
//                            if ( hasGameFreeSpins != null ) {
//                                target.onSuccess(hasGameFreeSpins.getFree_spins());
//                            } else {
//                                target.onSuccess(null);
//                            }
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NonNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getDrawerRegistrationBanner(GetRegistrationBannerTarget target) {
//        AApp.getApolloClient().query(new GetDrawerBannerQuery())
//                .enqueue(new ApolloCall.Callback<GetDrawerBannerQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetDrawerBannerQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getBanners());
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getSlides(GetSlidesTarget target) {
//        AApp.getApolloClient().query(new GetSlidesQuery())
//                .enqueue(new ApolloCall.Callback<GetSlidesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetSlidesQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors()
//                                && response.getData().getSlides() != null ) {
//                            List<Banner> banners = new ArrayList<>();
//                            //noinspection ConstantConditions
//                            for ( GetSlidesQuery.Slide slide : response.data().getSlides() ) {
//                                if ( !TextUtils.isEmpty(slide.getImage()) ) {
//                                    banners.add(new Banner(slide.getImage(), (slide.getHtml() != null ? slide.getHtml() : ""), slide.getUrl()));
//                                }
//                            }
//
//                            target.onSuccess(banners);
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void obtainDemoGameUrl(String shortUrl, GenericTarget<String> target) {
//        AApp.getApolloClient().query(new GetDemoGameUrlQuery(shortUrl, ApoloConfig.HOST))
//                .enqueue(new ApolloCall.Callback<GetDemoGameUrlQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetDemoGameUrlQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getDemoGameUrl() != null) {
//                            target.onSuccess(response.getData().getDemoGameUrl().getUrl());
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void obtainGameUrl(String shortUrl, String refCode, GenericTarget<String> target) {
//        User.State userState = Settings.get().getUserState();
//        if ( User.State.ORGANIC.equals(userState) ) {
//            return;
//        }
//
//        if ( TextUtils.isEmpty(refCode) ) {
//            refCode = null;
//        }
//
//        AApp.getApolloClient().query(new GetGameUrlQuery(shortUrl, ApoloConfig.HOST, Input.optional(refCode)))
//                .enqueue(new ApolloCall.Callback<GetGameUrlQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetGameUrlQuery.Data> response) {
//                        if ( response.getData() != null && response.getData().getGameUrl() != null) {
//                            target.onSuccess(response.getData().getGameUrl().getUrl());
//                        } else {
//                            onFailure(new ApolloException("Illegal server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getReCaptcha(CaptchaForm captchaForm, GenericTarget<GetReCaptchaQuery.ReCaptcha> target) {
//        GetReCaptchaQuery query = new GetReCaptchaQuery(captchaForm, Input.optional(ApoloConfig.RECAPTCHA_HOST));
//
//        AApp.getApolloClient().query(query)
//                .enqueue(new ApolloCall.Callback<GetReCaptchaQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetReCaptchaQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getReCaptcha());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getRegisterBonuses(GetRegisterBonusesTarget target) {
//        AApp.getApolloClient().query(new GetRegisterBonusesQuery())
//                .enqueue(new ApolloCall.Callback<GetRegisterBonusesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetRegisterBonusesQuery.Data> response) {
//                        if ( response.getData() != null ) {
//                            target.onSuccess(response.getData().getRegisterBonuses() != null
//                                    ? response.getData().getRegisterBonuses() : new ArrayList<>());
//                        } else {
//                            onFailure(new ApolloException("Incorrect server response"));
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void register(@NonNull String email, @NonNull String password, @NonNull String language,
//                                int registrationBonusId, String gCaptchaResponse, String adjustId,
//                                String pushSubscriptionData, GenericTarget<AuthResponse> target) {
//        RegistrationMutation mutation;
//        String gCaptchaHost = ApoloConfig.RECAPTCHA_HOST;
//        Integer registrationBonusIdInt = null;
//
//        Locale locale = Locale.Companion.safeValueOf(language);
//        if ( locale != Locale.EN || locale != Locale.ZH ) {
//            locale = Locale.RU;
//        }
//
//        if ( TextUtils.isEmpty(gCaptchaResponse) ) {
//            gCaptchaResponse = null;
//        }
//
//        if ( registrationBonusId != -1 && registrationBonusId != 0 ) {
//            registrationBonusIdInt = registrationBonusId;
//        }
//
//        mutation = new RegistrationMutation(email, password, locale,
//                Input.optional(gCaptchaResponse), Input.optional(gCaptchaHost), Input.optional(registrationBonusIdInt),
//                Input.optional(new RegistrationPayload(Input.absent(),
//                        Input.optional(AffDataGenerator.Companion.getAffData(adjustId)),
//                        Input.absent(), Input.absent(), Input.optional(pushSubscriptionData))));
//
//        AApp.getApolloClient().mutate(mutation)
//                .enqueue(new ApolloCall.Callback<RegistrationMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<RegistrationMutation.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    AuthResponse authResponse = new AuthResponse(response.getData().getRegisterUserByEmail().getViewer().getId(), email);
//                    target.onSuccess(authResponse);
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void login(@NonNull String email, @NonNull String password,
//                             @NonNull String gCaptchaResponse, String adjustId,
//                             String pushSubscriptionData, GenericTarget<AuthResponse> target) {
//        String gCaptchaHost = ApoloConfig.RECAPTCHA_HOST;
//        AuthMutation mutation;
//
//        if ( TextUtils.isEmpty(gCaptchaResponse) ) {
//            gCaptchaResponse = null;
//        }
//
//        mutation = new AuthMutation(email, password,
//                Input.optional(gCaptchaResponse), Input.optional(gCaptchaHost),
//                Input.optional(AffDataGenerator.Companion.getAffData(adjustId)),
//                Input.optional(pushSubscriptionData));
//
//        AApp.getApolloClient().mutate(mutation)
//                .enqueue(new ApolloCall.Callback<AuthMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<AuthMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            AuthResponse authResponse = new AuthResponse(response.getData().getAuth().getViewer().getId(), email);
//                            target.onSuccess(authResponse);
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void socialLogin(@NonNull String token, @NonNull String language, GenericTarget<AuthResponse> target) {
//        Locale locale = Locale.Companion.safeValueOf(language);
//        if ( locale != Locale.EN || locale != Locale.ZH ) {
//            locale = Locale.RU;
//        }
//
//        SocialAuthMutation mutation = new SocialAuthMutation(ApoloConfig.HOST, token, locale);
//        AApp.getApolloClient().mutate(mutation).enqueue(new ApolloCall.Callback<SocialAuthMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<SocialAuthMutation.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    SocialAuthMutation.Viewer viewer = response.getData().getLogin4playAuth().getViewer();
//                    AuthResponse authResponse = new AuthResponse(viewer.getId(), viewer.getProfile().getEmail());
//                    target.onSuccess(authResponse);
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void autoLogin(String loginToken, String refCode, String adjustId, GenericTarget<AutologinMutation.Viewer> target) {
//        if ( TextUtils.isEmpty(refCode) ) {
//            refCode = null;
//        }
//
//        AApp.getApolloClient().mutate(new AutologinMutation(loginToken, Input.optional(refCode),
//                Input.optional(AffDataGenerator.Companion.getAffData(adjustId))))
//                .enqueue(new ApolloCall.Callback<AutologinMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<AutologinMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getAutologin().getViewer());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void updateMobileAppSubscription(String pushSubscriptionData, GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new UpdateMobileAppSubscriptionMutation(pushSubscriptionData))
//                .enqueue(new ApolloCall.Callback<UpdateMobileAppSubscriptionMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<UpdateMobileAppSubscriptionMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getUpdateMobileAppSubscription());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void logout(GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new UserLogoutMutation())
//                .enqueue(new ApolloCall.Callback<UserLogoutMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<UserLogoutMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            boolean userLogout = response.getData().getUserLogout() != null ? response.getData().getUserLogout() : true;
//                            if ( userLogout ) {
//                                Settings.get().setUserId("");
//                                Settings.get().setUserName("");
//                                FirebaseCrashlytics.getInstance().setUserId("");
//                            }
//                            target.onSuccess(userLogout);
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void restorePasswordByMail(@NonNull String email, @NonNull GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new ResetPassMutation(email))
//                .enqueue(new ApolloCall.Callback<ResetPassMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<ResetPassMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getUserSendResetPasswordEmail());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getInitialViewerData(GetInitialViewerDataTarget target) {
//        AApp.getApolloClient().query(new GetInitialViewerDataQuery())
//                .enqueue(new ApolloCall.Callback<GetInitialViewerDataQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetInitialViewerDataQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            GetInitialViewerDataQuery.Viewer viewer =
//                                    response.getData().getViewer();
//                            if ( viewer != null ) {
//                                target.onSuccess(viewer);
//                            } else {
//                                processEmptyViewer(target);
//                            }
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    static <T extends Operation.Data> ApolloSubscriptionCall<T> subscribe(Subscription subscription,
//                                                                          SubscriptionCenter.SubscriptionCallback<T> callback) {
//        ApolloSubscriptionCall<T> call = AApp.getApolloClient().subscribe(subscription);
//
//        call.execute(
//                new ApolloSubscriptionCall.Callback<T>() {
//                    @Override
//                    public void onResponse(@NotNull Response<T> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            callback.onUpdate(response.getData());
//                        } else {
//                            callback.onFail(false);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        Log.d(TAG, subscription.getClass().getSimpleName() + " subscription onFailure: " + e.getLocalizedMessage());
//                        callback.onFail(true);
//                    }
//
//                    @Override
//                    public void onCompleted() {
//                        callback.onFail(false);
//                    }
//
//                    @Override
//                    public void onTerminated() {
//                        Log.w(TAG, subscription.getClass().getSimpleName() + " subscription onTerminated()");
//                        callback.onFail(false);
//                    }
//
//                    @Override
//                    public void onConnected() {
//                        //Successful connect
//                    }
//                }
//        );
//
//        return call;
//    }

//    public static void getPaymentUrl(PaymentUrlDirection direction, String refCode, GetUrlTarget target) {
//        if ( TextUtils.isEmpty(refCode) ) {
//            refCode = null;
//        }
//
//        AApp.getApolloClient().query(new GetPaymentUrlQuery(ApoloConfig.HOST, direction, Input.optional(refCode)))
//                .enqueue(new ApolloCall.Callback<GetPaymentUrlQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetPaymentUrlQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            if ( response.getData().getViewer() != null
//                                    && response.getData().getViewer().getPaymentUrl() != null ) {
//                                target.onSuccess(response.getData().getViewer().getPaymentUrl().getUrl());
//                            } else {
//                                processEmptyViewer(target);
//                            }
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getProfile(GetProfileTarget target) {
//        AApp.getApolloClient().query(new GetProfileQuery())
//                .enqueue(new ApolloCall.Callback<GetProfileQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetProfileQuery.Data> response) {
//                        if (response.getData() != null && !response.hasErrors()) {
//                            GetProfileQuery.Viewer viewer = response.getData().getViewer();
//                            if (viewer != null) {
//                                target.onSuccess(viewer.getProfile());
//                            } else {
//                                processEmptyViewer(target);
//                            }
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getTransactions(TransactionsTarget target) {
//        AApp.getApolloClient().query(new GetTransactionsQuery())
//                .enqueue(new ApolloCall.Callback<GetTransactionsQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetTransactionsQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            GetTransactionsQuery.Viewer viewer = response.getData().getViewer();
//                            if ( viewer != null ) {
//                                target.onSuccess(viewer.getTransactions());
//                            } else {
//                                processEmptyViewer(target);
//                            }
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    static void userChangePassword(@NonNull String oldPassword,
//                                          @NonNull String newPassword, GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new ChangePasswordMutation(oldPassword, newPassword))
//                .enqueue(new ApolloCall.Callback<ChangePasswordMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<ChangePasswordMutation.Data> response) {
//                if (response.getData() != null && !response.hasErrors()) {
//                    target.onSuccess(response.getData().getUserChangePassword());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void userChangeProfile(String userName, String phone, Gender gender,
//                                         String birthday, String gCaptchaResponse, GenericTarget<Boolean> target) {
//        String gCaptchaHost = ApoloConfig.RECAPTCHA_HOST;
//        ChangeProfileMutation mutation;
//
//        if (TextUtils.isEmpty(userName)) {
//            userName = null;
//        }
//        if (TextUtils.isEmpty(phone)) {
//            phone = null;
//        }
//        if (gender == null || gender == Gender.UNKNOWN__) {
//            gender = null;
//        }
//        if (TextUtils.isEmpty(birthday)) {
//            birthday = null;
//        }
//
//        if ( TextUtils.isEmpty(gCaptchaResponse) ) {
//            gCaptchaResponse = null;
//        }
//        mutation = new ChangeProfileMutation(Input.optional(userName), Input.optional(phone),
//                Input.optional(gender), Input.optional(birthday),
//                Input.optional(gCaptchaResponse), Input.optional(gCaptchaHost));
//
//        AApp.getApolloClient().mutate(mutation).enqueue(new ApolloCall.Callback<ChangeProfileMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<ChangeProfileMutation.Data> response) {
//                if (response.getData() != null && !response.hasErrors()) {
//                    target.onSuccess(response.getData().getUserChangeProfile());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void requestEmailConfirmation(GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new RequestEmailConfirmationMutation())
//                .enqueue(new ApolloCall.Callback<RequestEmailConfirmationMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<RequestEmailConfirmationMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getRequestEmailConfirmation());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void makeRebill(int sum, GenericTarget<String> target) {
//        AApp.getApolloClient().mutate(new MakeRebillMutation(Input.optional(sum), ApoloConfig.HOST))
//                .enqueue(new ApolloCall.Callback<MakeRebillMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<MakeRebillMutation.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    target.onSuccess(response.getData().getMakeRebill());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void getFastPaymentUrl(int sum, String refCode, GetFastPaymentUrlTarget target) {
//        if ( TextUtils.isEmpty(refCode) ) {
//            refCode = null;
//        }
//
//        AApp.getApolloClient().query(new GetFastPaymentUrlQuery(Input.optional(sum), ApoloConfig.HOST, Input.optional(refCode)))
//                .enqueue(new ApolloCall.Callback<GetFastPaymentUrlQuery.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<GetFastPaymentUrlQuery.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    GetFastPaymentUrlQuery.Viewer viewer = response.getData().getViewer();
//                    if ( viewer != null && viewer.getOneClickUrl() != null ) {
//                        target.onSuccess(viewer.getOneClickUrl());
//                    } else {
//                        processEmptyViewer(target);
//                    }
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void getFastClickPaymentSystem(FastClickPaymentSystemTarget target) {
//        AApp.getApolloClient().query(new GetFastClickPaymentSystemQuery())
//                .enqueue(new ApolloCall.Callback<GetFastClickPaymentSystemQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetFastClickPaymentSystemQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            GetFastClickPaymentSystemQuery.Viewer viewer = response.getData().getViewer();
//                            if ( viewer != null ) {
//                                target.onSuccess(viewer.getFastClickPaymentSystem());
//                            } else {
//                                processEmptyViewer(target);
//                            }
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getCurrentBonuses(GenericTarget<List<GetCurrentBonusesQuery.CurrentBonuse>> target) {
//        AApp.getApolloClient().query(new GetCurrentBonusesQuery())
//                .enqueue(new ApolloCall.Callback<GetCurrentBonusesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetCurrentBonusesQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getCurrentBonuses());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getBonuses(GenericTarget<List<GetBonusesQuery.Item>> target) {
//        AApp.getApolloClient().query(new GetBonusesQuery())
//                .enqueue(new ApolloCall.Callback<GetBonusesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetBonusesQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            GetBonusesQuery.Bonuses bonuses = response.getData().getBonuses();
//                            if ( bonuses != null && bonuses.getItems() != null ) {
//                                target.onSuccess(bonuses.getItems());
//                            } else {
//                                target.onSuccess(null);
//                            }
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void bonusActivate(String bonusId, String promoCode, GenericTarget<Integer> target) {
//        BonusActivateMutation mutation;
//
//        if ( TextUtils.isEmpty(promoCode) ) {
//            promoCode = null;
//        }
//        mutation = new BonusActivateMutation(bonusId, Input.optional(promoCode));
//
//        AApp.getApolloClient().mutate(mutation)
//                .enqueue(new ApolloCall.Callback<BonusActivateMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<BonusActivateMutation.Data> response) {
//                if ( !response.hasErrors() && response.getData() != null
//                        && response.getData().getBonusActivate() != null ) {
//                    target.onSuccess(response.getData().getBonusActivate().getId());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void bonusDeactivate(String bonusId, GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new BonusDeactivateMutation(bonusId))
//                .enqueue(new ApolloCall.Callback<BonusDeactivateMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<BonusDeactivateMutation.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    target.onSuccess(response.getData().getBonusDeactivate());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void bonusActivateWithHash(String bonusId, String userId, String hash,
//                                             GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new BonusActivateWithHashMutation(bonusId, userId, hash))
//                .enqueue(new ApolloCall.Callback<BonusActivateWithHashMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<BonusActivateWithHashMutation.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    target.onSuccess(response.getData().getBonusActivateWithHash());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void bonusReceiveWithHash(String bonusId, String userId, String hash,
//                                            GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new BonusReceiveWithHashMutation(bonusId, userId, hash))
//                .enqueue(new ApolloCall.Callback<BonusReceiveWithHashMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<BonusReceiveWithHashMutation.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    target.onSuccess(response.getData().getBonusReceiveWithHash());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void addGameToFavourites(int gameId, GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new AddGameToFavouritesMutation(gameId))
//                .enqueue(new ApolloCall.Callback<AddGameToFavouritesMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<AddGameToFavouritesMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getAddGameToFavourites().isFavourite());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void removeGameFromFavourites(int gameId, GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new RemoveGameFromFavouritesMutation(gameId))
//                .enqueue(new ApolloCall.Callback<RemoveGameFromFavouritesMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<RemoveGameFromFavouritesMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getRemoveGameFromFavourites().isFavourite());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getSupportConfig(GenericTarget<GetSupportConfigQuery.YhelperConfig> target) {
//        AApp.getApolloClient().query(new GetSupportConfigQuery(ApoloConfig.HOST))
//                .enqueue(new ApolloCall.Callback<GetSupportConfigQuery.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<GetSupportConfigQuery.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    target.onSuccess(response.getData().getYhelperConfig());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void getLoyaltyStatuses(GenericTarget<List<GetLoyaltyStatusesQuery.LoyaltyStatus>> target) {
//        AApp.getApolloClient().query(new GetLoyaltyStatusesQuery())
//                .enqueue(new ApolloCall.Callback<GetLoyaltyStatusesQuery.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<GetLoyaltyStatusesQuery.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    target.onSuccess(response.getData().getLoyaltyStatuses());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void getExchangePointsData(GenericTarget<GetExchangePointsDataQuery.Viewer> target) {
//        AApp.getApolloClient().query(new GetExchangePointsDataQuery())
//                .enqueue(new ApolloCall.Callback<GetExchangePointsDataQuery.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<GetExchangePointsDataQuery.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    target.onSuccess(response.getData().getViewer());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void loyaltyPointsExchange(int points, GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new LoyaltyPointsExchangeMutation(points))
//                .enqueue(new ApolloCall.Callback<LoyaltyPointsExchangeMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<LoyaltyPointsExchangeMutation.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    target.onSuccess(response.getData().getLoyaltyPointsExchange());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void getNews(int offset, int loadAmount, GetNewsTarget target) {
//        Integer loadAmountInt = null;
//
//        if ( loadAmount > 0 ) {
//            loadAmountInt = loadAmount;
//        }
//
//        AApp.getApolloClient().query(new GetNewsQuery(Input.optional(offset), Input.optional(loadAmountInt)))
//                .enqueue(new ApolloCall.Callback<GetNewsQuery.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<GetNewsQuery.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    GetNewsQuery.News news = response.getData().getNews();
//                    if ( news != null ) {
//                        target.onSuccess(news.getItems(), news.getTotalCount(), offset);
//                    } else {
//                        target.onSuccess(new ArrayList<>(), 0, offset);
//                    }
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void getNewsItemByUrl(String newsUrl, GenericTarget<FragmentNewsItem> target) {
//        AApp.getApolloClient().query(new GetNewsItemByUrlQuery(newsUrl))
//                .enqueue(new ApolloCall.Callback<GetNewsItemByUrlQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetNewsItemByUrlQuery.Data> response) {
//                        if ( response.getData() != null
//                                && response.getData().getNewsItemByUrl() != null
//                                && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getNewsItemByUrl().getFragments().getFragmentNewsItem());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void resetUserPasswordByToken(String resetToken, String newPassword, String refCode,
//                                                String affData, GenericTarget<String> target) {
//        ResetUserPasswordByTokenMutation mutation = new ResetUserPasswordByTokenMutation(resetToken, newPassword,
//                Input.optional(refCode), Input.optional(affData));
//
//        AApp.getApolloClient().mutate(mutation).enqueue(new ApolloCall.Callback<ResetUserPasswordByTokenMutation.Data>() {
//            @Override
//            public void onResponse(@NotNull Response<ResetUserPasswordByTokenMutation.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    target.onSuccess(response.getData().getResetUserPasswordByToken().getViewer().getId());
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NotNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

//    public static void confirmEmail(String confirmationCode, GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new UserConfirmEmailMutation(Input.optional(confirmationCode)))
//                .enqueue(new ApolloCall.Callback<UserConfirmEmailMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<UserConfirmEmailMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getUserConfirmEmail());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void userCheckPasswordResetToken(String token, GenericTarget<String> target) {
//        AApp.getApolloClient().query(new UserCheckPasswordResetTokenQuery(token))
//                .enqueue(new ApolloCall.Callback<UserCheckPasswordResetTokenQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<UserCheckPasswordResetTokenQuery.Data> response) {
//                        if ( response.getData() != null &&
//                                !response.hasErrors() ) {
//                            target.onSuccess(token);
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void checkPassword(String password, GenericTarget<Boolean> target) {
//        AApp.getApolloClient().query(new CheckPasswordQuery(password))
//                .enqueue(new ApolloCall.Callback<CheckPasswordQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<CheckPasswordQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getUserCheckPassword());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getMessages(MessagesTarget target) {
//        AApp.getApolloClient().query(new GetMessagesQuery())
//                .enqueue(new ApolloCall.Callback<GetMessagesQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetMessagesQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            GetMessagesQuery.Viewer viewer = response.getData().getViewer();
//                            if ( viewer != null ) {
//                                target.onSuccess(viewer.getMessages());
//                            } else {
//                                processEmptyViewer(target);
//                            }
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void deleteSeveralMessages(ArrayList<String> ids, GenericTarget<Integer> target) {
//        AApp.getApolloClient().mutate(new DeleteSeveralMessagesMutation(Input.optional(ids)))
//                .enqueue(new ApolloCall.Callback<DeleteSeveralMessagesMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<DeleteSeveralMessagesMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getDeleteSeveralNotifications());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void readSeveralMessages(ArrayList<String> ids, GenericTarget<Integer> target) {
//        AApp.getApolloClient().mutate(new ReadSeveralMessagesMutation(Input.optional(ids)))
//                .enqueue(new ApolloCall.Callback<ReadSeveralMessagesMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<ReadSeveralMessagesMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getReadSeveralNotifications());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void readAllMessages(GenericTarget<Integer> target) {
//        AApp.getApolloClient().mutate(new ReadAllMessagesMutation())
//                .enqueue(new ApolloCall.Callback<ReadAllMessagesMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<ReadAllMessagesMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getReadAllNotifications());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void resetBonusBalance(GenericTarget<Boolean> target) {
//        AApp.getApolloClient().mutate(new ResetBonusBalanceMutation())
//                .enqueue(new ApolloCall.Callback<ResetBonusBalanceMutation.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<ResetBonusBalanceMutation.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            target.onSuccess(response.getData().getResetUserBonusBalance());
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getBonusRefund(GenericTarget<GetBonusRefundQuery.Wallet> target) {
//        AApp.getApolloClient().query(new GetBonusRefundQuery())
//                .enqueue(new ApolloCall.Callback<GetBonusRefundQuery.Data>() {
//                    @Override
//                    public void onResponse(@NotNull Response<GetBonusRefundQuery.Data> response) {
//                        if ( response.getData() != null && !response.hasErrors() ) {
//                            GetBonusRefundQuery.Viewer viewer = response.getData().getViewer();
//
//                            if ( viewer != null ) {
//                                target.onSuccess(viewer.getWallet());
//                            }
//                        } else {
//                            processError(response, target);
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(@NotNull ApolloException e) {
//                        target.onFailure(e);
//                    }
//                });
//    }

//    public static void getTalisman(GenericTarget<GetTalismanQuery.Talisman> target) {
//        AApp.getApolloClient().query(new GetTalismanQuery()).enqueue(new ApolloCall.Callback<GetTalismanQuery.Data>() {
//            @Override
//            public void onResponse(@NonNull Response<GetTalismanQuery.Data> response) {
//                if ( response.getData() != null && !response.hasErrors() ) {
//                    GetTalismanQuery.Viewer viewer = response.getData().getViewer();
//                    if ( viewer != null ) {
//                        target.onSuccess(viewer.getTalisman());
//                    }
//                } else {
//                    processError(response, target);
//                }
//            }
//
//            @Override
//            public void onFailure(@NonNull ApolloException e) {
//                target.onFailure(e);
//            }
//        });
//    }

    static void processLoad(@NonNull GameList games, int offset, LoadTarget loadTarget) {
        List<LocalGameItem> localGameItems = new ArrayList<>();

        for ( int i = 0; i < games.getGameList().size(); i++ ) {
            LocalGameItem localGameItem = new LocalGameItem(games.getGameList().get(i).getFragments().getGameItem());
            localGameItems.add(localGameItem);
        }

        loadTarget.onSuccess(localGameItems, games.getTotalCount(), offset);
    }

    public static String getErrorMessage(ApolloResponse response) {
        if (response.errors != null) {
            return ((com.apollographql.apollo3.api.Error) response.errors.get(0)).getMessage();
        } else {
            return "Empty error message";
        }
    }

    public static String getErrorCode(ApolloResponse response) {
        Map<String, Object> customAttributes = null;
        if ( response.errors != null && !response.errors.isEmpty() ) {
            customAttributes = ((com.apollographql.apollo3.api.Error) response.errors.get(0)).getNonStandardFields();
        }
        return customAttributes != null ? String.valueOf(customAttributes.get(Constants.SERVER_CODE)) : "";
    }

    public static void processError(ApolloResponse response, BaseTargetOnError target) {
        String errorMessage = getErrorMessage(response);
        String errorCode = getErrorCode(response);
        FieldsErrorsHolder errorsHolder = FieldsErrorsHolder.parce(response.errors);
        target.onError(errorMessage, errorCode, errorsHolder);
    }

    static void processEmptyViewer(ViewerQuery viewerQuery) {
        Settings.get().setUserId("");
        Settings.get().setUserName("");
        FirebaseCrashlytics.getInstance().setUserId("");
        viewerQuery.onViewerNull();
    }

}
