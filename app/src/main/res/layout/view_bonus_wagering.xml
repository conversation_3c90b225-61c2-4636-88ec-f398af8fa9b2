<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginTop="16dp"
        android:text="@string/bonus_wagering"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_reset_question"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginTop="16dp"
        android:text="@string/reset_bonus_balance"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_reset_bonus_balance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:padding="8dp"
        android:src="@drawable/ic_trash"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="6dp"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:progressDrawable="@drawable/bonus_wagering_progress_bg"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_bet_refund_sum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginTop="12dp"
        android:alpha="0.6"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progress" />

    <TextView
        android:id="@+id/tv_percent_to_refund"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="16dp"
        android:alpha="0.4"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/progress" />

    <Button
        android:id="@+id/btn_no"
        style="@style/ButtonStyle"
        android:layout_width="0dp"
        android:layout_height="32dp"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginTop="@dimen/size_16"
        android:layout_marginEnd="16dp"
        android:background="@drawable/bg_btn_reset_bonus_balance_no"
        android:text="@string/no"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/btn_yes"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <Button
        android:id="@+id/btn_yes"
        style="@style/ButtonStyle"
        android:layout_width="0dp"
        android:layout_height="32dp"
        android:layout_marginTop="@dimen/size_16"
        android:layout_marginEnd="@dimen/size_16"
        android:text="@string/yes"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_no"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

</androidx.constraintlayout.widget.ConstraintLayout>
