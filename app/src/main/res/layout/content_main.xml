<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:showIn="@layout/app_bar_main"
    android:focusableInTouchMode="true"
    tools:visibility="gone">

    <fragment
        android:id="@+id/nav_host_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:defaultNavHost="true"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:navGraph="@navigation/mobile_navigation" />

    <LinearLayout
        android:id="@+id/containerBottomBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginRight="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btnBottomBalance"
            style="@style/ButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:visibility="visible">

            <TextView
                android:id="@+id/tvOnYourBalance"
                style="@style/ButtonTextStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/_in_your_account"
                android:textSize="10sp"
                app:layout_constraintBottom_toTopOf="@+id/tvBalance"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:ignore="SmallSp" />

            <TextView
                android:id="@+id/tvBalance"
                style="@style/ButtonTextStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOnYourBalance"
                tools:text="1 331 442 \u20BD" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <Button
            android:id="@+id/btnBottom"
            style="@style/ButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:clickable="false"
            android:text="@string/_registration" />

        <com.abrand.custom.ui.views.FastClickPaymentViewBottom
            android:visibility="gone"
            android:id="@+id/containerBottomFastPay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="0dp"/>

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:id="@+id/containerSystemMessages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:orientation="vertical" >

        </LinearLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
