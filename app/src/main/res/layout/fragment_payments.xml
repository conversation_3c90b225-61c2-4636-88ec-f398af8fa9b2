<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/containerTopBg"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/history_top_bg"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@+id/tabLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_real_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/payment_screen_content_margin"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:alpha="0.5"
            android:fontFamily="sans-serif-medium"
            android:text="@string/real_balance"
            android:textColor="#FFFFFF"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SmallSp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginLeft="16dp"
            android:text="@string/cashbox"
            android:textColor="#FFFFFF"
            android:textSize="24sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvBalance" />

        <TextView
            android:id="@+id/tvBalance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:layout_marginRight="16dp"
            android:textColor="#FFFFFF"
            android:textSize="24sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_real_balance" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_marginTop="22dp"
            app:layout_constraintTop_toBottomOf="@+id/tvBalance"
            app:tabIndicator="@drawable/payments_tab_indicator"
            app:tabIndicatorColor="@color/transactions_bg"
            app:tabIndicatorHeight="80dp"
            app:tabRippleColor="@null" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tabLayout" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view_root"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="end">

        <include
            android:id="@+id/drawer_history_filter"
            layout="@layout/drawer_history_filter"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.google.android.material.navigation.NavigationView>

</androidx.drawerlayout.widget.DrawerLayout>
