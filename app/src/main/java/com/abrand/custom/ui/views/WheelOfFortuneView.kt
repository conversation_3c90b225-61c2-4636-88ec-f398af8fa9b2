package com.abrand.custom.ui.views

import android.content.Context
import android.media.MediaPlayer
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import androidx.constraintlayout.widget.ConstraintLayout
import com.abrand.custom.R
import com.abrand.custom.data.entity.WheelSector
import com.abrand.custom.databinding.ViewWheelOfFortuneBinding

class WheelOfFortuneView : ConstraintLayout {
    private var binding: ViewWheelOfFortuneBinding? = null
    private var isSoundEnabled = true
    var isWheelRotating = false
        private set
    var listener: Listener? = null
    var winSector: WheelSector? = null
    var mediaPlayer: MediaPlayer? = null

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {
        binding = ViewWheelOfFortuneBinding.inflate(LayoutInflater.from(context), this, true)

        binding?.ivPlay?.setOnClickListener {
            if (isWheelRotating) {
                immediateFinishSpin()
            } else {
                listener?.onPlayClick()
            }
        }
    }

    fun doSpin() {
        isWheelRotating = true
        winSector = null
        hideResultBlackout()
        rotateWheel()
    }

    private fun immediateFinishSpin() {
        if (winSector != null) {
            binding?.sectorsView?.clearAnimation()
            binding?.sectorsView?.animate()?.cancel()
            rotateToWinSector(100)
        }
    }

    fun addSectors(sectors: List<WheelSector>) {
        binding?.sectorsView?.addSectors(sectors)
    }

    private fun rotateWheel() {
        binding?.sectorsView?.animate()
            ?.rotationBy(2160f)
            ?.withEndAction(accelerateEndAction)
            ?.setDuration(4000)
            ?.setInterpolator(AccelerateInterpolator())
            ?.start()

        mediaPlayer = MediaPlayer.create(context, R.raw.wheel_acceleration)
        mediaPlayer?.isLooping = true
        soundEnable(isSoundEnabled)
        mediaPlayer?.start()
    }

    private val accelerateEndAction = Runnable {
        rotateToWinSector(2000)

        mediaPlayer?.reset()
        mediaPlayer = MediaPlayer.create(context, R.raw.wheel_deceleration)
        soundEnable(isSoundEnabled)
        mediaPlayer?.start()
    }

    private fun rotateToWinSector(duration: Long) {
        val degreeToWinSector = getDegreeToWinSector().toFloat()
        binding?.sectorsView?.animate()
            ?.rotationBy(720f + degreeToWinSector)
            ?.withEndAction(decelerateEndAction)
            ?.setDuration(duration)
            ?.setInterpolator(DecelerateInterpolator(1.5f))
            ?.start()
    }

    private val decelerateEndAction = Runnable {
        mediaPlayer?.reset()
        mediaPlayer = MediaPlayer.create(context, R.raw.wheel_gretings)
        soundEnable(isSoundEnabled)
        mediaPlayer?.start()

        showResultBlackout()
        listener?.onWheelRotationEnd()
        isWheelRotating = false
    }

    private fun showResultBlackout() {
        binding?.ivBlackout?.visibility = VISIBLE
        binding?.ivGlass?.visibility = VISIBLE
    }

    private fun hideResultBlackout() {
        binding?.ivBlackout?.visibility = INVISIBLE
        binding?.ivGlass?.visibility = INVISIBLE
    }

    private fun getDegreeToWinSector(): Double {
        val rotation = binding?.sectorsView?.rotation
        val winPosition = winSector?.position
        if (rotation != null && winPosition != null) {
            val shouldBeDegree = 360 - 22.5 - 45 * winPosition
            val currentDegree = (rotation % 360)
            return if (shouldBeDegree > currentDegree) {
                shouldBeDegree - currentDegree
            } else {
                shouldBeDegree - currentDegree + 360
            }
        }
        return 0.0
    }

    fun soundEnable(soundEnable : Boolean) {
        isSoundEnabled = soundEnable
        if (isSoundEnabled) {
            mediaPlayer?.setVolume(1f, 1f)
        } else {
            mediaPlayer?.setVolume(0f, 0f)
        }
    }

    interface Listener {
        fun onPlayClick()
        fun onWheelRotationEnd()
    }
}
