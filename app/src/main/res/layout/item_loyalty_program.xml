<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ProgressBar
        android:id="@+id/progress_bar_top"
        style="@android:style/Widget.ProgressBar.Horizontal"
        android:layout_width="2dp"
        android:layout_height="6dp"
        android:layout_marginEnd="2dp"
        android:layout_marginRight="2dp"
        android:progress="0"
        android:progressDrawable="@drawable/bg_progress_vertical"
        android:rotation="180"
        app:layout_constraintEnd_toEndOf="@+id/iv_loyalty_status"
        app:layout_constraintStart_toStartOf="@+id/iv_loyalty_status"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/progress_bar"
        style="@android:style/Widget.ProgressBar.Horizontal"
        android:layout_width="2dp"
        android:layout_height="0dp"
        android:layout_marginEnd="2dp"
        android:layout_marginRight="2dp"
        android:progressDrawable="@drawable/bg_progress_vertical"
        android:rotation="180"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/iv_loyalty_status"
        app:layout_constraintStart_toStartOf="@+id/iv_loyalty_status"
        app:layout_constraintTop_toTopOf="@+id/view_progress_bar_top" />

    <View
        android:id="@+id/view_progress_bar_top"
        android:layout_width="1dp"
        android:layout_height="6dp"
        app:layout_constraintBottom_toBottomOf="@+id/iv_loyalty_status"
        app:layout_constraintEnd_toEndOf="@+id/iv_loyalty_status"
        app:layout_constraintStart_toStartOf="@+id/iv_loyalty_status" />

    <ImageView
        android:id="@+id/iv_loyalty_status"
        android:layout_width="26dp"
        android:layout_height="31dp"
        android:layout_marginStart="21dp"
        android:layout_marginLeft="21dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_loyalty_status_1_open" />

    <TextView
        android:id="@+id/tv_experience_points"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        app:layout_constraintBottom_toBottomOf="@+id/iv_loyalty_status"
        app:layout_constraintStart_toEndOf="@+id/iv_loyalty_status"
        app:layout_constraintTop_toTopOf="@+id/iv_loyalty_status"
        tools:text="Без опыта" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginLeft="@dimen/size_16"
        android:textFontWeight="300"
        android:textSize="24sp"
        app:layout_constraintStart_toEndOf="@+id/tv_experience_points"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Юнга" />

    <TextView
        android:drawablePadding="6dp"
        android:id="@+id/tv_prizes"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableLeft="@drawable/ic_gift"
        android:textColor="#FFFFFF"
        android:textFontWeight="400"
        android:textSize="15sp"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="Без опыта" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_prizes_privileges"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_prizes"
        app:layout_constraintTop_toBottomOf="@+id/tv_prizes" />

</androidx.constraintlayout.widget.ConstraintLayout>
