package com.abrand.custom.ui.messages

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.GetMessagesQuery
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.MessageItem
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.interfaces.MessagesTarget
import com.abrand.custom.network.ApolloProcessor
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException

class MessagesViewModel : ViewModel() {
    val messagesLiveData =
        MutableLiveData<Resource<GetMessagesQuery.Messages?, ServerError, ApolloException>>()
    val deleteMessageLiveData = MutableLiveData<Resource<Pair<MessageItem, Int?>, ServerError, ApolloException>>()
    val readMessageLiveData = MutableLiveData<Resource<Pair<MessageItem, Int?>, ServerError, ApolloException>>()
    val readAllMessagesLiveData = MutableLiveData<Resource<Int, ServerError, ApolloException>>()

    fun getMessages() {
        ApolloProcessorKt.getMessages(object : MessagesTarget {
            override fun onSuccess(messages: GetMessagesQuery.Messages?) {
                messagesLiveData.postValue(Resource.success(messages))
            }

            override fun onFailure(e: ApolloException) {
                messagesLiveData.postValue(Resource.failure(e))
            }

            override fun onError(
                errorMessage: String, errorCode: String,
                fieldsErrors: FieldsErrorsHolder?
            ) {
                messagesLiveData.postValue(
                    Resource.error(
                        ServerError(
                            errorMessage, errorCode,
                            fieldsErrors
                        )
                    )
                )
            }

            override fun onViewerNull() {
                messagesLiveData.postValue(Resource.viewerEmpty())
            }
        })
    }

    fun deleteMessage(id: String, itemPosition: Int) {
        ApolloProcessorKt.deleteSeveralMessages(arrayListOf(id), object : GenericTarget<Int> {
            override fun onSuccess(t: Int?) {
                deleteMessageLiveData.postValue(
                    Resource.success(Pair(MessageItem(id, itemPosition), t))
                )
            }

            override fun onError(
                errorMessage: String,
                errorCode: String,
                fieldsErrors: FieldsErrorsHolder?
            ) {
                deleteMessageLiveData.postValue(
                    Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                deleteMessageLiveData.postValue(Resource.failure(e))
            }
        })
    }

    fun markMessageAsRead(id: String, itemPosition: Int) {
        ApolloProcessorKt.readSeveralMessages(arrayListOf(id), object : GenericTarget<Int> {
            override fun onSuccess(t: Int?) {
                readMessageLiveData.postValue(
                    Resource.success(Pair(MessageItem(id, itemPosition), t))
                )
            }

            override fun onError(
                errorMessage: String,
                errorCode: String,
                fieldsErrors: FieldsErrorsHolder?
            ) {
                readMessageLiveData.postValue(
                    Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                readMessageLiveData.postValue(Resource.failure(e))
            }
        })
    }

    fun markAllMessagesAsRead() {
        ApolloProcessorKt.readAllMessages(object : GenericTarget<Int> {
            override fun onSuccess(t: Int?) {
                readAllMessagesLiveData.postValue(Resource.success(t))
            }

            override fun onError(
                errorMessage: String,
                errorCode: String,
                fieldsErrors: FieldsErrorsHolder?
            ) {
                readAllMessagesLiveData.postValue(
                    Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                readAllMessagesLiveData.postValue(Resource.failure(e))
            }
        })
    }
}
