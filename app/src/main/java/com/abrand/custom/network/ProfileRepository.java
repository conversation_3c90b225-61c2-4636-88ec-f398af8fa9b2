package com.abrand.custom.network;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.abrand.custom.GetInitialViewerDataQuery;
import com.abrand.custom.GetFastClickPaymentSystemQuery;
import com.abrand.custom.GetProfileQuery;
import com.abrand.custom.GetReCaptchaQuery;
import com.abrand.custom.data.Resource;
import com.abrand.custom.data.Settings;
import com.abrand.custom.data.entity.Profile;
import com.abrand.custom.data.entity.ServerError;
import com.abrand.custom.interfaces.FastClickPaymentSystemTarget;
import com.abrand.custom.interfaces.GenericTarget;
import com.abrand.custom.interfaces.GetInitialViewerDataTarget;
import com.abrand.custom.interfaces.GetFastPaymentUrlTarget;
import com.abrand.custom.interfaces.GetProfileTarget;
import com.abrand.custom.presenter.FieldsErrorsHolder;
import com.abrand.custom.tools.SingleLiveEvent;
import com.abrand.custom.type.CaptchaForm;
import com.abrand.custom.type.Gender;
import com.apollographql.apollo3.exception.ApolloException;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import kotlinx.coroutines.CoroutineScope;

public class ProfileRepository {
    private MutableLiveData<Profile> profileLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> profileChangedLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> passwordChangedLiveData = new MutableLiveData<>();
    private MutableLiveData<ServerError> serverErrorLiveData = new MutableLiveData<>();
    private MutableLiveData<ApolloException> apolloExceptionLiveData = new SingleLiveEvent<>();
    private MutableLiveData<Boolean> emailConfirmationRequestedLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> logoutResponseLiveData = new MutableLiveData<>();
    private MutableLiveData<GetInitialViewerDataQuery.Viewer> initialViewerDataLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> viewerEmptyLiveData = new MutableLiveData<>();
    private final MutableLiveData<Resource<String, ServerError, ApolloException>> makeRebillLiveData = new MutableLiveData();
    private MutableLiveData<String> fastPaymentUrlLiveData = new MutableLiveData<>();
    private MutableLiveData<GetFastClickPaymentSystemQuery.FastClickPaymentSystem>
                                                         fastClickPaymentSystemLiveData = new MutableLiveData<>();
    private MutableLiveData<GetReCaptchaQuery.ReCaptcha>                          reCaptchaLiveData               = new MutableLiveData<>();

    public LiveData<Profile> getProfileLiveData() {
        return profileLiveData;
    }

    public LiveData<Boolean> getProfileChangedLiveData() {
        return profileChangedLiveData;
    }

    public LiveData<Boolean> getPasswordChangedLiveData() {
        return passwordChangedLiveData;
    }

    public LiveData<ServerError> getServerErrorLiveData() {
        return serverErrorLiveData;
    }

    public LiveData<ApolloException> getApolloExceptionLiveData() {
        return apolloExceptionLiveData;
    }

    public LiveData<Boolean> getEmailConfirmationRequestedLiveData() {
        return emailConfirmationRequestedLiveData;
    }

    public MutableLiveData<Boolean> getLogoutResponseLiveData() {
        return logoutResponseLiveData;
    }

    public MutableLiveData<GetInitialViewerDataQuery.Viewer> getInitialViewerDataLiveData() {
        return initialViewerDataLiveData;
    }

    public MutableLiveData<Boolean> getViewerEmptyLiveData() {
        return viewerEmptyLiveData;
    }

    public MutableLiveData<Resource<String, ServerError, ApolloException>> getMakeRebillLiveData() {
        return makeRebillLiveData;
    }

    public MutableLiveData<String> getFastPaymentUrlLiveData() {
        return fastPaymentUrlLiveData;
    }

    public MutableLiveData<GetFastClickPaymentSystemQuery.FastClickPaymentSystem> getFastClickPaymentSystemLiveData() {
        return fastClickPaymentSystemLiveData;
    }

    public MutableLiveData<GetReCaptchaQuery.ReCaptcha> getReCaptchaLiveData() {
        return reCaptchaLiveData;
    }

    public void fetchProfile() {
        //GeneralTools.showCookie();
        ApolloProcessorKt.getProfile(new GetProfileTarget() {
            @Override
            public void onSuccess(GetProfileQuery.Profile apolloProfile) {
                profileLiveData.postValue(new Profile(apolloProfile));
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                serverErrorLiveData.postValue(new ServerError(errorMessage, errorCode, fieldsErrors));
            }

            @Override
            public void onFailure(ApolloException e) {
                apolloExceptionLiveData.postValue(e);
            }

            @Override
            public void onViewerNull() {
                viewerEmptyLiveData.postValue(true);
            }
        });
    }

    public void saveProfile(String userName, String phone, Gender gender, String birthday, String gCaptchaResponse) {
        ApolloProcessorKt.userChangeProfile(userName, phone, gender, birthday, gCaptchaResponse, new GenericTarget<Boolean>() {
            @Override
            public void onSuccess(@Nullable Boolean isSuccess) {
                profileChangedLiveData.postValue(isSuccess);
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                serverErrorLiveData.postValue(new ServerError(errorMessage, errorCode, fieldsErrors));
            }

            @Override
            public void onFailure(ApolloException e) {
                apolloExceptionLiveData.postValue(e);
            }
        });
    }

    public void changePassword(String oldPassword, String newPassword) {
        ApolloProcessorKt.userChangePassword(oldPassword, newPassword, new GenericTarget<Boolean>() {
            @Override
            public void onSuccess(@Nullable Boolean isSuccess) {
                passwordChangedLiveData.postValue(isSuccess);
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                serverErrorLiveData.postValue(new ServerError(errorMessage, errorCode, fieldsErrors));
            }

            @Override
            public void onFailure(ApolloException e) {
                apolloExceptionLiveData.postValue(e);
            }
        });
    }

    public void requestEmailConfirmation() {
        ApolloProcessorKt.requestEmailConfirmation(new GenericTarget<Boolean>() {
            @Override
            public void onSuccess(Boolean isSuccess) {
                emailConfirmationRequestedLiveData.postValue(isSuccess);
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                serverErrorLiveData.postValue(new ServerError(errorMessage, errorCode, fieldsErrors));
            }

            @Override
            public void onFailure(ApolloException e) {
                apolloExceptionLiveData.postValue(e);
            }
        });
    }

    public void logout() {
        ApolloProcessorKt.logout(new GenericTarget<Boolean>() {
            @Override
            public void onSuccess(@Nullable Boolean isSuccess) {
                logoutResponseLiveData.postValue(isSuccess);
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                serverErrorLiveData.postValue(new ServerError(errorMessage, errorCode, fieldsErrors));
            }

            @Override
            public void onFailure(ApolloException e) {
                apolloExceptionLiveData.postValue(e);
            }
        });
    }

    public void getInitialViewerData() {
        ApolloProcessorKt.getInitialViewerData(new GetInitialViewerDataTarget() {
            @Override
            public void onSuccess(@NotNull GetInitialViewerDataQuery.Viewer viewer) {
                initialViewerDataLiveData.postValue(viewer);
                Settings.get().setUserName(viewer.getProfile().getUserName());
            }

            @Override
            public void onFailure(ApolloException e) {
                apolloExceptionLiveData.postValue(e);
            }

            @Override
            public void onViewerNull() {
                viewerEmptyLiveData.postValue(true);
            }
        });
    }

    public void makeRebill(int sum) {
        ApolloProcessorKt.makeRebill(sum, new GenericTarget<String>() {
            @Override
            public void onSuccess(@NotNull String response) {
                makeRebillLiveData.postValue(Resource.Companion.success(response));
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                makeRebillLiveData.postValue(
                        Resource.Companion.error(new ServerError(errorMessage, errorCode, fieldsErrors)));
            }

            @Override
            public void onFailure(ApolloException e) {
                makeRebillLiveData.postValue(Resource.Companion.failure(e));
            }
        });
    }

    public void getFastPaymentUrl(int sum) {
        ApolloProcessorKt.getFastPaymentUrl(sum, Settings.get().getRefCode(), new GetFastPaymentUrlTarget() {
            @Override
            public void onSuccess(@NotNull String oneClickUrl) {
                fastPaymentUrlLiveData.postValue(oneClickUrl);
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                serverErrorLiveData.postValue(new ServerError(errorMessage, errorCode, fieldsErrors));
            }

            @Override
            public void onFailure(ApolloException e) {
                apolloExceptionLiveData.postValue(e);
            }

            @Override
            public void onViewerNull() {
                viewerEmptyLiveData.postValue(true);
            }
        });
    }

    public void getFastClickPaymentSystem() {
        ApolloProcessorKt.getFastClickPaymentSystem(new FastClickPaymentSystemTarget() {
            @Override
            public void onSuccess(GetFastClickPaymentSystemQuery.FastClickPaymentSystem fastClickPaymentSystem) {
                fastClickPaymentSystemLiveData.postValue(fastClickPaymentSystem);
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                serverErrorLiveData.postValue(new ServerError(errorMessage, errorCode, fieldsErrors));
            }

            @Override
            public void onFailure(ApolloException e) {
                apolloExceptionLiveData.postValue(e);
            }

            @Override
            public void onViewerNull() {
                viewerEmptyLiveData.postValue(true);
            }
        });
    }

    public void getReCapthca(CoroutineScope scope) {
        ApolloProcessorKt.getReCaptcha(CaptchaForm.PROFILE, new GenericTarget<GetReCaptchaQuery.ReCaptcha>() {
            //@NotNull here is incorrect - must read method signature that you use
            @Override
            public void onSuccess(@NotNull GetReCaptchaQuery.ReCaptcha reCaptcha) {
                reCaptchaLiveData.postValue(reCaptcha);
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                serverErrorLiveData.postValue(new ServerError(errorMessage, errorCode, fieldsErrors));
            }

            @Override
            public void onFailure(ApolloException e) {
                apolloExceptionLiveData.postValue(e);
            }
        }, scope);
    }

    public void confirmEmail(String confirmationCode) {
        ApolloProcessorKt.confirmEmail(confirmationCode, new GenericTarget<Boolean>() {
            @Override
            public void onSuccess(@Nullable Boolean isSuccess) {

            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                serverErrorLiveData.postValue(new ServerError(errorMessage, errorCode, fieldsErrors));
            }

            @Override
            public void onFailure(ApolloException e) {
                apolloExceptionLiveData.postValue(e);
            }
        });
    }
}
