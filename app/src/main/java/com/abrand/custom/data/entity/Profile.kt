package com.abrand.custom.data.entity

import com.abrand.custom.GetProfileQuery
import com.abrand.custom.type.Gender

data class Profile(var name: String? = "", var email: String? = "", var phone: String? = "",
                   var emailConfirmed : Boolean? = false, var birthday : String? = "", var gender: Gender? = Gender.UNKNOWN__) {

    constructor(apolloProfile: GetProfileQuery.Profile) :
            this(apolloProfile.userName, apolloProfile.email, apolloProfile.phone,
                    apolloProfile.emailConfirmed, apolloProfile.birthday, apolloProfile.gender)

}
