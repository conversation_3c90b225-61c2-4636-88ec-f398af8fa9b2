package com.abrand.custom.data.repositories

import com.abrand.custom.data.datasources.activateBonuses
import com.abrand.custom.data.datasources.deleteBonuses
import com.abrand.custom.data.datasources.getBonusBalances
import com.abrand.custom.data.datasources.getBonusBalancesCount
import com.abrand.custom.data.datasources.subscribeBonusBalancesWon
import com.abrand.custom.data.entity.DataResult
import com.abrand.custom.ui.game.BonusBalanceWon
import com.abrand.custom.ui.bonusbalances.IBonusBalancesRepo
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

object BonusBalancesRepository: IBonusBalancesRepo {
    private var repoDispatcher = Dispatchers.IO

    fun setDispatcher(newDispatcher: CoroutineDispatcher) {
        repoDispatcher = newDispatcher
    }

    override suspend fun obtainBonusBalances() = withContext(repoDispatcher) {
        val result = getBonusBalances()

        if (result is DataResult.Success) {
            return@withContext result.data
        }

        return@withContext emptyList()
    }

    override suspend fun obtainBonusBalancesCount()= withContext(repoDispatcher) {
        val result = getBonusBalancesCount()

        if (result is DataResult.Success) {
            return@withContext result.data
        }

        return@withContext 0
    }

    override suspend fun activateBonus(bonusId: Int) = withContext(repoDispatcher) {
        val result = activateBonuses(bonusId)

        if (result is DataResult.Success) {
            return@withContext result.data
        }

        return@withContext false
    }

    override suspend fun deleteBonus(bonusId: Int) = withContext(repoDispatcher) {
        val result = deleteBonuses(bonusId)

        if (result is DataResult.Success) {
            return@withContext result.data
        }

        return@withContext false
    }

    override fun getBonusBalanceWonSubscription(): Flow<BonusBalanceWon> {
        return subscribeBonusBalancesWon()
    }
}
