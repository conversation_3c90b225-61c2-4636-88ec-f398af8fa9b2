package com.abrand.custom.ui.home;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.RoundedBitmapDrawable;
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;
import androidx.transition.AutoTransition;
import androidx.transition.Transition;
import androidx.transition.TransitionManager;
import androidx.viewpager2.widget.ViewPager2;

import com.abrand.custom.R;
import com.abrand.custom.data.ApoloConfig;
import com.abrand.custom.data.Settings;
import com.abrand.custom.data.entity.Banner;
import com.abrand.custom.data.entity.GameListItem;
import com.abrand.custom.data.entity.HomeAdditionalBlocks;
import com.abrand.custom.data.entity.LocalGameItem;
import com.abrand.custom.data.entity.LocalNews;
import com.abrand.custom.data.entity.Screen;
import com.abrand.custom.data.entity.TournamentsItem;
import com.abrand.custom.data.entity.User;
import com.abrand.custom.data.entity.lottery.LocalLottery;
import com.abrand.custom.data.repositories.YearRepository;
import com.abrand.custom.databinding.ItemGameBinding;
import com.abrand.custom.databinding.ItemGameHeaderBinding;
import com.abrand.custom.databinding.ViewFooterBinding;
import com.abrand.custom.databinding.ViewHomeAdditionalBlocksBinding;
import com.abrand.custom.interfaces.FooterListener;
import com.abrand.custom.presenter.GameItemDiffCallback;
import com.abrand.custom.tools.CircleTransform;
import com.abrand.custom.tools.DateTools;
import com.abrand.custom.tools.GeneralTools;
import com.abrand.custom.ui.game.InGameMenuSearchFragment;
import com.abrand.custom.ui.interfaces.GameClickListener;
import com.abrand.custom.ui.interfaces.SlideClickListener;
import com.abrand.custom.ui.views.GameLoadView;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Target;
import com.abrand.custom.tools.KotlinExtensionsKt;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.ListIterator;
import java.util.concurrent.TimeUnit;

public class GamesAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private static final long                  CHANGE_AD_DELAY      = 4_000L;
    private static final int                   DEFAULT_LOAD_STEP = 12;
    private              List<GameListItem>    items             = new ArrayList<>();
    private              List<Banner>          banners           = new ArrayList<>();
    private              GameClickListener     clickListener;
    private              SlideClickListener    slideClickListener;
    private              SelectorListener      selectorListener;
    private              LoadMoreClickListener loadMoreClickListener;
    private              AdditionalBlocksClickListener additionalBlocksClickListener;
    private              FooterListener        footerListener;
    private              Screen                screen;
    private              Runnable              showNextAdTask;
    private              Handler               mainHandler;
    private              int                   currentBannerIndex;
    private              Context               context;
    private              boolean               isScreenResumed = false;
    private              int                   currentTotalGames;
    private              HeaderVH              headerVH;
    private              FooterVH              footerVH;
    private              GameLoadVH            gameLoadVH;
    private              GameTab               currentTab      = GameTab.BEST;
    private              GameTab               delayedTab      = null;
    private              boolean               isGamesLoading  = false;
    private              boolean               isUserLogged    = false;
    private              int                   headerTopMargin = 0;
    private              int                   gameIconSize;
    private              int                   additionalBlockWidth;
    private final        int                   loadStep;
    private              HomeAdditionalBlocks  homeAdditionalBlocks;

    public GamesAdapter(int gameIconSize, GameClickListener clickListener,
                        SlideClickListener slideClickListener, LoadMoreClickListener loadMoreClickListener,
                        Screen screen, Context context, int loadStep) {
        this.gameIconSize = gameIconSize;
        this.clickListener = clickListener;
        this.slideClickListener = slideClickListener;
        this.loadMoreClickListener = loadMoreClickListener;
        this.screen = screen;
        this.context = context;
        this.loadStep = loadStep;
    }

    GamesAdapter(Context context, int gameIconSize, int additionalBlockWidth, GameClickListener clickListener,
                 SlideClickListener slideClickListener, SelectorListener selectorListener,
                 AdditionalBlocksClickListener additionalBlocksClickListener,
                 LoadMoreClickListener loadMoreClickListener,
                 FooterListener footerListener,
                 Screen screen) {
        this(gameIconSize, clickListener, slideClickListener, loadMoreClickListener, screen, context, DEFAULT_LOAD_STEP);
        this.additionalBlockWidth = additionalBlockWidth;
        this.selectorListener = selectorListener;
        this.additionalBlocksClickListener = additionalBlocksClickListener;
        this.footerListener = footerListener;
        mainHandler = new Handler(Looper.getMainLooper());
    }

    public void setList(List<GameListItem> newList) {
        List<GameListItem> resultList = new ArrayList<>(newList);
        if ( User.State.ORGANIC == Settings.get().getUserState() ) {
            removeNoDemoGames(resultList);
        }

        if ( Screen.HOME.equals(screen) ) {
            addFakeItemForHeaderLoadAndFooter(resultList);
            addHomeAdditionalBlocks(resultList);
        } else if ( Screen.GAME_SEARCH.equals(screen)
                || Screen.INGAME_MENU_SEARCH_PORTRAIT.equals(screen)
                || Screen.INGAME_MENU_SEARCH_LANDSCAPE.equals(screen)
        ) {
            resultList.add(new GameListItem(GameListItem.ItemType.GAME_LOAD));
        }

        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new GameItemDiffCallback(items, resultList));
        this.items = resultList;
        diffResult.dispatchUpdatesTo(this);
    }

    public void applyLoggedState() {
        isUserLogged = true;
        if ( headerVH != null ) {
            headerVH.initTabs();
        }
    }

    public void applyNotLoggedState() {
        isUserLogged = false;
        if ( headerVH != null ) {
            headerVH.initTabs();
        }
        if ( GameTab.FAVOURITES == currentTab && headerVH != null ) {
            headerVH.selectSlotsTab();
        }
    }

    void showHeaderLoadAndFooter() {
        setList(new ArrayList<>());
    }

    public void appendList(List<GameListItem> newItems) {
        List<GameListItem> old = new ArrayList(items);
        //Add new items before GAME_LOAD and FOOTER item
        this.items.addAll(getGameLoadPosition(), newItems);

        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new GameItemDiffCallback(old, items));
        diffResult.dispatchUpdatesTo(this);
    }

    private int getGameLoadPosition() {
        for ( int index = 0; index < items.size(); index++ ) {
            if ( GameListItem.ItemType.GAME_LOAD == items.get(index).getItemType() ) {
                return index;
            }
        }
        return 0;
    }

    public void setHomeAdditionalBlocks(HomeAdditionalBlocks homeAdditionalBlocks) {
        this.homeAdditionalBlocks = homeAdditionalBlocks;
        if ( this.items.get(items.size() - 2).getItemType() != GameListItem.ItemType.HOME_ADDITIONAL_BLOCK ) {
            this.items.add(items.size() - 1, new GameListItem(GameListItem.ItemType.HOME_ADDITIONAL_BLOCK));
        }
        notifyItemChanged(items.size() - 1);
    }

    void setScreenResumed(boolean screenResumed) {
        isScreenResumed = screenResumed;
    }

    private void removeNoDemoGames(List<GameListItem> newList) {
        ListIterator<GameListItem> iterator = newList.listIterator();
        while ( iterator.hasNext() ) {
            GameListItem homeItem = iterator.next();
            if ( homeItem.getGameItem() != null && !homeItem.getGameItem().isHasDemo() ) {
                iterator.remove();
            }
        }
    }

    private void addFakeItemForHeaderLoadAndFooter(List<GameListItem> newList) {
        newList.add(0, new GameListItem(GameListItem.ItemType.HEADER));
        newList.add(new GameListItem(GameListItem.ItemType.GAME_LOAD));
        newList.add(new GameListItem(GameListItem.ItemType.FOOTER));
    }

    private void addHomeAdditionalBlocks(List<GameListItem> newList) {
        if ( homeAdditionalBlocks != null ) {
            newList.add(newList.size() - 1, new GameListItem(GameListItem.ItemType.HOME_ADDITIONAL_BLOCK));
        }
    }

    public void setBanners(List<Banner> banners) {
        this.banners = banners;
        if ( headerVH != null ) {
            headerVH.updateBanners();
        }
    }

    public void selectTab(GameTab newTab) {
        if ( headerVH != null ) {
            headerVH.selectTab(newTab);
        } else {
            delayedTab = newTab;
        }
    }

    void setFooterCurrentState() {
        if ( footerVH != null ) {
            footerVH.setFooterCurrentState();
        }
    }

    public void setGameLoadingState(boolean isLoading) {
        this.isGamesLoading = isLoading;
        if ( gameLoadVH != null ) {
            gameLoadVH.setGameLoadingState(isLoading);
        }
    }

    private void postChangeAdTask() {
        mainHandler.removeCallbacks(showNextAdTask);
        mainHandler.postDelayed(showNextAdTask, CHANGE_AD_DELAY);
    }

    void stopShowingNextAdTask() {
        mainHandler.removeCallbacks(showNextAdTask);
    }

    public void setCurrentTotalGames(int currentTotalGames) {
        this.currentTotalGames = currentTotalGames;
    }

    public void setHeaderTopMargin(int headerTopMargin) {
        this.headerTopMargin = headerTopMargin;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if ( GameListItem.ItemType.HEADER.getId() == viewType ) {
            ItemGameHeaderBinding headerBinding = ItemGameHeaderBinding.inflate(LayoutInflater.from(parent.getContext()));
            return new HeaderVH(headerBinding);
        } else if ( GameListItem.ItemType.HOME_ADDITIONAL_BLOCK.getId() == viewType ) {
            ViewHomeAdditionalBlocksBinding titleBinding = ViewHomeAdditionalBlocksBinding.inflate(LayoutInflater.from(parent.getContext()));
            return new HomeAdditionalBlocksVH(titleBinding);
        } else if ( GameListItem.ItemType.FOOTER.getId() == viewType ) {
            ViewFooterBinding footerBinding = ViewFooterBinding.inflate(LayoutInflater.from(parent.getContext()));
            return new FooterVH(footerBinding);
        } else if ( GameListItem.ItemType.GAME_LOAD.getId() == viewType ) {
            GameLoadView gameLoadView = new GameLoadView(parent.getContext());
            return new GameLoadVH(gameLoadView);
        } else {
            ItemGameBinding gameBinding = ItemGameBinding.inflate(LayoutInflater.from(parent.getContext()));
            return new GameVH(gameBinding);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if ( holder instanceof HeaderVH ) {
            headerVH = (HeaderVH) holder;
            if ( delayedTab != null ) {
                headerVH.selectTab(delayedTab);
                delayedTab = null;
            }
        } else if ( holder instanceof GameVH ) {
            ((GameVH) holder).bind(items.get(position), position);
        } else if ( holder instanceof GameLoadVH ) {
            gameLoadVH = (GameLoadVH) holder;
            gameLoadVH.bind();
        } else if ( holder instanceof FooterVH ) {
            footerVH = (FooterVH) holder;
        }
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    public int getGameCount() {
        int result = 0;
        for ( GameListItem item : items ) {
            if ( GameListItem.ItemType.GAME == item.getItemType() ) {
                result++;
            }
        }
        return result;
    }

    @Override
    public int getItemViewType(int position) {
        return items.get(position).getItemType().getId();
    }

    class GameVH extends RecyclerView.ViewHolder {
        ItemGameBinding binding;
        LocalGameItem gameItem;
        Target target;
        Picasso picasso;

        GameVH(ItemGameBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
            picasso = Picasso.get();

            target = new Target() {
                @Override
                public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
                    setupImageViewBeforeDisplayImage();
                    binding.ivItemGameImage.setImageBitmap(bitmap);
                }

                @Override
                public void onBitmapFailed(Exception e, Drawable errorDrawable) {
                    setupImageViewBeforeDisplayImage();
                    binding.ivItemGameImage.setImageResource(R.drawable.default_thumb_rounded);
                }

                @Override
                public void onPrepareLoad(Drawable placeHolderDrawable) {
                    //do nothing
                }
            };
        }

        void bind(GameListItem homeItem, int position) {
            binding.ivItemGameImage.clearAnimation();
            setTopMarginForGameSearchScreen(position);
            setGameIconImageViewSize();

            Animation animation = AnimationUtils
                    .loadAnimation(binding.getRoot().getContext(), R.anim.rotation_loader);

            this.gameItem = homeItem.getGameItem();

            binding.ivItemGameImage.setImageResource(R.drawable.ic_preload);
            binding.ivItemGameImage.startAnimation(animation);

            binding.tvItemGameName.setText(gameItem.getName());
            if ( !TextUtils.isEmpty(gameItem.getMobileIcon()) ) {
                int iconCornerRadius = context.getResources().getDimensionPixelSize(R.dimen.game_icon_corner_radius);
                picasso.load(ApoloConfig.getFullUrl(gameItem.getMobileIcon()))
                        .resize(gameIconSize, gameIconSize)
                        .transform(new CircleTransform(iconCornerRadius)) // TODO: 2019-12-06 refactor to remove errors
                        .into(target);
            } else {
                setupImageViewBeforeDisplayImage();
                binding.ivItemGameImage.setImageResource(R.drawable.default_thumb_rounded);
            }

            binding.getRoot().setOnClickListener(view -> {
                clickListener.onGameClick(gameItem, view);
            });
        }

        void setGameIconImageViewSize() {
            ViewGroup.LayoutParams params = binding.ivItemGameImage.getLayoutParams();
            params.height = gameIconSize;
            params.width = gameIconSize;
            binding.ivItemGameImage.setLayoutParams(params);
        }

        void setupImageViewBeforeDisplayImage() {
            binding.ivItemGameImage.clearAnimation();
            binding.ivItemGameImage.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        }

        void setTopMarginForGameSearchScreen(int position) {
            if ( Screen.GAME_SEARCH.equals(screen)
                    || Screen.INGAME_MENU_SEARCH_PORTRAIT.equals(screen)
                    || Screen.INGAME_MENU_SEARCH_LANDSCAPE.equals(screen)
            ) {
                int colsCount;
                if ( Screen.INGAME_MENU_SEARCH_LANDSCAPE.equals(screen) ) {
                    colsCount = InGameMenuSearchFragment.GAMES_COLS_COUNT_LANDSCAPE;
                } else if ( Screen.INGAME_MENU_SEARCH_PORTRAIT.equals(screen) ) {
                    colsCount = InGameMenuSearchFragment.GAMES_COLS_COUNT_PORTRAIT;
                } else {
                    colsCount = HomeFragment.COLS_COUNT;
                }

                ViewGroup.MarginLayoutParams marginParams =
                        (ViewGroup.MarginLayoutParams) binding.ivItemGameImage.getLayoutParams();
                if ( position <= colsCount - 1 ) {
                    marginParams.setMargins(0,
                            context.getResources().getDimensionPixelSize(R.dimen.size_16),
                            0, 0);
                } else {
                    marginParams.setMargins(0, 0, 0, 0);
                }
            }
        }
    }

    class HeaderVH extends RecyclerView.ViewHolder {
        private       ItemGameHeaderBinding binding;
        private       String                ICON_SELECTED_GRADIENT_START = "#FF6ED200";
        private       String                ICON_SELECTED_GRADIENT_END   = "#FF008E00";
        private       String                TEXT_SELECTED_COLOR          = "#FF008E00";
        private       String                TEXT_DEFAULT_COLOR           = "#FFFFFF";
        private final int                   BANNER_ANIMATION_DURATION    = 600;
        private       BannerPagerAdapter    bannerAdapter                = new BannerPagerAdapter(context, slideClickListener);

        HeaderVH(ItemGameHeaderBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
            currentTab = GameTab.BEST;
            initTabs();
            applyHeaderTopMargin(headerTopMargin);
            initViewPagerBanner();
        }

        private void initTabs() {
            setupSelector();
            if (isUserLogged) {
                applyLoggedState();
            } else {
                applyNotLoggedState();
            }
        }

        private void applyLoggedState() {
            binding.ibMainFavouritesBtn.setVisibility(View.VISIBLE);
        }

        private void applyNotLoggedState() {
            binding.ibMainFavouritesBtn.setVisibility(View.GONE);
        }

        public void selectSlotsTab() {
            binding.ibMainSlotsBtn.performClick();
        }

        @SuppressLint("ClickableViewAccessibility")
        private void initViewPagerBanner() {
            binding.viewPagerBanner.setAdapter(bannerAdapter);
            binding.viewPagerBanner.setOffscreenPageLimit(2);

            binding.viewPagerBanner.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
                @Override
                public void onPageSelected(int position) {
                    super.onPageSelected(position);
                    currentBannerIndex = position;
                }
            });

            binding.viewPagerBanner.getChildAt(0).setOnTouchListener((v, event) -> {
                switch ( event.getAction() ) {
                    case MotionEvent.ACTION_DOWN:
                        stopShowingNextAdTask();
                        break;
                    case MotionEvent.ACTION_UP:
                        postChangeAdTask();
                        break;
                }
                return false;
            });
        }

        private void initChangeAdTasks() {
            showNextAdTask = () -> {
                if ( !isScreenResumed ) {
                    return;
                }
                currentBannerIndex++;
                KotlinExtensionsKt.setCurrentItem(binding.viewPagerBanner, currentBannerIndex, BANNER_ANIMATION_DURATION);

                postChangeAdTask();
            };
        }

        public void updateBanners() {
            bannerAdapter.updateBanners(banners);
            currentBannerIndex = banners.size() * 10000;
            if ( !binding.viewPagerBanner.isFakeDragging() ) {
                binding.viewPagerBanner.setCurrentItem(currentBannerIndex, false);
            }
            initChangeAdTasks();
            if ( !banners.isEmpty() ) {
                postChangeAdTask();
            }
        }

        private void applyHeaderTopMargin(int headerTopMargin) {
            ViewGroup.LayoutParams layoutParams = binding.viewPagerBanner.getLayoutParams();
            if ( layoutParams instanceof ViewGroup.MarginLayoutParams ) {
                ((ViewGroup.MarginLayoutParams) layoutParams).topMargin = headerTopMargin;
            }
        }

        private void setupSelector() {
            binding.ibMainBestBtn.setOnClickListener(v -> selectTab(GameTab.BEST));
            binding.ibMainNewBtn.setOnClickListener(v -> selectTab(GameTab.NEW));
            binding.ibMainFavouritesBtn.setOnClickListener(v -> selectTab(GameTab.FAVOURITES));
            binding.ibMainSlotsBtn.setOnClickListener(v -> selectTab(GameTab.SLOTS));
            binding.ibMainTablesBtn.setOnClickListener(v -> selectTab(GameTab.TABLE));
            binding.ibMainLiveCasinoBtn.setOnClickListener(view -> selectTab(GameTab.LIVE_CASINO));
            selectTab(currentTab);
        }

        public void selectTab(GameTab newTab) {
            setTabUnSelected(currentTab);
            currentTab = newTab;
            switch ( newTab ) {
                case BEST:
                    setTabSelected(binding.ibMainBestBtn.getId(), R.drawable.ic_best, binding.ivBest, binding.tvBest);
                    if ( selectorListener != null ) {
                        selectorListener.onSelectedTypeChanged(HomeFragment.GameType.BEST);
                    }
                    break;
                case NEW:
                    setTabSelected(binding.ibMainNewBtn.getId(), R.drawable.ic_new, binding.ivNew, binding.tvNew);
                    if ( selectorListener != null ) {
                        selectorListener.onSelectedTypeChanged(HomeFragment.GameType.NEW);
                    }
                    break;
                case FAVOURITES:
                    setTabSelected(binding.ibMainFavouritesBtn.getId(), R.drawable.ic_favourite, binding.ivFavourites, binding.tvFavourites);
                    if ( selectorListener != null ) {
                        selectorListener.onSelectedTypeChanged(HomeFragment.GameType.FAVOURITES);
                    }
                    break;
                case SLOTS:
                    setTabSelected(binding.ibMainSlotsBtn.getId(), R.drawable.ic_cherries, binding.ivSlots, binding.tvSlots);
                    if ( selectorListener != null ) {
                        selectorListener.onSelectedTypeChanged(HomeFragment.GameType.SLOT);
                    }
                    break;
                case TABLE:
                    setTabSelected(binding.ibMainTablesBtn.getId(), R.drawable.ic_roulette, binding.ivTables, binding.tvTables);
                    if ( selectorListener != null ) {
                        selectorListener.onSelectedTypeChanged(HomeFragment.GameType.TABLES);
                    }
                    break;
                case LIVE_CASINO:
                    setTabSelected(binding.ibMainLiveCasinoBtn.getId(), R.drawable.ic_live_casino, binding.ivLiveCasino, binding.tvLiveCasino);
                    if ( selectorListener != null ) {
                        selectorListener.onSelectedTypeChanged(HomeFragment.GameType.LIVE_CASINO);
                    }
                    break;
            }
        }

        private void setTabSelected(int toView, int drawableId, ImageView imageView, TextView textView) {
            moveSelector(toView);
            Bitmap bitmap = GeneralTools.getBitmapFromVectorDrawable(context, drawableId);
            imageView.setImageBitmap(GeneralTools.addGradient(bitmap, ICON_SELECTED_GRADIENT_START, ICON_SELECTED_GRADIENT_END));
            textView.setTextColor(Color.parseColor(TEXT_SELECTED_COLOR));
        }

        private void setTabUnSelected(GameTab gameTab) {
            switch ( gameTab ) {
                case BEST:
                    binding.ivBest.setImageDrawable(ContextCompat.getDrawable(context, R.drawable.ic_best));
                    binding.tvBest.setTextColor(Color.parseColor(TEXT_DEFAULT_COLOR));
                    break;
                case NEW:
                    binding.ivNew.setImageDrawable(ContextCompat.getDrawable(context, R.drawable.ic_new));
                    binding.tvNew.setTextColor(Color.parseColor(TEXT_DEFAULT_COLOR));
                    break;
                case FAVOURITES:
                    binding.ivFavourites.setImageDrawable(ContextCompat.getDrawable(context, R.drawable.ic_favourite));
                    binding.tvFavourites.setTextColor(Color.parseColor(TEXT_DEFAULT_COLOR));
                    break;
                case SLOTS:
                    binding.ivSlots.setImageDrawable(ContextCompat.getDrawable(context, R.drawable.ic_cherries));
                    binding.tvSlots.setTextColor(Color.parseColor(TEXT_DEFAULT_COLOR));
                    break;
                case TABLE:
                    binding.ivTables.setImageDrawable(ContextCompat.getDrawable(context, R.drawable.ic_roulette));
                    binding.tvTables.setTextColor(Color.parseColor(TEXT_DEFAULT_COLOR));
                    break;
                case LIVE_CASINO:
                    binding.ivLiveCasino.setImageDrawable(ContextCompat.getDrawable(context, R.drawable.ic_live_casino));
                    binding.tvLiveCasino.setTextColor(Color.parseColor(TEXT_DEFAULT_COLOR));
                    break;
            }
        }

        private void moveSelector(int toView) {
            ConstraintSet set = new ConstraintSet();

            set.clone(binding.clTabsLayout);

            set.connect(binding.vSelector.getId(), ConstraintSet.LEFT, toView, ConstraintSet.LEFT);
            set.connect(binding.vSelector.getId(), ConstraintSet.RIGHT, toView, ConstraintSet.RIGHT);

            Transition transition = new AutoTransition();
            TransitionManager.beginDelayedTransition(binding.clTabsLayout, transition);
            set.applyTo(binding.clTabsLayout);
        }

    }

    class HomeAdditionalBlocksVH extends RecyclerView.ViewHolder {
        private ViewHomeAdditionalBlocksBinding binding;

        private final Target tournamentBannerTarget = new Target() {
            @Override
            public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
                setupImageViewBeforeDisplayImage();
                RoundedBitmapDrawable roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(context.getResources(), bitmap);
                roundedBitmapDrawable.setCornerRadius(context.getResources().getDimensionPixelSize(R.dimen.news_card_corner_radius));
                binding.tournamentView.ivItemTournamentImage.setBackground(roundedBitmapDrawable);
            }

            @Override
            public void onBitmapFailed(Exception e, Drawable errorDrawable) {

            }

            @Override
            public void onPrepareLoad(Drawable placeHolderDrawable) {

            }
        };

        private final Target lotteryBannerTarget = new Target() {
            @Override
            public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
                RoundedBitmapDrawable roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(context.getResources(), bitmap);
                roundedBitmapDrawable.setCornerRadius(context.getResources().getDimensionPixelSize(R.dimen.news_card_corner_radius));
                binding.lotteryView.ivLotteryImage.setBackground(roundedBitmapDrawable);
            }

            @Override
            public void onBitmapFailed(Exception e, Drawable errorDrawable) {

            }

            @Override
            public void onPrepareLoad(Drawable placeHolderDrawable) {

            }
        };

        private final Target newsBannerTarget = new Target() {

            @Override
            public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
                RoundedBitmapDrawable roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(context.getResources(), bitmap);
                roundedBitmapDrawable.setCornerRadius(context.getResources().getDimensionPixelSize(R.dimen.news_card_corner_radius));
                binding.newsView.ivBg.setBackground(roundedBitmapDrawable);
            }

            @Override
            public void onBitmapFailed(Exception e, Drawable errorDrawable) {

            }

            @Override
            public void onPrepareLoad(Drawable placeHolderDrawable) {

            }
        };

        HomeAdditionalBlocksVH(ViewHomeAdditionalBlocksBinding binding) {
            super(binding.getRoot());
            this.binding = binding;

            bind();
        }

        private void bind() {
            initTournament();
            initLottery();
            initNews();
        }

        private void initTournament() {
            TournamentsItem tournamentItem = homeAdditionalBlocks.getTournamentItem();
            if ( tournamentItem != null ) {
                binding.tournamentView.tvItemTournamentName.setText(tournamentItem.getTitle());
                String tournamentPrize = GeneralTools.getTournamentFormattedBalance(Double.parseDouble(tournamentItem.getPrizeFund()));
                KotlinExtensionsKt.setHtmlText(binding.tournamentView.tvItemTournamentPrize, tournamentPrize);
                setTimeEnd(tournamentItem.getDateEnd());
                setTournamentBackground(tournamentItem.getBannerMob(), context);

                binding.tournamentView.btnAbout.setOnClickListener(view -> {
                    additionalBlocksClickListener.onTournamentClick(tournamentItem.getId());
                });

            } else {
                binding.tvTournamentBlockTitle.setVisibility(View.GONE);
                binding.tournamentView.getRoot().setVisibility(View.GONE);
            }
        }

        private void setTournamentBackground(String mobileIcon, Context context) {
            if ( !TextUtils.isEmpty(mobileIcon) ) {
                Picasso.get()
                        .load(ApoloConfig.getFullUrl(mobileIcon))
                        .resize(additionalBlockWidth, context.getResources().getDimensionPixelSize(R.dimen.tournaments_big_card_height))
                        .centerCrop()
                        .into(tournamentBannerTarget);
            } else {
                setupImageViewBeforeDisplayImage();
            }
        }

        private void setupImageViewBeforeDisplayImage() {
            binding.tournamentView.ivItemTournamentImage.clearAnimation();
            binding.tournamentView.ivItemTournamentImage.setImageResource(0);
            binding.tournamentView.ivItemTournamentImage.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        }

        private void setTimeEnd(Object dateEnd) {
            Date serverDate = DateTools.getServerDate(context, dateEnd);
            if ( serverDate == null ) {
                return;
            }

            long endTime = serverDate.getTime();
            long currentTime = Calendar.getInstance().getTimeInMillis();
            long count = (endTime - currentTime) / (DateTools.MILLIS_IN_DAY);
            if ( count < 2 ) {
                CountDownTimer timer = new CountDownTimer(endTime-currentTime, DateTools.MILLIS_IN_SECOND) {
                    String hms = "";
                    @Override
                    public void onTick(long millisUntilFinished) {
                        hms = String.format(
                                "%02d:%02d:%02d", TimeUnit.MILLISECONDS.toHours(millisUntilFinished),
                                TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished) - TimeUnit.HOURS.toMinutes(
                                        TimeUnit.MILLISECONDS.toHours(millisUntilFinished)),
                                TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) - TimeUnit.MINUTES.toSeconds(
                                        TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)));
                        binding.tournamentView.tvItemTournamentDay.setText(hms);
                    }

                    @Override
                    public void onFinish() { }
                };
                timer.start();
            } else {
                String dayToEnd = context.getResources().getQuantityString(R.plurals.days, (int) count, (int) count);
                binding.tournamentView.tvItemTournamentDay.setText(dayToEnd);
            }
        }

        private void initLottery() {
            LocalLottery localLottery = homeAdditionalBlocks.getLocalLottery();
            if ( localLottery != null ) {
                binding.lotteryView.tvLotteryName.setText(localLottery.getName());

                String prizeFund;
                if ( localLottery.getPrizeFundByString() == null ||
                        localLottery.getPrizeFundByString().isEmpty() ) {
                    prizeFund = GeneralTools.formatBalance(Settings.get().getUserCurrencyCode(), localLottery.getPrizesSum());
                } else {
                    prizeFund = localLottery.getPrizeFundByString();
                }
                binding.lotteryView.tvLotteryPrize.setText(prizeFund);

                setTimeToEnd(localLottery);
                setLotteryBackground(localLottery.getBannerMobile());

                binding.lotteryView.btnAbout.setOnClickListener(view ->
                        additionalBlocksClickListener.onLotteryClick(localLottery.getId()));
            } else {
                binding.tvLotteryBlockTitle.setVisibility(View.GONE);
                binding.lotteryView.getRoot().setVisibility(View.GONE);
            }
        }

        private void setTimeToEnd(LocalLottery lottery) {
            long endTime = DateTools.getServerDate(context, lottery.getFinishAt()).getTime();
            long currentTime = Calendar.getInstance().getTimeInMillis();
            long days = (endTime - currentTime) / (DateTools.MILLIS_IN_DAY);
            if (days < 2) {
                CountDownTimer timer = new CountDownTimer(endTime - currentTime, 1000) {
                    String hms = "";
                    @Override
                    public void onTick(long millisUntilFinished) {
                        hms = String.format(
                                "%02d:%02d:%02d", TimeUnit.MILLISECONDS.toHours(millisUntilFinished),
                                TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished) - TimeUnit.HOURS.toMinutes(
                                        TimeUnit.MILLISECONDS.toHours(millisUntilFinished)
                                ),
                                TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) - TimeUnit.MINUTES.toSeconds(
                                        TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)
                                )
                        );
                        binding.lotteryView.tvToEnd.setText(hms);
                    }

                    @Override
                    public void onFinish() { }
                };
                timer.start();
            } else {
                binding.lotteryView.tvToEnd.setText(context.getResources().getQuantityString(
                        R.plurals.days, (int) days, (int) days));
            }
        }

        private void setLotteryBackground(String bannerMobile) {
            if (!TextUtils.isEmpty(bannerMobile)) {
                Picasso.get()
                        .load(ApoloConfig.getFullUrl(bannerMobile))
                        .resize(additionalBlockWidth, context.getResources().getDimensionPixelSize(R.dimen.lottery_active_card_height))
                        .centerCrop()
                        .into(lotteryBannerTarget);
            } else {
                binding.lotteryView.ivLotteryImage.setBackground(null);
            }
        }

        private void initNews() {
            LocalNews localNews = homeAdditionalBlocks.getLocalNews();
            if ( localNews != null ) {
                binding.newsView.tvTitle.setText(localNews.getName());
                binding.newsView.tvMonthDay.setText(DateTools.getDay(context, localNews.getCreatedAt()));
                binding.newsView.tvMonthName.setText(DateTools.getMonth(context, localNews.getCreatedAt()));
                setNewsBackground(localNews.getMobileIcon());

                binding.newsView.getRoot().setOnClickListener(view -> additionalBlocksClickListener.onNewsClick(localNews));
            } else {
                binding.tvNewsBlockTitle.setVisibility(View.GONE);
                binding.newsView.getRoot().setVisibility(View.GONE);
            }
        }

        private void setNewsBackground(String mobileIcon) {
            if (!TextUtils.isEmpty(mobileIcon)) {
                Picasso.get()
                        .load(ApoloConfig.getFullUrl(mobileIcon))
                        .resize(additionalBlockWidth, context.getResources().getDimensionPixelSize(R.dimen.news_card_height))
                        .centerCrop()
                        .into(newsBannerTarget);
            } else {
                binding.newsView.ivBg.setBackground(null);
            }
        }
    }

    class FooterVH extends RecyclerView.ViewHolder {
        private ViewFooterBinding binding;

        FooterVH(ViewFooterBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
            setFooterCurrentState();
            binding.tvCopyrightYear.setText(binding.tvCopyrightYear.getContext().getString(
                    R.string.app_copyright_year, YearRepository.INSTANCE.getCurrentYear()
            ));
            binding.glPaymentSystemsView.setOnClickListener(view -> footerListener.onClickPaymentSystems());
        }

        void setFooterCurrentState() {
            if ( User.State.ORGANIC.equals(Settings.get().getUserState()) ) {
                binding.lFooterEmblems.setVisibility(View.GONE);
            } else {
                binding.lFooterEmblems.setVisibility(View.VISIBLE);
                if ( context != null ) {
                    int paddingBottom = context.getResources().getDimensionPixelSize(R.dimen.not_organic_user_footer_padding_bottom);
                    binding.root.setPadding(0, 0, 0, paddingBottom);
                }
            }
        }
    }

    class GameLoadVH extends RecyclerView.ViewHolder {
        private GameLoadView gameLoadView;

        GameLoadVH(GameLoadView gameLoadView) {
            super(gameLoadView);
            this.gameLoadView = gameLoadView;

            Button btnLoadMore = gameLoadView.getBtnLoadMore();
            if ( btnLoadMore != null ) {
                btnLoadMore.setOnClickListener(v -> {
                    loadMoreClickListener.onClick();
                });
            }
        }

        void bind() {
            setGameLoadingState(isGamesLoading);
        }

        void setGameLoadingState(boolean isLoading) {
            gameLoadView.setGameLoadingState(isLoading, currentTotalGames, getGameCount(), loadStep);
        }
    }

    public enum GameTab {
        BEST, NEW, FAVOURITES, SLOTS, TABLE, LIVE_CASINO
    }

    public interface SelectorListener {
        void onSelectedTypeChanged(HomeFragment.GameType selectedType);
    }

    public interface LoadMoreClickListener {
        void onClick();
    }

    public interface AdditionalBlocksClickListener {
        void onTournamentClick(int tournamentId);
        void onLotteryClick(int lotteryId);
        void onNewsClick(LocalNews localNews);
    }

}
