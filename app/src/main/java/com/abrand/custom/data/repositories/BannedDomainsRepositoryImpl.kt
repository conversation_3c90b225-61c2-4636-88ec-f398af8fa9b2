package com.abrand.custom.data.repositories

import com.abrand.custom.data.datasources.BannedDomainsLocalDataSource
import com.abrand.custom.data.datasources.BannedDomainsLocalDataSourceImpl
import com.abrand.custom.data.datasources.BannedDomainsRemoteDataSource
import com.abrand.custom.data.datasources.BannedDomainsRemoteDataSourceImpl
import com.abrand.custom.data.datasources.DeviceInfoProvider
import com.abrand.custom.data.datasources.DeviceInfoProviderImpl
import com.abrand.custom.data.entity.BannedDomainDTO
import com.abrand.custom.data.entity.BannedDomainResponseDTO
import com.abrand.custom.data.entity.appconfig.LocalConfig
import com.abrand.custom.domain.BannedDomain
import com.abrand.custom.domain.BannedDomainResponse
import com.abrand.custom.domain.BannedDomainsRepository

// Should be refactored after DI implementation
internal object BannedDomainsRepositoryImpl : BannedDomainsRepository {
    //    private val reserveDomainProvider: ReserveDomainProvider,
    private val bannedDomainsLocalDataSource: BannedDomainsLocalDataSource =
        BannedDomainsLocalDataSourceImpl()
    private val bannedDomainsRemoteDataSource: BannedDomainsRemoteDataSource =
        BannedDomainsRemoteDataSourceImpl()
    private val deviceInfoProvider: DeviceInfoProvider = DeviceInfoProviderImpl()

    override suspend fun saveBannedDomain(domain: BannedDomainDTO) {
        bannedDomainsLocalDataSource.saveBannedDomain(domain)
    }

    override suspend fun getBannedDomainsList(): List<BannedDomain> {
        return bannedDomainsLocalDataSource.getBannedDomainList().map { it.toBannedDomain() }
    }

    override suspend fun deleteAllBannedDomains() {
        bannedDomainsLocalDataSource.deleteAllBannedDomains()
    }

    override suspend fun sendBannedDomains(
        bannedDomains: List<BannedDomain>,
        appConfig: LocalConfig
    ): BannedDomainResponse {
        val deviceInfo = deviceInfoProvider.getDeviceInfo()
        return bannedDomainsRemoteDataSource.sendBannedDomains(
            appConfig = appConfig,
            bannedDomainsList = bannedDomains.map { domain ->
                BannedDomainDTO(
                    id = domain.id,
                    domain = domain.domain,
                    reason = domain.reason,
                    code = domain.code,
                    action = domain.action,
                    time = domain.time
                )
            },
            deviceId = deviceInfo.deviceId,
            country = deviceInfo.country
        ).toBannedDomainResponse()
    }

    override suspend fun saveLastFailedBannedDomainsRequestTimestamp(lastTime: Long) {
        bannedDomainsLocalDataSource.saveLastFailedRequestTimestamp(lastTime)
    }

    override suspend fun getLastFailedBannedDomainsRequestTimestamp(): Long {
        return bannedDomainsLocalDataSource.getLastFailedRequestTimestamp()
    }

//    override suspend fun getReservedDomain(): String {
//        return reserveDomainProvider.getReservedDomain()
//    }

    private fun BannedDomainResponseDTO.toBannedDomainResponse() = BannedDomainResponse(
        status = status,
        domains = data.domains,
        updaterDomains = data.updaterDomains
    )

    private fun BannedDomainDTO.toBannedDomain() = BannedDomain(
        id = id,
        domain = domain,
        reason = reason,
        code = code,
        action = action,
        time = time
    )
}
