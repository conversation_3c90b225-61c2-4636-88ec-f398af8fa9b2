package com.abrand.custom.tools

import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri

class MessengerHelper {

    companion object {
        private const val TELEGRAM_URL = "https://t.me"
        private const val VIBER_URI = "viber://"
        private const val VIBER_INVITE_URL = "https://invite.viber.com"
        private const val VKONTAKTE_URL = "https://vk.com"
        private const val DISCORD_URL = "https://discord.com"
        private const val WHATSAPP_URL = "https://chat.whatsapp.com"
        private const val OK_URL = "https://ok.ru"

        private const val TELEGRAM_PACKAGE = "org.telegram.messenger"
        private const val VIBER_PACKAGE = "com.viber.voip"
        private const val VK_PACKAGE = "com.vkontakte.android"
        private const val DISCORD_PACKAGE = "com.discord"
        private const val WHATSAPP_PACKAGE = "com.whatsapp"
        private const val OK_PACKAGE = "ru.ok.android"
    }

    fun detectMessengerLinkAndProcess(
        deepLinkUrl: String,
        packageManager: PackageManager,
        activity: Activity,
        callback: MessengerCallback
    ): Boolean {
        if (deepLinkUrl.startsWith(TELEGRAM_URL)) {
            openMessengerByUrl(TELEGRAM_PACKAGE, deepLinkUrl, packageManager, activity)
            return true
        } else if (deepLinkUrl.startsWith(VIBER_INVITE_URL)) {
            openMessengerByUrl(VIBER_PACKAGE, deepLinkUrl, packageManager, activity)
            return true
        } else if (deepLinkUrl.startsWith(VIBER_URI)) {
            if (!openMessengerByUri(VIBER_PACKAGE, deepLinkUrl, packageManager, activity)) {
                callback.onMessengerError(MessengerError.VIBER_IS_NOT_INSTALLED)
            }
            return true
        } else if (deepLinkUrl.startsWith(VKONTAKTE_URL)) {
            openMessengerByUrl(VK_PACKAGE, deepLinkUrl, packageManager, activity)
            return true
        } else if (deepLinkUrl.startsWith(DISCORD_URL)) {
            openMessengerByUrl(DISCORD_PACKAGE, deepLinkUrl, packageManager, activity)
            return true
        } else if (deepLinkUrl.startsWith(WHATSAPP_URL)) {
            openMessengerByUrl(WHATSAPP_PACKAGE, deepLinkUrl, packageManager, activity)
            return true
        }
        else if (deepLinkUrl.startsWith(OK_URL)) {
            openMessengerByUrl(OK_PACKAGE, deepLinkUrl, packageManager, activity)
            return true
        }
        return false
    }

    private fun openMessengerByUrl(messengerPackage: String, url: String, packageManager: PackageManager, activity: Activity) {
        val messengerIntent = packageManager.getLaunchIntentForPackage(messengerPackage)
        if (messengerIntent != null) {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            intent.setPackage(messengerPackage)
            activity.startActivity(intent)
        } else {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            activity.startActivity(intent)
        }
    }

    private fun openMessengerByUri(messengerPackage: String, uri: String, packageManager: PackageManager, activity: Activity) : Boolean {
        val messengerIntent = packageManager.getLaunchIntentForPackage(messengerPackage)
        return if (messengerIntent != null) {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(uri))
            intent.setPackage(messengerPackage)
            activity.startActivity(intent)
            true
        } else {
            false
        }
    }

    interface MessengerCallback {
        fun onMessengerError(error: MessengerError)
    }

    enum class MessengerError() {
        VIBER_IS_NOT_INSTALLED()
    }
}