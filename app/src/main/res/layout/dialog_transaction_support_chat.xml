<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_transaction_dialog"
    android:paddingBottom="32dp">

    <ImageButton
        android:id="@+id/ib_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:padding="12dp"
        android:src="@drawable/ic_unknown_link_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:paddingLeft="32dp"
        android:paddingRight="32dp"
        android:text="@string/ask_question"
        android:textColor="#000000"
        android:textSize="24sp"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_question"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:paddingLeft="32dp"
        android:paddingRight="32dp"
        android:text="@string/contact_support_question"
        android:textColor="#000000"
        android:textSize="12sp"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <Button
        android:id="@+id/btn_no"
        style="@style/ButtonGreyStyle"
        android:layout_width="wrap_content"
        android:layout_height="48dp"
        android:layout_marginStart="32dp"
        android:layout_marginTop="20dp"
        android:text="@string/no"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_question" />

    <Button
        android:id="@+id/btn_yes"
        style="@style/ButtonStyle"
        android:layout_width="wrap_content"
        android:layout_height="48dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="32dp"
        android:text="@string/yes"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btn_no"
        app:layout_constraintTop_toBottomOf="@+id/tv_question" />

</androidx.constraintlayout.widget.ConstraintLayout>
