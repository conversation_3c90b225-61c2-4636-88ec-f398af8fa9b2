<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginTop="@dimen/size_16"
        android:layout_marginEnd="@dimen/size_16"
        android:background="@drawable/message_item_bg"
        tools:ignore="WebViewLayout">

        <ImageView
            android:id="@+id/iv_banner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:alpha="0.4"
            android:scaleType="center"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="@dimen/size_16"
            android:textColor="#99FFFFFF"
            android:textSize="10sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp"
            android:src="@drawable/ic_close_message"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="@dimen/size_16"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_date" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/text_barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_title,iv_banner" />

        <WebView
            android:id="@+id/wv_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="16dp"
            android:scrollbars="none"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/text_barrier" />

        <View
            android:id="@+id/wv_text_bottom_margin"
            android:layout_width="wrap_content"
            android:layout_height="10dp"
            app:layout_constraintTop_toBottomOf="@+id/wv_text" />

        <Button
            android:id="@+id/btn_bottom_1"
            style="@style/ButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginTop="10dp"
            android:paddingStart="@dimen/size_16"
            android:paddingEnd="@dimen/size_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/wv_text" />

        <Button
            android:id="@+id/btn_bottom_2"
            style="@style/ButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginTop="10dp"
            android:paddingStart="@dimen/size_16"
            android:paddingEnd="@dimen/size_16"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_bottom_1" />

        <View
            android:id="@+id/btn_bottom_margin"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            app:layout_constraintTop_toBottomOf="@+id/btn_bottom_2" />

        <View
            android:id="@+id/message_read_mask"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:background="@drawable/message_item_read_mask_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>

