<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/navigation_menu_footer_height_with_navbar"
    android:background="@color/white">

    <com.abrand.custom.ui.views.TextViewNoUnderline
        android:id="@+id/tv_drawer_support_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_drawer_support_mail"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:clickable="true"
        android:focusable="true"
        android:autoLink="phone"
        android:textSize="24sp"
        android:gravity="center"
        android:textColor="@color/black"
        android:textColorLink="@color/black"
        android:text="@string/support_phone"/>

    <com.abrand.custom.ui.views.TextViewNoUnderline
        android:id="@+id/tv_drawer_support_mail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:autoLink="email"
        android:clickable="true"
        android:focusable="true"
        android:paddingStart="16dp"
        android:paddingTop="16dp"
        android:paddingEnd="16dp"
        android:paddingBottom="@dimen/tv_drawer_support_mail_bottom_padding"
        android:text="@string/support_mail"
        android:textColor="@color/black_50"
        android:textColorLink="@color/black_50"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@+id/btn_support_chat"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <Button
        android:id="@+id/btn_support_chat"
        style="@style/ButtonBlueStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/size_16"
        android:layout_marginRight="@dimen/size_16"
        android:layout_marginBottom="8dp"
        android:clickable="false"
        android:text="@string/support_chat_button_text"
        app:layout_constraintBottom_toTopOf="@+id/tv_version_info"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/tv_version_info"
        android:layout_height="16dp"
        android:layout_width="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginBottom="@dimen/size_16"
        android:textColor="@color/black_50"
        android:textSize="12sp"/>

</androidx.constraintlayout.widget.ConstraintLayout>
