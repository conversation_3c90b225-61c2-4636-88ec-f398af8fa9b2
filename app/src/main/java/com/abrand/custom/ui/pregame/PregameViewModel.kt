package com.abrand.custom.ui.pregame

import android.annotation.SuppressLint
import android.text.TextUtils
import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.data.Resource
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.*
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.network.retrySubscription
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException
import com.apollographql.apollo3.exception.ApolloNetworkException
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class PregameViewModel : ViewModel() {
    val demoUrlLiveData: MutableLiveData<String> = SingleLiveEvent()
    val gameUrlLiveData: MutableLiveData<String> = SingleLiveEvent()
    val gameByIdLiveData = SingleLiveEvent<Resource<LocalGameItem, ServerError, ApolloException>>()
    val demoUrlApolloExceptionLiveData: SingleLiveEvent<ApolloException> = SingleLiveEvent()
    val gameUrlApolloExceptionLiveData: SingleLiveEvent<ApolloException> = SingleLiveEvent()
    val apolloExceptionLiveData: MutableLiveData<ApolloException> = SingleLiveEvent()
    val serverErrorLiveData: MutableLiveData<ServerError> = MutableLiveData()
    val joinLiveData: MutableLiveData<TournamentSingleResponse> = MutableLiveData()
    val joinErrorLiveData: SingleLiveEvent<String> = SingleLiveEvent()
    val hasGameFreeSpinsLiveData = MutableLiveData<Resource<Boolean, ServerError, ApolloException>>()
    private val exceptionHandler = CoroutineExceptionHandler { _, e -> Log.e(TAG, e.toString()) }
    private val dispatcher = Dispatchers.Default + exceptionHandler

    fun hasGameFreeSpins() {
        val gameUrl = SessionDataHolder.getInstance().currentGameItem.url
        ApolloProcessorKt.hasGameFreeSpins(gameUrl, object : GenericTarget<Boolean?> {
            override fun onSuccess(t: Boolean?) {
                hasGameFreeSpinsLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                hasGameFreeSpinsLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                hasGameFreeSpinsLiveData.postValue(Resource.failure(e))
            }
        }, viewModelScope)
    }

    fun getURLs() {
        getDemoUrl()
        getGameUrl()
    }

    private fun getDemoUrl() {
        if (SessionDataHolder.getInstance().currentGameItem.isHasDemo) {
            val shortGameUrl = SessionDataHolder.getInstance().currentGameItem.url

            ApolloProcessorKt.obtainDemoGameUrl(shortGameUrl, object : GenericTarget<String> {
                @SuppressLint("NullSafeMutableLiveData")
                override fun onSuccess(url: String?) {
                    demoUrlLiveData.postValue(url)
                }

                @SuppressLint("NullSafeMutableLiveData")
                override fun onFailure(e: ApolloException?) {
                    demoUrlApolloExceptionLiveData.postValue(e)
                }

                override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                    serverErrorLiveData.postValue(ServerError(errorMessage, errorCode, fieldsErrors))
                }
            }, viewModelScope)
        }
    }

    private fun getGameUrl() {
        val shortGameUrl = SessionDataHolder.getInstance().currentGameItem.url
        val userState = Settings.get().userState
        if (!TextUtils.isEmpty(shortGameUrl) && User.State.PLAYER == userState) {
            ApolloProcessorKt.obtainGameUrl(shortGameUrl, Settings.get().refCode, object : GenericTarget<String> {
                @SuppressLint("NullSafeMutableLiveData")
                override fun onSuccess(url: String?) {
                    gameUrlLiveData.postValue(url)
                }

                @SuppressLint("NullSafeMutableLiveData")
                override fun onFailure(e: ApolloException?) {
                    gameUrlApolloExceptionLiveData.postValue(e)
                }

                override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                    serverErrorLiveData.postValue(ServerError(errorMessage, errorCode, fieldsErrors))
                }
            }, viewModelScope)
        }
    }

    fun getGameById(gameId: Int) {
        ApolloProcessorKt.getGameById(gameId, object : GenericTarget<LocalGameItem> {
            @Suppress("PARAMETER_NAME_CHANGED_ON_OVERRIDE")
            override fun onSuccess(localGameItem: LocalGameItem?) {
                gameByIdLiveData.postValue(Resource.success(localGameItem))
            }

            override fun onFailure(e: ApolloException) {
                gameByIdLiveData.postValue(Resource.failure(e))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                gameByIdLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

        }, viewModelScope)
    }

    fun changeGameFavouriteState(gameId: Int, isFavourite: Boolean) {
        if (isFavourite) {
            ApolloProcessorKt.addGameToFavourites(gameId, changeGameFavouriteStateTarget)
        } else {
            ApolloProcessorKt.removeGameFromFavourites(gameId, changeGameFavouriteStateTarget)
        }
    }

    private val changeGameFavouriteStateTarget = object : GenericTarget<Boolean> {
        override fun onSuccess(isFavourite: Boolean?) {

        }

        @SuppressLint("NullSafeMutableLiveData")
        override fun onFailure(e: ApolloException?) {
            apolloExceptionLiveData.postValue(e)
        }

        override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
            serverErrorLiveData.postValue(ServerError(errorMessage, errorCode, fieldsErrors))
        }
    }

    fun joinTournament(id: Int) {
        ApolloProcessorKt.joinTournament(id, object : GenericTarget<TournamentsItem> {
            override fun onSuccess(t: TournamentsItem?) {
                t?.apply {
                    joinLiveData.postValue(TournamentSingleResponse(this))
                }
            }

            override fun onFailure(e: ApolloException?) {
            }

            @SuppressLint("NullSafeMutableLiveData")
            override fun onError(errorMessage: String?, errorCode: String?, fieldsErrors: FieldsErrorsHolder?) {
                joinErrorLiveData.postValue(errorMessage)
            }

        }, viewModelScope)
        viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeTournament(id.toString()).retrySubscription().collect {
                    Log.d("ApolloSubscription", "Tournament subscription")
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "$TAG subscribeTournament")
                crashlytics.recordException(e)
            }
        }
    }

    companion object {
        const val TAG = "PregameViewModel"
    }
}
