package com.abrand.custom.data.datasources

import com.abrand.custom.data.entity.BannedDomainDTO


interface BannedDomainsLocalDataSource {

    suspend fun getBannedDomainList(): List<BannedDomainDTO>

    suspend fun saveBannedDomain(domain: BannedDomainDTO)

    suspend fun removeBannedDomainById(id: Long)

    suspend fun deleteAllBannedDomains()

    suspend fun saveLastFailedRequestTimestamp(lastTime: Long)

    suspend fun getLastFailedRequestTimestamp(): Long
}
