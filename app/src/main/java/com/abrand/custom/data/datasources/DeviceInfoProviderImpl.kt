package com.abrand.custom.data.datasources

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.os.Build
import android.os.SystemClock
import android.provider.Settings
import android.util.DisplayMetrics
import android.view.WindowManager
import com.abrand.custom.AApp
import com.abrand.custom.data.entity.DeviceInfo
import java.util.Locale
import kotlin.math.pow
import kotlin.math.sqrt

class DeviceInfoProviderImpl(
    private val context: Context = AApp.getContext(),
) : DeviceInfoProvider {

    @SuppressLint("HardwareIds")
    override fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            isEmulator = false,
            deviceId = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ANDROID_ID
            ),
            locale = Locale.getDefault().displayLanguage,
            diagonalInches = getDisplayMetrics(context),
            androidApiVersion = Build.VERSION.SDK_INT,
            simRegion = "stub-temporary",
            brand = Build.BRAND,
            deviceUpTimeSeconds = SystemClock.elapsedRealtime() / 1_000,
            accelerometer = false,
            modelName = Build.MODEL,
            operatorName = "stub-temporary",
            country = getCountry()
        )
    }

    private fun getDisplayMetrics(context: Context): Double {
        val metrics = DisplayMetrics()
        (context.getSystemService(Context.WINDOW_SERVICE) as WindowManager).defaultDisplay.getMetrics(
            metrics
        )
        val x = (metrics.widthPixels / metrics.xdpi).toDouble().pow(2.0)
        val y = (metrics.heightPixels / metrics.ydpi).toDouble().pow(2.0)
        return sqrt(x + y)
    }

    private fun getCountry(): String? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                Resources.getSystem().configuration.locales.get(0).country
            } catch (e: Exception) {
                null
            }
        } else {
            Resources.getSystem().configuration.locale.country
        }
    }
}
