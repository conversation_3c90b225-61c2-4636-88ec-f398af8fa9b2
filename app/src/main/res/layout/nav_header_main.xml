<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="UnusedAttribute, ContentDescription"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/drawer_header_bg">

    <FrameLayout
        android:id="@+id/fl_drawer_header_no_user"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible">

        <ImageView
            android:id="@+id/iv_drawer_ad"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true" />

        <Button
            android:id="@+id/btn_drawer_register"
            style="@style/ButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_margin="16dp"
            android:text="@string/_registration"
            tools:visibility="invisible" />

    </FrameLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_drawer_header_logged"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_profile"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_drawer_container_profile"
            android:paddingBottom="10dp"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_loyalty_status"
                android:layout_width="36dp"
                android:layout_height="44dp"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_drawer_user_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="12dp"
                android:ellipsize="end"
                android:ems="50"
                android:singleLine="true"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/header_messages"
                app:layout_constraintStart_toEndOf="@+id/iv_loyalty_status"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="someusername" />

            <TextView
                android:id="@+id/tv_drawer_loyalty_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="#FFFFFF"
                android:textSize="10sp"
                app:layout_constraintStart_toStartOf="@+id/tv_drawer_user_name"
                app:layout_constraintTop_toBottomOf="@+id/tv_drawer_user_name" />

            <View
                android:id="@+id/drawer_loyalty_status_circle"
                android:layout_width="2dp"
                android:layout_height="2dp"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:background="@drawable/bg_drawer_loyalty_status_circle"
                app:layout_constraintBottom_toBottomOf="@+id/tv_drawer_loyalty_status"
                app:layout_constraintStart_toEndOf="@+id/tv_drawer_loyalty_status"
                app:layout_constraintTop_toTopOf="@+id/tv_drawer_loyalty_status" />

            <TextView
                android:id="@+id/tv_loyalty_percent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:textColor="#FFFFFF"
                android:textSize="10sp"
                app:layout_constraintStart_toEndOf="@+id/drawer_loyalty_status_circle"
                app:layout_constraintTop_toTopOf="@+id/tv_drawer_loyalty_status" />

            <ProgressBar
                android:id="@+id/loyalty_progress"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="0dp"
                android:layout_height="6dp"
                android:layout_marginStart="8dp"
                android:layout_marginLeft="8dp"
                android:layout_marginEnd="50dp"
                android:layout_marginRight="50dp"
                android:progressDrawable="@drawable/loyalty_progress_bg"
                app:layout_constraintBottom_toBottomOf="@+id/tv_loyalty_percent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tv_loyalty_percent"
                app:layout_constraintTop_toTopOf="@+id/tv_loyalty_percent" />

            <FrameLayout
                android:id="@+id/header_messages"
                android:layout_width="38dp"
                android:layout_height="38dp"
                android:layout_marginEnd="2dp"
                app:layout_constraintBottom_toBottomOf="@+id/tv_drawer_user_name"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_drawer_user_name">

                <ImageView
                    android:id="@+id/iv_header_messages"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="6dp"
                    android:layout_marginEnd="6dp"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_messages_default_white" />

                <FrameLayout
                    android:id="@+id/header_messages_counter"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_gravity="top|end"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="2dp"
                    android:background="@drawable/circle_messages_counter"
                    android:visibility="invisible">

                    <TextView
                        android:id="@+id/tv_header_messages_counter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:textSize="8sp" />
                </FrameLayout>
            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.abrand.custom.ui.views.BonusWageringDrawerView
            android:id="@+id/bonus_wagering"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/container_profile" />

        <TextView
            android:id="@+id/tv_drawer_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:textColor="@color/white"
            android:textSize="32sp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/bonus_wagering"
            tools:text="41 622 254 &#8381;" />

        <TextView
            android:id="@+id/tv_drawer_balance_real_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="12dp"
            android:layout_marginRight="12dp"
            android:gravity="center_horizontal"
            android:textSize="16sp"
            app:layout_constraintEnd_toStartOf="@+id/tv_drawer_plus"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_balance"
            tools:text="200 &#8381;" />

        <TextView
            android:id="@+id/tv_drawer_balance_real_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:layout_marginRight="12dp"
            android:text="@string/real"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/tv_drawer_plus"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_balance_real_amount" />

        <TextView
            android:id="@+id/tv_drawer_plus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:text="@string/plus"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_drawer_balance_real_text"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_drawer_balance_real_amount" />

        <TextView
            android:id="@+id/tv_drawer_balance_bonus_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginLeft="12dp"
            android:layout_marginTop="10dp"
            android:gravity="center_horizontal"
            android:textSize="16sp"
            app:layout_constraintStart_toEndOf="@+id/tv_drawer_plus"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_balance"
            tools:text="200 &#8381;" />

        <TextView
            android:id="@+id/tv_drawer_balance_bonus_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginLeft="12dp"
            android:text="@string/bonus"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@+id/tv_drawer_plus"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_balance_bonus_amount" />

        <FrameLayout
            android:id="@+id/bonus_balances_counter_compose_container"
            android:layout_width="wrap_content"
            android:layout_height="12dp"
            android:layout_marginStart="4dp"
            android:layout_marginBottom="1dp"
            app:layout_constraintStart_toEndOf="@+id/tv_drawer_balance_bonus_text"
            app:layout_constraintBottom_toBottomOf="@+id/tv_drawer_balance_bonus_text"/>

        <TextView
            android:id="@+id/tv_drawer_x_on_points"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_x_on_points"
            android:paddingLeft="2dp"
            android:paddingRight="2dp"
            android:textColor="#000000"
            android:textSize="11sp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/tv_drawer_balance_points"
            app:layout_constraintStart_toEndOf="@+id/tv_drawer_balance_points" />

        <LinearLayout
            android:id="@+id/ll_drawer_x_on_points_info"
            android:layout_width="200dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:elevation="20dp"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@+id/tv_drawer_x_on_points"
            app:layout_constraintStart_toStartOf="@+id/tv_drawer_x_on_points"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_x_on_points">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/ic_triangle_up" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_drawer_x_on_points_info"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_x_on_points_info_description"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_16"
                    android:paddingTop="10dp"
                    android:paddingEnd="@dimen/size_16"
                    android:textColor="#91A1BA"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_x_on_points_info_privilege_active"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_16"
                    android:paddingTop="10dp"
                    android:paddingEnd="@dimen/size_16"
                    android:text="@string/x_on_points_privilege_active"
                    android:textColor="#000"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_x_on_points_to_end"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/size_16"
                    android:paddingEnd="@dimen/size_16"
                    android:paddingBottom="10dp"
                    android:textColor="#000"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:id="@+id/tv_drawer_balance_points"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="@+id/tv_drawer_balance_real_text"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_balance_bonus_text"
            tools:text="5" />

        <TextView
            android:id="@+id/tv_drawer_balance_points_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/drawer_points"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="@+id/btn_drawer_exchange_points"
            app:layout_constraintEnd_toEndOf="@+id/tv_drawer_balance_real_text" />

        <Button
            android:id="@+id/btn_drawer_exchange_points"
            style="@style/ButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginTop="25dp"
            android:background="@drawable/btn_bg_drawer_exchange_points"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            android:text="@string/drawer_exchange"
            android:textColor="#000000"
            app:layout_constraintStart_toStartOf="@+id/tv_drawer_balance_bonus_text"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_balance_bonus_text" />

        <TextView
            android:id="@+id/tv_drawer_cashback"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="22dp"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="@+id/tv_drawer_balance_real_text"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_balance_points_title"
            tools:text="5р" />

        <TextView
            android:id="@+id/tv_drawer_cashback_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/cashback"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="@+id/tv_drawer_balance_real_text"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_cashback" />

        <Button
            android:id="@+id/btn_replenish_balance"
            style="@style/ButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginLeft="24dp"
            android:layout_marginTop="30dp"
            android:layout_marginRight="24dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/btn_bg_enable"
            android:text="@string/replenish_balance"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_cashback_title" />

        <com.abrand.custom.ui.views.FastClickPaymentView
            android:id="@+id/fast_click_payment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="30dp"
            android:layout_marginRight="16dp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_drawer_cashback_title" />

        <TextView
            android:id="@+id/tv_drawer_other_methods"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:text="@string/other_payment_methods"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fast_click_payment" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_drawer_header_organic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/organic_drawer_banner"
        android:visibility="gone" />

</FrameLayout>
