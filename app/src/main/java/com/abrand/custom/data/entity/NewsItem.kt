package com.abrand.custom.data.entity

import java.io.Serializable

class NewsItem : Serializable {
    var itemType: ItemType
    var title: String? = null
    var createdAt: Any? = null
    var mobileIcon: String? = null
    var content: String? = null

    constructor(itemType: ItemType) {
        this.itemType = itemType
    }

    constructor(itemType: ItemType, title: String, createdAt: Any, mobileIcon: String?,
                content: String) {
        this.itemType = itemType
        this.title = title
        this.createdAt = createdAt
        this.mobileIcon = mobileIcon
        this.content = content
    }

    constructor(itemType: ItemType, localNews: LocalNews) {
        this.itemType = itemType
        this.title = localNews.name
        this.createdAt = localNews.createdAt
        this.mobileIcon = localNews.mobileIcon
        this.content = localNews.content
    }

    enum class ItemType(val id: Int) {
        TITLE(0),
        CARD_NEWS(1),
        SMALL_NEWS(2),
        NEWS_LOAD(3),
        FOOTER(4)
    }
}
