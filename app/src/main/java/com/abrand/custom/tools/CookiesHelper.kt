package com.abrand.custom.tools

import android.content.Context
import android.os.Build
import android.webkit.CookieManager
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import java.net.MalformedURLException
import java.net.URL

class CookiesHelper {

    fun removeCloudflareCookie(context: Context) {
        val webkitCookieManager = CookieManager.getInstance()
        try {
            val aURL = URL(ApoloConfig.BASE_URL)
            val hostArr = aURL.host.split(".")
            val domain = hostArr[hostArr.size - 2] + "." + hostArr[hostArr.size - 1]
            val cfClearanceCookieExpire: String = context.getString(R.string.cf_clearance_cookie_expire, domain)
            webkitCookieManager.setCookie(ApoloConfig.BASE_URL, cfClearanceCookieExpire)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                webkitCookieManager.flush()
            } else {
                @Suppress("DEPRECATION")
                android.webkit.CookieSyncManager.getInstance().sync()
            }
        } catch (e: MalformedURLException) {
            e.printStackTrace()
        }
    }

}
