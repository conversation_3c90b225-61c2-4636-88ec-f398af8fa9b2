<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/card_root"
        android:layout_width="match_parent"
        android:layout_height="@dimen/promotion_card_defautl_height"
        android:layout_marginTop="@dimen/size_16"
        android:background="@drawable/promotion_card_default_bg">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_w"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5"/>

        <ImageView
            android:id="@+id/iv_promotion"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxHeight="@dimen/promotion_card_defautl_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/gl_w"
            android:scaleType="centerInside"
            android:adjustViewBounds="true" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginTop="@dimen/size_16"
            android:layout_marginRight="@dimen/size_16"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            app:layout_constraintEnd_toStartOf="@+id/ib_promotion_info"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/ib_promotion_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@null"
            android:paddingTop="@dimen/size_16"
            android:paddingRight="@dimen/size_16"
            android:paddingBottom="@dimen/size_16"
            android:src="@drawable/ic_promotion_info"
            app:layout_constraintEnd_toStartOf="@+id/ib_promotion_deactivate"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/ib_promotion_deactivate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@null"
            android:paddingTop="@dimen/size_16"
            android:paddingRight="@dimen/size_16"
            android:paddingBottom="@dimen/size_16"
            android:src="@drawable/ic_promotion_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progress"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginTop="10dp"
            android:progressDrawable="@drawable/bonus_progress_bg"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_name" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/size_16"
            android:paddingTop="2dp"
            android:paddingRight="@dimen/size_16"
            android:paddingBottom="2dp"
            android:textSize="10sp"
            app:layout_constraintBottom_toBottomOf="@+id/progress"
            app:layout_constraintStart_toStartOf="@+id/progress"
            app:layout_constraintTop_toTopOf="@+id/progress"
            tools:ignore="SmallSp" />

        <TextView
            android:id="@+id/tv_deposit_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginTop="22dp"
            android:text="@string/promotion_deposit_title"
            android:textColor="#99FFFFFF"
            android:textSize="11sp"
            app:layout_constraintBottom_toTopOf="@+id/tv_deposit"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="SmallSp" />

        <TextView
            android:id="@+id/tv_deposit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginBottom="18dp"
            android:textColor="#FFFFFF"
            android:textSize="15sp"
            app:layout_constraintBottom_toTopOf="@+id/container_bottom_view"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tv_date_end_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32dp"
            android:layout_marginTop="22dp"
            android:text="@string/promotion_date_end_title"
            android:textColor="#99FFFFFF"
            android:textSize="11sp"
            app:layout_constraintBottom_toTopOf="@+id/tv_date_end"
            app:layout_constraintStart_toEndOf="@+id/tv_deposit_title"
            tools:ignore="SmallSp" />

        <TextView
            android:id="@+id/tv_date_end"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="32dp"
            android:layout_marginBottom="18dp"
            android:textColor="#FFFFFF"
            android:textSize="15sp"
            app:layout_constraintBottom_toTopOf="@+id/container_bottom_view"
            app:layout_constraintStart_toEndOf="@+id/tv_deposit_title" />

        <TextView
            android:id="@+id/tv_confirm_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginLeft="@dimen/size_16"
            android:textColor="#99FFFFFF"
            android:textSize="11sp"
            app:layout_constraintBottom_toTopOf="@+id/tv_to_end_promotion"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="SmallSp" />

        <TextView
            android:id="@+id/tv_to_end_promotion_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginBottom="22dp"
            android:text="@string/until_promotion_end"
            android:textColor="#99FFFFFF"
            android:textSize="11sp"
            app:layout_constraintBottom_toTopOf="@+id/container_bottom_view"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="SmallSp" />

        <TextView
            android:id="@+id/tv_to_end_promotion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:layout_marginLeft="2dp"
            android:layout_marginBottom="22dp"
            android:textColor="#FFFFFF"
            android:textSize="11sp"
            app:layout_constraintBottom_toTopOf="@+id/container_bottom_view"
            app:layout_constraintStart_toEndOf="@+id/tv_to_end_promotion_title"
            tools:ignore="SmallSp" />

        <FrameLayout
            android:id="@+id/container_bottom_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent">

            <com.abrand.custom.ui.views.FastClickPaymentView
                android:id="@+id/fast_click_payment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/size_16"
                android:layout_marginRight="@dimen/size_16"
                android:layout_marginBottom="@dimen/size_16" />

            <Button
                android:id="@+id/btn_action"
                style="@style/ButtonStyle"
                android:layout_width="176dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/size_16"
                android:layout_marginRight="@dimen/size_16"
                android:layout_marginBottom="@dimen/size_16"
                android:background="@drawable/btn_promotion_bg"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="gone" />

            <RelativeLayout
                android:id="@+id/container_promo_code"
                android:layout_width="176dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/size_16"
                android:layout_marginBottom="@dimen/size_16"
                android:background="@drawable/bonus_promo_code_bg"
                app:layout_constraintStart_toStartOf="parent">

                <EditText
                    android:id="@+id/et_promo_code"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toLeftOf="@+id/ib_promo_code_enter"
                    android:background="@null"
                    android:hint="@string/enter_promo_code"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/size_16"
                    android:textColor="#000000"
                    android:textColorHint="#000000"
                    android:textSize="12sp" />

                <ImageButton
                    android:id="@+id/ib_promo_code_enter"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_margin="8dp"
                    android:background="@drawable/btn_promotion_bg"
                    android:padding="8dp"
                    android:src="@drawable/ic_bonus_promo_code_arrow" />

            </RelativeLayout>

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
