package com.abrand.custom.data.entity

import com.abrand.custom.fragment.FragmentBonusPrize

data class Bonus(
    var id: Int? = null,
    var name: String? = null,
    var depositAmount: Double? = null,
    var buttonName: String? = null,
    var imgCharacter: String? = null,
    var imgPattern: String? = null,
    var isActivated: Boolean? = null,
    var isDeactivated: Boolean? = null,
    var rules: String? = null,
    var isProgressive: Boolean? = null,
    var progressiveCurrentStep: Int? = null,
    var paidSumOfDeposits: Double? = null,
    var isPromoCodeRequired: Boolean? = null,
    var availableAfterDeactivation: Boolean? = null,
    var dateEnd: Any? = null,
    var secondsToEnd: Int? = null,
    var bonusEvent: PromotionItem.BonusEvent? = null,
    var isCalculatedByDepositSum: Boolean? = null,
    var prizes: ArrayList<FragmentBonusPrize> = ArrayList()
)
