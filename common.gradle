//:::::::::::::::::::::::::: Config :::::::::::::::::::::::::::
ext.anko_version = '0.10.5'
ext.support_version = '28.0.0'
ext.androidX_version = '1.0.0'

ext {
    appNumber = '4549'
    testAppId = "com.abrand.custom"
    debugHost           = "adm.stagings.tools"
    debugReCaptchaHost  = "com.abrand.custom"
    debugBaseUrl        = "https://adm.stagings.tools/"
//    debugEndpoint       = "https://adm.stagings.tools/api-gateway/debug-graphql"
    debugEndpoint       = "https://adm.stagings.tools/api-gateway/graphql"
    debugWWSEndpoint    = "wss://adm.stagings.tools/api-gateway/websocket"
    debugCaptchaPref    = "https://adm.stagings.tools/captcha/image/"
    debugAnalyticsUrl   = "https://stage-log.hobaevci.net/notify"
    debugL4PAuthUrl     = "https://login4play.com/api/social/auth"
    debugL4PApiKey      = "8348957923894583452346561876"
    debugL4PApiSecret   = "LGNJVFIrgjdowpeori903gk9eu834"
    debugRedirectorHost1 = "devfront-ad-redirector.hwtool.biz"
    debugRedirectorHost2 = "fscrm-dev.hwtool.biz"
    debugRedirectorHost3 = ""
    debugRedirectorHost4 = ""
    debugRedirectorHost5 = ""
}

ext.applyConfig = { enableProguard ->

    android {
        compileSdkVersion 34
        compileOptions {
            sourceCompatibility JavaVersion.VERSION_17
            targetCompatibility JavaVersion.VERSION_17
        }

        defaultConfig {
            minSdkVersion 21
            targetSdkVersion 34

            def apkFolder = file("../build/apk")

            def releaseTask = project.tasks.create("activateRelease")

//            android.applicationVariants.all { variant ->
//                variant.outputs.all { output ->
//                    if(output.name.toLowerCase().endsWith("release")) {
//                        apkFolder.list().each {
//                            if (it.startsWith("$rootProject.name-$output.name")) {
//                                println "delete $it, "+apkFolder.getPath() + "/" + it
//                                file(apkFolder.getPath() + "/" + it).delete()
//                            }
//                        }
//                        def task = project.tasks.create("activateRelease-${variant.name}", Copy)
//                        task.from(output.outputFile)
//                        task.into(apkFolder.getPath())
//                        task.rename(output.name, "$output.name-$defaultConfig.versionCode")
//                        task.dependsOn variant.assemble
//                        releaseTask.dependsOn task
//                    }
//                }
//            }

        }

        buildTypes {
            debug {
                signingConfig null
//                applicationVariants.all { variant ->
//                    renameAPK(variant, defaultConfig, variant.name)
//                }
            }

            release {
                minifyEnabled enableProguard
                proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                proguardFiles fileTree('proguard').asList().toArray()
                applicationVariants.all { variant ->
                    renameAPK(variant, defaultConfig, variant.name)
                }
            }
        }

        def keyFile = file("../${rootProject.name}.jks")
        def testKeyFile = file("../testkey.jks")

//        if(!keyFile.exists()) throwLog("Key file not found")
        if(!keyFile.exists()) {
            preBuild.dependsOn createKeystore
        }
//
//        if(!testKeyFile.exists()) throwLog("Test key file not found")
        if(!testKeyFile.exists()) {
            preBuild.dependsOn createTestKeystore
        }

        signingConfigs {
            apkTesting {
                storeFile testKeyFile
                storePassword "r_1008_sound_images"
                keyAlias "r_1008_sound_images"
                keyPassword "r_1008_sound_images"
            }

            finalVersion {
                storeFile keyFile
                storePassword stripExtension(keyFile.getName())
                keyAlias stripExtension(keyFile.getName())
                keyPassword stripExtension(keyFile.getName())
            }
        }
    }
}


//:::::::::::::::::::::::::: Dependencies ::::::::::::::::::::::::::

ext.applyCommonDependencies = {
    repositories {
        maven {
            url 'https://jitpack.io'
        }
    }

    dependencies {
        implementation "com.android.support:appcompat-v7:$support_version"
        implementation "com.android.support:recyclerview-v7:$support_version"
        implementation "com.android.support:support-v4:$support_version"
        implementation "com.android.support:design:$support_version"
        implementation 'com.android.support.constraint:constraint-layout:1.1.3'
    }
}

ext.applyCommonXDependencies = {
    repositories {
        maven {
            url 'https://jitpack.io'
        }
    }

    dependencies {
        implementation "androidx.appcompat:appcompat:1.7.0"
        implementation "androidx.recyclerview:recyclerview:1.1.0"
        implementation "androidx.legacy:legacy-support-v4:1.0.0"
        implementation "com.google.android.material:material:1.0.0"
        implementation "androidx.annotation:annotation:1.1.0"
        implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    }
}

ext.applyDifferentStuffDependencies = {
    dependencies {
        implementation 'io.reactivex:rxjava:1.3.3'
        implementation 'io.reactivex:rxandroid:1.2.1'

        implementation 'com.jakewharton:butterknife:8.8.1'
        annotationProcessor 'com.jakewharton:butterknife-compiler:8.8.1'

        implementation 'com.github.bumptech.glide:glide:4.7.1'
        implementation 'jp.wasabeef:glide-transformations:3.3.0'
        implementation 'org.threeten:threetenbp:1.3.6'
        implementation 'me.relex:circleindicator:1.2.2@aar'
        //        implementation 'com.merhold.extensiblepageindicator:extensiblepageindicator:1.0.1'

        implementation ('com.karumi:dexter:4.2.0') {
            exclude group: "com.android.support", module: "design"
        }
    }
}

ext.applyKotlinDependencies = {
    kotlin {
        experimental {
            coroutines "enable"
        }
    }

    dependencies {
        ext.kotlin_version = '1.3.10'

        implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
        implementation "org.jetbrains.anko:anko:$anko_version"
        implementation "org.jetbrains.anko:anko-appcompat-v7:$anko_version"
        implementation "org.jetbrains.anko:anko-coroutines:$anko_version"
        implementation "org.jetbrains.anko:anko-design:$anko_version"
        implementation "org.jetbrains.anko:anko-design-coroutines:$anko_version"
        implementation "org.jetbrains.anko:anko-recyclerview-v7:$anko_version"
        implementation "org.jetbrains.anko:anko-recyclerview-v7-coroutines:$anko_version"
        implementation "org.jetbrains.anko:anko-support-v4:$anko_version"
        implementation "org.jetbrains.anko:anko-constraint-layout:$anko_version"
    }
}

ext.applyAdjustDependencies = {
    dependencies {
        implementation 'com.adjust.sdk:adjust-android:4.21.0'
        implementation 'com.android.installreferrer:installreferrer:1.0'
        implementation 'com.google.android.gms:play-services-analytics:17.0.0'
    }
}

ext.applyCloakaDependencies = {
    dependencies {
        //Cloaka

        implementation 'com.squareup.retrofit2:retrofit:2.5.0'
        implementation 'com.squareup.retrofit2:converter-gson:2.5.0'
        implementation 'com.squareup.retrofit2:converter-scalars:2.5.0'
        implementation 'com.squareup.okhttp3:okhttp:3.12.0'
        implementation 'com.squareup.okhttp3:logging-interceptor:3.12.0'
        implementation 'com.squareup.picasso:picasso:2.71828'
    }
}

ext.applyDaggerDependencies = {
    dependencies {
        //Cloak
        implementation 'com.google.dagger:dagger-android:2.15'
        implementation('com.google.dagger:dagger-android-support:2.11') {
            exclude group: "com.android.support", module: "appcompat-v7"
            exclude group: "com.android.support", module: "support-v4"
        }
        annotationProcessor 'com.google.dagger:dagger-android-processor:2.13'
        annotationProcessor 'com.google.dagger:dagger-compiler:2.13'
    }
}

ext.applyKochavaDependencies = {
    repositories {
        maven { url "http://kochava.bintray.com/maven" }
    }

    dependencies {
        implementation 'com.kochava.base:tracker:3.4.0'
        implementation 'com.google.android.gms:play-services-base:16.0.1'
        implementation 'com.google.android.instantapps:instantapps:1.1.0'
    }
}

ext.appleFirebaseDependencies = {
    dependencies {
        implementation 'com.google.firebase:firebase-messaging:17.3.4'
        implementation 'com.google.firebase:firebase-core:16.0.5'
        implementation 'com.google.firebase:firebase-iid:17.0.4'

        implementation 'com.firebase:firebase-jobdispatcher:0.8.5'

    }
}

ext.applyFacebookDependencies = {

    dependencies {
        implementation 'com.android.support:multidex:1.0.3'
        implementation 'com.facebook.android:facebook-android-sdk:5.0.1'
    }
}

//.......................... Methods ............................
import java.security.MessageDigest

ext.throwLog = { text ->
    throw new GradleException(text)
}

ext.encrypt = { str ->
    return str.bytes.encodeBase64().toString().bytes.encodeBase64().toString()
}

ext.buildConfigMessage = { flavor, message ->
    flavor.buildConfigField "int", "GRADLE_TEST", "\"" + message + "\""
}

ext.applySafety = { flavor ->
    buildConfigMessage(flavor, "This flavor shouldn't be ran on devices")
}

ext.assertTrue = { flavor, condition, message, shouldThrow = true ->
    if (!condition) {
        //println message
        buildConfigMessage(flavor, message)
        if (shouldThrow) throw new GradleException(message)
    }
}


ext.generateHash = {
    //requires import java.security.MessageDigest
    MessageDigest.getInstance("MD5").digest(Math.random().toString().bytes).encodeHex().toString().substring(0,8)
}

ext.createKeystoreCommand = { rawName ->
    def name = rawName
    def command = "keytool -genkey -v -keystore ${name}.jks -alias ${name} -keypass ${name} " +
            "-storepass ${name} -keyalg RSA -keysize 2048 -validity 10000 -dname \"cn=${generateHash()}\""
    //println command
    return command
}

ext.validProConfig = "-obfuscationdictionary dictionary.txt\n" +
        "-classobfuscationdictionary dictionary.txt\n" +
        "-useuniqueclassmembernames\n" +
        "-dontshrink"

task runCodegen(type: Exec) {
    def command = "java -jar codegen.jar \"" + project.name + "\\src\\main\""
    workingDir '..'
    commandLine 'cmd', '/c', command
}

task createKeystore(type: Exec) {
    workingDir '..'
    def command = createKeystoreCommand(rootProject.name)

    if ( System.getProperty('os.name').toLowerCase(Locale.ROOT).contains('windows') ) {
        commandLine 'cmd', '/c', command
    } else {
        commandLine 'sh', '-c', command
    }
}

task createTestKeystore(type: Exec) {
    workingDir '..'
    def command = createKeystoreCommand("testkey")

    if ( System.getProperty('os.name').toLowerCase(Locale.ROOT).contains('windows') ) {
        commandLine 'cmd', '/c', command
    } else {
        commandLine 'sh', '-c', command
    }
}


ext.getFile = { File fileP, name = null ->
    try {
        def filename = fileP.list(new FilenameFilter() {
            @Override
            boolean accept(File file1, String s) {
                if (name != null) return s == name
                else return file1.isDirectory() && !s.startsWith(".") && !file1.isHidden()
            }
        })[0]
        return file(fileP.getPath() + "\\" + filename)
    } catch(Exception e) {
        return null
    }
}

ext.getPackageName = {
    def java = getFile(file("src/main"), "java")
    def com = getFile(java)
    def mydomain = getFile(com)
    def name = getFile(mydomain)
    String pkg = com.getName() + "." + mydomain.getName() + "." + name.getName()
    return pkg
}

//ext.getBuildVersion = {
//    def versionPropsFile = file('version.properties')
//    def versionProps = new Properties()
//
//    if (versionPropsFile.canRead()) {
//        versionProps.load(new FileInputStream(versionPropsFile))
//        def code = versionProps['VERSION_CODE'].toInteger() + 1
//        versionProps['VERSION_CODE'] = code.toString()
//        versionProps.store(versionPropsFile.newWriter(), null)
//        return code
//    } else {
//        versionProps['VERSION_CODE'] = "1"
//        versionProps.store(versionPropsFile.newWriter(), null)
//        throw new GradleException("Could not read version.properties!")
//    }
//}

//ext.getBuildVersionString = {
//    return "1.${getBuildVersion()}"
//}

ext.addBuildConfigField = { flavor, name, value, shouldEncrypt = false ->
    if (shouldEncrypt) flavor.buildConfigField "String", name, "\"" + encrypt(value) + "\""
    else flavor.buildConfigField "String", name, "\"" + value + "\""
}

ext.getJavaRoot = { src ->
    return getFile(getFile(getFile(getFile(src, "java"))))
}

ext.stripExtension = { filename -> return filename.split("\\.")[0] }


ext.getRenamed = { string ->
    if(string.contains("__")) {
        String[] splitted = string.split("__")
        return splitted[0]+"__"+generateHash()+"__.java"
    }
    return string.replace(".java", "__"+generateHash()+"__.java")
}

ext.renameRootClasses = {
    def manifest = file("src/main/AndroidManifest.xml")
    def root = getFile(getFile(getFile(getFile(file("src/main"), "java"))))
    def toRename = new ArrayList();

    root.eachFile { f -> if (!f.isDirectory()) toRename.add(f) }
    toRename.each { File fileP ->
        if(fileP.getName() != "AppConfig.java" && fileP.getName().startsWith("Cgn")) {
            println fileP.getName()
            String newName = getRenamed(fileP.getName())
            //replaceInFile(fileP, fileP.getName().replace(".java", ""), newName.replace(".java", ""))
            //replaceInFile(manifest, fileP.getName().replace(".java", ""), newName.replace(".java", ""))
            //fileP.renameTo(fileP.getPath().replace(fileP.getName(), newName))
        }
    }
}

def renameAPK(variant, defaultConfig, buildType) {
    variant.outputs.all { output ->
        outputFileName = "a_4549_v" + defaultConfig.versionName + "_b" + defaultConfig.versionCode + "_" + buildType +".apk"
    }
}

