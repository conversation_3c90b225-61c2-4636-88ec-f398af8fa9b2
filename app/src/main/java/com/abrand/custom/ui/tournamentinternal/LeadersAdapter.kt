package com.abrand.custom.ui.tournamentinternal

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.tools.GeneralTools
import kotlin.math.roundToInt

class LeadersAdapter(
    private val data: List<TournamentsItem.TournamentParticipant>,
    private val prizes: List<TournamentsItem.TournamentPrize>,
    private val isDynamic: Boolean, private val sum: Double
) : RecyclerView.Adapter<LeadersAdapter.LeaderViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LeaderViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return LeaderViewHolder(inflater, parent)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, position: Int) {
        val participant = data.get(position)
        holder.tvNumber.text = participant.place.toString()
        if (participant.user?.id != Settings.get().userId) {
            holder.tvName.text = participant.user?.formattedUserName
        } else {
            if (Settings.get().userName.equals("")) {
                holder.tvName.text = Settings.get().loggedUserEmail
            } else {
                holder.tvName.text = Settings.get().userName
            }
        }
        holder.tvScore.text = GeneralTools.formatNumber(participant.score?.toInt().toString())
        if (isDynamic) {
            holder.tvFund.text = GeneralTools.formatBalance(Settings.get().userCurrencyCode,
                (sum / 100 * prizes[position].sum.toDouble().roundToInt()))
        } else {
            holder.tvFund.text = GeneralTools.formatBalance(
                Settings.get().userCurrencyCode,
                prizes[position].sum.toDouble())
        }
    }

    override fun getItemCount() = data.size

    class LeaderViewHolder(inflater: LayoutInflater, parent: ViewGroup) :
        RecyclerView.ViewHolder(inflater.inflate(R.layout.item_leader_list, parent, false)) {
        var tvName: TextView = itemView.findViewById(R.id.tv_name)
        var tvScore: TextView = itemView.findViewById(R.id.tv_score)
        var tvFund: TextView = itemView.findViewById(R.id.tv_fund)
        var tvNumber: TextView = itemView.findViewById(R.id.tv_number)
    }

}
