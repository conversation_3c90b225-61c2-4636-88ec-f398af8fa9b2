package com.abrand.custom.ui.views

import android.content.Context
import android.util.AttributeSet
import android.view.animation.Animation
import android.view.animation.RotateAnimation
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.abrand.custom.R


class LoaderAnchor : ConstraintLayout {

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {
        val rootView = inflate(context, R.layout.view_loader_anchor, this)

        val ivLoader = rootView.findViewById<ImageView>(R.id.iv_loader)

        val rotate = RotateAnimation(
            0f, 360f,
            Animation.RELATIVE_TO_SELF, 0.5f,
            Animation.RELATIVE_TO_SELF, 0.5f)

        rotate.duration = 900
        rotate.repeatCount = Animation.INFINITE
        ivLoader.startAnimation(rotate)
    }

}
