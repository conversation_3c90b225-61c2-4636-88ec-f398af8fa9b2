package com.abrand.custom.presenter;

import android.util.Log;

import androidx.annotation.Nullable;

import com.apollographql.apollo3.api.Error;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FieldsErrorsHolder {
    private static final String FIELD_ERRORS = "field_errors";
    private static final String FIELD_KEY    = "field";
    private static final String ERRORS_KEY   = "errors";

    private final Map<Fields, List<String>> errorsMap;

    private FieldsErrorsHolder() {
        errorsMap = new HashMap<>();
    }

    public Map<Fields, List<String>> getErrorsMap() {
        return errorsMap;
    }

    @Nullable
    public static FieldsErrorsHolder parce(List<Error> apolloErrors) {
        try {
            FieldsErrorsHolder errorsHolder = new FieldsErrorsHolder();

            for ( Error error : apolloErrors ) {
                if ( error.getNonStandardFields() != null
                        && error.getNonStandardFields().get(FIELD_ERRORS) != null ) {
                    //noinspection unchecked
                    List<Map<String, Object>> fieldErrors = (List<Map<String, Object>>) error
                            .getNonStandardFields().get(FIELD_ERRORS);

                    //noinspection ConstantConditions
                    for ( Map<String, Object> errorsForField : fieldErrors ) {
                        String       fieldStr = (String) errorsForField.get(FIELD_KEY);
                        Fields       field    = Fields.getByString(fieldStr);
                        List<String> errorsList;

                        if ( field == null ) {
                            continue;
                        }

                        //noinspection unchecked
                        errorsList = (List<String>) errorsForField.get(ERRORS_KEY);
                        errorsHolder.errorsMap.put(field, errorsList);
                    }
                }
            }


            if ( errorsHolder.errorsMap.size() > 0 ) {
                return errorsHolder;
            } else {
                return null;
            }
        } catch ( Throwable e ) {
            Log.w("FieldsErrorsHolder", "Error while parse: server errors with message "
                    + e.getLocalizedMessage());
            return null;
        }
    }
}
