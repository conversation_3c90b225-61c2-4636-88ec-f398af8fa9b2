package com.abrand.custom.ui.lotteryinternal

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.entity.lottery.LocalLotteryTicket
import com.abrand.custom.data.entity.lottery.LotteryTicketItem
import com.abrand.custom.databinding.ItemLotteryTicketBinding
import com.abrand.custom.databinding.ItemNewsLoadBinding

class TicketsAdapter(private val items: MutableList<LotteryTicketItem>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    var listener: Listener? = null
    var currentTotalTickets = 0
    var loadTicketsVH: LoadTicketsViewHolder? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            LotteryTicketItem.ItemType.TICKET.id -> {
                val view = ItemLotteryTicketBinding.inflate(inflater, parent, false)
                return TicketViewHolder(view)
            }
            else -> {
                val view = ItemNewsLoadBinding.inflate(inflater, parent, false)
                return LoadTicketsViewHolder(view)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = items[position]

        when (holder) {
            is TicketViewHolder -> {
                item.ticket?.let { holder.bind(it) }
            }
            is LoadTicketsViewHolder -> {
                loadTicketsVH = holder
            }
        }
    }

    override fun getItemCount() = items.size

    override fun getItemViewType(position: Int): Int {
        return items[position].itemType.id
    }

    fun getTicketsCount() = items.count {
        LotteryTicketItem.ItemType.TICKET == it.itemType
    }

    fun setTicketsLoadingState(isTicketsLoading: Boolean) {
        loadTicketsVH?.setLotteriesLoadingState(isTicketsLoading)
    }

    fun appendList(ticketItems: MutableList<LotteryTicketItem>) {
        items.addAll(items.size - 1, ticketItems)
        notifyDataSetChanged()
    }

    class TicketViewHolder(val view: ItemLotteryTicketBinding) : RecyclerView.ViewHolder(view.root) {
        fun bind(ticket: LocalLotteryTicket) {
            view.tvTicketId.text = ticket.id.toString()

            if (ticket.isGold) {
                view.tvTicketId.setTextColor(Color.BLACK)
                view.ivBgTicket.setImageDrawable(ContextCompat.getDrawable(view.root.context, R.drawable.bg_ticket_gold))
            } else {
                view.tvTicketId.setTextColor(Color.WHITE)
                view.ivBgTicket.setImageDrawable(ContextCompat.getDrawable(view.root.context, R.drawable.bg_ticket_regular))
            }
        }
    }

    inner class LoadTicketsViewHolder(val view: ItemNewsLoadBinding) : RecyclerView.ViewHolder(view.root) {
        init {
            setLoadMoreView()
            bind()
        }

        fun bind() {
            view.btnLoadMore.setOnClickListener {
                listener?.onLoadMoreClicked()
            }
        }

        private fun setLoadMoreView() {
            if (currentTotalTickets > getTicketsCount()) {
                if (currentTotalTickets - getTicketsCount() >= LotteryInternalViewModel.TICKETS_LOAD_STEP) {
                    view.btnLoadMore.text =
                        itemView.context.resources.getQuantityString(R.plurals.more_tickets,
                            LotteryInternalViewModel.TICKETS_LOAD_STEP, LotteryInternalViewModel.TICKETS_LOAD_STEP)
                } else {
                    val diffLotteriesCount = currentTotalTickets - getTicketsCount()
                    view.btnLoadMore.text = itemView.context.resources.getQuantityString(R.plurals.more_tickets, diffLotteriesCount, diffLotteriesCount)
                }
                view.btnLoadMore.visibility = View.VISIBLE
            } else {
                view.btnLoadMore.visibility = View.GONE
            }
        }

        fun setLotteriesLoadingState(isLoading: Boolean) {
            if (isLoading) {
                view.ivLoader.visibility = View.VISIBLE
                val animation = AnimationUtils.loadAnimation(itemView.context, R.anim.rotation_loader)
                view.ivLoader.startAnimation(animation)
                view.btnLoadMore.visibility = View.INVISIBLE
            } else {
                view.ivLoader.clearAnimation()
                view.ivLoader.visibility = View.GONE
                setLoadMoreView()
            }
        }
    }

    interface Listener {
        fun onLoadMoreClicked()
    }

}
