# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception  # Optional: Keep custom exceptions.

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

### ### General ###
#-obfuscationdictionary dictionary.txt
#-classobfuscationdictionary dictionary.txt
#-packageobfuscationdictionary dictionary.txt
#-useuniqueclassmembernames
-dontshrink #TODO decide: are we need this?
### ### ### ###

# Google samples
-keepattributes InnerClasses
-keepattributes EnclosingMethod

-dontwarn org.xmlpull.v1.**
-dontnote org.xmlpull.v1.**
-keep class org.xmlpull.** { *; }
-keepclassmembers class org.xmlpull.** { *; }

### FACEBOOK ###
-keepclassmembers class * implements java.io.Serializable {
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

-keepnames class com.facebook.FacebookActivity
-keepnames class com.facebook.CustomTabActivity

-keep class com.facebook.all.All

-keep public class com.android.vending.billing.IInAppBillingService {
    public static com.android.vending.billing.IInAppBillingService asInterface(android.os.IBinder);
    public android.os.Bundle getSkuDetails(int, java.lang.String, java.lang.String, android.os.Bundle);
}

-keep class android.support.v7.app.AppCompatActivity {
  public void onCreate(...);
}
-keep class com.google.firebase.analytics.FirebaseAnalytics {
  public protected *;
}
-keep class com.google.android.instantapps.InstantApps {
  public boolean isInstantApp(...);
}

# enums
-optimizations !class/unboxing/enum
-keepclassmembers class * extends java.lang.Enum {
    <fields>;
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class com.google.android.material.button.** { *; }
-keepnames class com.google.android.material.button.** { *; }
-keep  class androidx.appcompat.widget.** { *; }
-keepnames  class androidx.appcompat.widget.** { *; }

# Kotlin coroutines
-keepnames class kotlinx.coroutines.android.AndroidExceptionPreHandler {}
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepnames class kotlinx.** { *; }
-keep class kotlinx.** { *; }
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}

## Square Picasso specific rules ##
## https://square.github.io/picasso/ ##
-dontwarn com.squareup.okhttp.**

# keep lambdas
-dontwarn java.lang.invoke.*
-dontwarn **$$Lambda$*
-keep class **$$Lambda$*

# other
-dontwarn com.google.android.gms.**

# Retrofit 2.X
## https://square.github.io/retrofit/ ##
# Retrofit does reflection on generic parameters. InnerClasses is required to use Signature and
# EnclosingMethod is required to use InnerClasses.
-keepattributes Signature, InnerClasses, EnclosingMethod

# Retrofit does reflection on method and parameter annotations.
-keepattributes RuntimeVisibleAnnotations, RuntimeVisibleParameterAnnotations

# Retain service method parameters when optimizing.
-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Ignore annotation used for build tooling.
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement

# Ignore JSR 305 annotations for embedding nullability information.
-dontwarn javax.annotation.**

# Guarded by a NoClassDefFoundError try/catch and only used when on the classpath.
-dontwarn kotlin.Unit

# Top-level functions that can only be used by Kotlin.
-dontwarn retrofit2.KotlinExtensions
-dontwarn retrofit2.KotlinExtensions$*

# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>
#Retrofit END

# Dagger ProGuard rules.
# https://github.com/square/dagger
-dontwarn dagger.internal.codegen.**
-keepclassmembers,allowobfuscation class * {
    @javax.inject.* *;
    @dagger.* *;
    <init>();
}
-keep class dagger.* { *; }
-keep class javax.inject.* { *; }
-keep class * extends dagger.internal.Binding
-keep class * extends dagger.internal.ModuleAdapter
-keep class * extends dagger.internal.StaticInjection
-keep class org.threeten.**

# RxJava 0.21
-keep class rx.schedulers.Schedulers {
    public static <methods>;
}
-keep class rx.schedulers.ImmediateScheduler {
    public <methods>;
}
-keep class rx.schedulers.TestScheduler {
    public <methods>;
}
-keep class rx.schedulers.Schedulers {
    public static ** test();
}
-keepclassmembers class rx.internal.util.unsafe.** {
    long producerIndex;
    long consumerIndex;
}
-dontwarn rx.internal.util.unsafe.**

# GSON
-keepattributes Signature
-keepattributes *Annotation*
-keep class com.google.gson.annotations.** { *; }

# Prevent proguard from stripping interface information from TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# OkHttp
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okhttp3.internal.platform.*

# android.animation
-dontwarn android.animation.**
-keep class android.animation.** { *; }

# Glide
-keep public class com.bumptech.glide.** {
  public protected *;
}
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-dontwarn com.bumptech.glide.**

## AndroidAnnotations
-dontwarn org.androidannotations.api.rest.**
-dontwarn com.google.errorprone.annotations.*

# Android Support
-dontwarn android.support.v4.content.res.**
-keep class android.support.v4.content.res.** { *; }
-keep class android.support.v7.widget.RoundRectDrawable { *; }
-keep public class android.support.v7.widget.** { *; }
-keep public class android.support.v7.internal.widget.** { *; }
-keep public class android.support.v7.internal.view.menu.** { *; }
-keep public class * extends android.support.v4.view.ActionProvider {
    public <init>(android.content.Context);
}
-dontwarn android.support.design.**
-keep class android.support.design.** { *; }
-keep interface android.support.design.** { *; }
-keep public class android.support.design.R$* { *; }


# Okio
-keep class sun.misc.Unsafe { *; }
-dontwarn okio.**
-dontwarn java.nio.file.*
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement


#lifecycle
-keep public class android.arch.lifecycle.** {     public protected *; }
-keepclassmembers class ** { @android.arch.lifecycle.OnLifecycleEvent public *; }
-keep public class * extends android.arch.lifecycle.LifecycleFragment{ }
-keep public class * extends android.arch.lifecycle.ViewModel

# Android Arch
-keep class android.arch.** { *; }
#-dontwarn android.arch.lifecycle.HolderFragment.**
#
#-keep class * extends android.arch.lifecycle.HolderFragment{ }
#-keep class android.arch.** { *; }

#databinding
-dontwarn android.databinding.**
-keep class android.databinding.** { *; }
-keep class android.databinding.annotationprocessor.** { *; }

#Adjust sdk
#https://github.com/adjust/android_sdk#qs-proguard
#If you are not publishing your app in the Google Play Store, use the following com.adjust.sdk package rules:
#-keep public class com.adjust.sdk.** { *; }
#else:
-keep class com.adjust.sdk.** { *; }
-keep class com.google.android.gms.common.ConnectionResult {
    int SUCCESS;
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient {
    com.google.android.gms.ads.identifier.AdvertisingIdClient$Info getAdvertisingIdInfo(android.content.Context);
}
-keep class com.google.android.gms.ads.identifier.AdvertisingIdClient$Info {
    java.lang.String getId();
    boolean isLimitAdTrackingEnabled();
}
-keep public class com.android.installreferrer.** { *; }

# Fix JSON model for current project
-keep class **.json** { *; }

# Cipher.so
-keep class net.idik.lib.cipher.so.** {*;}

# Gson models
-keep class com.abrand.custom.data.entity.appconfig.InstallConfig { private <fields>; }

### KotlinX Serialization
# Keep `Companion` object fields of serializable classes.
# This avoids serializer lookup through `getDeclaredClasses` as done for named companion objects.
-if @kotlinx.serialization.Serializable class **
-keepclassmembers class <1> {
    static <1>$Companion Companion;
}

# Keep `serializer()` on companion objects (both default and named) of serializable classes.
-if @kotlinx.serialization.Serializable class ** {
    static **$* *;
}
-keepclassmembers class <2>$<3> {
    kotlinx.serialization.KSerializer serializer(...);
}

# Keep `INSTANCE.serializer()` of serializable objects.
-if @kotlinx.serialization.Serializable class ** {
    public static ** INSTANCE;
}
-keepclassmembers class <1> {
    public static <1> INSTANCE;
    kotlinx.serialization.KSerializer serializer(...);
}

# @Serializable and @Polymorphic are used at runtime for polymorphic serialization.
-keepattributes RuntimeVisibleAnnotations,AnnotationDefault

# Don't print notes about potential mistakes or omissions in the configuration for kotlinx-serialization classes
# See also https://github.com/Kotlin/kotlinx.serialization/issues/1900
-dontnote kotlinx.serialization.**

# Serialization core uses `java.lang.ClassValue` for caching inside these specified classes.
# If there is no `java.lang.ClassValue` (for example, in Android), then R8/ProGuard will print a warning.
# However, since in this case they will not be used, we can disable these warnings
-dontwarn kotlinx.serialization.internal.ClassValueReferences

# disable optimisation for descriptor field because in some versions of ProGuard, optimization generates incorrect bytecode that causes a verification error
# see https://github.com/Kotlin/kotlinx.serialization/issues/2719
-keepclassmembers public class **$$serializer {
    private ** descriptor;
}

-dontwarn kotlinx.serialization.DeserializationStrategy
-dontwarn kotlinx.serialization.InternalSerializationApi
-dontwarn kotlinx.serialization.MissingFieldException
-dontwarn kotlinx.serialization.SealedClassSerializer
-dontwarn kotlinx.serialization.SerializationException
-dontwarn kotlinx.serialization.SerializationStrategy
-dontwarn kotlinx.serialization.descriptors.ClassSerialDescriptorBuilder
-dontwarn kotlinx.serialization.descriptors.PrimitiveKind$STRING
-dontwarn kotlinx.serialization.descriptors.PrimitiveKind
-dontwarn kotlinx.serialization.descriptors.SerialDescriptor
-dontwarn kotlinx.serialization.descriptors.SerialDescriptorsKt
-dontwarn kotlinx.serialization.encoding.CompositeDecoder
-dontwarn kotlinx.serialization.encoding.CompositeEncoder
-dontwarn kotlinx.serialization.encoding.Decoder
-dontwarn kotlinx.serialization.encoding.Encoder
-dontwarn kotlinx.serialization.internal.AbstractPolymorphicSerializer
-dontwarn kotlinx.serialization.internal.EnumSerializer
-dontwarn kotlinx.serialization.internal.IntSerializer
-dontwarn kotlinx.serialization.internal.LongSerializer
-dontwarn kotlinx.serialization.internal.ShortSerializer
### KotlinX Serialization END

# Datastore
#TODO: remove on 1.1.7 DataStore version
-keepclassmembers class * extends androidx.datastore.preferences.protobuf.GeneratedMessageLite {
    <fields>;
}
