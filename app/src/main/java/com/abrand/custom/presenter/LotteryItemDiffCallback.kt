package com.abrand.custom.presenter

import androidx.recyclerview.widget.DiffUtil
import com.abrand.custom.data.entity.lottery.LotteryItem

class LotteryItemDiffCallback(
    private val oldList: List<LotteryItem>,
    private val newList: List<LotteryItem>
) : DiffUtil.Callback() {

    override fun getOldListSize() = oldList.size

    override fun getNewListSize() = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition].isTheSame(newList[newItemPosition])
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Bo<PERSON>an {
        return oldList[oldItemPosition] == newList[newItemPosition]
    }
}

