package com.abrand.custom.ui.tournamentinternal

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.tools.GeneralTools

class PrizesAdapter(private val data: List<TournamentsItem.TournamentPrize>, private val isDynamic: Boolean) :
        RecyclerView.Adapter<PrizesAdapter.PrizeViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PrizeViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return PrizeViewHolder(inflater, parent)
    }

    override fun onBindViewHolder(holder: PrizeViewHolder, position: Int) {
        val prize = data[position]
        holder.tv_number.text = prize.placeNumber.toString()
        if(isDynamic){
            holder.tv_fund.text = prize.sum + " %"
        }else{
            holder.tv_fund.text = GeneralTools.formatBalance(Settings.get().userCurrencyCode, prize.sum.toDouble())
        }

        when (prize.placeNumber) {
            1 -> {
                holder.tv_fund.setTextColor(Color.parseColor("#FFA200"))
                holder.tv_number.setTextColor(Color.parseColor("#000000"))
                holder.tv_number.setBackgroundResource(R.drawable.ic_gold)
                holder.space.visibility = View.VISIBLE
            }
            2 -> {
                holder.tv_fund.setTextColor(Color.parseColor("#ffffff"))
                holder.tv_number.setTextColor(Color.parseColor("#000000"))
                holder.tv_number.setBackgroundResource(R.drawable.ic_silver)
                holder.space.visibility = View.VISIBLE
            }
            3 -> {
                holder.tv_fund.setTextColor(Color.parseColor("#FF7D01"))
                holder.tv_number.setTextColor(Color.parseColor("#000000"))
                holder.tv_number.setBackgroundResource(R.drawable.ic_bronze)
                holder.space.visibility = View.VISIBLE
            }
            else -> { // Note the block
                holder.tv_fund.setTextColor(Color.parseColor("#ffffff"))
                holder.tv_number.setTextColor(Color.parseColor("#ffffff"))
                holder.tv_number.setBackgroundResource(R.drawable.ic_noplace)
            }
        }


    }

    override fun getItemCount() = data.size

    class PrizeViewHolder(inflater: LayoutInflater, parent: ViewGroup) :
        RecyclerView.ViewHolder(inflater.inflate(R.layout.item_prize_list, parent, false)) {
        var tv_number: TextView = itemView.findViewById(R.id.tv_number)
        var tv_fund: TextView = itemView.findViewById(R.id.tv_fund)
        var space: ImageView = itemView.findViewById(R.id.space)
    }

}
