package com.abrand.custom.ui.news

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.NewsItem
import com.abrand.custom.data.entity.UrlSource
import com.abrand.custom.data.entity.User
import com.abrand.custom.data.repositories.YearRepository
import com.abrand.custom.databinding.FragmentNewsInternalBinding
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.readTextFromAsset
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment
import com.squareup.picasso.Picasso
import com.squareup.picasso.Picasso.LoadedFrom
import com.squareup.picasso.Target

class NewsInternalFragment : Fragment(R.layout.fragment_news_internal) {
    private val TAG = "NewsInternalFragment"
    private var newsItem: NewsItem? = null
    private val BONUSES_PATH = "bonuses"
    private var binding: FragmentNewsInternalBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            newsItem = (it.getSerializable(NEWS_ITEM_KEY) as NewsItem)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentNewsInternalBinding.bind(view)
        binding?.tvTitle?.text = newsItem?.title

        binding?.lFooter?.glPaymentSystemsView?.setOnClickListener {
            if (User.State.PLAYER == Settings.get().userState) {
                (activity as? MainActivity)?.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
            } else {
                (activity as? MainActivity)?.openEnterScreen(false)
            }
        }

        binding?.lFooter?.tvCopyrightYear?.text = context?.getString(
            R.string.app_copyright_year,
            YearRepository.getCurrentYear()
        )

        setCreatedAt()
        loadImageBackground()
        setHtmlContent()

        setupInsets()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun setupInsets() {
        binding?.apply {
            ViewCompat.setOnApplyWindowInsetsListener(tvTitle) { view, insets ->
                view.updatePadding(top = insets.systemWindowInsetTop)
                insets
            }

            ViewCompat.setOnApplyWindowInsetsListener(scrollContainer) { view, insets ->
                view.updatePadding(bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }

    private fun setCreatedAt() {
        val createdAt = newsItem?.createdAt
        if (!TextUtils.isEmpty(createdAt as String)) {
            val formattedDate = DateTools.getFormattedDate(
                requireContext(),
                createdAt,
                DateTools.FormatDateType.NEWS
            )
            if (!TextUtils.isEmpty(formattedDate)) {
                binding?.tvCreatedAt?.text = formattedDate
            } else {
                binding?.tvCreatedAt?.text = createdAt
            }
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun setHtmlContent() {
        val CSS_ASSET_FILE_NAME = "news.css"

        binding?.apply {
            wvContent.setBackgroundColor(Color.TRANSPARENT)
            wvContent.settings.javaScriptEnabled = true
            wvContent.webViewClient = object : WebViewClient() {
                override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                    (activity as MainActivity).processDeepLinkUrl(url, UrlSource.NEWS)
                    return true
                }

                override fun onPageFinished(view: WebView, url: String) {
                    val css = context?.readTextFromAsset(CSS_ASSET_FILE_NAME)
                    val js =
                        "var style = document.createElement('style'); style.innerHTML = `$css`; document.head.appendChild(style);"
                    wvContent.evaluateJavascript(js, null)
                    wvContent.visibility = View.VISIBLE
                    super.onPageFinished(view, url)
                }
            }
            newsItem?.content?.let {
                wvContent.loadDataWithBaseURL(
                    ApoloConfig.BASE_URL, it, "text/html",
                    "UTF-8", ""
                )
            }
        }
    }

    private fun loadImageBackground() {
        if (newsItem != null && !TextUtils.isEmpty(newsItem?.mobileIcon)) {
            Picasso.get()
                .load(ApoloConfig.getFullUrl(newsItem?.mobileIcon))
                .resize(
                    GeneralTools.getScreenWidth(requireContext()),
                    requireContext().resources.getDimensionPixelSize(R.dimen.news_internal_image_bg_height)
                )
                .centerCrop()
                .into(backgroundTarget)
        }
    }

    //strong reference to the Target otherwise onBitmapLoaded does not always work
    var backgroundTarget: Target = object : Target {
        override fun onPrepareLoad(placeHolderDrawable: Drawable?) {}

        override fun onBitmapFailed(e: Exception?, errorDrawable: Drawable?) {}

        override fun onBitmapLoaded(bitmap: Bitmap, from: LoadedFrom?) {
            binding?.ivBg?.setImageBitmap(bitmap)
        }
    }

    companion object {
        const val NEWS_ITEM_KEY = "NewsItemKey"
    }
}
