package com.abrand.custom.ui.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.abrand.custom.R
import com.abrand.custom.ui.views.textfield.TextField

class PromoCodeField : TextField {
    var listener : PromoCodeListener? = null

    constructor(context: Context) : super(context) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init()
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr) {
        init()
    }

    fun init() {
        rootView.findViewById<View>(R.id.ib_promo_code_enter).setOnClickListener {
            listener?.onPromoCodeEntered(text)
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.view_promocode_field
    }

    interface PromoCodeListener {
        fun onPromoCodeEntered(promoCode: String)
    }
}
