package com.abrand.custom.ui.lotteryinternal

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.lottery.LocalLotteryPrize
import com.abrand.custom.databinding.ItemPrizeListBinding
import com.abrand.custom.tools.GeneralTools

class PrizesAdapter(private val items: List<LocalLotteryPrize>) :
    RecyclerView.Adapter<PrizesAdapter.PrizeViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PrizeViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = ItemPrizeListBinding.inflate(inflater, parent, false)
        return PrizeViewHolder(view)
    }

    override fun onBindViewHolder(holder: PrizeViewHolder, position: Int) {
        val prize = items[position]

        holder.view.tvNumber.text = prize.place.toString()
        holder.view.tvFund.text = GeneralTools.formatBalance(Settings.get().userCurrencyCode, prize.sum.toDouble())

        when (prize.place) {
            1 -> {
                holder.view.tvFund.setTextColor(Color.parseColor("#FFA200"))
                holder.view.tvNumber.setTextColor(Color.parseColor("#000000"))
                holder.view.tvNumber.setBackgroundResource(R.drawable.ic_gold)
                holder.view.space.visibility = View.VISIBLE
            }
            2 -> {
                holder.view.tvFund.setTextColor(Color.parseColor("#ffffff"))
                holder.view.tvNumber.setTextColor(Color.parseColor("#000000"))
                holder.view.tvNumber.setBackgroundResource(R.drawable.ic_silver)
                holder.view.space.visibility = View.VISIBLE
            }
            3 -> {
                holder.view.tvFund.setTextColor(Color.parseColor("#FF7D01"))
                holder.view.tvNumber.setTextColor(Color.parseColor("#000000"))
                holder.view.tvNumber.setBackgroundResource(R.drawable.ic_bronze)
                holder.view.space.visibility = View.VISIBLE
            }
            else -> {
                holder.view.tvFund.setTextColor(Color.parseColor("#ffffff"))
                holder.view.tvNumber.setTextColor(Color.parseColor("#ffffff"))
                holder.view.tvNumber.setBackgroundResource(R.drawable.ic_noplace)
            }
        }
    }

    override fun getItemCount() = items.size

    class PrizeViewHolder(val view: ItemPrizeListBinding) : RecyclerView.ViewHolder(view.root)
}
