package com.abrand.custom.data;

import com.abrand.custom.BuildConfig;
import com.abrand.custom.tools.Crypt;

public class Constants {
    public static final String CAPTCHA_URL_PREF_        = Crypt.decrypt(BuildConfig.CAPTCHA_PREF);
    public static final String CLIENT_SHORT_DATE_FORMAT = "dd MMMM yyyy";
    public static final String CLIENT_SHORT_DATE_FORMAT_2  = "dd.MM.yyyy";
    public static final String SERVER_SHORT_DATE_FORMAT = "yyyy-MM-dd";
    public static final String CLIENT_LONG_DATE_FORMAT  = "dd.MM.yyyy, HH:mm";
    public static final String SERVER_LONG_DATE_FORMAT  = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String SERVER_LONG_DATE_FORMAT_UTC  = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    public static final String MESSAGES_DATE_FORMAT     = "dd MMMM yyyy HH:mm";
    public static final String REF_CODE = "ref_code";
    public static final String REF_CODE_URL = "refCode";
    public static final String TRACK_CLICK = "track_click";
    public static final String LOGIN_TOKEN = "login_token";
    public static final String LOGIN_TOKEN_URL = "alogin";
    public static final String OPEN_LOGIN_SCREEN = "openLoginScreen";
    public static final String BONUS_ACTION = "action";
    public static final String BONUS_ACTION_RECEIVE = "bonus_receive";
    public static final String BONUS_ACTION_ACTIVATE = "bonus_activate";
    public static final String BONUS_ID = "bonusId";
    public static final String USER_ID = "userId";
    public static final String HASH = "hash";
    public static final String URI_URL = "uri";
    public static final String REDIRECT_URL = "redirect";
    public static final String TARGET_URL = "target";

    public static final String DEEP_LINK_URL = "url";
    public static final String DEEP_LINK_PAYMENTS_REF = "popup-payments";
    public static final String DEEP_LINK_PAYMENTS_HISTORY_REF = "popup-payments-his";
    public static final String DEEP_LINK_LOGIN_REF    = "popup-login";
    public static final String DEEP_LINK_REGISTER_REF = "popup-reg";
    public static final String DEEP_LINK_PROFILE_REF = "popup-profile";
    public static final String DEEP_LINK_LOYALTY_EXCHANGE_REF = "popup-loyality";
    public static final String DEEP_LINK_MESSAGEBOX_REF = "popup-messagebox";
    public static final String DEEP_LINK_NEW_PASS_REF = "popup-newpass";
    public static final String DEEP_LINK_RESULTS_REF = "results";
    public static final String DEEP_LINK_WHEEL_OF_FORTUNE_PAID_REF = "popup-wheelOfForunePaid";
    public static final String DEEP_LINK_WHEEL_OF_FORTUNE_REF = "popup-wheelOfFortune";
    public static final String DEEP_LINK_BONUSES = "/bonuses/list";
    public static final String DEEP_LINK_GAME = "/games";
    public static final String DEEP_LINK_DEMO_GAME = "/igrovye-avtomaty";
    public static final String DEEP_LINK_GAME_ROOM = "/gamehall";
    public static final String DEEP_LINK_TERMS = "/terms";
    public static final String DEEP_LINK_NEWS = "/news";
    public static final String DEEP_LINK_LOYALTY_PROGRAM = "/privileges";
    public static final String DEEP_LINK_GAME_POPULAR = "/game/popular";
    public static final String DEEP_LINK_GAME_NEW = "/game/new";
    public static final String DEEP_LINK_GAME_SLOTS = "/game/slots";
    public static final String DEEP_LINK_GAME_FAVOURITES = "/game/favourites";
    public static final String DEEP_LINK_GAME_TABLES = "/game/tables";
    public static final String DEEP_LINK_BONUS_ACTIVATE = "/bonus/api/activate";
    public static final String DEEP_LINK_BONUS_RECEIVE = "/bonus/api/receive";
    public static final String DEEP_LINK_EMAIL_CONFIRMATION = "/email-confirmation/confirm";
    public static final String DEEP_LINK_TOURNAMENTS = "/tournaments";
    public static final String DEEP_LINK_CASHBACK = "/cashback/info";
    public static final String DEEP_LINK_LOTTERIES = "/lotteries";

    public static final String PATH_PAYMENTS_SUCCESS = "/payments-success";
    public static final String PATH_PAYMENTS_ERROR = "/payments-error";
    public static final String PATH_PAYOUT_INPROGRESS = "/payout-inprogress";

    //Server fields
    public static final String SERVER_CODE = "code";
    public static final String SERVER_EMAIL_CONFIRMATION_ALREADY_REQUESTED = "email_confirmation_already_requested";
    public static final String SERVER_UNAUTHORIZED = "unauthorized";
    public static final String SERVER_INVALID_PROMO_CODE = "invalid_promocode";

    //Analytics
    public static final int ANALYTICS_PUSH_SUBSCRIPTION_ON_DATA  = 1;
    public static final int ANALYTICS_PUSH_SUBSCRIPTION_OFF_DATA = 0;

    //Profile
    public static final int MIN_PASSWORD_LENGTH = 6;

    //Request codes
    public static final int BIOMETRIC_SETTINGS_REQUEST_CODE = 1001;
}
