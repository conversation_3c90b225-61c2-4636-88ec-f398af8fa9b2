package com.abrand.custom.ui.lotteryinternal

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.data.entity.lottery.LocalLottery
import com.abrand.custom.data.entity.lottery.LocalLotteryTicket
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException

class LotteryInternalViewModel : ViewModel() {
    private val _lotteryLiveData = MutableLiveData<Resource<LocalLottery, ServerError, ApolloException>>()
    val lotteryLiveData: LiveData<Resource<LocalLottery, ServerError, ApolloException>> = _lotteryLiveData

    private val _buyTicketsLiveData = MutableLiveData<Resource<MutableList<LocalLotteryTicket>, Server<PERSON>rror, ApolloException>>()
    val buyTicketsLiveData: LiveData<Resource<MutableList<LocalLotteryTicket>, ServerError, ApolloException>> = _buyTicketsLiveData

    private val _getPrizeLiveData = SingleLiveEvent<Resource<Int, ServerError, ApolloException>>()
    val getPrizeLiveData: LiveData<Resource<Int, ServerError, ApolloException>> = _getPrizeLiveData

    fun getLottery(isUserAuthorized: Boolean, id: Int) {
        ApolloProcessorKt.getLottery(isUserAuthorized, id, object : GenericTarget<LocalLottery> {
            override fun onSuccess(t: LocalLottery?) {
                _lotteryLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _lotteryLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _lotteryLiveData.postValue(Resource.failure(e))
            }
        })
    }

    fun buyTickets(lotteryId: Int, ticketsCount: Int) {
        ApolloProcessorKt.buyLotteryTickets(lotteryId, ticketsCount, object : GenericTarget<MutableList<LocalLotteryTicket>> {
            override fun onSuccess(t: MutableList<LocalLotteryTicket>?) {
                _buyTicketsLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _buyTicketsLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _buyTicketsLiveData.postValue(Resource.failure(e))
            }

        })
    }

    fun buyTicketsPackage(lotteryId: Int, ticketsCount: Int) {
        ApolloProcessorKt.buyLotteryTicketsPackage(lotteryId, ticketsCount, object : GenericTarget<MutableList<LocalLotteryTicket>> {
            override fun onSuccess(t: MutableList<LocalLotteryTicket>?) {
                _buyTicketsLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _buyTicketsLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _buyTicketsLiveData.postValue(Resource.failure(e))
            }

        })
    }

    fun getPrize(winnerId: Int) {
        ApolloProcessorKt.getLotteryPrize(winnerId, object : GenericTarget<Int> {
            override fun onSuccess(t: Int?) {
                _getPrizeLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _getPrizeLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _getPrizeLiveData.postValue(Resource.failure(e))
            }

        })
    }

    companion object {
        const val TICKETS_LOAD_STEP = 25
        const val DEFAULT_NUMBER_OF_TICKETS_TO_BUY = "30"
    }
}
