package com.abrand.custom.data

data class Resource<out T, out D, out V>(val status: Status, val data: T?, val error: D?, val failure: V?) {
    companion object {
        fun <T, D, V> success(data: T?): Resource<T, D, V> {
            return Resource(Status.SUCCESS, data, null, null)
        }

        fun <T, D, V> error(error: D): Resource<T, D, V> {
            return Resource(Status.ERROR, null, error, null)
        }

        fun <T, D, V> failure(exception: V): Resource<T, D, V> {
            return Resource(Status.FAILURE, null, null, exception)
        }

        fun <T, D, V> viewerEmpty() : Resource<T, D, V> {
            return Resource(Status.VIEWER_EMPTY, null, null, null)
        }
    }
}
