package com.abrand.custom.ui.home;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;

import com.abrand.custom.R;
import com.abrand.custom.data.Settings;
import com.abrand.custom.data.entity.GameListItem;
import com.abrand.custom.data.entity.LoadGamesResult;
import com.abrand.custom.data.entity.LocalGameItem;
import com.abrand.custom.data.entity.LocalNews;
import com.abrand.custom.data.entity.NewsItem;
import com.abrand.custom.data.entity.Screen;
import com.abrand.custom.data.entity.SessionDataHolder;
import com.abrand.custom.data.entity.UrlSource;
import com.abrand.custom.data.entity.User;
import com.abrand.custom.databinding.FragmentHomeBinding;
import com.abrand.custom.interfaces.FooterListener;
import com.abrand.custom.tools.GameSpacingItemDecoration;
import com.abrand.custom.tools.GeneralTools;
import com.abrand.custom.ui.activitymain.MainActivity;
import com.abrand.custom.ui.interfaces.GameClickListener;
import com.abrand.custom.ui.interfaces.SlideClickListener;
import com.abrand.custom.ui.interfaces.UserStateListener;
import com.abrand.custom.ui.payments.PaymentsFragment;
import com.google.android.material.snackbar.Snackbar;

import java.util.ArrayList;
import java.util.List;

public class HomeFragment extends Fragment implements
        GameClickListener,
        SlideClickListener,
        UserStateListener {

    private static final String TAG        = HomeFragment.class.getSimpleName();
    public static final  int    LOAD_STEP  = 12;
    static final         int    COLS_COUNT = 3;

    private FragmentHomeBinding binding;
    private GamesAdapter        gamesAdapter;
    private GameType            selectedType;
    private HomeViewModel       viewModel;

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {

        viewModel = new ViewModelProvider(this).get(HomeViewModel.class);
        observeViewModel();

        if ( binding == null || gamesAdapter.getGameCount() == 0) {
            binding = FragmentHomeBinding.inflate(getLayoutInflater());

            gamesAdapter = new GamesAdapter(getContext(), GeneralTools.getGameIconSize(getActivity(), Screen.HOME),
                    getAdditionalBlockWidth(), this, this,
                    selectorListener, additionalBlocksClickListener, loadMoreClickListener, footerListener, Screen.HOME);

            gamesAdapter.setHeaderTopMargin(GeneralTools.getStatusBarHeight(getContext()));

            GridLayoutManager layoutManager = new GridLayoutManager(getContext(), COLS_COUNT);
            layoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
                @Override
                public int getSpanSize(int position) {
                    if ( gamesAdapter.getItemViewType(position) == GameListItem.ItemType.GAME.getId() ) {
                        return 1;
                    } else {
                        return COLS_COUNT;
                    }
                }
            });
            binding.rvGamesList.setLayoutManager(layoutManager);
            binding.rvGamesList.setAdapter(gamesAdapter);

            int gameIconStartEndMargin = getContext().getResources().getDimensionPixelSize(R.dimen.game_icon_start_end_margin);
            int gameIconLeftRightSpacing = getContext().getResources().getDimensionPixelSize(R.dimen.game_icon_left_right_spacing);
            int gameIconBottomSpacing = getContext().getResources().getDimensionPixelSize(R.dimen.game_icon_bottom_spacing);
            binding.rvGamesList.addItemDecoration(new GameSpacingItemDecoration(COLS_COUNT,
                    gameIconStartEndMargin, gameIconLeftRightSpacing, gameIconBottomSpacing, Screen.HOME));

            selectedType = GameType.BEST;
            gamesAdapter.showHeaderLoadAndFooter();

            loadGames(selectedType, 0);
            viewModel.getHomeAdditionalBlocks();
        }

        applyState(Settings.get().getUserState());
        setupInsets();

        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if ( GameType.FAVOURITES == selectedType && SessionDataHolder.getInstance().needUpdateFavouriteList ) {
            loadGames(selectedType, 0);
        }
        SessionDataHolder.getInstance().needUpdateFavouriteList = false;
    }

    private void setupInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.rvGamesList, (view, insets) -> {
            binding.rvGamesList.setPadding(binding.rvGamesList.getPaddingLeft(), binding.rvGamesList.getPaddingTop(),
                    binding.rvGamesList.getPaddingRight(), insets.getSystemWindowInsetBottom());
            return insets;
        });
    }

    private void observeViewModel() {
        viewModel.getSlidesLiveData().observe(getViewLifecycleOwner(), it -> {
            switch ( it.getStatus() ) {
                case SUCCESS:
                    Activity activity = getActivity();
                    if ( activity != null && isResumed()) {
                        activity.runOnUiThread(() -> gamesAdapter.setBanners(it.getData()));
                    }
                    break;
                case FAILURE:
                    Log.e(TAG, "Failed to load slides: " + it.getFailure());
                    if ( getActivity() != null && HomeFragment.this.isResumed() ) {
                        getActivity().runOnUiThread(() -> {
                            ((MainActivity) getActivity()).showConnectionIssueMessage(it.getFailure());
                        });
                    }
                    break;
            }
        });

        viewModel.getGamesLiveData().observe(getViewLifecycleOwner(), it -> {
            switch ( it.getStatus() ) {
                case SUCCESS:
                    if ( !isResumed() ) { //Think it useless [by Reaver]
                        return;
                    }

                    binding.getRoot().post(() -> {
                        LoadGamesResult loadGamesResult = it.getData();
                        if ( loadGamesResult != null ) {
                            gamesAdapter.setCurrentTotalGames(loadGamesResult.getTotal());

                            List<LocalGameItem> gameItems = loadGamesResult.getGameItems();
                            if ( loadGamesResult.getOffset() == 0 ) {
                                if ( gameItems.size() == 0 ) {
                                    Snackbar.make(binding.rvGamesList, R.string.no_games_here,
                                            Snackbar.LENGTH_SHORT).show();
                                }

                                List<GameListItem> newList = new ArrayList<>();
                                for ( LocalGameItem game : gameItems ) {
                                    newList.add(new GameListItem(GameListItem.ItemType.GAME, game));
                                }
                                gamesAdapter.setList(newList);
                            } else {
                                List<GameListItem> newList = new ArrayList<>();
                                for ( LocalGameItem game : gameItems ) {
                                    newList.add(new GameListItem(GameListItem.ItemType.GAME, game));
                                }
                                gamesAdapter.appendList(newList);
                                if ( gameItems.size() == 0 ) {
                                    Toast.makeText(HomeFragment.this.getContext(),
                                            "Nothing to load in that category", Toast.LENGTH_SHORT).show();
                                }
                            }
                        }
                        gamesAdapter.setGameLoadingState(false);
                    });
                    break;
                case FAILURE:
                    Log.e(TAG, "Failed to load games: " + it.getFailure());
                    if ( getActivity() != null && HomeFragment.this.isResumed() ) {
                        getActivity().runOnUiThread(() -> {
                            ((MainActivity) getActivity()).showConnectionIssueMessage(it.getFailure());
                        });
                    }
                    break;
            }
        });

        viewModel.getHomeAdditionalBlocksLiveData().observe(getViewLifecycleOwner(), it -> {
            switch ( it.getStatus() ) {
                case SUCCESS:
                    gamesAdapter.setHomeAdditionalBlocks(it.getData());
                    break;
                case ERROR:
                    break;
                case FAILURE:
                    Log.e(TAG, "Failed to load home additional blocks: " + it.getFailure());
                    Activity activity = getActivity();
                    if ( activity != null) {
                        ((MainActivity) activity).showConnectionIssueMessage(it.getFailure());
                    }
                    break;
            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        viewModel.getSlides(Settings.get().getRefCode());
    }

    @Override
    public void onResume() {
        super.onResume();
        gamesAdapter.setScreenResumed(true);
    }

    @Override
    public void onPause() {
        gamesAdapter.setScreenResumed(false);
        super.onPause();
    }

    @Override
    public void onStop() {
        super.onStop();
        gamesAdapter.stopShowingNextAdTask();
    }

    private final GamesAdapter.SelectorListener selectorListener = selectedType -> {
        if ( !selectedType.equals(HomeFragment.this.selectedType) ) {
            HomeFragment.this.selectedType = selectedType;
//            gamesAdapter.showHeaderLoadAndFooter();
            loadGames(selectedType, 0);
        }
    };

    private final GamesAdapter.AdditionalBlocksClickListener additionalBlocksClickListener =
            new GamesAdapter.AdditionalBlocksClickListener() {
                @Override
                public void onTournamentClick(int tournamentId) {
                    FragmentActivity activity = getActivity();
                    if ( activity != null ) {
                        ((MainActivity) activity).showTournamentInternal(tournamentId);
                    }
                }

                @Override
                public void onLotteryClick(int lotteryId) {
                    FragmentActivity activity = getActivity();
                    if ( activity != null ) {
                        ((MainActivity) activity).showLotteryInternal(lotteryId, false);
                    }
                }

                @Override
                public void onNewsClick(LocalNews localNews) {
                    FragmentActivity activity = getActivity();
                    if ( activity != null ) {
                        NewsItem newsItem = new NewsItem(NewsItem.ItemType.CARD_NEWS, localNews);
                        ((MainActivity) activity).openNewsInternalScreen(newsItem);
                    }
                }
            };

    private final GamesAdapter.LoadMoreClickListener loadMoreClickListener = new GamesAdapter.LoadMoreClickListener() {
        @Override
        public void onClick() {
            loadGames(selectedType, gamesAdapter.getGameCount());
        }
    };

    private final FooterListener footerListener = () -> {
        Activity activity = getActivity();
        if (activity instanceof MainActivity) {
            if (User.State.PLAYER == Settings.get().getUserState()) {
                ((MainActivity) getActivity()).openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT);
            } else {
                ((MainActivity) getActivity()).openEnterScreen(false);
            }
        }
    };

    @Override
    public void onGameClick(LocalGameItem game, View view) {
        SessionDataHolder dataHolder = SessionDataHolder.getInstance();
        dataHolder.currentGameItem = game;
        ImageView gameIcon = view.findViewById(R.id.iv_item_game_image);
        if ( gameIcon.getDrawable() instanceof BitmapDrawable ) {
            dataHolder.currentGameImage = ((BitmapDrawable) gameIcon.getDrawable()).getBitmap();
        }
        showPregame(game, view);
    }

    @Override
    public void onSlideClick(String slideUrl) {
        if ( TextUtils.isEmpty(slideUrl) ) {
            return;
        }

        try {
            User.State userState = Settings.get().getUserState();

            if ( userState.equals(User.State.NOT_LOGGED) ) {
                //noinspection ConstantConditions
                ((MainActivity) getActivity()).showEnterScreen(false);
                return;
            }
            //noinspection ConstantConditions
            ((MainActivity) getActivity()).processDeepLinkUrl(slideUrl, UrlSource.SLIDES);
        } catch ( NullPointerException | ClassCastException e ) {
            //NOP
        }
    }

    @Override
    public void applyLoggedState() {
        gamesAdapter.applyLoggedState();
        gamesAdapter.setFooterCurrentState();
    }

    @Override
    public void applyNotLoggedState() {
        gamesAdapter.applyNotLoggedState();
        gamesAdapter.setFooterCurrentState();
    }

    @Override
    public void applyOrganicState() {
        gamesAdapter.setFooterCurrentState();
    }

    private void showPregame(LocalGameItem game, View view) {
        FragmentActivity activity = getActivity();
        if ( activity != null ) {
            ((MainActivity) activity).showPregame(game, view);
        }
    }

    private void loadGames(GameType type, int loaded) {
        gamesAdapter.setGameLoadingState(true);
        if ( type == GameType.BEST ) {
            viewModel.loadPopularGames(LOAD_STEP, loaded);
        } else if ( type == GameType.NEW ) {
            viewModel.loadNewGames(LOAD_STEP, loaded);
        } else if ( type == GameType.FAVOURITES ) {
            loadFavouriteGames(loaded);
        } else if ( type == GameType.SLOT ) {
            viewModel.loadSlotGames(LOAD_STEP, loaded);
        } else if ( type == GameType.TABLES ) {
            viewModel.loadTableGames(LOAD_STEP, loaded);
        } else if ( type == GameType.LIVE_CASINO ) {
            viewModel.loadLiveCasinoGames(LOAD_STEP, loaded);
        } else {
            gamesAdapter.setGameLoadingState(false);
            Log.w(TAG, "Load incorrect state: " + type);
        }
    }

    private void loadFavouriteGames(int offset) {
        viewModel.loadFavouriteGames(LOAD_STEP, offset);
    }

    public void selectTab(GamesAdapter.GameTab tab) {
        gamesAdapter.selectTab(tab);
    }

    private int getAdditionalBlockWidth() {
        Context context     = getContext();
        int     screenWidth = GeneralTools.getScreenWidth(context);
        return screenWidth - 2 * context.getResources().getDimensionPixelSize(R.dimen.activity_horizontal_margin);
    }

    enum GameType {
        BEST, NEW, FAVOURITES, SLOT, TABLES, LIVE_CASINO
    }
}
