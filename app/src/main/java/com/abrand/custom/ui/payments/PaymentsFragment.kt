package com.abrand.custom.ui.payments

import android.graphics.Typeface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.GravityCompat
import androidx.core.view.ViewCompat
import androidx.customview.widget.ViewDragHelper
import androidx.drawerlayout.widget.DrawerLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.navigation.Navigation
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.abrand.custom.R
import com.abrand.custom.data.Constants
import com.abrand.custom.data.entity.AnalyticsEvent
import com.abrand.custom.data.entity.Transaction
import com.abrand.custom.network.OkHttpProcessor
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.HistoryFilter
import com.abrand.custom.databinding.FragmentPaymentsBinding
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.type.PaymentUrlDirection
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.activitymain.MainViewModel
import com.abrand.custom.ui.activitymain.MainViewModelKt
import com.abrand.custom.ui.history.HistoryFragment
import com.abrand.custom.ui.pregame.PregameFragment
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.google.android.material.tabs.TabLayoutMediator
import java.util.*

class PaymentsFragment : Fragment(R.layout.fragment_payments) {
    private val mainViewModel: MainViewModel by activityViewModels()
    private val mainViewModelKt: MainViewModelKt by activityViewModels()
    private var binding: FragmentPaymentsBinding? = null
    private lateinit var viewPagerAdapter: ViewPagerAdapter
    private lateinit var depositFragment: LinkFragment
    private lateinit var extractFragment: LinkFragment
    private lateinit var historyFragment: HistoryFragment
    var depositUrl: String? = null
    var fullGameUrl: String? = null
    private var screen: Screen? = null

    private var filterSelectedOperations = mutableListOf<HistoryFilter.Operation>()
    private var filterCurrentOperations = mutableListOf<HistoryFilter.Operation>()
    private var filterSelectedStatuses = mutableListOf<HistoryFilter.Status>()
    private var filterCurrentStatuses = mutableListOf<HistoryFilter.Status>()
    private var serverSelectedDateFrom: String = ""
    private var serverCurrentDateFrom: String = ""
    private var serverSelectedDateTo: String = ""
    private var serverCurrentDateTo: String = ""

    companion object {
        const val KEY_SCREEN_NAME = "screenName"
        const val KEY_DEPOSIT_URL = "depositUrl"
        const val KEY_FULL_GAME_URL = "fullGameUrl"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            screen = it.getString(KEY_SCREEN_NAME)?.let { it1 -> Screen.valueOf(it1) }
            depositUrl = it.getString(KEY_DEPOSIT_URL)
            fullGameUrl = it.getString(KEY_FULL_GAME_URL)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val localBinding = FragmentPaymentsBinding.inflate(inflater, container, false)
        binding = localBinding
        return localBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        if (!this::depositFragment.isInitialized) {
            depositFragment = LinkFragment.newInstance(depositUrl, PaymentUrlDirection.IN)
            depositFragment.linkFragmentListener = linkFragmentListener
        }

        if (!this::extractFragment.isInitialized) {
            extractFragment = LinkFragment.newInstance(null, PaymentUrlDirection.OUT)
            extractFragment.linkFragmentListener = linkFragmentListener
        }

        if (!this::historyFragment.isInitialized) {
            historyFragment = HistoryFragment.newInstance()
            historyFragment.historyFragmentListener = historyFragmentListener
        }

        viewPagerAdapter = ViewPagerAdapter(this, depositFragment, extractFragment, historyFragment)

        binding?.apply {
            viewPager.adapter = viewPagerAdapter
            viewPager.offscreenPageLimit = 3
            viewPager.isUserInputEnabled = false

            TabLayoutMediator(tabLayout, viewPager) { tab, position ->
                tab.customView = getTabView(position, position == screen?.index)
            }.attach()

            val localContext = context

        tabLayout.addOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabReselected(tab: TabLayout.Tab?) {}

                override fun onTabUnselected(tab: TabLayout.Tab?) {
                    (tab?.customView)?.findViewById<TextView>(R.id.text)?.setTypeface(null, Typeface.NORMAL)
                }

                override fun onTabSelected(tab: TabLayout.Tab?) {
                    if (tab != null && localContext != null) {
                    when(tab.position) {
                        0 -> OkHttpProcessor.sendAnalytics(localContext, AnalyticsEvent.OPEN_DEP, null, null)
                        1 -> OkHttpProcessor.sendAnalytics(localContext, AnalyticsEvent.OPEN_PAYMENT, null, null)
                    }
                }
                (tab?.customView)?.findViewById<TextView>(R.id.text)?.setTypeface(null, Typeface.BOLD)
            }
        })

            viewPager.post {
                screen?.index?.let {
                if (0 == it && localContext != null) {
                    OkHttpProcessor.sendAnalytics(localContext, AnalyticsEvent.OPEN_DEP, null, null)
                }
                viewPager.setCurrentItem(it, true)
            }
        }

            drawerLayout.addDrawerListener(object : DrawerLayout.DrawerListener {
                override fun onDrawerSlide(drawerView: View, slideOffset: Float) {}

                override fun onDrawerOpened(drawerView: View) {}

                override fun onDrawerClosed(drawerView: View) {}

                override fun onDrawerStateChanged(newState: Int) {
                    if (ViewDragHelper.STATE_DRAGGING == newState) {
                        (activity as MainActivity).hideToolbar()
                    }

                    if ((ViewDragHelper.STATE_IDLE == newState && binding?.drawerLayout?.isDrawerOpen(GravityCompat.END) == false)) {
                        (activity as MainActivity).showToolbar()
                    }
                }

            })

            val clDateRange = navViewRoot.findViewById<ConstraintLayout>(R.id.cl_date_range)
            clDateRange.setOnClickListener {
                showDateRangeDialog()
            }
        }

        initFilter()

        setupInsets()
        observeViewModel()
    }

    private fun initFilter() {
        binding?.drawerHistoryFilter?.btnTypeAll?.let { initFilterOperationBtn(HistoryFilter.Operation.ALL, it) }
        binding?.drawerHistoryFilter?.btnTypeTransactionIn?.let { initFilterOperationBtn(HistoryFilter.Operation.IN, it) }
        binding?.drawerHistoryFilter?.btnTypeTransactionOut?.let { initFilterOperationBtn(HistoryFilter.Operation.OUT, it) }
        binding?.drawerHistoryFilter?.btnTypeExchangePoints?.let { initFilterOperationBtn(HistoryFilter.Operation.POINTS_EXCHANGE, it) }
        binding?.drawerHistoryFilter?.btnTypeBonusesPrises?.let { initFilterOperationBtn(HistoryFilter.Operation.BONUSES_PRIZES, it) }
        binding?.drawerHistoryFilter?.btnTypeOther?.let { initFilterOperationBtn(HistoryFilter.Operation.OTHERS, it) }

        binding?.drawerHistoryFilter?.btnStatusAll?.let { initFilterStatusBtn(HistoryFilter.Status.ALL, it) }
        binding?.drawerHistoryFilter?.btnStatusNew?.let { initFilterStatusBtn(HistoryFilter.Status.NEW, it) }
        binding?.drawerHistoryFilter?.btnStatusSuccess?.let { initFilterStatusBtn(HistoryFilter.Status.SUCCESS, it) }
        binding?.drawerHistoryFilter?.btnStatusUserCancelled?.let { initFilterStatusBtn(HistoryFilter.Status.USER_CANCELLED, it) }
        binding?.drawerHistoryFilter?.btnStatusFail?.let { initFilterStatusBtn(HistoryFilter.Status.FAIL, it) }

        binding?.drawerHistoryFilter?.btnClearAll?.setOnClickListener {
            unSelectAllOperationBtn()
            unSelectAllStatusBtn()
            clearDateRange()
            setFilterClearAllBtnState()
            setFilterApplyBtnState()
        }

        binding?.drawerHistoryFilter?.btnApply?.setOnClickListener {
            binding?.drawerLayout?.closeDrawer(GravityCompat.END)
            historyFragment.getTransactions(filterSelectedOperations, filterSelectedStatuses,
                serverSelectedDateFrom, serverSelectedDateTo)
            updateCurrentFilters()
            setSelectedFiltersCounter()
            setFilterApplyBtnState()
        }
    }

    private fun updateCurrentFilters() {
        filterCurrentOperations.clear()
        filterCurrentOperations.addAll(filterSelectedOperations)

        filterCurrentStatuses.clear()
        filterCurrentStatuses.addAll(filterSelectedStatuses)

        serverCurrentDateFrom = serverSelectedDateFrom
        serverCurrentDateTo = serverSelectedDateTo
    }

    private fun setSelectedFiltersCounter() {
        var filtersSelected = filterSelectedOperations.size + filterSelectedStatuses.size
        if (serverSelectedDateFrom.isNotEmpty()) {
            filtersSelected++
        }
        historyFragment.setAdapterCounter(filtersSelected)
    }

    private fun initFilterOperationBtn(operation: HistoryFilter.Operation, btn: Button) {
        btn.setOnClickListener {
            changeFilterOperationBtnState(operation, btn)
        }
    }

    private fun initFilterStatusBtn(status: HistoryFilter.Status, btn: Button) {
        btn.setOnClickListener {
            changeFilterStatusBtnState(status, btn)
        }
    }

    private fun changeFilterOperationBtnState(operation: HistoryFilter.Operation, btn: Button) {
        if (HistoryFilter.Operation.ALL == operation) {
            if (filterSelectedOperations.isNotEmpty()) {
                unSelectAllOperationBtn()
            }
        } else {
            if (filterSelectedOperations.contains(operation)) {
                filterSelectedOperations.remove(operation)
                setFilterBtnUnSelected(btn)
                if (filterSelectedOperations.isEmpty()) {
                    binding?.drawerHistoryFilter?.btnTypeAll?.let { setFilterBtnSelected(it) }
                }
            } else {
                if (filterSelectedOperations.isEmpty()) {
                    binding?.drawerHistoryFilter?.btnTypeAll?.let { setFilterBtnUnSelected(it) }
                }
                filterSelectedOperations.add(operation)
                setFilterBtnSelected(btn)
            }
        }

        setFilterClearAllBtnState()
        setFilterApplyBtnState()
    }

    private fun changeFilterStatusBtnState(status: HistoryFilter.Status, btn: Button) {
        if (HistoryFilter.Status.ALL == status) {
            if (filterSelectedStatuses.isNotEmpty()) {
                unSelectAllStatusBtn()
            }
        } else {
            if (filterSelectedStatuses.contains(status)) {
                filterSelectedStatuses.remove(status)
                setFilterBtnUnSelected(btn)
                if (filterSelectedStatuses.isEmpty()) {
                    binding?.drawerHistoryFilter?.btnStatusAll?.let { setFilterBtnSelected(it) }
                }
            } else {
                if (filterSelectedStatuses.isEmpty()) {
                    binding?.drawerHistoryFilter?.btnStatusAll?.let { setFilterBtnUnSelected(it) }
                }
                filterSelectedStatuses.add(status)
                setFilterBtnSelected(btn)
            }
        }

        setFilterClearAllBtnState()
        setFilterApplyBtnState()
    }

    private fun unSelectAllOperationBtn() {
        binding?.drawerHistoryFilter?.btnTypeAll?.let { setFilterBtnSelected(it) }
        binding?.drawerHistoryFilter?.btnTypeTransactionIn?.let { setFilterBtnUnSelected(it) }
        binding?.drawerHistoryFilter?.btnTypeTransactionOut?.let { setFilterBtnUnSelected(it) }
        binding?.drawerHistoryFilter?.btnTypeExchangePoints?.let { setFilterBtnUnSelected(it) }
        binding?.drawerHistoryFilter?.btnTypeBonusesPrises?.let { setFilterBtnUnSelected(it) }
        binding?.drawerHistoryFilter?.btnTypeOther?.let { setFilterBtnUnSelected(it) }
        filterSelectedOperations.clear()
    }

    private fun unSelectAllStatusBtn() {
        binding?.drawerHistoryFilter?.btnStatusAll?.let { setFilterBtnSelected(it) }
        binding?.drawerHistoryFilter?.btnStatusNew?.let { setFilterBtnUnSelected(it) }
        binding?.drawerHistoryFilter?.btnStatusSuccess?.let { setFilterBtnUnSelected(it) }
        binding?.drawerHistoryFilter?.btnStatusUserCancelled?.let { setFilterBtnUnSelected(it) }
        binding?.drawerHistoryFilter?.btnStatusFail?.let { setFilterBtnUnSelected(it) }
        filterSelectedStatuses.clear()
    }

    private fun clearDateRange() {
        serverSelectedDateFrom = ""
        serverSelectedDateTo = ""
        binding?.drawerHistoryFilter?.tvDateRange?.text = context?.getString(R.string.filter_all_time)
    }

    private fun setFilterBtnSelected(btn: Button) {
        btn.setBackgroundResource(R.drawable.bg_btn_filter_transaction_selected)
        context?.let {
            btn.setTextColor(ContextCompat.getColor(it, R.color.btn_filter_history_selected_text))
        }
    }

    private fun setFilterBtnUnSelected(btn: Button) {
        btn.setBackgroundResource(R.drawable.bg_btn_filter_transaction_unselected)
        context?.let {
            btn.setTextColor(ContextCompat.getColor(it, R.color.btn_filter_history_unselected_text))
        }
    }

    private fun setFilterClearAllBtnState() {
        if (filterSelectedOperations.isNotEmpty() || filterSelectedStatuses.isNotEmpty() || serverSelectedDateFrom.isNotEmpty()) {
            binding?.drawerHistoryFilter?.btnClearAll?.visibility = View.VISIBLE
        } else {
            binding?.drawerHistoryFilter?.btnClearAll?.visibility = View.INVISIBLE
        }
    }

    private fun setFilterApplyBtnState() {
        if (filterCurrentOperations == filterSelectedOperations &&
            filterCurrentStatuses == filterSelectedStatuses &&
            serverCurrentDateFrom == serverSelectedDateFrom &&
            serverCurrentDateTo == serverSelectedDateTo) {
            binding?.drawerHistoryFilter?.btnApply?.visibility = View.GONE
        } else {
            binding?.drawerHistoryFilter?.btnApply?.visibility = View.VISIBLE
        }
    }

    private fun showDateRangeDialog() {
        val builder = MaterialDatePicker.Builder.dateRangePicker()
        builder.setTheme(R.style.MaterialCalendarTheme)

        val today = MaterialDatePicker.todayInUtcMilliseconds()
        builder.setCalendarConstraints(
            CalendarConstraints.Builder()
                .setEnd(today)
                .build())

        val picker = builder.build()
        activity?.supportFragmentManager?.let { picker.show(it, picker.toString()) }

        var clientDateFrom = ""
        var clientDateTo = ""

        picker.addOnPositiveButtonClickListener {
            val dateFromMilliseconds = it.first
            val dateToMilliseconds = it.second

            val MAX_RANGE_MILLIS = 3 * DateTools.MILLIS_IN_MONTH
            if (dateToMilliseconds != null && dateFromMilliseconds != null) {
                if ((dateToMilliseconds - dateFromMilliseconds) > MAX_RANGE_MILLIS) {
                    Toast.makeText(context, getString(R.string.filter_max_range_warning), Toast.LENGTH_LONG).show()
                    return@addOnPositiveButtonClickListener
                }
            }

            val clientDateFormat = DateTools.getClientShortDateFormat2(context)
            val serverDateFormat = DateTools.getServerLongDateFormatUTC(context)

            if (dateFromMilliseconds != null) {
                clientDateFrom = DateTools.getFormattedDate(Date(dateFromMilliseconds), clientDateFormat)
                serverSelectedDateFrom = DateTools.getFormattedDate(Date(dateFromMilliseconds), serverDateFormat)
            }

            if (dateToMilliseconds != null) {
                clientDateTo = DateTools.getFormattedDate(Date(dateToMilliseconds), clientDateFormat)
                serverSelectedDateTo = DateTools.getFormattedDate(Date(dateToMilliseconds + DateTools.MILLIS_IN_DAY), serverDateFormat)
            }

            val clientDateFromTo = if (clientDateTo.isNotEmpty()) {
                "$clientDateFrom - $clientDateTo"
            } else {
                clientDateFrom
            }
            binding?.drawerHistoryFilter?.tvDateRange?.text = clientDateFromTo

            setFilterClearAllBtnState()
            setFilterApplyBtnState()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private val linkFragmentListener: LinkFragment.Listener = object : LinkFragment.Listener {
        override fun paymentsCompleted() {
            navigateUp()
            if (!fullGameUrl.isNullOrEmpty()) {
                (activity as? MainActivity)?.openGameActivity(fullGameUrl, fullGameUrl, 0)
            }
        }

        override fun onViewerNull() {
            navigateUp()
        }

        override fun termsClicked() {
            navigateUp()
        }

        override fun gameClicked(gameUrl: String) {
            (activity as MainActivity).openHomeScreen()
            (activity as MainActivity).openGameWithPregame(gameUrl, PregameFragment.GAME_TYPE_FULL)
        }

        override fun updateHistory() {
            historyFragment.updateTransactions()
        }

        override fun openHistory() {
            binding?.viewPager?.setCurrentItem(2, true)
        }
    }

    private val historyFragmentListener: HistoryFragment.Listener = object : HistoryFragment.Listener {
        override fun openDeposit(url: String) {
            binding?.viewPager?.setCurrentItem(0, true)
            if (url.isNotEmpty()) {
                depositFragment.openUrl(url)
            }
        }

        override fun onOpenFilter() {
            (activity as MainActivity).hideToolbar()
            binding?.drawerLayout?.openDrawer(GravityCompat.END)
        }
    }

    private fun setupInsets() {
        binding?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it.viewPager) { _, insets ->
                depositFragment.setInsetBottom(insets.systemWindowInsetBottom)
                extractFragment.setInsetBottom(insets.systemWindowInsetBottom)
                historyFragment.setInsetBottom(insets.systemWindowInsetBottom)
                (it.tvRealBalance.layoutParams as ViewGroup.MarginLayoutParams).topMargin =
                        insets.systemWindowInsetTop + (context?.resources?.getDimensionPixelSize(R.dimen.payment_screen_content_margin)
                                ?: 0)
                insets
            }
        }
    }

    private fun observeViewModel() {
//<<<<<<< HEAD
//        mainViewModelKt.getBalanceLiveData().observe(viewLifecycleOwner, Observer {
//            val realBalance = if (it?.viewerWallet?.realBalance != null) {
//                it.viewerWallet.realBalance.toString().toDouble()
//            } else {
//                null
//            }
//            if (realBalance != null) {
//                binding?.tvBalance?.text = GeneralTools.formatBalance(
//                    Settings.get().userCurrencyCode, realBalance)
//            }
//        })
//
//        mainViewModel.initialViewerDataQueryLiveData.observe(viewLifecycleOwner, {
//            val realBalance = it.wallet?.realBalance.toString().toDouble()
//            binding?.tvBalance?.text = GeneralTools.formatBalance(Settings.get().userCurrencyCode, realBalance)
//        })
//=======
        //was refactored to single point of truth
        // TODO: move to kt version
        mainViewModel.userBalance.observe(viewLifecycleOwner) {
            binding?.tvBalance?.text = GeneralTools.formatBalance(Settings.get().userCurrencyCode, it.realBalance, 2)
        }
//>>>>>>> dev
    }

    private fun getTabView(position: Int, selected: Boolean): View? {
        val tabView: View = LayoutInflater.from(context).inflate(R.layout.payments_tab, null)
        val tabTv: TextView = tabView.findViewById(R.id.text)
        val tabText = when (position) {
            0 -> context?.getString(R.string.history_tab_charity)
            1 -> context?.getString(R.string.history_tab_extract)
            2 -> context?.getString(R.string.history_tab_history)
            else -> ""
        }
        tabTv.text = tabText
        if (selected) {
            tabTv.setTypeface(null, Typeface.BOLD)
        }
        return tabView
    }

    private fun navigateUp() {
        view?.let {
            activity?.runOnUiThread { Navigation.findNavController(it).navigateUp() }
        }
    }

    class ViewPagerAdapter(parentFragment: Fragment, private val depositFragment: Fragment,
                           private val extractFragment: Fragment, private val historyFragment: Fragment) :
            FragmentStateAdapter(parentFragment) {

        override fun getItemCount(): Int = 3

        override fun createFragment(position: Int): Fragment {
            return when (position) {
                0 -> depositFragment
                1 -> extractFragment
                else -> historyFragment
            }
        }
    }

    enum class Screen(val index: Int) {
        DEPOSIT(0), EXTRACT(1), HISTORY(2)
    }
}
