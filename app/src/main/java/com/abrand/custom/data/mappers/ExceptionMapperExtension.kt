package com.abrand.custom.data.mappers

import com.abrand.custom.data.entity.ResponseException
import com.apollographql.apollo3.exception.ApolloException
import com.apollographql.apollo3.exception.ApolloHttpException
import com.apollographql.apollo3.exception.ApolloNetworkException

fun ApolloException.mapToResponseException() : ResponseException {
    val statusCode = if (this is ApolloHttpException) {
        this.statusCode
    } else {
        -1
    }

    val exceptionType = if (this is ApolloNetworkException) {
        ResponseException.ExceptionType.NETWORK
    } else if (this is ApolloHttpException) {
        ResponseException.ExceptionType.HTTP
    } else {
        ResponseException.ExceptionType.UNKNOWN
    }

    return ResponseException(statusCode = statusCode, exceptionType = exceptionType)
}
