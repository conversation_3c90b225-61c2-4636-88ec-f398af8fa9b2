package com.abrand.custom.ui.views;

import android.app.DatePickerDialog;
import android.content.Context;
import android.util.AttributeSet;
import android.widget.ImageView;
import android.widget.Toast;

import com.abrand.custom.R;
import com.abrand.custom.data.Constants;
import com.abrand.custom.tools.DateTools;
import com.abrand.custom.ui.views.textfield.TextField;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

public class BirthDateField extends TextField {
    private DatePickerDialog datePickerDialog;
    private ImageView ivDatePicker;
    private String birthDate = "";

    public BirthDateField(Context context) {
        super(context);
        init();
    }

    public BirthDateField(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public BirthDateField(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    public BirthDateField(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init();
    }

    private void init() {
        ivDatePicker = rootView.findViewById(R.id.iv_date_picker);
        setEditTextNotEditable();
    }

    public String getBirthDate() {
        return birthDate;
    }

    /**
     *
     * @param birthDate The date string must be in format {@link Constants#CLIENT_SHORT_DATE_FORMAT}
     */
    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
        try {
            Date date = DateTools.getServerShortDateFormat(BirthDateField.this.getContext()).parse(birthDate);
            if ( date != null ) {
                String formattedDate = DateTools.getClientShortDateFormat(BirthDateField.this.getContext()).format(date);
                super.setText(formattedDate);
            }
        } catch (ParseException e) {
            e.printStackTrace();
            super.setText(getContext().getString(R.string.wrong_data_format));
        }
    }

    @Override
    public void setFieldNotEditable() {
        super.setFieldNotEditable();
        ivDatePicker.setImageDrawable(null);
    }

    @Override
    public int getLayoutId() {
        return R.layout.view_birth_field;
    }

    @Override
    protected void setUpFocusManagement(Context context) {
        rootView.setOnClickListener(v -> {
            if (isFieldEditable()) {
                showDatePickerDialog(context);
            } else {
                Toast.makeText(context, R.string.change_bidrthday_warning, Toast.LENGTH_LONG).show();
            }
        });
    }

    private void showDatePickerDialog(Context context) {
        Calendar calendar = Calendar.getInstance();
        if (datePickerDialog == null) {
            datePickerDialog = new DatePickerDialog(context, R.style.DatePicker, onBirthDateSetListener, calendar.get(Calendar.YEAR),
                    calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH));
        }
        datePickerDialog.show();
    }

    private DatePickerDialog.OnDateSetListener onBirthDateSetListener = (view, year, month, dayOfMonth) -> {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
        setBirthDate(DateTools.getServerShortDateFormat(BirthDateField.this.getContext()).format(calendar.getTime()));
    };
}
