<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/loyalty_bonus_circle_bg"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="@dimen/size_16"
        android:background="@drawable/loyalty_bonus_circle_bg"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_title" />

    <View
        android:id="@+id/loyalty_bonus_circle"
        android:layout_width="4dp"
        android:layout_height="4dp"
        android:background="@drawable/loyalty_bonus_circle"
        app:layout_constraintBottom_toBottomOf="@+id/loyalty_bonus_circle_bg"
        app:layout_constraintEnd_toEndOf="@+id/loyalty_bonus_circle_bg"
        app:layout_constraintStart_toStartOf="@+id/loyalty_bonus_circle_bg"
        app:layout_constraintTop_toTopOf="@+id/loyalty_bonus_circle_bg" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:textColor="#B2E3FF"
        android:textSize="14sp"
        app:layout_constraintStart_toEndOf="@+id/loyalty_bonus_circle_bg"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
