package com.abrand.custom.tools;

import android.content.Context;
import android.util.Log;

import com.abrand.custom.data.Constants;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

public class DateTools {

    public static final int MILLIS_IN_SECOND = 1000;
    public static final int MILLIS_IN_MINUTE = MILLIS_IN_SECOND * 60;
    public static final int MILLIS_IN_HOUR = MILLIS_IN_MINUTE * 60;
    public static final int MILLIS_IN_DAY = MILLIS_IN_HOUR * 24;
    public static final long MILLIS_IN_MONTH = MILLIS_IN_DAY * 31L;
    private static final String TAG = "DateTools";
    private static final Locale ruLocale = new Locale("ru","RU");

    public static SimpleDateFormat getServerShortDateFormat(Context context) {
        return new SimpleDateFormat(Constants.SERVER_SHORT_DATE_FORMAT,
                context.getResources().getConfiguration().locale);
    }

    public static SimpleDateFormat getClientShortDateFormat(Context context) {
        return new SimpleDateFormat(Constants.CLIENT_SHORT_DATE_FORMAT,
                context.getResources().getConfiguration().locale);
    }

    public static SimpleDateFormat getClientShortDateFormat2(Context context) {
        return new SimpleDateFormat(Constants.CLIENT_SHORT_DATE_FORMAT_2,
                context.getResources().getConfiguration().locale);
    }

    public static SimpleDateFormat getServerLongDateFormat(Context context) {
        return new SimpleDateFormat(Constants.SERVER_LONG_DATE_FORMAT,
                context.getResources().getConfiguration().locale);
    }

    public static SimpleDateFormat getServerLongDateFormatUTC(Context context) {
        return new SimpleDateFormat(Constants.SERVER_LONG_DATE_FORMAT_UTC,
                context.getResources().getConfiguration().locale);
    }

    public static SimpleDateFormat getClientLongDateFormat(Context context) {
        return new SimpleDateFormat(Constants.CLIENT_LONG_DATE_FORMAT,
                context.getResources().getConfiguration().locale);
    }

    public static SimpleDateFormat getMessagesDateFormat(Context context) {
        return new SimpleDateFormat(Constants.MESSAGES_DATE_FORMAT,
                context.getResources().getConfiguration().locale);
    }

    public static Date getServerDate(Context context, Object serverObjectDate) {
        SimpleDateFormat serverDateFormat = getServerLongDateFormat(context);
        serverDateFormat.setTimeZone(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeZone());

        try {
            String stringDate = serverObjectDate.toString();
            return serverDateFormat.parse(stringDate);
        } catch ( ParseException e ) {
            Log.e(TAG, e.toString());
        }
        return null;
    }

    public static String getFormattedDate(Context context, Object serverObjectDate, FormatDateType formatDateType) {
        Date date = getServerDate(context, serverObjectDate);
        if ( date != null ) {
            if ( FormatDateType.TRANSACTION.equals(formatDateType) ) {
                return getClientLongDateFormat(context).format(date);
            } else if ( FormatDateType.MESSAGES.equals(formatDateType) ) {
                return getMessagesDateFormat(context).format(date);
            } else {
                return getClientShortDateFormat(context).format(date);
            }
        } else {
            return "";
        }
    }

    public static String getFormattedDate(Date date, SimpleDateFormat simpleDateFormat) {
        return simpleDateFormat.format(date);
    }

    public static String getMonth(Context context, Object serverObjectDate) {
        Date date = getServerDate(context, serverObjectDate);
        if ( date != null ) {
            return new SimpleDateFormat("MMMM", ruLocale).format(date);
        } else {
            return "";
        }
    }

    public static String getDay(Context context, Object serverObjectDate) {
        Date date = getServerDate(context, serverObjectDate);
        if ( date != null ) {
            return new SimpleDateFormat("dd",
                    context.getResources().getConfiguration().locale).format(date);
        } else {
            return "";
        }
    }

    public static String getDayMonth(Context context, Object serverObjectDate) {
        Date date = getServerDate(context, serverObjectDate);
        if ( date != null ) {
            return new SimpleDateFormat("dd.MM",
                    context.getResources().getConfiguration().locale).format(date);
        } else {
            return "";
        }
    }

    public static long getDifferenceDays(long dateTime1, long dateTime2) {
        long diff = dateTime1 - dateTime2;
        return TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
    }

    public static String formatTime(Long millis)  {
        long hours = millis / DateTools.MILLIS_IN_HOUR;
        long minutes = millis % DateTools.MILLIS_IN_HOUR / DateTools.MILLIS_IN_MINUTE;
        long seconds = millis % DateTools.MILLIS_IN_HOUR % DateTools.MILLIS_IN_MINUTE /
                DateTools.MILLIS_IN_SECOND;
        return String.format(Locale.US, "%02d:%02d:%02d", hours, minutes, seconds);
    }

    public enum FormatDateType {
        TRANSACTION, NEWS, MESSAGES
    }
}
