<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/card_root"
        android:layout_width="match_parent"
        android:layout_height="@dimen/permanent_promotion_card_default_height"
        android:layout_marginTop="@dimen/size_16"
        android:background="@drawable/promotion_card_default_bg">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_w"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5"/>

        <ImageView
            android:id="@+id/iv_promotion"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:maxHeight="@dimen/promotion_card_defautl_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/gl_w"
            android:scaleType="centerInside"
            android:adjustViewBounds="true" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginTop="@dimen/size_16"
            android:layout_marginRight="@dimen/size_16"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            app:layout_constraintEnd_toStartOf="@+id/ib_promotion_info"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageButton
            android:id="@+id/ib_promotion_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@null"
            android:paddingTop="@dimen/size_16"
            android:paddingEnd="@dimen/size_16"
            android:paddingBottom="@dimen/size_16"
            android:src="@drawable/ic_promotion_info"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/container_bottom_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent">

            <Button
                android:id="@+id/btn_action"
                style="@style/ButtonStyle"
                android:layout_width="176dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/size_16"
                android:layout_marginRight="@dimen/size_16"
                android:layout_marginBottom="@dimen/size_16"
                android:background="@drawable/btn_promotion_bg"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="gone" />

            <com.abrand.custom.ui.views.PromoCodeField
                android:id="@+id/promo_code_view"
                android:layout_width="176dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_16"
                android:layout_marginBottom="@dimen/size_16"
                app:hint="@string/enter_promo_code"
                app:hintTextSize="12"
                app:layout_constraintStart_toStartOf="parent" />

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
