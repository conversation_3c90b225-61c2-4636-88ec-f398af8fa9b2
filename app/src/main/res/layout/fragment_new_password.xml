<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient"
    tools:context=".ui.newpassword.NewPasswordFragment">

    <TextView
        android:id="@+id/tv_new_passwrd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="80dp"
        android:text="@string/new_password"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.abrand.custom.ui.views.textfield.TextField
        android:id="@+id/et_new_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        app:hint="@string/new_password"
        app:layout_constraintTop_toBottomOf="@+id/tv_new_passwrd" />

    <Button
        android:id="@+id/btn_save"
        style="@style/ButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginRight="16dp"
        android:text="@string/save"
        app:layout_constraintTop_toBottomOf="@+id/et_new_password" />

</androidx.constraintlayout.widget.ConstraintLayout>
