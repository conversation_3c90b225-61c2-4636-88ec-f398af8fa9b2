package com.abrand.custom.tools

object Utils {

    private val base64EncodingProvider: Base64EncodingProvider = Base64EncodingProviderImpl

    fun validateUrl(url: String): String {
        var returnValue = url

        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            returnValue = "https://$returnValue"
        }

        if (!returnValue.endsWith("/")) {
            returnValue = "$returnValue/"
        }

        return returnValue
    }

    fun doubleBase64Encode(message: String): String {
        return base64Encode(base64Encode(message))
    }

    fun base64Encode(message: String): String {
        return base64EncodingProvider.base64Encode(message)
    }

    fun doubleBase64Decode(message: String): String {
        return base64Decode(base64Decode(message))
    }

    fun base64Decode(message: String): String {
        return base64EncodingProvider.base64Decode(message)
    }
}
