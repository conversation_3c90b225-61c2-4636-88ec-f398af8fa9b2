package com.abrand.custom.ui.pregame;

import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;

import com.abrand.custom.data.ApoloConfig;
import com.abrand.custom.data.Constants;
import com.abrand.custom.data.Resource;
import com.abrand.custom.data.Settings;
import com.abrand.custom.data.entity.LocalGameItem;
import com.abrand.custom.data.entity.LoyaltyStatus;
import com.abrand.custom.data.entity.ServerError;
import com.abrand.custom.data.entity.TournamentsItem;
import com.abrand.custom.data.entity.User;
import com.abrand.custom.R;
import com.abrand.custom.data.entity.SessionDataHolder;
import com.abrand.custom.databinding.FragmentPregameBinding;
import com.abrand.custom.tools.GeneralTools;
import com.abrand.custom.ui.activitymain.MainActivity;
import com.abrand.custom.ui.payments.PaymentsFragment;
import com.apollographql.apollo3.exception.ApolloException;
import com.google.android.material.appbar.AppBarLayout;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Target;

import org.jetbrains.annotations.NotNull;

import jp.wasabeef.blurry.Blurry;

public class PregameFragment extends Fragment {
    public static final  String GAME_TYPE_KEY  = "gameType";
    public static final  String GAME_TYPE_FULL = "gameTypeFull";
    public static final  String GAME_TYPE_DEMO = "gameTypeDemo";
    private static final String TAG            = PregameFragment.class.getSimpleName();

    private FragmentPregameBinding binding;
    private String                 demoUrl;
    private String                 fullUrl;
    private User.State             userState;
    private PregameViewModel       viewModel;
    private Toast                       somethingGoesWrongToast;
    private LocalGameItem   game;
    private TournamentsItem tournament;
    private String          gameTypeOpen;
    private Boolean                hasGameFreeSpins = null;
    private boolean autoJoinedToTournament = false;

    @Override
    public View onCreateView(@NotNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = FragmentPregameBinding.inflate(inflater);
        demoUrl = null;
        fullUrl = null;
        userState = Settings.get().getUserState();

        Bundle bundle = getArguments();
        if ( bundle != null ) {
            gameTypeOpen = bundle.getString(GAME_TYPE_KEY);
            autoJoinedToTournament = bundle.getBoolean(MainActivity.AUTO_JOIN, false);
            if(bundle.getBoolean(MainActivity.FROM_TOURNAMENT,false))
                updateBtnTournament();
        }

        viewModel = new ViewModelProvider(this).get(PregameViewModel.class);
        somethingGoesWrongToast = Toast.makeText(PregameFragment.this.getContext(), R.string.something_goes_wrong, Toast.LENGTH_SHORT);
        init();
        observeViewModel();
        setupInsets();
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        if ( SessionDataHolder.getInstance().currentGameItem != null ) {
            if ( Settings.get().getUserState() == User.State.PLAYER ) {
                viewModel.hasGameFreeSpins();
            } else {
                hasGameFreeSpins = false;
                viewModel.getURLs();
            }
        } else {
            somethingGoesWrongToast.show();
            navigateUp();
            FirebaseCrashlytics.getInstance().log("PregameFragment: currentGameThumb is null");
        }
    }

    private void setupInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.layButtons, (view, insets) -> {
            ViewGroup.LayoutParams layoutParams = binding.layButtons.getLayoutParams();
            if ( layoutParams instanceof ViewGroup.MarginLayoutParams ) {
                ((ViewGroup.MarginLayoutParams) layoutParams).bottomMargin =
                        insets.getSystemWindowInsetBottom() + getResources().getDimensionPixelSize(R.dimen.size_16);
            }
            return insets;
        });
    }

    @Override
    public void onStart() {
        super.onStart();
//        if ( getActivity() != null ) {
//            setScrollingEnabled(getActivity(), false);
//        }

        if ( SessionDataHolder.getInstance().currentGameItem != null ) {
            initGameButtonsState();
        } else {
            somethingGoesWrongToast.show();
            navigateUp();
        }

        if ( binding.background.getDrawable() == null ) {
            Bitmap gameBitmap = SessionDataHolder.getInstance().currentGameImage;
            if ( gameBitmap != null ) {
                setGameBitmap(gameBitmap);
            } else {
                loadGameBitmap();
            }
        }
    }

    @Override
    public void onStop() {
//        if ( getActivity() != null ) {
//            setScrollingEnabled(getActivity(), true);
//        }
        super.onStop();
    }

    private void init() {
        game = SessionDataHolder.getInstance().currentGameItem;
        tournament = SessionDataHolder.getInstance().currentTournamentItem;
        Bitmap bitmap = SessionDataHolder.getInstance().currentGameImage;

        if ( game != null ) {
            binding.lItemGame.tvItemGameName.setText(game.getName());
        }

        if ( bitmap != null ) {
            setGameBitmap(bitmap);
        } else if ( game != null ) {
            loadGameBitmap();
        }

        if ( game != null ) {
            setSwitchFavouriteChecked(game.isFavorite());
            viewModel.getGameById(game.getId());
        }

        if ( tournament != null ) {
            if ( autoJoinedToTournament ) {
                viewModel.joinTournament(tournament.getId());
            }
            for ( LocalGameItem gameItem : tournament.getGameList() ) {
                if ( gameItem.getId() == game.getId() ) {
                    updateBtnTournament();
                }
            }
        }

        binding.tvDemo.setPaintFlags(binding.tvDemo.getPaintFlags() | Paint.UNDERLINE_TEXT_FLAG);
    }

    private void observeViewModel() {
        viewModel.getDemoUrlLiveData().observe(getViewLifecycleOwner(), url -> {
            demoUrl = url;
            updateBtnGameState(); //TODO possible bug. Should use updateTvDemoState fun?

            if ( GAME_TYPE_DEMO.equals(gameTypeOpen) ) {
                openGameScreen(demoUrl);
            }
        });

        viewModel.getJoinLiveData().observe(getViewLifecycleOwner(), e -> {
            binding.btnTournament.setEnabled(false);
            binding.btnTournament.setText(R.string.tournament_join_already);
        });

        viewModel.getJoinErrorLiveData().observe(getViewLifecycleOwner(), e -> {
            Toast.makeText(PregameFragment.this.getContext(), e, Toast.LENGTH_SHORT).show();
        });

        viewModel.getDemoUrlApolloExceptionLiveData().observe(getViewLifecycleOwner(), e -> {
            Log.e(TAG, "Failed to load game: " + e.getLocalizedMessage());
            if ( !somethingGoesWrongToast.getView().isShown() ) {
                somethingGoesWrongToast.show();
            }
            if ( User.State.ORGANIC == Settings.get().getUserState() ) {
                binding.btnGame.setVisibility(View.INVISIBLE);
            } else {
                binding.tvDemo.setVisibility(View.INVISIBLE);
            }

            showConnectionIssueMessage(e);
        });

        viewModel.getGameUrlLiveData().observe(getViewLifecycleOwner(), url -> {
            fullUrl = url;
            updateBtnGameState();

            if ( GAME_TYPE_FULL.equals(gameTypeOpen) ) {
                openGameScreen(fullUrl);
            }
        });

        viewModel.getGameUrlApolloExceptionLiveData().observe(getViewLifecycleOwner(), e -> {
            Log.e(TAG, "Failed to load demo game: " + e.getLocalizedMessage());
            if ( !somethingGoesWrongToast.getView().isShown() ) {
                somethingGoesWrongToast.show();
            }
            binding.btnGame.setVisibility(View.INVISIBLE);

            showConnectionIssueMessage(e);
        });

        viewModel.getServerErrorLiveData().observe(getViewLifecycleOwner(), this::processServerError);

        viewModel.getGameByIdLiveData().observe(getViewLifecycleOwner(), gameById -> {
            switch ( gameById.getStatus() ) {
                case SUCCESS:
                    setSwitchFavouriteChecked(gameById.getData().isFavorite());
                    viewModel.getGameByIdLiveData().removeObservers(getViewLifecycleOwner());
                    break;
                case ERROR:
                    processServerError(gameById.getError());
                    break;
                case FAILURE:
                    showConnectionIssueMessage(gameById.getFailure());
                    break;
            }

        });

        viewModel.getApolloExceptionLiveData().observe(getViewLifecycleOwner(), this::showConnectionIssueMessage);

        viewModel.getHasGameFreeSpinsLiveData().observe(getViewLifecycleOwner(), hasGameFreeSpinsObserver);
    }

    private void processServerError(ServerError serverError) {
        if ( serverError.getErrorCode().equals(Constants.SERVER_UNAUTHORIZED) ) {
            binding.getRoot().post(() -> {
                MainActivity activity = (MainActivity) getActivity();
                if ( activity != null ) {
                    activity.applyNotLoggedState();
                }
                navigateUp();
            });
        }
    }

    private final Observer<Resource<Boolean, ServerError, ApolloException>> hasGameFreeSpinsObserver = responseResource -> {
        switch ( responseResource.getStatus() ) {
            case SUCCESS:
                hasGameFreeSpins = responseResource.getData();
                viewModel.getURLs();
                break;
            case ERROR:
                MainActivity activity = (MainActivity) getActivity();
                ServerError serverError = responseResource.getError();
                if ( activity != null && serverError != null ) {
                    activity.showMessage(serverError.getErrorMessage());
                }
                break;
            case FAILURE:
                showConnectionIssueMessage(responseResource.getFailure());
                break;
        }
    };

    private void setGameBitmap(Bitmap bitmap) {
        if ( bitmap != null && getContext() != null ) {
            binding.lItemGame.ivItemGameImage.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
            binding.lItemGame.ivItemGameImage.setImageBitmap(bitmap);
            Blurry.with(getContext()).radius(30).from(bitmap).into(binding.background);
        }
    }

    private void loadGameBitmap() {
        Picasso.get().load(ApoloConfig.getFullUrl(game.getMobileIcon()))
                .into(gameImageTarget);
    }

    //strong reference to the Target otherwise onBitmapLoaded does not always work
    private final Target gameImageTarget = new Target() {
        @Override
        public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
            SessionDataHolder.getInstance().currentGameImage = bitmap;
            setGameBitmap(bitmap);
        }

        @Override
        public void onBitmapFailed(Exception e, Drawable errorDrawable) {

        }

        @Override
        public void onPrepareLoad(Drawable placeHolderDrawable) {

        }
    };

    private void showConnectionIssueMessage(ApolloException exception) {
        Activity activity = getActivity();
        if ( activity instanceof MainActivity ) {
            ((MainActivity) activity).showConnectionIssueMessage(exception);
        }
    }

    private void initGameButtonsState() {
        updateItemGameState();
        updateTvDemoState();
        updateBtnGameState();
        updateSwitcherFavourite();
    }

    private void updateItemGameState() {
        switch ( userState ) {
            case ORGANIC:
                binding.lItemGame.getRoot().setOnClickListener(v -> openGameScreen(demoUrl));
                break;
            case NOT_LOGGED:
                binding.lItemGame.getRoot().setOnClickListener(v -> openRegisterScreen());
                break;
            case PLAYER:
                Double balance = ((MainActivity) getActivity()).getBalance();
                if ( hasGameFreeSpins == null ) {
                    return;
                } else if ( (balance != null && balance > 0) || hasGameFreeSpins ) {
                    binding.lItemGame.getRoot().setOnClickListener(v -> openGameScreen(fullUrl));
                } else {
                    binding.lItemGame.getRoot().setOnClickListener(v ->
                            ((MainActivity) getActivity()).openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT));
                }
                break;
        }
    }

    private void updateTvDemoState() {
        if ( SessionDataHolder.getInstance().currentGameItem.isHasDemo() ) {
            switch ( userState ) {
                case ORGANIC:
                    binding.tvDemo.setVisibility(View.GONE);
                    break;
                case NOT_LOGGED:
                case PLAYER:
                    binding.tvDemo.setVisibility(View.VISIBLE);
                    binding.tvDemo.setOnClickListener(v -> openGameScreen(demoUrl));
                    break;
            }
        } else {
            binding.tvDemo.setVisibility(View.GONE);
        }
    }

    private void updateBtnTournament() {
        TournamentsItem tournamentsItem        = SessionDataHolder.getInstance().currentTournamentItem;
        Integer         minBaseLoyaltyStatusId = tournamentsItem.getMinBaseLoyaltyStatusId();
        Integer         maxBaseLoyaltyStatusId = tournamentsItem.getMaxBaseLoyaltyStatusId();
        LoyaltyStatus   loyaltyStatus          = Settings.get().getCurrentLoyaltyStatus();
        if ( (minBaseLoyaltyStatusId == null || loyaltyStatus.getStatusId() >= minBaseLoyaltyStatusId) &&
                (maxBaseLoyaltyStatusId == null ||
                        loyaltyStatus.getStatusId() <= maxBaseLoyaltyStatusId) ) {
            binding.btnTournament.setVisibility(View.VISIBLE);
            if ( SessionDataHolder.getInstance().currentTournamentItem.getTournamentViewerProgress() == null ) {
                binding.btnTournament.setText(R.string.tournament_join);
                binding.btnTournament.setEnabled(true);
                if ( Settings.get().getUserState() == User.State.PLAYER ) {
                    binding.btnTournament.setOnClickListener(v ->
                            viewModel.joinTournament(SessionDataHolder.getInstance().currentTournamentItem.getId()));
                } else {
                    binding.btnTournament.setOnClickListener(v ->
                            ((MainActivity) getActivity()).showEnterScreenFromPregame(false, tournament));
                }
            } else {
                binding.btnTournament.setText(R.string.tournament_join_already);
                binding.btnTournament.setEnabled(false);
            }
        } else {
            binding.btnTournament.setVisibility(View.GONE);
        }
    }

    private void updateBtnGameState() {
        switch ( userState ) {
            case ORGANIC:
                if ( !TextUtils.isEmpty(demoUrl) ) {
                    binding.btnGame.setEnabled(true);
                }
                binding.btnGame.setText(R.string._play);
                binding.btnGame.setOnClickListener(v -> openGameScreen(demoUrl));
                break;
            case NOT_LOGGED:
                binding.btnGame.setText(R.string._registration);
                binding.btnGame.setEnabled(true);
                binding.btnGame.setOnClickListener(v -> openRegisterScreen());
                break;
            case PLAYER:
                Double balance = ((MainActivity) getActivity()).getBalance();
                if ( hasGameFreeSpins == null ) {
                    binding.btnGame.setText(R.string._play);
                    return;
                } else if ( (balance != null && balance > 0) || hasGameFreeSpins ) {
                    if ( !TextUtils.isEmpty(fullUrl) ) {
                        binding.btnGame.setEnabled(true);
                    }
                    binding.btnGame.setBackground(ContextCompat.getDrawable(getContext(), R.drawable.btn_bg));
                    binding.btnGame.setText(R.string._play);
                    binding.btnGame.setOnClickListener(v -> openGameScreen(fullUrl));
                } else {
                    binding.btnGame.setBackground(ContextCompat.getDrawable(getContext(), R.drawable.btn_blue_bg));
                    binding.btnGame.setEnabled(true);
                    binding.btnGame.setText(R.string._replenish);
                    binding.btnGame.setOnClickListener(v ->
                            ((MainActivity) getActivity()).openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT));
                }
                break;
        }
    }

    private void updateSwitcherFavourite() {
        switch ( userState ) {
            case ORGANIC:
            case NOT_LOGGED:
                binding.containerSwitchFavourite.setVisibility(View.GONE);
                break;
            case PLAYER:
                binding.containerSwitchFavourite.setVisibility(View.VISIBLE);
                binding.switchFavourite.setOnCheckedChangeListener(switchFavouriteListener);
                break;
        }
    }

    private final CompoundButton.OnCheckedChangeListener switchFavouriteListener = new CompoundButton.OnCheckedChangeListener() {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
            viewModel.getGameByIdLiveData().removeObservers(getViewLifecycleOwner());
            viewModel.changeGameFavouriteState(game.getId(), isChecked);
            SessionDataHolder.getInstance().needUpdateFavouriteList = true;
        }
    };

    private void setSwitchFavouriteChecked(boolean checked) {
        binding.switchFavourite.setOnCheckedChangeListener(null);
        binding.switchFavourite.setChecked(checked);
        binding.switchFavourite.setOnCheckedChangeListener(switchFavouriteListener);
    }

    private static void setScrollingEnabled(@NonNull Activity activity, boolean isEnabled) {
        AppBarLayout.LayoutParams params = (AppBarLayout.LayoutParams)
                activity.findViewById(R.id.toolbar).getLayoutParams();

        params.setScrollFlags(isEnabled ? (AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL
                | AppBarLayout.LayoutParams.SCROLL_FLAG_ENTER_ALWAYS) : 0);
    }

    private void openGameScreen(String gameUrl) {
        if ( TextUtils.isEmpty(gameUrl) ) {
            return;
        }

        Activity activity = getActivity();
        if ( activity instanceof MainActivity ) {
            if ( GeneralTools.isNetworkConnected(activity) ) {
                LocalGameItem gameItemById = null;
                if ( viewModel.getGameByIdLiveData().getValue() != null ) {
                    gameItemById = viewModel.getGameByIdLiveData().getValue().getData();
                }
                int tournamentId = (gameItemById != null && gameItemById.getTournamentId() != null) ?
                        gameItemById.getTournamentId() : 0;
                ((MainActivity) activity).openGameActivity(gameUrl, fullUrl, tournamentId);
            } else {
                ((MainActivity) activity).showNoInternetMessage();
            }
        }
    }

    private void openRegisterScreen() {
        Activity activity = getActivity();
        if ( activity instanceof MainActivity ) {
            ((MainActivity) activity).openEnterScreen(false);
        }
    }

    private void navigateUp() {
        View fragmentView = getView();
        if ( fragmentView != null ) {
            Navigation.findNavController(fragmentView).navigateUp();
        }
    }
}
