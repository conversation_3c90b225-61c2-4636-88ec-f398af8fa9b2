package com.abrand.custom.tools

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log
import com.abrand.custom.data.entity.AnalyticsEvent
import com.abrand.custom.network.OkHttpProcessor
import kotlin.system.exitProcess

class ExitService : Service() {

    companion object {
        const val TAG = "CloseApp"
    }

    override fun onBind(intent: Intent): IBinder? {
        return null
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        Log.d(TAG, "App closed: event sending...")
        OkHttpProcessor.sendAnalytics(baseContext, AnalyticsEvent.CLOSE_APP, null,
                object : OkHttpProcessor.AnalyticsListener {
                    override fun onSuccess() {
                        Log.d(TAG, "App closed: event sent")
                        exitProcess(0);
                    }

                    override fun onFailure() {
                        Log.d(TAG, "App closed: event onFailure")
                        exitProcess(0);
                    }
                })

        val broadcastIntent = Intent()
        broadcastIntent.action = "APP_CLOSED"
        broadcastIntent.setClass(this, ExitServiceRestartReceiver::class.java)
        this.sendBroadcast(broadcastIntent)

        super.onTaskRemoved(rootIntent)
    }
}
