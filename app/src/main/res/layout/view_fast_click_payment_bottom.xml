<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/lay_1click"
        android:visibility="visible"
        android:weightSum="10"
        android:background="@drawable/pay_field_bg"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <RelativeLayout
            android:id="@+id/container_fast_payment_amount"
            android:layout_width="0dp"
            android:layout_weight="5"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="@+id/fast_payment_divider"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentStart="true"
                android:layout_toStartOf="@+id/iv_fast_payment_logo"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_fast_payment_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="center_vertical"
                    android:focusedByDefault="false"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:inputType="number"
                    android:maxLength="15"
                    android:minWidth="10dp"
                    android:textColor="#000000"
                    android:textSize="16dp" />

                <TextView
                    android:id="@+id/tv_fast_payment_currency"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:layout_marginStart="5dp"
                    android:textColor="#000000"
                    android:textSize="16dp" />
            </LinearLayout>

            <ImageView
                android:id="@+id/iv_fast_payment_logo"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:layout_alignParentTop="true"
                android:layout_alignParentBottom="true"
                android:layout_marginEnd="8dp"
                />
        </RelativeLayout>



        <Button
            android:layout_weight="5"
            android:id="@+id/btn_fast_payment"
            style="@style/ButtonStyle"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:layout_marginBottom="8dp"
            android:textSize="12dp"
            android:background="@drawable/btn_bg_enable"
            android:text="@string/to_pay_1click" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/lay_confirm"
        android:visibility="gone"
        android:background="@drawable/pay_field_bg"
        android:layout_width="match_parent"
        android:layout_height="112dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/textConfirm"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:text="@string/to_pay_text"
            android:textColor="@color/black"
            android:gravity="center"
            android:textSize="14dp"

            app:layout_constraintBottom_toTopOf="@id/buttons"
            />

        <LinearLayout
            android:id="@+id/buttons"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:weightSum="10"
            android:layout_marginBottom="8dp"
            android:layout_marginEnd="8dp"
            android:layout_marginStart="8dp"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <TextView
                android:id="@+id/btn_fast_payment_dismiss"

                android:layout_weight="4"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:textColor="@color/black"
                android:textSize="@dimen/size_16"
                android:textStyle="bold"
                android:gravity="center"
                android:text="@string/to_pay_dismiss"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <Button
                android:id="@+id/btn_fast_payment_confirm"
                style="@style/ButtonStyle"
                android:layout_weight="6"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:textSize="@dimen/size_16"
                android:background="@drawable/btn_bg_enable"
                android:text="@string/to_pay_confirm"
                android:textStyle="bold"

                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </LinearLayout>



    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/fast_payment_divider"
        android:layout_width="1dp"
        android:layout_height="24dp"
        android:layout_marginEnd="11dp"
        android:layout_marginRight="11dp"
        android:background="#33000000"
        app:layout_constraintBottom_toBottomOf="@+id/lay_1click"
        app:layout_constraintEnd_toStartOf="@+id/view_circle_fast_payment_requisite"
        app:layout_constraintTop_toTopOf="@+id/lay_1click" />


    <View
        android:id="@+id/view_circle_fast_payment_requisite"
        android:layout_width="2dp"
        android:layout_height="2dp"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="2dp"
        android:layout_marginRight="2dp"
        android:background="@drawable/circle_fast_payment_requisite" />

    <RelativeLayout
        android:id="@+id/container_fast_payment_loader"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:background="@drawable/pay_field_in_process_bg"
        android:elevation="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <ProgressBar
            android:id="@+id/iv_loader"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:progress="0"
            android:progressDrawable="@drawable/progress_bar_payment" />

        <TextView
            android:id="@+id/tv_pay_processing"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:gravity="center"
            android:text="@string/to_pay_processing"
            android:textColor="@color/black"
            android:textSize="14dp" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/container_fast_payment_result"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:background="@drawable/pay_field_in_process_bg"
        android:elevation="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ProgressBar
            android:id="@+id/pb_loader_success"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:progressDrawable="@drawable/progress_bar_payment" />

        <TextView
            android:id="@+id/tv_fast_payment_result"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="8dp"
            android:textColor="#279822"
            android:text="@string/transaction_successful"
            android:visibility="visible" />

        <TextView
            android:id="@+id/tv_fast_payment_result_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_fast_payment_result"
            android:layout_marginStart="@dimen/size_16"
            android:textSize="10sp"
            android:text="@string/transaction_successful_message"
            android:textColor="#000000"
            android:visibility="visible" />

        <ImageView
            android:id="@+id/iv_fast_payment_result_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="0dp"
            android:paddingStart="8dp"
            android:paddingTop="8dp"
            android:paddingEnd="8dp"
            android:paddingBottom="8dp"
            android:src="@drawable/ic_close" />


    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
