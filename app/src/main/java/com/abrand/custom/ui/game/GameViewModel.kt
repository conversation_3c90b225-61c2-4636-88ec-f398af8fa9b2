package com.abrand.custom.ui.game

import android.annotation.SuppressLint
import android.text.TextUtils
import android.util.Log
import android.webkit.WebView
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.*
import com.abrand.custom.data.Resource
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.MessageItem
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.data.entity.Talisman
import com.abrand.custom.data.entity.User
import com.abrand.custom.data.repositories.BonusBalancesRepository
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.*
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException
import com.apollographql.apollo3.exception.ApolloNetworkException
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class GameViewModel : ViewModel() {
    private val profileRepository: ProfileRepository = ProfileRepository()
    private val bonusBalancesRepository = BonusBalancesRepository
    val fastPaymentRebillingLiveData = MutableLiveData<Boolean>()
    val makeRebillLiveData = profileRepository.makeRebillLiveData
    val fastPaymentUrlLiveData: LiveData<String> = profileRepository.fastPaymentUrlLiveData
    val viewerWalletLiveData: MutableLiveData<SubscribeBalanceSubscription.ViewerWallet> = MutableLiveData()
    val fastClickPaymentSystemLiveData = profileRepository.fastClickPaymentSystemLiveData
    private val _viewerMessageLiveData = MutableLiveData<ArrayList<MessageItem>>()
    val viewerMessageLiveData: LiveData<ArrayList<MessageItem>> = _viewerMessageLiveData
    val talismanLiveData = MutableLiveData<Resource<Talisman?, ServerError, ApolloException>>()
    private val _sideEffectChannel = Channel<SideEffect>(capacity = Channel.BUFFERED)
    val sideEffectFlow: Flow<SideEffect>
        get() = _sideEffectChannel.receiveAsFlow()

    @SuppressLint("StaticFieldLeak")
    var webViewGame: WebView? = null

    var isRebilling = false
    var messagesJob: Job? = null
    var balanceJob: Job? = null
    private var bonusBalanceWonSubscription: Job? = null

    private val exceptionHandler = CoroutineExceptionHandler { _, e -> Log.e(TAG, e.toString()) }
    private val dispatcher = Dispatchers.Default + exceptionHandler


    init {
        _viewerMessageLiveData.value = ArrayList()
    }

    override fun onCleared() {
        super.onCleared()
        webViewGame = null
    }

    fun bonusBalanceWonSubscribe() {
        if (User.State.PLAYER != Settings.get().userState) {
            return
        }

        bonusBalanceWonUnsubscribe()
        bonusBalanceWonSubscription = viewModelScope.launch(dispatcher) {

            try {
                bonusBalancesRepository.getBonusBalanceWonSubscription().retrySubscription().collect {
                    _sideEffectChannel.send(SideEffect.BonusBalanceWonEffect(it.mapToUiModel()))
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "$TAG bonusBalanceWonSubscribe")
                crashlytics.recordException(e)
            }
        }

    }

    fun bonusBalanceWonUnsubscribe() {
        bonusBalanceWonSubscription?.cancel()
    }

    @SuppressLint("NullSafeMutableLiveData")
    fun balanceSubscribe() {
        if (User.State.PLAYER != Settings.get().userState) {
            return
        }

        balanceUnSubscribe()
        balanceJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeBalance().retrySubscription().collect { response ->
                    updateFastClickPaymentSystemIfReplenishment(response.data)
                    viewerWalletLiveData.postValue(response.data?.viewerWallet)
                    if (isRebilling) {
                        fastPaymentRebillingLiveData.postValue(false.also { isRebilling = it })
                    }
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "$TAG balanceSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun balanceUnSubscribe() {
        balanceJob?.cancel()
    }

    fun messagesSubscribe() {
        if (User.State.PLAYER != Settings.get().userState) {
            return
        }

        messagesUnSubscribe()
        messagesJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeMessages().retrySubscription().collect {
                    it.data?.viewerMessagesV2?.let { message ->
                        appendNewMessage(message)
                    }
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "$TAG messagesSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    private fun appendNewMessage(message: SubscribeMessagesSubscription.ViewerMessagesV2) {
        val messageItem = MessageItem(message)

        _viewerMessageLiveData.value?.add(messageItem)
        _viewerMessageLiveData.postValue(_viewerMessageLiveData.value)
    }

    fun messagesUnSubscribe() {
        messagesJob?.cancel()
    }

    fun removeMessageBy(index: Int) {
        _viewerMessageLiveData.value?.removeAt(index)
        _viewerMessageLiveData.postValue(_viewerMessageLiveData.value)
    }

    fun makeRebill(sum: Int) {
        profileRepository.makeRebill(sum)
        fastPaymentRebillingLiveData.postValue(true.also { isRebilling = it })
    }

    fun getFastPaymentUrl(sum: Int) {
        profileRepository.getFastPaymentUrl(sum)
    }

    private fun updateFastClickPaymentSystemIfReplenishment(data: SubscribeBalanceSubscription.Data?) {
        if (!TextUtils.isEmpty(data?.viewerWallet?.balance?.toString())) {
            val subscriptionBalance = data?.viewerWallet?.balance?.toString()?.toDouble()
            val currentBalance: Double? = getBalance()
            if (subscriptionBalance != null) {
                if (currentBalance != null && subscriptionBalance > currentBalance ||
                    currentBalance == null && subscriptionBalance > 0
                ) {
                    getFastClickPaymentSystem()
                }
            }
        }
    }

    private fun getBalance(): Double? {
        val viewerWalletData: SubscribeBalanceSubscription.ViewerWallet? = viewerWalletLiveData.value
        return viewerWalletData?.balance?.toString()?.toDouble()
    }

    fun getFastClickPaymentSystem() {
        profileRepository.getFastClickPaymentSystem()
    }

    fun getTalisman() {
        ApolloProcessorKt.getTalisman(object  : GenericTarget<Talisman>{
            override fun onSuccess(t: Talisman?) {
                talismanLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                talismanLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                talismanLiveData.postValue(Resource.failure(e))
            }
        })
    }

    companion object {
        const val TAG = "GameViewModel"
    }

    sealed interface SideEffect {
        data class BonusBalanceWonEffect(val balance: BonusBalanceWonUI) : SideEffect
    }

    data class BonusBalanceWonUI(val value: String, val id: Int)

    private fun BonusBalanceWon.mapToUiModel(): BonusBalanceWonUI {
        val rubSymbol = " ₽"
        val textValue = "${this.value.toInt()}$rubSymbol"

        return BonusBalanceWonUI(textValue, this.id)
    }
}
