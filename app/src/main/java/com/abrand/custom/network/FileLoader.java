package com.abrand.custom.network;

import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.util.Log;

import androidx.core.content.FileProvider;

import com.abrand.custom.BuildConfig;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class FileLoader {
    private final String  TAG            = "FileLoader";
    private final String  newApkName     = "armada.apk";
    private       boolean cancelDownload = false;

    public void download(Context context, String url, DownloadListener downloadListener) {
        File apkFile = getApkFile(context);
        final Uri uri =
                (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) ?
                        FileProvider.getUriForFile(context, BuildConfig.APPLICATION_ID + ".provider", apkFile) :
                        Uri.fromFile(apkFile);

        InputStream       input      = null;
        OutputStream      output     = null;
        HttpURLConnection connection = null;
        try {
            URL sUrl = new URL(url);
            connection = (HttpURLConnection) sUrl.openConnection();
            connection.connect();

            input = connection.getInputStream();
            output = new FileOutputStream(apkFile);

            byte[] data = new byte[4096];
            int    count;
            while ( (count = input.read(data)) != -1 ) {
                if ( cancelDownload ) {
                    input.close();
                    fileDelete(apkFile);
                    cancelDownload = false;
                    downloadListener.onDownloadCancelled();
                    return;
                }
                output.write(data, 0, count);
                if ( apkFile.length() > 0 && connection.getContentLength() > 0 ) {
                    downloadListener.onProgress((int) (apkFile.length() * 100 / connection.getContentLength()));
                }
            }
            downloadListener.onFinish(true, uri);
        } catch ( Exception e ) {
            Log.e(TAG, e.toString());
            downloadListener.onFinish(false, uri);
        } finally {
            try {
                if ( output != null )
                    output.close();
                if ( input != null )
                    input.close();
            } catch ( IOException ignored ) {
            }

            if ( connection != null ) {
                connection.disconnect();
            }
        }
    }

    private File getApkFile(Context context) {
        String dirPath = Build.VERSION.SDK_INT >= Build.VERSION_CODES.N
                ? context.getFilesDir().getAbsolutePath()
                : Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).getAbsolutePath();
        return new File(dirPath + File.separator + newApkName);
    }

    private void fileDelete(File file) {
        if ( file.exists() ) {
            file.delete();
        }
    }

    public void deleteApkFile(Context context) {
        fileDelete(getApkFile(context));
    }

    public void cancelDownload() {
        cancelDownload = true;
    }

    public interface DownloadListener {
        void onProgress(int progress);

        void onDownloadCancelled();

        void onFinish(boolean isSuccess, Uri fileUri);
    }
}
