package com.abrand.custom.tools

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import com.abrand.custom.data.Constants
import com.abrand.custom.interfaces.BiometricAuthListener

object BiometricUtil {
    private val TAG = "BiometricUtil"

    fun canAuthenticate(context: Context) : Int {
        val biometricManager = BiometricManager.from(context)
        val authenticationStatus = biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG)
        when (authenticationStatus) {
            BiometricManager.BIOMETRIC_SUCCESS ->
                Log.d(TAG, "BIOMETRIC_SUCCESS: The user can successfully authenticate")
            BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED ->
                Log.d(TAG, "BIOMETRIC_ERROR_NONE_ENROLLED: " +
                        "The user can't authenticate because no biometric or device credential is enrolled")
            BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE ->
                Log.d(TAG, "BIOMETRIC_ERROR_NO_HARDWARE: No biometric features available on this device.")
            BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE ->
                Log.d(TAG, "BIOMETRIC_ERROR_HW_UNAVAILABLE: Biometric features are currently unavailable.")
            BiometricManager.BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED ->
                Log.d(TAG, "BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED")
            BiometricManager.BIOMETRIC_ERROR_UNSUPPORTED ->
                Log.d(TAG, "BIOMETRIC_ERROR_UNSUPPORTED: The user can't authenticate because " +
                        "the specified options are incompatible with the current Android version")
            BiometricManager.BIOMETRIC_STATUS_UNKNOWN ->
                Log.d(TAG, "BIOMETRIC_STATUS_UNKNOWN: Unable to determine whether the user can authenticate")
        }
        return authenticationStatus
    }

    @RequiresApi(Build.VERSION_CODES.M)
    fun hasFeatureFingerPrint(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_FINGERPRINT);
    }

    fun launchBiometricSettings(activity: Activity) {
        try {
            activity.startActivityForResult(createBiometricIntent(), Constants.BIOMETRIC_SETTINGS_REQUEST_CODE)
        } catch (e: ActivityNotFoundException) {
            activity.startActivityForResult(Intent(Settings.ACTION_FINGERPRINT_ENROLL), Constants.BIOMETRIC_SETTINGS_REQUEST_CODE)
        }
    }

    private fun createBiometricIntent(): Intent {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.P -> Intent(Settings.ACTION_BIOMETRIC_ENROLL)
            else -> Intent(Settings.ACTION_SECURITY_SETTINGS)
        }
    }

    fun showBiometricPrompt(
        title: String = "Biometric Authentication",
        subtitle: String = "",
        description: String = "",
        negativeButtonText: String,
        activity: AppCompatActivity,
        listener: BiometricAuthListener,
        cryptoObject: BiometricPrompt.CryptoObject? = null) {

        val promptInfo = setBiometricPromptInfo(title, subtitle, description, negativeButtonText)

        val biometricPrompt = initBiometricPrompt(activity, listener)

        biometricPrompt.apply {
            if (cryptoObject == null) authenticate(promptInfo)
            else authenticate(promptInfo, cryptoObject)
        }
    }

    fun initBiometricPrompt(activity: AppCompatActivity, listener: BiometricAuthListener): BiometricPrompt {
        val executor = ContextCompat.getMainExecutor(activity)

        val callback = object : BiometricPrompt.AuthenticationCallback() {
            override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                super.onAuthenticationError(errorCode, errString)
                listener.onBiometricAuthenticationError(errorCode, errString.toString())
            }

            override fun onAuthenticationFailed() {
                super.onAuthenticationFailed()
                Log.w(TAG, "Authentication failed for an unknown reason")
            }

            override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                super.onAuthenticationSucceeded(result)
                listener.onBiometricAuthenticationSuccess(result)
            }
        }

        return BiometricPrompt(activity, executor, callback)
    }

    private fun setBiometricPromptInfo(title: String, subtitle: String, description: String,
        negativeButtonText: String): BiometricPrompt.PromptInfo {
        val builder = BiometricPrompt.PromptInfo.Builder()
            .setTitle(title)
            .setSubtitle(subtitle)
            .setDescription(description)
            .setNegativeButtonText(negativeButtonText)

        return builder.build()
    }
}
