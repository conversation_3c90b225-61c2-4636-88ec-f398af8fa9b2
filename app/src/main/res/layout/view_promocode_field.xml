<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="54dp"
        android:paddingStart="16dp"
        android:paddingTop="8dp"
        android:paddingEnd="16dp"
        android:paddingBottom="8dp"
        tools:background="@drawable/bg_text_field">

        <TextView
            android:id="@+id/hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/hint_editable_field"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.5"
            tools:text="Hint" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@null"
            android:textColor="#000"
            android:textSize="14sp"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@+id/ib_promo_code_enter"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/hint"
            tools:text="Input data"
            tools:visibility="visible" />

        <ImageButton
            android:id="@+id/ib_promo_code_enter"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/btn_promotion_bg"
            android:padding="8dp"
            android:src="@drawable/ic_bonus_promo_code_arrow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/error"
        android:layout_width="match_parent"
        android:layout_height="20dp"
        android:layout_gravity="center_horizontal"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        android:textAlignment="center"
        android:textColor="#ffffff"
        android:textFontWeight="400"
        android:textSize="11sp"
        android:visibility="gone"
        tools:text="error message"
        tools:visibility="visible" />
</LinearLayout>
