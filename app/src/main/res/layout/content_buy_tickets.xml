<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingStart="@dimen/size_16"
    android:paddingEnd="@dimen/size_16">

    <TextView
        android:id="@+id/tv_ticket_packages_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/lottery_ticket_packages_title"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_ticket_packages"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/tv_ticket_packages_label" />

    <Button
        android:id="@+id/btn_buy_package"
        style="@style/ButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="24dp"
        android:text="@string/lottery_buy"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rv_ticket_packages" />

    <TextView
        android:id="@+id/tv_buy_quantity_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/lottery_buy_another_quantity"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toStartOf="@id/iv_buy_quantity_info"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_buy_package" />

    <ImageView
        android:id="@+id/iv_buy_quantity_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="4dp"
        android:paddingEnd="8dp"
        android:src="@drawable/ic_tournament_info"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/tv_buy_quantity_label"
        app:layout_constraintTop_toTopOf="@+id/tv_buy_quantity_label" />

    <com.abrand.custom.ui.views.textfield.TextField
        android:id="@+id/etTickets"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="26dp"
        android:layout_marginEnd="@dimen/size_16"
        app:hint="Билеты"
        app:layout_constraintEnd_toStartOf="@+id/ivExchangeArrows"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_buy_quantity_label" />

    <ImageView
        android:id="@+id/ivExchangeArrows"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_exchange_arrows"
        app:layout_constraintBottom_toBottomOf="@+id/etTickets"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/etTickets" />

    <com.abrand.custom.ui.views.textfield.TextField
        android:id="@+id/etPrice"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_16"
        app:hint="Стоимость"
        app:layout_constraintBottom_toBottomOf="@+id/etTickets"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivExchangeArrows"
        app:layout_constraintTop_toTopOf="@+id/etTickets" />

    <Button
        android:id="@+id/btn_buy_quantity"
        style="@style/ButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="24dp"
        android:text="@string/lottery_buy"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etTickets" />

</androidx.constraintlayout.widget.ConstraintLayout>
