package com.abrand.custom;

import android.text.TextUtils;
import android.util.Log;

import androidx.fragment.app.testing.FragmentScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.abrand.custom.data.entity.AuthResponse;
import com.abrand.custom.data.entity.LocalGameItem;
import com.abrand.custom.fragment.GameThumb;
import com.abrand.custom.interfaces.GenericTarget;
import com.abrand.custom.interfaces.LoadTarget;
import com.abrand.custom.network.ApolloProcessor;
import com.abrand.custom.presenter.FieldsErrorsHolder;
//import com.abrand.custom.type.GameAttributeType;
//import com.abrand.custom.type.GameOrderField;
import com.abrand.custom.type.OrderDirection;
import com.abrand.custom.ui.enter.EnterFragment;
import com.apollographql.apollo3.exception.ApolloException;

import org.jetbrains.annotations.Nullable;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.List;

@RunWith(AndroidJUnit4.class)
public class GamesTest {
    private final String[] error         = {null};
    private final int      RESPONSE_TIME = 5_000;

    @Before
    public void setUp() {
//        FragmentScenario.launch(EnterFragment.class, null, R.style.AppTheme, null);

//        ApolloProcessor.getCaptchas(false, new GetCaptchaTarget() {
//            @Override
//            public void onSuccess(boolean isCaptchaEnabled, String captchaId, boolean isReCaptchaEnabled, String reCaptchaId) {
//                if ( isCaptchaEnabled ) {
//                    error[0] = "Captcha enabled, can't login";
//                } else if ( isReCaptchaEnabled ) {
//                    error[0] = "ReCaptcha enabled, can't login";
//                } else {
//                    login();
//                }
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = "getCaptchas: " + e.toString();
//            }
//        });

        waitResponseAndAssertFailIfError();
    }

    private void login() {
        // TODO: 28.10.2022 rewrite to Kt version
//        final String email    = "<EMAIL>";
//        final String password = "qweqwe1";
//
//        ApolloProcessor.login(email, password, "", "",
//                "", new GenericTarget<AuthResponse>() {
//                    @Override
//                    public void onSuccess(@Nullable AuthResponse authResponse) {
//                        if ( TextUtils.isEmpty(authResponse.getUserId()) ) {
//                            error[0] = "success response but empty userId";
//                        }
//                    }
//
//                    @Override
//                    public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                        error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//                    }
//
//                    @Override
//                    public void onFailure(ApolloException e) {
//                        error[0] = e.toString();
//                    }
//                });
    }

    @Test
    public void obtainGameUrlsTest() {
        System.out.println("obtainGameUrlTest 1");
        Log.d("TestUrl", "obtainGameUrlTest");
        // TODO: 19.10.2022 refactor to proper solution
        //removed as unused network call
//        ApolloProcessor.loadByAttribute(GameAttributeType.POPULAR, 20, 0,
//                GameOrderField.POPULARITY, OrderDirection.ASC, new LoadTarget() {
//                    @Override
//                    public void onSuccess(List<LocalGameItem> localGameItems, int total, int offset) {
//                        if ( localGameItems.size() > 0 ) {
//                            for ( LocalGameItem localGameItem : localGameItems ) {
//                                if ( !TextUtils.isEmpty(localGameItem.getUrl()) &&
//                                        !TextUtils.isEmpty(localGameItem.getDemoUrl()) ) {
//                                    obtainGameUrl(localGameItem.getUrl());
//                                    obtainDemoGameUrl(localGameItem.getDemoUrl());
//                                    break;
//                                }
//                            }
//                        } else {
//                            error[0] = "server returned empty game list";
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(ApolloException e) {
//                        error[0] = e.toString();
//                    }
//                });
//
//        waitResponseAndAssertFailIfError();
    }

    private void obtainGameUrl(String shortUrl) {
        // TODO: 28.10.2022 rewrite to Kt version
//        ApolloProcessor.obtainGameUrl(shortUrl, "", new GenericTarget<String>() {
//            @Override
//            public void onSuccess(String url) {
//                if ( TextUtils.isEmpty(url) ) {
//                    error[0] = "game url is empty";
//                }
//            }
//
//            @Override
//            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
    }

    private void obtainDemoGameUrl(String shortUrl) {
        // TODO: 28.10.2022 change to Kt version
//        ApolloProcessor.obtainDemoGameUrl(shortUrl, new GenericTarget<String>() {
//            @Override
//            public void onSuccess(String url) {
//                if ( TextUtils.isEmpty(url) ) {
//                    error[0] = "demo game url is empty";
//                }
//            }
//
//            @Override
//            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
    }

    private void waitResponseAndAssertFailIfError() {
        try {
            Thread.sleep(RESPONSE_TIME);
            if ( !TextUtils.isEmpty(error[0]) ) {
                Assert.fail(error[0]);
            }
        } catch ( InterruptedException e ) {
            e.printStackTrace();
        }
    }
}
