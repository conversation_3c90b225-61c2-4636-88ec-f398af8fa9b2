<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/menu_item_bg">

    <RelativeLayout
        android:id="@+id/menu_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/icon_menu"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginLeft="@dimen/size_16" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="40dp"
            android:gravity="center_vertical"
            android:padding="10dp"
            android:textColor="#000000" />

        <ImageView
            android:id="@+id/iv_drop_down"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginEnd="20dp" />

        <TextView
            android:id="@+id/tv_counter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="10dp"
            android:layout_toStartOf="@+id/iv_selected_item"
            android:background="@drawable/bg_drawer_counter"
            android:paddingStart="6dp"
            android:paddingEnd="6dp"
            android:text="2"
            android:textColor="#000000" />

        <ImageView
            android:id="@+id/iv_selected_item"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:rotation="180"
            android:src="@drawable/ic_menu_selected" />

    </RelativeLayout>
</FrameLayout>
