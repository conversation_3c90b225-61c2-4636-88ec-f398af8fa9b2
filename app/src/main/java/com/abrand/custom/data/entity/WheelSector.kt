package com.abrand.custom.data.entity

import androidx.annotation.DrawableRes
import com.abrand.custom.R

data class WheelSector(val position: Int?, val title: String?, val type: PrizeType?) {

    enum class PrizeType(val alias: String, @DrawableRes val icon: Int) {
        ZERO("zero", R.drawable.ic_wheel_prise_zero),
        RESPIN("paid_wof_free_spin", R.drawable.ic_wheel_prise_respin),
        MONEY("money", R.drawable.ic_wheel_prise_money),
        POINTS("points", R.drawable.ic_wheel_prise_points),
        X2("x2", R.drawable.ic_wheel_prise_x2),
        X3("x3", R.drawable.ic_wheel_prise_x3),
        GIFT_SPINS("gift_spins", R.drawable.ic_wheel_prise_gift);

        companion object {
            fun getByAlias(alias: String): PrizeType? = values().find { it.alias == alias }
        }
    }
}
