<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="0dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="7dp"
        android:elevation="10dp"
        android:text="@string/bonus_wagering"
        android:textAllCaps="true"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_reset_question"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:elevation="10dp"
        android:text="@string/reset_bonus_balance_drawer"
        android:textAllCaps="true"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_yes"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_bet_refund_sum"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="2dp"
        android:paddingBottom="7dp"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_percent_to_refund"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/iv_reset_bonus_balance"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_reset_bonus_balance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:padding="8dp"
        android:src="@drawable/ic_bonus_wagering_cancel"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_yes"
        style="@style/ButtonStyle"
        android:layout_width="0dp"
        android:layout_height="30dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/bg_btn_reset_bonus_balance_no"
        android:minWidth="0dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="@string/yes"
        android:textAllCaps="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btn_no"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_no"
        style="@style/ButtonStyle"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/btn_bg_drawer_exchange_points"
        android:minWidth="0dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:text="@string/no"
        android:textAllCaps="true"
        android:textColor="#005BEA"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/progress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:progressDrawable="@drawable/bonus_wagering_drawer_progress_bg"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

</androidx.constraintlayout.widget.ConstraintLayout>
