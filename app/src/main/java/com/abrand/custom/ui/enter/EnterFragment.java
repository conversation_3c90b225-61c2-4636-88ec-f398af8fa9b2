package com.abrand.custom.ui.enter;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.text.InputType;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.biometric.BiometricManager;
import androidx.biometric.BiometricPrompt;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.core.view.ViewCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.transition.TransitionManager;

import com.abrand.custom.AApp;
import com.abrand.custom.BuildConfig;
import com.abrand.custom.GetRegisterBonusesQuery;
import com.abrand.custom.data.JsonDataGenerator;
import com.abrand.custom.data.ApoloConfig;
import com.abrand.custom.data.Settings;
import com.abrand.custom.data.entity.AnalyticsEvent;
import com.abrand.custom.data.entity.AvailableSocialNetworksResponse;
import com.abrand.custom.data.entity.BiometricCredentials;
import com.abrand.custom.data.entity.EncryptedMessage;
import com.abrand.custom.data.entity.LoginType;
import com.abrand.custom.data.entity.Screen;
import com.abrand.custom.data.entity.SocialNetwork;
import com.abrand.custom.data.entity.TournamentsItem;
import com.abrand.custom.data.entity.AuthResponse;
import com.abrand.custom.data.entity.UrlSource;
import com.abrand.custom.data.entity.lottery.LocalLottery;
import com.abrand.custom.interfaces.BiometricAuthListener;
import com.abrand.custom.interfaces.GenericTarget;
import com.abrand.custom.interfaces.GetRegisterBonusesTarget;
import com.abrand.custom.network.OkHttpProcessor;
import com.abrand.custom.tools.BiometricUtil;
import com.abrand.custom.tools.CryptographyUtil;
import com.abrand.custom.tools.DateTools;
import com.abrand.custom.social.MailRuNetwork;
import com.abrand.custom.social.OkRuNetwork;
import com.abrand.custom.social.YandexNetwork;
import com.abrand.custom.tools.GeneralTools;
import com.abrand.custom.ui.activitymain.MainActivity;
import com.abrand.custom.R;
import com.abrand.custom.databinding.FragmentEnterBinding;
import com.abrand.custom.presenter.Fields;
import com.abrand.custom.presenter.FieldsErrorsHolder;
import com.abrand.custom.ui.views.textfield.TextField;
import com.abrand.custom.ui.views.textfield.Validator;
import com.adjust.sdk.Adjust;
import com.apollographql.apollo3.exception.ApolloException;
import com.facebook.login.LoginManager;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.squareup.picasso.Picasso;
import com.vk.api.sdk.VK;
import com.vk.api.sdk.auth.VKScope;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Date;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.crypto.Cipher;

public class EnterFragment extends Fragment {
    // TODO: 2019-12-19 add password check by regexp from back team
    private static final String TAG = EnterFragment.class.getSimpleName();
    private static final int RULES_VIEW_PREFIX_LENGTH = 33;
    private final int MIN_PASSWORD_LENGTH = 6;
    public static final String IS_LOGIN_KEY = "is_login";
    public static final String DEEP_LINK_URL = "deepLinkUrl";

    private EnterViewModel viewModel;
    private FragmentEnterBinding binding;
    private boolean              isLogin;
    private String               deepLinkUrl = "";
    private TournamentsItem tournamentItem;
    private LocalLottery    lottery;
    private Screen openScreen;
    private boolean isLoginMode;
    private GetRegisterBonusesQuery.RegisterBonuse selectedRegisterBonus = null;
    private List<? extends GetRegisterBonusesQuery.RegisterBonuse> registerBonuses = new ArrayList<>();
    private BiometricCredentials biometricCredentials;
    private String decryptedPassword;
    private BiometricPrompt.CryptoObject cryptoObject;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        binding = FragmentEnterBinding.inflate(inflater);

        if ( viewModel == null ) {
            viewModel = new ViewModelProvider(this).get(EnterViewModel.class);
        }

        if ( getArguments() != null ) {
            isLogin = getArguments().getBoolean(IS_LOGIN_KEY, true);
            deepLinkUrl = getArguments().getString(DEEP_LINK_URL, "");
            tournamentItem = (TournamentsItem) getArguments().get(MainActivity.TOURNAMENT_ITEM);
            lottery = (LocalLottery) getArguments().get(MainActivity.LOTTERY_ITEM);
            openScreen = (Screen) getArguments().get(MainActivity.KEY_SCREEN);
        }

        if ( getContext() != null ) {
            setupMode(isLogin, getContext(), false);
        }

        binding.etEnterLogin.setInputType(InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS);
        binding.etEnterPassword.setInputType(InputType.TYPE_TEXT_VARIATION_WEB_PASSWORD);
        binding.etEnterPassword.subscribeToUpdates((data, valid) -> {
            if ( !valid ) {
                binding.etEnterPassword.showErrorMessage("");
            } else {
                if ( isLogin() ) {
                    binding.etEnterLogin.hideErrorMessage();
                }
                binding.etEnterPassword.hideErrorMessage();
            }
        });
        binding.etEnterPassword.setHidePassword(true);

        setupRegistrationRulesView();
        setOCL();

        observeViewModel();

        //Pass to network layer from view observer with link on view is bad, need to be refactored
        viewModel.getRegisterBonuses(getRegisterBonusesTarget);

        viewModel.getSocialNetworks();

        setupInsets();

        //Implement after move EnterFragment .java to .kt
//        viewLifecycleOwner.lifecycleScope.launch {
//            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
//                viewModel.eventFlow.collect { event ->
//                    when(event) {
//                        EnterViewModel.EnterEffects.EmptyReCaptchaID -> {
//                            MainActivity mainActivity = (MainActivity) getActivity();
//                            if ( mainActivity != null ) {
//                                mainActivity.showMessage(getString(R.string.captcha_id_empty_message));
//                            }
//                        }
//                        EnterViewModel.EnterEffects.CaptchaStatusError -> {
//                            if ( isResumed() ) {
//                                val errorMessage = event.message
//                                getActivity().runOnUiThread(() -> {
//                                    Toast.makeText(getActivity(), errorMessage, Toast.LENGTH_SHORT).show();
//                                });
//                            }
//                        }
//                        EnterViewModel.EnterEffects.CaptchaStatusNetworkError -> {
//                            if ( isResumed() ) {
//                                binding.getRoot().post(() ->
//                                        Toast.makeText(getContext(), "Error: network error",
//                                                Toast.LENGTH_SHORT).show());
//                                ((MainActivity) getActivity()).showConnectionIssueMessage(event.e);
//                            }
//                        }
//                    }
//                }
//            }
//        }

        return binding.getRoot();
    }

    @SuppressLint("NewApi")
    private void initBiometric() {
        if ( isLogin ) {
            biometricCredentials = Settings.get().getBiometricCredentials();
            int authenticationStatus = BiometricUtil.INSTANCE.canAuthenticate(getContext());
            if ( biometricCredentials != null &&
                    BiometricManager.BIOMETRIC_SUCCESS == authenticationStatus &&
                    getCryptoObject() != null ) {
                binding.btnBiometric.setVisibility(View.VISIBLE);
                binding.btnBiometric.setOnClickListener(view -> showBiometricPromptToDecrypt());
                showBiometricPromptToDecrypt();
            } else {
                binding.btnBiometric.setVisibility(View.GONE);
            }
        } else {
            binding.btnBiometric.setVisibility(View.GONE);
        }
    }

    @SuppressLint("NewApi")
    private BiometricPrompt.CryptoObject getCryptoObject() {
        try {
            byte[] initializationVector = biometricCredentials.getEncryptedPassword().getInitializationVector();
            cryptoObject = new BiometricPrompt.CryptoObject(
                    CryptographyUtil.INSTANCE.getInitializedCipherForDecryption(initializationVector));
            return cryptoObject;
        } catch ( Exception exception ) {
            Settings.get().setBiometricCredentials(null);
            return null;
        }
    }

    private void observeViewModel() {
        viewModel.getSocialNetworksLiveData().observe(getViewLifecycleOwner(), it -> {
            binding.viewSocialLogin.hideLoader();
            switch ( it.getStatus() ) {
                case SUCCESS:
                    AvailableSocialNetworksResponse availableSocialNetworksResponse = it.getData();
                    if ( availableSocialNetworksResponse != null ) {
                        List<String> socialNetworksForLogin = availableSocialNetworksResponse.getSocialNetworksForLogin();
                        if ( socialNetworksForLogin != null ) {
                            binding.viewSocialLogin.createAvailableSocialNetworksButtons(socialNetworksForLogin);
                        }
                    }
                    break;
                case FAILURE:
                    if ( getActivity() != null && EnterFragment.this.isResumed() ) {
                        ((MainActivity) getActivity()).showConnectionIssueMessage(it.getFailure());
                    }
                    break;
            }
        });
    }

    private void setupInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(binding.glTitleBot, (view, insets) -> {
            int guidelineBegin = getResources().getDimensionPixelSize(R.dimen.enter_title_guideline_begin) +
                    insets.getSystemWindowInsetTop();
            binding.glTitleBot.setGuidelineBegin(guidelineBegin);
            return insets;
        });

        ViewCompat.setOnApplyWindowInsetsListener(binding.clRoot, (view, insets) -> {
            view.setPadding(view.getPaddingLeft(), view.getPaddingTop(), view.getPaddingRight(), insets.getSystemWindowInsetBottom());
            return insets;
        });
    }

    @Override
    public void onDestroyView() {
        GeneralTools.hideKeyboard(getActivity());
        super.onDestroyView();
    }

    public boolean isLogin() {
        return isLogin;
    }

    public void setLogin(boolean login) {
        isLogin = login;
    }

    private TextField.UpdateListener loginUpdateListener = new TextField.UpdateListener() {
        @Override
        public void onUpdate(String data, boolean valid) {
            if ( !valid ) {
                binding.etEnterLogin.showErrorMessage(getResources().getString(R.string._incorrect_email));
            } else {
                binding.etEnterLogin.hideErrorMessage();
                binding.etEnterPassword.hideErrorMessage();
            }
        }
    };

    private void setOCL() {
        binding.tvEnterForgetPass.setOnClickListener(v -> {
            if ( getActivity() != null && getActivity() instanceof MainActivity ) {
                ((MainActivity) getActivity()).openForgotPassScreen();
            }
        });
        binding.btnEnterGo.setOnClickListener(v -> {
            if ( !binding.etEnterLogin.isSubscribed(loginUpdateListener) ) {
                binding.etEnterLogin.subscribeToUpdates(loginUpdateListener);
            }

            if ( TextUtils.isEmpty(binding.etEnterLogin.getText()) ) {
                binding.etEnterLogin.showErrorMessage(getResources().getString(R.string._field_cant_be_empty));
            } else if ( !Validator.isValidEmail(binding.etEnterLogin.getText()) ) {
                binding.etEnterLogin.showErrorMessage(getResources().getString(R.string._incorrect_email));
            }

            if ( TextUtils.isEmpty(binding.etEnterPassword.getText()) ) {
                binding.etEnterPassword.showErrorMessage(getResources().getString(R.string._field_cant_be_empty));
            } else if ( binding.etEnterPassword.getText().length() < MIN_PASSWORD_LENGTH ) {
                binding.etEnterPassword.showErrorMessage(
                        getResources().getString(R.string.min_symbols_validation_message, MIN_PASSWORD_LENGTH));
            }

            // TODO: 2019-12-20 check and process
            if ( !binding.etEnterLogin.isNotEmpty() || !binding.etEnterPassword.isNotEmpty()
                    || !binding.etEnterLogin.isValid() || !binding.etEnterPassword.isValid() ) {
                Toast.makeText(getContext(), R.string.fix_errors, Toast.LENGTH_SHORT).show();
                return;
            }

            proceedEnter();
        });
        binding.tvRegistrationRules.setOnClickListener(v -> {
            if ( getActivity() != null && getActivity() instanceof MainActivity ) {
                ((MainActivity) getActivity()).openRulesScreen();
            }
        });

        binding.containerRegistrationBonusFirst.setOnClickListener(v -> {
            if ( registerBonuses.size() > 0 ) {
                clearContainersRegistrationBonusBackground();
                binding.containerRegistrationBonusFirst.setBackground(getResources().getDrawable(R.drawable.register_bonus_bg));
                binding.cbRegistrationBonus.setChecked(false);
                selectedRegisterBonus = registerBonuses.get(0);
            }
        });
        binding.containerRegistrationBonusSecond.setOnClickListener(v -> {
            if ( registerBonuses.size() > 1 ) {
                clearContainersRegistrationBonusBackground();
                binding.containerRegistrationBonusSecond.setBackground(getResources().getDrawable(R.drawable.register_bonus_bg));
                binding.cbRegistrationBonus.setChecked(false);
                selectedRegisterBonus = registerBonuses.get(1);
            }
        });
        binding.containerRegistrationBonusThird.setOnClickListener(v -> {
            if ( registerBonuses.size() > 2 ) {
                clearContainersRegistrationBonusBackground();
                binding.containerRegistrationBonusThird.setBackground(getResources().getDrawable(R.drawable.register_bonus_bg));
                binding.cbRegistrationBonus.setChecked(false);
                selectedRegisterBonus = registerBonuses.get(2);
            }
        });
        binding.cbRegistrationBonus.setOnClickListener(v -> {
            if ( binding.cbRegistrationBonus.isChecked() ) {
                clearContainersRegistrationBonusBackground();
                selectedRegisterBonus = null;
            } else {
                clearContainersRegistrationBonusBackground();
                binding.containerRegistrationBonusFirst.setBackground(getResources().getDrawable(R.drawable.register_bonus_bg));
                selectedRegisterBonus = registerBonuses.get(0);
            }
        });

        binding.viewSocialLogin.setListener(socialNetwork -> {
            Activity activity = getActivity();
            if ( activity != null ) {
                switch ( socialNetwork ) {
                    case VKONTAKTE:
                        VK.login(activity, Arrays.asList(VKScope.EMAIL, VKScope.OFFLINE));
                        break;
                    case MAILRU:
                        new MailRuNetwork().showAuthWebView(activity,
                                token -> ((MainActivity) getActivity()).sendSocialTokenToL4P(token, SocialNetwork.MAILRU));
                        break;
                    case ODNOKLASSNIKI:
                        new OkRuNetwork().showAuthWebView(activity,
                                token -> ((MainActivity) getActivity()).sendSocialTokenToL4P(token, SocialNetwork.ODNOKLASSNIKI));
                        break;
                    case YANDEX:
                        new YandexNetwork().auth(activity);
                        break;
                    case FACEBOOK:
                        LoginManager.getInstance().logInWithReadPermissions(activity, Arrays.asList("email"));
                        break;
                }
            }
        });
    }

    private void clearContainersRegistrationBonusBackground() {
        binding.containerRegistrationBonusFirst.setBackground(null);
        binding.containerRegistrationBonusSecond.setBackground(null);
        binding.containerRegistrationBonusThird.setBackground(null);
    }

    private void proceedEnter() {
        if ( isLogin ) {
            goLogin();
        } else {
            goRegister();
        }
    }

    private void goRegister() {
        if ( !isVisible() ) {
            return;
        }

        Locale locale = GeneralTools.getLocale(requireContext());

        // TODO: 2019-12-21 make target -> controller
        viewModel.register(binding.etEnterLogin.getText(),
                binding.etEnterPassword.getText(), locale.getLanguage(), getSelectedRegistrationBonusId(),
                Adjust.getAdid(),
                new JsonDataGenerator().getPushSubscriptionAuthDataJson(BuildConfig.APPLICATION_ID,
                        Settings.get().getPushToken(), GeneralTools.getAndroidId(getContext())),
                authTarget);
    }

    private void goLogin() {
        Context context = getContext();
        if ( context != null ) {
            if ( TextUtils.isEmpty(decryptedPassword) ) {
                viewModel.login(binding.etEnterLogin.getText(), binding.etEnterPassword.getText(),
                        Adjust.getAdid(),
                        new JsonDataGenerator().getPushSubscriptionAuthDataJson(BuildConfig.APPLICATION_ID,
                                Settings.get().getPushToken(), GeneralTools.getAndroidId(context)),
                        authTarget);
            } else {
                viewModel.login(biometricCredentials.getLogin(), decryptedPassword,
                        Adjust.getAdid(),
                        new JsonDataGenerator().getPushSubscriptionAuthDataJson(BuildConfig.APPLICATION_ID,
                                Settings.get().getPushToken(), GeneralTools.getAndroidId(context)),
                        authTarget);
            }
        } else {
            Log.e(TAG, "Context is null. Can't login");
        }
    }

    private final GenericTarget<AuthResponse> authTarget = new GenericTarget<AuthResponse>() {
        @Override
        public void onSuccess(@org.jetbrains.annotations.Nullable AuthResponse authResponse) {
            if ( isAdded() ) {
                getActivity().runOnUiThread(() -> processAuthentication(authResponse.getUserId(),
                        authResponse.getEmail(), false));
            }

            LoginType loginType = TextUtils.isEmpty(decryptedPassword) ? LoginType.EMAIL : LoginType.BIOMETRICS;
            String loginDataJson = new JsonDataGenerator().getLoginDataJson(loginType.getValue(),
                    authResponse.getUserId());
            OkHttpProcessor.INSTANCE.sendAnalytics(AApp.Companion.getContext(), AnalyticsEvent.LOGIN_APP,
                    loginDataJson, null);
        }

        @Override
        public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
            if ( isResumed() ) {
                getActivity().runOnUiThread(() -> {
                    if ( fieldsErrors != null ) {
                        Map<Fields, List<String>> errorsMap = fieldsErrors.getErrorsMap();
                        if ( errorsMap.containsKey(Fields.EMAIL) || errorsMap.containsKey(Fields.PLAIN_PASSWORD) ) {
                            binding.getRoot().post(() -> {
                                binding.etEnterLogin.showErrorIfNeeded(fieldsErrors, Fields.EMAIL);
                                binding.etEnterPassword.showErrorIfNeeded(fieldsErrors, Fields.PLAIN_PASSWORD);
                            });
                        }

                        if ( errorsMap.containsKey(Fields.LOGIN) || errorsMap.containsKey(Fields.PASSWORD) ) {
                            binding.etEnterLogin.showErrorIfNeeded(fieldsErrors, Fields.LOGIN);
                            binding.etEnterPassword.showErrorIfNeeded(fieldsErrors, Fields.PASSWORD);
                        }

                        if ( errorsMap.containsKey(Fields.RECAPTCHA) || errorsMap.containsKey(Fields.CAPTCHA) ) {
                            Toast.makeText(getContext(), errorMessage, Toast.LENGTH_LONG).show();
                        }

                        if ( errorsMap.containsKey(Fields.LIMIT_REQUESTS) ) {
                            List<String> limitRequestErrors = errorsMap.get(Fields.LIMIT_REQUESTS);
                            if ( limitRequestErrors != null && !limitRequestErrors.isEmpty() ) {
                                Toast.makeText(getContext(), limitRequestErrors.get(0), Toast.LENGTH_LONG).show();
                            }
                        }
                    } else {
                        Toast.makeText(getContext(), errorMessage, Toast.LENGTH_LONG).show();
                    }
                });
            }
        }

        @Override
        public void onFailure(ApolloException e) {
            if ( isResumed() ) {
                binding.getRoot().post(() ->
                        Toast.makeText(getContext(), "Error: " + e.getLocalizedMessage(),
                                Toast.LENGTH_SHORT).show());
                ((MainActivity) getActivity()).showConnectionIssueMessage(e);
            }
        }
    };

    public void setupMode(boolean isLoginMode, @NonNull Context context, boolean animated) {
        this.isLoginMode = isLoginMode;
        if ( animated ) {
            TransitionManager.beginDelayedTransition(binding.clRoot);
        }

        if ( isLoginMode ) {
            binding.tvEnterLoginTitle.setVisibility(View.VISIBLE);
            binding.tvEnterRegistrationTitle.setVisibility(View.GONE);
            binding.btnEnterGo.setText(R.string.to_login);
            binding.tvEnterForgetPass.setVisibility(View.VISIBLE);
            binding.vPadding.setVisibility(View.GONE);
            binding.tvRegistrationRules.setVisibility(View.GONE);
            hideRegistrationBonusViews();

            List<String> socialNetworksForLogin = getSocialNetworksForLogin();
            binding.viewSocialLogin.createAvailableSocialNetworksButtons(socialNetworksForLogin);
        } else {
            binding.tvEnterLoginTitle.setVisibility(View.INVISIBLE);
            binding.tvEnterRegistrationTitle.setVisibility(View.VISIBLE);
            binding.btnEnterGo.setText(R.string.to_register);
            binding.tvEnterForgetPass.setVisibility(View.GONE);
            binding.vPadding.setVisibility(View.VISIBLE);
            binding.tvRegistrationRules.setVisibility(View.VISIBLE);
            if ( !registerBonuses.isEmpty() ) {
                showRegistrationBonusViews();
            }

            List<String> socialNetworksForRegistration = getSocialNetworksForRegistration();
            binding.viewSocialLogin.createAvailableSocialNetworksButtons(socialNetworksForRegistration);
        }
        binding.etEnterLogin.hideErrorMessage();
        binding.etEnterPassword.hideErrorMessage();
        initBiometric();
    }

    private List<String> getSocialNetworksForLogin() {
        if ( viewModel.getSocialNetworksLiveData().getValue() != null ) {
            AvailableSocialNetworksResponse availableSocialNetworksResponse =
                    viewModel.getSocialNetworksLiveData().getValue().getData();
            if ( availableSocialNetworksResponse != null ) {
                return availableSocialNetworksResponse.getSocialNetworksForLogin();
            }
        }
        return null;
    }

    private List<String> getSocialNetworksForRegistration() {
        if ( viewModel.getSocialNetworksLiveData().getValue() != null ) {
            AvailableSocialNetworksResponse availableSocialNetworksResponse =
                    viewModel.getSocialNetworksLiveData().getValue().getData();
            if ( availableSocialNetworksResponse != null ) {
                return availableSocialNetworksResponse.getSocialNetworksForRegistration();
            }
        }
        return null;
    }

    public void processAuthentication(String userId, String email, boolean socialAuth) {
        Settings.get().setLoggedUserEmail(email);
        Settings.get().setUserId(userId);
        Settings.get().setSocialAuth(socialAuth);
        Activity activity = getActivity();
        if ( isResumed() && activity != null ) {
            GeneralTools.hideKeyboard(activity);
            FirebaseCrashlytics.getInstance().setUserId(userId);
            FirebaseAnalytics.getInstance(activity).setUserId(userId);
            ((MainActivity) activity).getInitialViewerData();
            ((MainActivity) activity).getUnvisitedCount();
            ((MainActivity) activity).balanceSubscribe();
            ((MainActivity) activity).loyaltyPointsSubscribe();
            ((MainActivity) activity).loyaltyProgressSubscribe();
            ((MainActivity) activity).loyaltyStatusSubscribe();
            ((MainActivity) activity).loyaltyXOnPointsSubscribe();
            ((MainActivity) activity).realTimeMessagesSubscribe();
            ((MainActivity) activity).messagesSubscribe();
            ((MainActivity) activity).onlineUsersSubscribe();
            ((MainActivity) activity).promotionsCountSubscribe();
            ((MainActivity) activity).tournamentsCountSubscribe();
            ((MainActivity) activity).lotteriesCountSubscribe();

            if ( !TextUtils.isEmpty(deepLinkUrl) ) {
                ((MainActivity) activity).openHomeScreen();
                ((MainActivity) activity).processDeepLinkUrl(deepLinkUrl, UrlSource.CRM_PUSH);
            } else if ( Screen.TOURNAMENT == openScreen ) {
                activity.onBackPressed();
                ((MainActivity) activity).showTournamentInternal(tournamentItem, true);
            } else if (Screen.PREGAME == openScreen) {
                activity.onBackPressed();
                ((MainActivity) activity).showPregame(true);
            } else if ( lottery != null ) {
                activity.onBackPressed();
                ((MainActivity) activity).showLotteryInternal(lottery);
            } else {
                invokeBackPress(activity);
            }

            if ( Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !socialAuth ) {
                suggestEnableBiometric();
            }

            ((MainActivity) activity).requestNotificationPermission();
        }
    }

    public void invokeBackPress(Activity activity) {
        binding.getRoot().post(activity::onBackPressed);
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    private void suggestEnableBiometric() {
        long lastBiometricSuggestDateTime = Settings.get().getLastBiometricSuggestDateTime();
        if ( lastBiometricSuggestDateTime == 0 ||
                DateTools.getDifferenceDays(new Date().getTime(), lastBiometricSuggestDateTime) >= 7 ||
                (!isLogin && Settings.get().getBiometricCredentials() == null) ) {
            int authenticationStatus = BiometricUtil.INSTANCE.canAuthenticate(AApp.Companion.getContext());
            if ( BiometricManager.BIOMETRIC_SUCCESS == authenticationStatus) {
                Settings.get().setLastBiometricSuggestDateTime(new Date().getTime());
                showBiometricPromptToEncrypt();
            }
        }
    }

    private void setupRegistrationRulesView() {
        String text = getString(R.string.registration_rules);
        Spannable spannable = new SpannableString(text);

        spannable.setSpan(new ForegroundColorSpan(Color.BLUE), RULES_VIEW_PREFIX_LENGTH, text.length(),
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        binding.tvRegistrationRules.setText(spannable, TextView.BufferType.SPANNABLE);
    }

    private int getSelectedRegistrationBonusId() {
        if ( selectedRegisterBonus != null ) {
            return selectedRegisterBonus.getId();
        } else {
            return 0;
        }
    }

    private void showRegistrationBonusViews() {
        binding.tvChooseRegistrationBonusTitle.setVisibility(View.VISIBLE);
        binding.containerRegistrationBonusFirst.setVisibility(View.VISIBLE);
        binding.containerRegistrationBonusSecond.setVisibility(View.VISIBLE);
        binding.containerRegistrationBonusThird.setVisibility(View.VISIBLE);
        binding.cbRegistrationBonus.setVisibility(View.VISIBLE);
        setRegisterTitleConstraintsForVisibleBonuses();
    }

    private void hideRegistrationBonusViews() {
        binding.tvChooseRegistrationBonusTitle.setVisibility(View.GONE);
        binding.containerRegistrationBonusFirst.setVisibility(View.GONE);
        binding.containerRegistrationBonusSecond.setVisibility(View.GONE);
        binding.containerRegistrationBonusThird.setVisibility(View.GONE);
        binding.cbRegistrationBonus.setVisibility(View.GONE);
    }

    private void setRegisterTitleConstraintsForVisibleBonuses() {
        ConstraintSet constraintSet = new ConstraintSet();
        constraintSet.clone(binding.clRoot);
        constraintSet.connect(R.id.tv_enter_registration_title, ConstraintSet.BOTTOM,
                ConstraintSet.UNSET, ConstraintSet.BOTTOM, 0);
        constraintSet.connect(R.id.tv_enter_registration_title, ConstraintSet.TOP,
                R.id.cb_registration_bonus, ConstraintSet.BOTTOM,
                getResources().getDimensionPixelSize(R.dimen.register_title_bonuses_visible_top_margin));
        constraintSet.applyTo(binding.clRoot);
    }

    private void setRegistrationBonusesIntoViews() {
        for ( int bonusIndex = 0; bonusIndex < registerBonuses.size(); bonusIndex++ ) {
            if ( registerBonuses.get(bonusIndex) != null ) {
                switch ( bonusIndex ) {
                    case 0:
                        setRegistrationBonusIntoViews(registerBonuses.get(bonusIndex),
                                binding.ivRegistrationBonusFirst, binding.tvRegistrationBonusFirst,
                                binding.containerRegistrationBonusFirst);
                        break;
                    case 1:
                        setRegistrationBonusIntoViews(registerBonuses.get(bonusIndex),
                                binding.ivRegistrationBonusSecond, binding.tvRegistrationBonusSecond,
                                binding.containerRegistrationBonusSecond);
                        break;
                    case 2:
                        setRegistrationBonusIntoViews(registerBonuses.get(bonusIndex),
                                binding.ivRegistrationBonusThird, binding.tvRegistrationBonusThird,
                                binding.containerRegistrationBonusThird);
                        break;
                }
            }
        }
    }

    private void setRegistrationBonusIntoViews(GetRegisterBonusesQuery.RegisterBonuse registrationBonus,
                                               ImageView ivRegistrationBonus, TextView tvRegistrationBonus,
                                               LinearLayout containerRegistrationBonus) {
        if ( registrationBonus.getPopupRegistrationChoiceImage() != null ) {
            Picasso.get().load(ApoloConfig.getFullUrl(registrationBonus.getPopupRegistrationChoiceImage()))
                    .into(ivRegistrationBonus);
        }
        tvRegistrationBonus.setText(registrationBonus.getPopupRegistrationChoiceAbout());
        if ( registrationBonus.getPopupRegistrationChoiceIsDefault() != null &&
                registrationBonus.getPopupRegistrationChoiceIsDefault() ) {
            containerRegistrationBonus.setBackground(getResources().getDrawable(R.drawable.register_bonus_bg));
            selectedRegisterBonus = registrationBonus;
        }
    }

    private GetRegisterBonusesTarget getRegisterBonusesTarget = new GetRegisterBonusesTarget() {
        @Override
        public void onSuccess(@NotNull List<GetRegisterBonusesQuery.RegisterBonuse> registerBonuses) {

            binding.getRoot().post(() -> {
                if ( !registerBonuses.isEmpty() ) {
                    EnterFragment.this.registerBonuses = registerBonuses;
                    if ( !isLoginMode ) {
                        showRegistrationBonusViews();
                    }
                    setRegistrationBonusesIntoViews();
                }
            });
        }

        @Override
        public void onFailure(ApolloException e) {
            Log.w(TAG, "Register bonuses failure: " + e.getMessage());
            if ( (getActivity()) != null ) {
                ((MainActivity) getActivity()).showConnectionIssueMessage(e);
            }
        }
    };

    @RequiresApi(Build.VERSION_CODES.M)
    public void showBiometricPromptToDecrypt() {
        BiometricUtil.INSTANCE.showBiometricPrompt(getString(R.string.biometric_enter_title), "",
                getString(R.string.biometric_enter_description),
                getString(R.string.biometric_enter_negative_button), (AppCompatActivity) getActivity(),
                biometricAuthDecryptListener, cryptoObject);
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private void showBiometricPromptToEncrypt() {
        Cipher cipherForEncryption = CryptographyUtil.INSTANCE.getInitializedCipherForEncryption();
        BiometricPrompt.CryptoObject encryptCryptoObject = new BiometricPrompt.CryptoObject(cipherForEncryption);
        BiometricUtil.INSTANCE.showBiometricPrompt(getString(R.string.biometric_password_title),
                "", getString(R.string.biometric_password_description),
                getString(R.string.biometric_password_negative_button), (AppCompatActivity) getActivity(),
                biometricAuthEncryptListener, encryptCryptoObject);
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private final BiometricAuthListener biometricAuthDecryptListener = new BiometricAuthListener() {
        @Override
        public void onBiometricAuthenticationSuccess(@NotNull BiometricPrompt.AuthenticationResult result) {
            decryptAndLogin(result.getCryptoObject().getCipher());
        }

        @Override
        public void onBiometricAuthenticationError(int errorCode, @NotNull String errorMessage) {
            Log.d(TAG, "Biometric error: " + errorMessage);
        }
    };

    @RequiresApi(Build.VERSION_CODES.M)
    private final BiometricAuthListener biometricAuthEncryptListener = new BiometricAuthListener() {
        @Override
        public void onBiometricAuthenticationSuccess(@NotNull BiometricPrompt.AuthenticationResult result) {
            if ( result.getCryptoObject() != null ) {
                Cipher cipher = result.getCryptoObject().getCipher();
                encryptAndSave(binding.etEnterPassword.getText(), cipher);
            }
        }

        @Override
        public void onBiometricAuthenticationError(int errorCode, @NotNull String errorMessage) {
            Log.d(TAG, "Biometric error: " + errorMessage);
        }
    };

    @RequiresApi(Build.VERSION_CODES.M)
    private void decryptAndLogin(Cipher cipher) {
        byte[] cipherText        = biometricCredentials.getEncryptedPassword().getCipherText();
        decryptedPassword = CryptographyUtil.INSTANCE.decryptData(cipherText, cipher);
        proceedEnter();
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private void encryptAndSave(String plainTextMessage, Cipher cipher) {
        EncryptedMessage encryptedPassword = CryptographyUtil.INSTANCE.encryptData(plainTextMessage, cipher);
        String email = Settings.get().getLoggedUserEmail();
        Settings.get().setBiometricCredentials(new BiometricCredentials(email, encryptedPassword));

        Context context = AApp.Companion.getContext();
        if ( context != null ) {
            String biometryAuthDataJson = new JsonDataGenerator().getBiometryAuthDataJson(
                    true, Settings.get().getUserId());
            OkHttpProcessor.INSTANCE.sendAnalytics(context, AnalyticsEvent.BIOMETRY_AUTH,
                    biometryAuthDataJson, null);
        }
    }

    public void showSocialNetworkLoader(SocialNetwork socialNetwork) {
        binding.viewSocialLogin.showSocialNetworkLoader(socialNetwork);
    }

    public void hideSocialNetworkLoader(SocialNetwork socialNetwork) {
        binding.viewSocialLogin.hideSocialNetworkLoader(socialNetwork);
    }
}
