package com.abrand.custom.ui.supportChat

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.view.animation.AnimationUtils
import android.webkit.JavascriptInterface
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import com.abrand.custom.BuildConfig
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.databinding.FragmentSupportChatBinding
import com.abrand.custom.ui.activitymain.MainActivity
import java.util.*
import kotlin.concurrent.timerTask

class SupportChatFragment : Fragment(R.layout.fragment_support_chat) {
    private lateinit var supportChatConfiguration: SupportChatConfiguration
    private val viewModel: SupportChatViewModel by viewModels()
    private var countAttemptsOpenChat = 0
    private var isChatOpened = false
    private var timer: Timer? = null
    private lateinit var supportChatHostUrl: String
    private var gameUrl: String? = null
    private var fullGameUrl: String? = null
    private var firstMessage: String? = null
    private var binding: FragmentSupportChatBinding? = null

    private val supportChatJavascriptInterface: SupportChatJavascriptInterface by lazy {
        SupportChatJavascriptInterface(object : SupportChatJavascriptInterface.ChatJSEventListener {
            override fun onReceiveChatEventFromJs(event: String?) {
                activity?.runOnUiThread {
                    val supportChatEvent =
                            SupportChatJSEvent.values().firstOrNull { it.eventName == event }
                    when (supportChatEvent) {
                        SupportChatJSEvent.INIT -> {
                            onChatInitialized()
                        }
                        SupportChatJSEvent.WINDOW_OPENED -> {
                            onWindowChatOpened()
                        }
                        SupportChatJSEvent.WINDOW_CLOSED -> {
                            onWindowChatClosed()
                        }
                        else -> {
                            // TODO: decide what should u do in another cases
                        }
                    }
                }
            }
        })
    }

    private var filePathUploadCallback: ValueCallback<Array<Uri>>? = null
    private val chatWebChromeClient: WebChromeClient by lazy {
        object : WebChromeClient() {

            override fun onShowFileChooser(webView: WebView?, filePathCallback: ValueCallback<Array<Uri>>?, fileChooserParams: FileChooserParams?): Boolean {
                filePathUploadCallback = filePathCallback
                if (fileChooserParams != null) {
                    try {
                        startActivityForResult(fileChooserParams.createIntent(), REQUEST_CODE_IMAGE_CHOOSER_AUTO_INTENT)
                    } catch (e: ActivityNotFoundException) {
                        showFileChooser()
                    }
                } else {
                    showFileChooser()
                }

                return true
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentSupportChatBinding.bind(view)
        detectBackPress(view)
        binding?.apply {
            swipeRefreshLayout.isEnabled = false
            progressLogoVisibility(true)
            linkWebView.settings.apply {
                loadsImagesAutomatically = true
                useWideViewPort = true
                loadWithOverviewMode = true
                setSupportZoom(true)
                builtInZoomControls = true
                displayZoomControls = false
                javaScriptEnabled = true
                domStorageEnabled = true
            }
            linkWebView.webChromeClient = chatWebChromeClient
            linkWebView.addJavascriptInterface(
                supportChatJavascriptInterface,
                SupportChatJavascriptInterface.TAG
            )
        }

        viewModel.getSupportChatConfig()

        observeViewModel()

        gameUrl = arguments?.getString(MainActivity.GAME_URL)
        fullGameUrl = arguments?.getString(MainActivity.FULL_GAME_URL)
        firstMessage = arguments?.getString(MainActivity.MESSAGE_TEXT)
    }

    override fun onStart() {
        super.onStart()
        (activity as? MainActivity)?.hideToolbar()
    }

    private fun detectBackPress(view: View) {
        view.isFocusableInTouchMode = true
        view.requestFocus()
        view.setOnKeyListener { v, keyCode, event ->
            if (event.action == KeyEvent.ACTION_UP && keyCode == KeyEvent.KEYCODE_BACK) {
                if (!gameUrl.isNullOrEmpty()) {
                    (activity as MainActivity).openGameActivity(gameUrl, fullGameUrl, 0)
                }
            }
            false
        }
    }

    override fun onDestroyView() {
        (activity as MainActivity).showToolbar()
        binding?.linkWebView?.removeJavascriptInterface(SupportChatJavascriptInterface.TAG)
        super.onDestroyView()
        binding = null
    }

    private fun observeViewModel() {
        viewModel.supportConfigLiveData.observe(viewLifecycleOwner, Observer {

            val userId = if (!TextUtils.isEmpty(Settings.get().userId)) {
                Settings.get().userId
            } else {
                "0"
            }
            supportChatHostUrl = getHostUrlBuilder(it.host).toString()

            supportChatConfiguration = SupportChatConfiguration(
                    it.widgetId.toString(),
                    it.signature,
                    userId,
                    SupportChatType.STANDART)

            activity?.runOnUiThread {
                binding?.linkWebView?.loadDataWithBaseURL(
                        supportChatHostUrl,
                        viewModel.buildSupportChatContentHtml(supportChatConfiguration, supportChatHostUrl),
                        "text/html", "utf-8", null
                )
            }
        })

        viewModel.serverErrorLiveData.observe(viewLifecycleOwner, Observer {
            Toast.makeText(context, it.errorMessage, Toast.LENGTH_LONG).show()
            activity?.onBackPressed()
        })

        viewModel.apolloExceptionLiveData.observe(viewLifecycleOwner, Observer {
            (activity as MainActivity).showConnectionIssueMessage(it)
        })
    }

    fun getHostUrlBuilder(authority: String): Uri.Builder = Uri.Builder()
            .scheme("https")
            .authority(authority)

    fun onChatInitialized() {
        if (!isChatOpened) {
            timer?.cancel()
            timer = Timer()
            val timerTask = timerTask {
                activity?.runOnUiThread {
                    when {
                        isChatOpened -> {
                            timer?.cancel()
                        }
                        countAttemptsOpenChat < MAX_ATTEMPTS_OPEN_CHAT -> {
                            openChatJS(supportChatConfiguration.chatType)
                        }
                        else -> {
                            <EMAIL>?.also {
                                Toast.makeText(
                                        it,
                                        it.getString(R.string.error_support_chat_connection_failed),
                                        Toast.LENGTH_LONG
                                ).show()
                            }
                            activity?.onBackPressed()
                        }
                    }
                }
            }
            timer?.schedule(timerTask, 0L, 1000) //TODO: Why one second? [by Reaver]
        }
    }

    fun onWindowChatOpened() {
        progressLogoVisibility(false)
        isChatOpened = true
    }

    fun onWindowChatClosed() {
        if (!gameUrl.isNullOrEmpty()) {
            (activity as MainActivity).openGameActivity(gameUrl, fullGameUrl, 0)
        }
        activity?.onBackPressed()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            REQUEST_CODE_IMAGE_CHOOSER_AUTO_INTENT -> {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                    filePathUploadCallback?.onReceiveValue(
                            WebChromeClient.FileChooserParams.parseResult(
                                    resultCode,
                                    data
                            )
                    )
                }
                filePathUploadCallback = null
            }
            REQUEST_CODE_IMAGE_CHOOSER -> {
                if (filePathUploadCallback == null)
                    return

                val resultData = data?.data?.takeIf { resultCode == Activity.RESULT_OK }
                filePathUploadCallback?.onReceiveValue(resultData?.let {
                    arrayOf(it)
                })
                filePathUploadCallback = null
            }
        }
    }

    private fun showFileChooser() {
        val i = Intent(Intent.ACTION_GET_CONTENT).apply {
            type = "image/*"
            addCategory(Intent.CATEGORY_OPENABLE)
        }

        startActivityForResult(
                Intent.createChooser(i, "Select Picture"),
                REQUEST_CODE_IMAGE_CHOOSER
        )
    }

    private fun openChatJS(chatType: SupportChatType) {
        countAttemptsOpenChat++
        val chatTypeCommand = when (chatType) {
            SupportChatType.STANDART -> {
                if (firstMessage.isNullOrEmpty()) {
                    "window.chat.show();"
                } else {
                    "window.chat.openChat({customMessage: '$firstMessage'});"
                }
            }
            SupportChatType.REGISTRATION -> "window.chat.openRegistrationChat();"
        }
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT) {
            binding?.linkWebView?.evaluateJavascript("javascript:(function() {$chatTypeCommand})();", null)
        }
    }

    private fun progressLogoVisibility(isVisible: Boolean) {
        if (isVisible) {
            showLoader()
        } else {
            hideLoader()
        }
    }

    private fun showLoader() {
        binding?.apply {
            lPreloader.root.visibility = View.VISIBLE
            val animation = AnimationUtils.loadAnimation(context, R.anim.rotation_loader)
            lPreloader.ivLoader.startAnimation(animation)
        }
    }

    private fun hideLoader() {
        binding?.apply {
            lPreloader.root.visibility = View.GONE
            lPreloader.ivLoader.clearAnimation()
        }
    }

    companion object {
        private const val MAX_ATTEMPTS_OPEN_CHAT = 5
        private const val REQUEST_CODE_IMAGE_CHOOSER_AUTO_INTENT = 501
        private const val REQUEST_CODE_IMAGE_CHOOSER = 502
    }
}

data class SupportChatConfiguration(
        val widgetId: String,
        val signature: String,
        val userId: String,
        val chatType: SupportChatType
) {
    val isProd: Boolean = (!BuildConfig.FLAVOR.contains("ApkTesting"))
}

enum class SupportChatType {
    STANDART, REGISTRATION
}

class SupportChatJavascriptInterface(private val chatEventListener: ChatJSEventListener) {

    companion object {
        const val TAG = "SupportChatJavascriptInterface"
    }

    @JavascriptInterface
    fun receiveChatEventFromJs(event: String?) {
        chatEventListener.onReceiveChatEventFromJs(event)
    }

    interface ChatJSEventListener {
        fun onReceiveChatEventFromJs(event: String?)
    }

}

enum class SupportChatJSEvent(val eventName: String) {
    INIT("init"),
    WINDOW_OPENED("windowOpened"),
    WINDOW_CLOSED("windowClosed"),
    UNDEFINED("undefined")
}
