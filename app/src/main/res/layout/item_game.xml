<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_item_game_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:scaleType="center"
        android:src="@drawable/ic_preload" />

    <TextView
        android:id="@+id/tv_item_game_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:gravity="center"
        android:minHeight="40dp"
        android:textColor="@color/white"
        android:textFontWeight="400"
        android:textSize="12sp" />

</LinearLayout>
