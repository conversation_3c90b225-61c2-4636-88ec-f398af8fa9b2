package com.abrand.custom.presenter

import androidx.recyclerview.widget.DiffUtil
import com.abrand.custom.data.entity.TournamentsItem


class TournamentsItemDiffCallback(private val oldList: List<TournamentsItem>,
                                  private val newList: List<TournamentsItem>) : DiffUtil.Callback() {

    override fun getOldListSize() =  oldList.size

    override fun getNewListSize() = newList.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList[oldItemPosition] == newList[newItemPosition]
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Bo<PERSON><PERSON> {
        return oldList[oldItemPosition] == newList[newItemPosition]
    }
}