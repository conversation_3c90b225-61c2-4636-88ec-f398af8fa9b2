#import "./fragments.graphql"

query getLotteries($offset: Int,
    $limitPagination: PaginationAmount) {
    lotteries (
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        offset: $offset
        limitPagination: $limitPagination
    )
    {
        totalCount
        items {
            ...on StandardLottery {
                ...FragmentLotteriesItem
            }
        }
    }
}

query getLottery($id: Int!,$isAutorized: Boolean!) {
    lottery (
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        id: $id
    )
    {
        ...on StandardLottery {
            ...FragmentFullLotteryItem
        }

    }
}

mutation buyLotteryTickets($lotteryId: Int!,
    $ticketsCount: Int!) {
    buyLotteryTickets(lotteryId: $lotteryId,
        ticketsCount: $ticketsCount)
    {
        id,
        isGold
    }
}

mutation buyLotteryTicketsPackage($lotteryId: Int!,
    $ticketsCount: Int!) {
    buyLotteryTicketsPackage(lotteryId: $lotteryId,
        ticketsCount: $ticketsCount)
    {
        id,
        isGold
    }
}

mutation getLotteryPrize($winnerId: Int!) {
    getLotteryPrize(winnerId: $winnerId)
    {
        id
    }
}

fragment FragmentFullLotteryItem on StandardLottery {
    id
    name
    status
    prizesSum
    prizeFundByString
    startAt
    finishAt
    bannerMobile
    textMobile
    redemptionType
    ticketPrice @include(if: $isAutorized)
    prizes {
        place
        sum
    }
    winners {
        id
        distributed
        user {
            id
            formattedUserName
        }
        ticketId
        isGold
        prize {
            place
            sum
            prize
        }
    }
    userTicketsBatch @include(if: $isAutorized) {
        totalCount
        items {
            id
            isGold
        }
    }
    ticketsPackageSettings @include(if: $isAutorized) {
        ticketCount
        discount
        priceWithDiscount
        fullPrice
    }
}
