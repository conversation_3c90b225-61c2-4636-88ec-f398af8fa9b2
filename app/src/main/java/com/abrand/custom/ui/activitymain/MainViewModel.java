package com.abrand.custom.ui.activitymain;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModel;

import com.abrand.custom.GetInitialViewerDataQuery;
import com.abrand.custom.GetFastClickPaymentSystemQuery;
import com.abrand.custom.SubscribeBalanceSubscription;
import com.abrand.custom.SubscribeLoyaltyPointsSubscription;
import com.abrand.custom.SubscribeLoyaltyProgressSubscription;
import com.abrand.custom.SubscribeLoyaltyStatusSubscription;
import com.abrand.custom.SubscribeLoyaltyXOnPointsSubscription;
import com.abrand.custom.SubscribeMessagesSubscription;
import com.abrand.custom.SubscribeRealTimeNotificationSubscription;
import com.abrand.custom.data.Resource;
import com.abrand.custom.data.Settings;
import com.abrand.custom.data.entity.ServerError;
import com.abrand.custom.data.entity.User;
import com.abrand.custom.data.entity.UserBalance;
import com.abrand.custom.interfaces.GenericTarget;
import com.abrand.custom.network.ApolloProcessorKt;
import com.abrand.custom.network.ProfileRepository;
import com.abrand.custom.presenter.FieldsErrorsHolder;
import com.abrand.custom.tools.SynchronizedLiveData;
import com.apollographql.apollo3.exception.ApolloException;

import org.jetbrains.annotations.Nullable;

public class MainViewModel extends ViewModel {

    private       ProfileRepository                                               profileRepository;
    private final MutableLiveData<Boolean>                                        logoutResponseLiveData = new MutableLiveData<>();
    private final LiveData<GetInitialViewerDataQuery.Viewer>                      initialViewerDataQueryLiveData;
    private final LiveData<ServerError>                                           serverErrorLiveData;
    private final LiveData<ApolloException>                                       apolloExceptionLiveData;
    private final MutableLiveData<SubscribeBalanceSubscription.Data>              balanceLiveData = null;
    private final MutableLiveData<UserBalance>                             userBalance           = new MutableLiveData<>();
    private final MutableLiveData<SubscribeLoyaltyPointsSubscription.Data> loyaltyPointsLiveData = new MutableLiveData<>();
    private final MutableLiveData<SubscribeLoyaltyProgressSubscription.Data>      loyaltyProgressLiveData      = new MutableLiveData<>();
    private final MutableLiveData<SubscribeLoyaltyStatusSubscription.Data>        loyaltyStatusLiveData        = new MutableLiveData<>();
    private final MutableLiveData<SubscribeLoyaltyXOnPointsSubscription.Data>     loyaltyXOnPointsLiveData     = new MutableLiveData<>();
    private final SynchronizedLiveData<SubscribeRealTimeNotificationSubscription.Data> realTimeNotificationLiveData = new SynchronizedLiveData<>();
    private final SynchronizedLiveData<SubscribeMessagesSubscription.Data>             messagesLiveData = new SynchronizedLiveData<>();
    private final LiveData<Boolean>                                               viewerEmptyLiveData;
    private final MutableLiveData<Resource<String, ServerError, ApolloException>> makeRebillLiveData;
    private final LiveData<String>                                                fastPaymentUrlLiveData;
    private final LiveData<GetFastClickPaymentSystemQuery.FastClickPaymentSystem> fastClickPaymentSystemLiveData;
    private final MutableLiveData<Boolean>                                        fastPaymentRebillingLiveData    = new MutableLiveData<>();
    private final MutableLiveData<Resource<String, ServerError, ApolloException>>                      checkPasswordResetTokenLiveData = new MutableLiveData();
    private       boolean                                                                              isRebilling                     = false;

    public MainViewModel() {
        profileRepository = new ProfileRepository();
        serverErrorLiveData = profileRepository.getServerErrorLiveData();
        apolloExceptionLiveData = profileRepository.getApolloExceptionLiveData();
        initialViewerDataQueryLiveData = profileRepository.getInitialViewerDataLiveData();
        viewerEmptyLiveData = profileRepository.getViewerEmptyLiveData();
        makeRebillLiveData = profileRepository.getMakeRebillLiveData();
        fastPaymentUrlLiveData = profileRepository.getFastPaymentUrlLiveData();
        fastClickPaymentSystemLiveData = profileRepository.getFastClickPaymentSystemLiveData();
        initSubscribers();
    }

    LiveData<Boolean> getLogoutResponseLiveData() {
        return logoutResponseLiveData;
    }

    LiveData<ServerError> getServerErrorLiveData() {
        return serverErrorLiveData;
    }

    public LiveData<GetInitialViewerDataQuery.Viewer> getInitialViewerDataQueryLiveData() {
        return initialViewerDataQueryLiveData;
    }

    public MutableLiveData<SubscribeBalanceSubscription.Data> getBalanceLiveData() {
        return balanceLiveData;
    }

    public MutableLiveData<UserBalance> getUserBalance() {
        return userBalance;
    }

    public void resetBalanceLiveData() {
        balanceLiveData.postValue(null);
        userBalance.postValue(null);
    }

    public MutableLiveData<SubscribeLoyaltyPointsSubscription.Data> getLoyaltyPointsLiveData() {
        return loyaltyPointsLiveData;
    }

    public MutableLiveData<SubscribeLoyaltyProgressSubscription.Data> getLoyaltyProgressLiveData() {
        return loyaltyProgressLiveData;
    }

    public MutableLiveData<SubscribeLoyaltyStatusSubscription.Data> getLoyaltyStatusLiveData() {
        return loyaltyStatusLiveData;
    }

    public MutableLiveData<SubscribeLoyaltyXOnPointsSubscription.Data> getLoyaltyXOnPointsLiveData() {
        return loyaltyXOnPointsLiveData;
    }

    public MutableLiveData<SubscribeRealTimeNotificationSubscription.Data> getRealTimeNotificationLiveData() {
        return realTimeNotificationLiveData;
    }

    public SynchronizedLiveData<SubscribeMessagesSubscription.Data> getMessagesLiveData() {
        return messagesLiveData;
    }

    LiveData<ApolloException> getApolloExceptionLiveData() {
        return apolloExceptionLiveData;
    }

    public LiveData<Boolean> getViewerEmptyLiveData() {
        return viewerEmptyLiveData;
    }

    public MutableLiveData<Resource<String, ServerError, ApolloException>> getMakeRebillLiveData() {
        return makeRebillLiveData;
    }

    public LiveData<String> getFastPaymentUrlLiveData() {
        return fastPaymentUrlLiveData;
    }

    public MutableLiveData<Boolean> getFastPaymentRebillingLiveData() {
        return fastPaymentRebillingLiveData;
    }

    public LiveData<GetFastClickPaymentSystemQuery.FastClickPaymentSystem> getFastClickPaymentSystemLiveData() {
        return fastClickPaymentSystemLiveData;
    }

    public MutableLiveData<Resource<String, ServerError, ApolloException>> getCheckPasswordResetTokenLiveData() {
        return checkPasswordResetTokenLiveData;
    }

//    public MutableLiveData<Resource<AuthResponse, ServerError, ApolloException>> getSocialAuthLiveData() {
//        return socialAuthLiveData;
//    }

    void getInitialViewerData() {
        User.State userState = Settings.get().getUserState();
        if ( userState.equals(User.State.PLAYER) ) {
            profileRepository.getInitialViewerData();
        }
    }

    void logout() {
        profileRepository.logout();
    }

    private void initSubscribers() {
        profileRepository.getLogoutResponseLiveData().observeForever(new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                logoutResponseLiveData.postValue(aBoolean);
            }
        });
    }

    /**
     *
     * @return return balance from last request, but if subscription balance is not null then here is actual balance
     */
    private Double getViewerBalance() {
        GetInitialViewerDataQuery.Viewer viewer =
                initialViewerDataQueryLiveData.getValue();
        if ( viewer != null && viewer.getWallet() != null && viewer.getWallet().getBalance() != null ) {
            return Double.parseDouble(viewer.getWallet().getBalance().toString());
        } else {
            return null;
        }
    }

    public String getCurrencyCode() {
        GetInitialViewerDataQuery.Viewer viewer =
                initialViewerDataQueryLiveData.getValue();
        if ( viewer != null && viewer.getWallet() != null && viewer.getWallet().getCurrency() != null ) {
            return viewer.getWallet().getCurrency().getCode();
        } else {
            return null;
        }
    }

    void makeRebill(int sum) {
        profileRepository.makeRebill(sum);
        fastPaymentRebillingLiveData.postValue(isRebilling = true);
    }

    void confirmEmail(String confirmationCode) {
        profileRepository.confirmEmail(confirmationCode);
    }

    void getFastPaymentUrl(int sum) {
        profileRepository.getFastPaymentUrl(sum);
    }

    void getFastClickPaymentSystem() {
        profileRepository.getFastClickPaymentSystem();
    }

    void checkPasswordResetToken(String token) {
        ApolloProcessorKt.userCheckPasswordResetToken(token, new GenericTarget<String>() {
            @Override
            public void onSuccess(@Nullable String token) {
                checkPasswordResetTokenLiveData.postValue(Resource.Companion.success(token));
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                checkPasswordResetTokenLiveData.postValue(
                        Resource.Companion.error(new ServerError(errorMessage, errorCode, fieldsErrors)));
            }

            @Override
            public void onFailure(ApolloException e) {
                checkPasswordResetTokenLiveData.postValue(Resource.Companion.failure(e));
            }
        });
    }

    void consumeLogoutEvent() {
        logoutResponseLiveData.postValue(null);
    }
}
