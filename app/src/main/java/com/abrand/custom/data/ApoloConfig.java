package com.abrand.custom.data;


import android.text.TextUtils;

import com.abrand.custom.BuildConfig;
import com.abrand.custom.tools.Crypt;

import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okio.Buffer;

public class ApoloConfig {
    public static final String ENDPOINT       = Crypt.decrypt(BuildConfig.ENDPOINT);
    public static final String WWS_ENDPOINT   = Crypt.decrypt(BuildConfig.WWS_ENDPOINT);
    public static final String BASE_URL       = Crypt.decrypt(BuildConfig.BASE_URL);
    public static final String HOST           = Crypt.decrypt(BuildConfig.HOST);
    public static final String RECAPTCHA_HOST = Crypt.decrypt(BuildConfig.RECAPTCHA_HOST);

    public static String getFullUrl(String url) {
        if ( url.startsWith("/") ) {
            url = url.substring(1);
        }
        return BASE_URL + url;
    }

    public static Interceptor getFixPersistentInterceptor() {
        return new Interceptor() {
            @NotNull
            @Override
            public Response intercept(@NotNull Chain chain) throws IOException {
                Request request     = chain.request();
                String  operationId = chain.request().headers().get("X-APOLLO-OPERATION-ID");

                if ( TextUtils.isEmpty(operationId) ) {
                    return chain.proceed(request);
                }

                Request.Builder requestBuilder = request.newBuilder();
                RequestBody     requestBody    = request.body();

                try {
                    if ( requestBody != null
                            && requestBody.contentType() != null ) {
                        //noinspection ConstantConditions
                        if ( requestBody.contentType().subtype().contains("json") ) {
                            requestBody = processApplicationJsonRequestBody(requestBody, operationId);

                            if ( requestBody != null ) {
                                request = requestBuilder.post(requestBody).build();
                            }
                        }
                    }
                } catch ( NullPointerException e ) {
                    //do nothing
                }

                return chain.proceed(request);
            }
        };
    }

    private static RequestBody processApplicationJsonRequestBody(RequestBody requestBody,
                                                                 String token) {
        String customReq = bodyToString(requestBody);

        try {
            JSONObject obj = new JSONObject(customReq);

            obj.put("id", token);

            return RequestBody.create(obj.toString(), requestBody.contentType());
        } catch ( JSONException e) {
            e.printStackTrace();
        }

        return null;
    }

    private static String bodyToString(final RequestBody request) {
        try {
            final RequestBody copy   = request;
            final Buffer      buffer = new Buffer();

            if ( copy != null ) {
                copy.writeTo(buffer);
            } else {
                return "";
            }

            return buffer.readUtf8();
        } catch (final IOException e) {
            return "did not work";
        }
    }
}
