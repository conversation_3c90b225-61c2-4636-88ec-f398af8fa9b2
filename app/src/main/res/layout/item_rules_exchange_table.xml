<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="20dp">

    <TextView
        android:id="@+id/tv_status_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/user_status"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_exchange_arrows"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_exchange_arrows"
        app:layout_constraintBottom_toBottomOf="@+id/tv_status_title"
        app:layout_constraintEnd_toEndOf="@+id/tv_status_1_exchange_rate"
        app:layout_constraintStart_toStartOf="@+id/tv_status_1_exchange_rate"
        app:layout_constraintTop_toTopOf="@+id/tv_status_title" />

    <TextView
        android:id="@+id/tv_bonus_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="24dp"
        android:text="@string/user_bonus"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="12sp"
        app:layout_constraintStart_toEndOf="@+id/iv_exchange_arrows"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_status_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/user_status_1"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_title" />

    <TextView
        android:id="@+id/tv_status_1_points"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/user_status_1_points"
        android:textColor="#fff"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_1" />

    <TextView
        android:id="@+id/tv_status_1_exchange_rate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:text="@string/user_status_1_exchange_rate"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/tv_status_6_points"
        app:layout_constraintTop_toTopOf="@+id/tv_status_1" />

    <TextView
        android:id="@+id/tv_status_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/user_status_2"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_1_points" />

    <TextView
        android:id="@+id/tv_status_2_points"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/user_status_2_points"
        android:textColor="#fff"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_2" />

    <TextView
        android:id="@+id/tv_status_2_exchange_rate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:text="@string/user_status_2_exchange_rate"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/tv_status_6_points"
        app:layout_constraintTop_toTopOf="@+id/tv_status_2" />

    <TextView
        android:id="@+id/tv_status_3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/user_status_3"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_2_points" />

    <TextView
        android:id="@+id/tv_status_3_points"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/user_status_3_points"
        android:textColor="#fff"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_3" />

    <TextView
        android:id="@+id/tv_status_3_exchange_rate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:text="@string/user_status_3_exchange_rate"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/tv_status_6_points"
        app:layout_constraintTop_toTopOf="@+id/tv_status_3" />

    <TextView
        android:id="@+id/tv_status_3_bonus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/user_status_3_bonus"
        android:textColor="#fff"
        app:layout_constraintStart_toStartOf="@+id/tv_bonus_title"
        app:layout_constraintTop_toTopOf="@+id/tv_status_3" />

    <TextView
        android:id="@+id/tv_status_4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/user_status_4"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_3_points" />

    <TextView
        android:id="@+id/tv_status_4_points"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/user_status_4_points"
        android:textColor="#fff"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_4" />

    <TextView
        android:id="@+id/tv_status_4_exchange_rate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:text="@string/user_status_4_exchange_rate"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/tv_status_6_points"
        app:layout_constraintTop_toTopOf="@+id/tv_status_4" />

    <TextView
        android:id="@+id/tv_status_4_bonus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/user_status_4_bonus"
        android:textColor="#fff"
        app:layout_constraintStart_toStartOf="@+id/tv_bonus_title"
        app:layout_constraintTop_toTopOf="@+id/tv_status_4" />

    <TextView
        android:id="@+id/tv_status_5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/user_status_5"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_4_points" />

    <TextView
        android:id="@+id/tv_status_5_points"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/user_status_5_points"
        android:textColor="#fff"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_5" />

    <TextView
        android:id="@+id/tv_status_5_exchange_rate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:text="@string/user_status_5_exchange_rate"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/tv_status_6_points"
        app:layout_constraintTop_toTopOf="@+id/tv_status_5" />

    <TextView
        android:id="@+id/tv_status_5_bonus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/user_status_5_bonus"
        android:textColor="#fff"
        app:layout_constraintStart_toStartOf="@+id/tv_bonus_title"
        app:layout_constraintTop_toTopOf="@+id/tv_status_5" />

    <TextView
        android:id="@+id/tv_status_6"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/user_status_6"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_5_points" />

    <TextView
        android:id="@+id/tv_status_6_points"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/user_status_6_points"
        android:textColor="#fff"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_6" />

    <TextView
        android:id="@+id/tv_status_6_exchange_rate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:text="@string/user_status_6_exchange_rate"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/tv_status_6_points"
        app:layout_constraintTop_toTopOf="@+id/tv_status_6" />

    <TextView
        android:id="@+id/tv_status_6_bonus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/user_status_6_bonus"
        android:textColor="#fff"
        app:layout_constraintStart_toStartOf="@+id/tv_bonus_title"
        app:layout_constraintTop_toTopOf="@+id/tv_status_6" />

    <TextView
        android:id="@+id/tv_status_7"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/user_status_7"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_6_points" />

    <TextView
        android:id="@+id/tv_status_7_points"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/user_status_7_points"
        android:textColor="#fff"
        android:textSize="10sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status_7" />

    <TextView
        android:id="@+id/tv_status_7_exchange_rate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16dp"
        android:text="@string/user_status_7_exchange_rate"
        android:textAllCaps="true"
        android:textColor="#fff"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/tv_status_6_points"
        app:layout_constraintTop_toTopOf="@+id/tv_status_7" />

    <TextView
        android:id="@+id/tv_status_7_bonus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/user_status_7_bonus"
        android:textColor="#fff"
        app:layout_constraintStart_toStartOf="@+id/tv_bonus_title"
        app:layout_constraintTop_toTopOf="@+id/tv_status_7" />

</androidx.constraintlayout.widget.ConstraintLayout>
