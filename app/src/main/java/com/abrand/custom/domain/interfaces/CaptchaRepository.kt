package com.abrand.custom.domain.interfaces

import com.abrand.custom.data.entity.ResponseException
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.domain.CaptchaType

interface CaptchaRepository {
    suspend fun getRecaptcha(type: CaptchaType, appId: String): ReCaptchaResult

    data class CaptchaStatus(
        val enabled: <PERSON><PERSON>an,
        val provider: Captcha<PERSON>rovider,
        val captchaId: String? // Don't need it?
    )

    sealed interface ReCaptchaResult {
        data class Success(val captchaStatus: CaptchaStatus) : ReCaptchaResult
        data class ResponseError(
            val serverError: ServerError,
        ) : ReCaptchaResult

        data class NetworkError(val e: ResponseException) : ReCaptchaResult
    }

    enum class CaptchaProvider(val rawValue: String) {
        GOOGLE("GOOGLE"),
        GOOGLE_ENTERPRISE("GOOGLE_ENTERPRISE"),
        CLOUDFLARE("CLOUDFLARE"),
        UNKNOW<PERSON>__("UNKNOWN__"),
    }
}
