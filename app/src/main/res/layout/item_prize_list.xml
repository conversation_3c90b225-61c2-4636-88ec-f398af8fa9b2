<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/parent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_number"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        android:layout_alignTop="@+id/tv_fund"
        android:layout_alignBottom="@+id/tv_fund"
        android:layout_alignParentLeft="true"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="3dp"
        android:background="@drawable/ic_gold"
        android:gravity="center_vertical"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:text="1"
        android:textColor="#ffffff"
        android:textSize="10sp" />

    <TextView
        android:id="@+id/tv_fund"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp"
        android:foregroundGravity="right"
        android:gravity="right"
        android:text="2000000"
        android:textColor="#FFFFFF"
        android:textSize="16sp" />

    <ImageView
        android:id="@+id/space"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="8dp"
        android:layout_below="@+id/tv_fund"></ImageView>

</RelativeLayout>
