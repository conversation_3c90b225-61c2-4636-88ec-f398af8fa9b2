package com.abrand.custom.ui.views

import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import com.abrand.custom.R
import com.abrand.custom.data.entity.WheelSector
import com.abrand.custom.databinding.ViewSectorsBinding
import com.abrand.custom.tools.dpToPx
import kotlin.math.cos
import kotlin.math.sin

class SectorsView : ConstraintLayout {
    private var binding: ViewSectorsBinding? = null
    private var sectors: List<WheelSector>? = null

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {
        binding = ViewSectorsBinding.inflate(LayoutInflater.from(context), this, true)
    }

    fun addSectors(sectors: List<WheelSector>) {
        this.sectors = sectors
        addPrizes()
    }

    private fun addPrizes() {
        var angle = -67.5
        for (index in 0..7) {
            addPrize(index, angle)
            angle += 45
        }
    }

    private fun addPrize(index: Int, angle: Double) {
        addPrizeImage(index, angle)
        addPrizeTitle(index, angle)
    }

    private fun addPrizeImage(index: Int, angle: Double) {
        val distance = 88
        val xPos = distance * cos (angle * Math.PI / 180)
        val yPos = distance * sin (angle * Math.PI / 180)

        val prizeIv = ImageView(context)
        val width = resources.getDimensionPixelSize(R.dimen.wheel_prize_width)
        val height = resources.getDimensionPixelSize(R.dimen.wheel_prize_height)
        prizeIv.layoutParams = ViewGroup.LayoutParams(width, height)

        prizeIv.setImageDrawable(sectors?.get(index)?.type?.icon?.let {
            ContextCompat.getDrawable(context, it) })
        prizeIv.rotation = (angle + 90).toFloat()
        prizeIv.translationX = context.dpToPx(xPos.toInt()).toFloat()
        prizeIv.translationY = context.dpToPx(yPos.toInt()).toFloat()

        addViewToCenter(prizeIv)
    }

    private fun addPrizeTitle(index: Int, angle: Double) {
        val distance = 64
        val xPos = distance * cos (angle * Math.PI / 180)
        val yPos = distance * sin (angle * Math.PI / 180)

        val titleTv = TextView(context)
        titleTv.text = sectors?.get(index)?.title
        titleTv.isAllCaps = true
        titleTv.setTextSize(
            TypedValue.COMPLEX_UNIT_PX,
            context.resources.getDimension(R.dimen.wheel_prize_text_size))

        titleTv.rotation = (angle + 90).toFloat()
        titleTv.translationX = context.dpToPx(xPos.toInt()).toFloat()
        titleTv.translationY = context.dpToPx(yPos.toInt()).toFloat()

        addViewToCenter(titleTv)
    }

    private fun addViewToCenter(view: View) {
        val set = ConstraintSet()
        view.id = View.generateViewId()
        binding?.rootContainer?.addView(view)

        set.clone(binding?.rootContainer)
        binding?.rootContainer?.id?.let {
            set.connect(view.id, ConstraintSet.START, it, ConstraintSet.START, 0)
            set.connect(view.id, ConstraintSet.END, it, ConstraintSet.END, 0)
            set.connect(view.id, ConstraintSet.TOP, it, ConstraintSet.TOP, 0)
            set.connect(view.id, ConstraintSet.BOTTOM, it, ConstraintSet.BOTTOM, 0)
        }
        set.applyTo(binding?.rootContainer)
    }
}
