<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8dp"
    android:orientation="horizontal">

    <ImageView
        android:id="@+id/iv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:paddingEnd="@dimen/size_16"
        android:paddingRight="@dimen/size_16"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="@+id/tv_status"
        app:layout_constraintTop_toTopOf="parent" />

</LinearLayout>
