package com.abrand.custom.ui.home;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.Html;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.abrand.custom.R;
import com.abrand.custom.data.ApoloConfig;
import com.abrand.custom.data.entity.Banner;
import com.abrand.custom.tools.GeneralTools;
import com.abrand.custom.ui.interfaces.SlideClickListener;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Target;

import java.util.ArrayList;
import java.util.List;

public class BannerPagerAdapter extends RecyclerView.Adapter<BannerPagerAdapter.PagerVH> {
    private Context      context;
    private List<Banner> banners = new ArrayList<>();
    private SlideClickListener clickListener;

    public BannerPagerAdapter(Context context, SlideClickListener clickListener) {
        this.context = context;
        this.clickListener = clickListener;
    }

    public void updateBanners(List<Banner> banners) {
        this.banners = banners;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public PagerVH onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.view_banner, parent, false);
        return new PagerVH(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PagerVH holder, int position) {
        if ( banners.size() > 0 ) {
            // TODO: Warning multiple get() call on collection, use cashing
            String bannerImageUrl = ApoloConfig.getFullUrl(banners.get(position % banners.size()).getImageUrl());

            holder.setBannerText(banners.get(position % banners.size()).getTextHtml());
           Picasso.get()
                    .load(bannerImageUrl)
                    .resize(GeneralTools.getScreenWidth(context), context.getResources().getDimensionPixelSize(R.dimen.home_banner_height))
                    .centerCrop()
                    .into(holder.loadAdTarget);
            if ( clickListener != null ) {
                String url = banners.get(position % banners.size()).getUrl();

                holder.itemView.setOnClickListener(v -> clickListener.onSlideClick(url));
            }
        }
    }

    @Override
    public int getItemCount() {
        return banners.size() > 1 ? Integer.MAX_VALUE : 1;
    }

    @Override
    public long getItemId(int position) {
        return position % banners.size();
    }

    static class PagerVH extends RecyclerView.ViewHolder {
        public ImageView ivBanner;
        public TextView  tvBanner;

        public PagerVH(@NonNull View itemView) {
            super(itemView);
            ivBanner = itemView.findViewById(R.id.iv_banner);
            tvBanner = itemView.findViewById(R.id.tv_banner);
        }

        void setBannerText(String bannerTextHtml) {
            if ( TextUtils.isEmpty(bannerTextHtml) ) {
                tvBanner.setVisibility(View.INVISIBLE);
                return;
            } else {
                tvBanner.setVisibility(View.VISIBLE);
            }

            String htmlSpanClassText = "span class=\"text-";
            if ( bannerTextHtml.contains(htmlSpanClassText) ) {
                bannerTextHtml = bannerTextHtml
                        .replace(htmlSpanClassText, "font color=\"")
                        .replace("</span>", "</font>");
            }
            bannerTextHtml = bannerTextHtml
                    .replace("<p>", "")
                    .replace("</p>", "");

            if ( Build.VERSION.SDK_INT >= Build.VERSION_CODES.N ) {
                tvBanner.setText(Html.fromHtml(bannerTextHtml, Html.FROM_HTML_MODE_COMPACT));
            } else {
                tvBanner.setText(Html.fromHtml(bannerTextHtml));
            }
        }

        public Target loadAdTarget = new Target() {
            @Override
            public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
                ivBanner.setImageBitmap(bitmap);
            }

            @Override
            public void onBitmapFailed(Exception e, Drawable errorDrawable) {
                ivBanner.setImageResource(0);
            }

            @Override
            public void onPrepareLoad(Drawable placeHolderDrawable) {
                //do nothing
            }
        };
    }
}
