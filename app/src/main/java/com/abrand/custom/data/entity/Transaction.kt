package com.abrand.custom.data.entity

import com.abrand.custom.type.TransactionDirectionType
import com.abrand.custom.type.TransactionOperationType
import com.abrand.custom.type.TransactionStatus

class Transaction(var baseTransaction: LocalBaseTransaction? = null,
                  var children: MutableList<LocalBaseTransaction>? = mutableListOf()) {

    data class LocalBaseTransaction(
        val id: Int?,
        val direction: TransactionDirectionType?,
        val amount: Any?,
        val currency: Currency?,
        val date: Any?,
        val status: TransactionStatus?,
        val operationType: TransactionOperationType?,
        val paymentSystemName: String?,
        val userComment: String?,
        val rejectionEnable: Boolean?
    ) {
        data class Currency(val code: String?)
    }
}
