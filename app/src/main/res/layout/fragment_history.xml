<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transactions_bg">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvHistory"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false" />

    <LinearLayout
        android:id="@+id/ll_history_empty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/ic_wallet_blue" />

        <TextView
            android:id="@+id/tvPaymentHistoryEmpty"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="@dimen/size_16"
            android:gravity="center_horizontal"
            android:text="@string/payment_history_empty"
            android:textColor="#8DA1BD"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btn_replenish_account"
            style="@style/ButtonStyle"
            android:layout_width="220dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="32dp"
            android:text="@string/replenish_account" />

    </LinearLayout>

    <ImageView
        android:id="@+id/ivLoader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:src="@drawable/ic_preload"
        android:visibility="gone" />

</FrameLayout>
