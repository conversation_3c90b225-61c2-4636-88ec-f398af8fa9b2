package com.abrand.custom.ui.cashback

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.data.Constants
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException

class CashbackViewModel : ViewModel() {

    private val _cashbackPageLiveData = MutableLiveData<Resource<String, ServerError, ApolloException>>()
    val cashbackPageLiveData: LiveData<Resource<String, ServerError, ApolloException>> = _cashbackPageLiveData

    init {
        getCashbackPage()
    }

    private fun getCashbackPage() {
        ApolloProcessorKt.getPage(Constants.DEEP_LINK_CASHBACK, object : GenericTarget<String> {
            override fun onSuccess(t: String?) {
                _cashbackPageLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _cashbackPageLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _cashbackPageLiveData.postValue(Resource.failure(e))
            }

        })
    }
}
