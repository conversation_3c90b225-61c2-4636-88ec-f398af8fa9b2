package com.abrand.custom.data

import android.content.Context
import com.abrand.custom.R
import com.abrand.custom.data.entity.Bonus
import com.abrand.custom.data.entity.PrizeInfo
import com.abrand.custom.fragment.FragmentBonusPrize
import com.abrand.custom.tools.GeneralTools

class PrizeUtil {

    fun getPrizesInfo(context: Context, bonus: Bonus): ArrayList<PrizeInfo> {
        val prizesInfo = ArrayList<PrizeInfo>()

        val prizes = bonus.prizes
        for (prize in prizes) {
            prize.asBonusMoneyPrize?.let {
                prizesInfo.addAll(
                    getBonusMoneyPrize(
                        context,
                        bonus.isCalculatedByDepositSum,
                        it
                    )
                )
            }
            prize.asBonusFreeSpinsPrize?.let {
                prizesInfo.addAll(getBonusFreeSpinsPrize(context, it))
            }
            prize.asBonusGiftSpinsPrize?.let {
                prizesInfo.addAll(getBonusGiftSpinsPrize(context, it))
            }
            prize.asBonusPointsPrize?.let {
                prizesInfo.addAll(getBonusPointsPrize(context, it))
            }
            prize.asBonusX2Prize?.let {
                prizesInfo.addAll(getBonusX2Prize(context, it))
            }
            prize.asBonusX3Prize?.let {
                prizesInfo.addAll(getBonusX3Prize(context, it))
            }
            prize.asBonusLotteryTicketsPrize?.let {
                prizesInfo.addAll(getBonusLotteryTicketsPrize(context, it))
            }
            prize.asBonusWheelFortuneSpinPrize?.let {
                prizesInfo.addAll(getBonusWheelFortuneSpinPrize(context, it))
            }
            prize.asBonusTalismanPrize?.let {
                prizesInfo.addAll(getBonusTalismanPrize(context, it))
            }
//            when (prize) {
//                is FragmentBonusPrize.AsBonusMoneyPrize -> {
//                    prizesInfo.addAll(getBonusMoneyPrize(context, promotion.isCalculatedByDepositSum, prize))
//                }
//                is FragmentBonusPrize.AsBonusFreeSpinsPrize -> {
//                    prizesInfo.addAll(getBonusFreeSpinsPrize(context, prize))
//                }
//                is FragmentBonusPrize.AsBonusGiftSpinsPrize -> {
//                    prizesInfo.addAll(getBonusGiftSpinsPrize(context, prize))
//                }
//                is FragmentBonusPrize.AsBonusPointsPrize -> {
//                    prizesInfo.addAll(getBonusPointsPrize(context, prize))
//                }
//                is FragmentBonusPrize.AsBonusX2Prize -> {
//                    prizesInfo.addAll(getBonusX2Prize(context, prize))
//                }
//                is FragmentBonusPrize.AsBonusX3Prize -> {
//                    prizesInfo.addAll(getBonusX3Prize(context, prize))
//                }
//                is FragmentBonusPrize.AsBonusLotteryTicketsPrize -> {
//                    prizesInfo.addAll(getBonusLotteryTicketsPrize(context, prize))
//                }
//                is FragmentBonusPrize.AsBonusWheelFortuneSpinPrize -> {
//                    prizesInfo.addAll(getBonusWheelFortuneSpinPrize(context, prize))
//                }
//                is FragmentBonusPrize.AsBonusTalismanPrize -> {
//                    prizesInfo.addAll(getBonusTalismanPrize(context, prize))
//                }
//            }
        }
        return prizesInfo
    }

    private fun getBonusMoneyPrize(context: Context, isCalculatedByDepositSum: Boolean?, prize: FragmentBonusPrize.AsBonusMoneyPrize): ArrayList<PrizeInfo> {
        val prizesInfo = ArrayList<PrizeInfo>()

        val value = if (prize.sum.toString().toDouble() > 0) {
            GeneralTools.formatBalance(Settings.get().userCurrencyCode, prize.sum.toString().toDouble())
        } else {
            "${prize.percent} %"
        }
        if (isCalculatedByDepositSum != null && isCalculatedByDepositSum) {
            prizesInfo.add(PrizeInfo(context.getString(R.string.bonus_on_amount_deposits), value))
        } else {
            prizesInfo.add(PrizeInfo(context.getString(R.string.bonus_on_deposit), value))
        }

        if (prize.wager > 0) {
            prizesInfo.add(PrizeInfo(context.getString(R.string.wager_on_amount_bonus), prize.wager.toString()))
        }

        if (prize.maxSum != null && prize.maxSum.toString().toDouble() > 0) {
            val value = GeneralTools.formatBalance(Settings.get().userCurrencyCode, prize.maxSum.toString().toDouble())
            prizesInfo.add(PrizeInfo(context.getString(R.string.max_bonus_sum_for_all_prizes), value))
        }

        if (prize.maxBonusSum.toString().toDouble() > 0) {
            val value = GeneralTools.formatBalance(Settings.get().userCurrencyCode, prize.maxBonusSum.toString().toDouble())
            prizesInfo.add(PrizeInfo(context.getString(R.string.max_bonus_amount), value))
        }

        return prizesInfo;
    }

    private fun getBonusFreeSpinsPrize(context: Context, prize: FragmentBonusPrize.AsBonusFreeSpinsPrize): ArrayList<PrizeInfo> {
        val prizesInfo = ArrayList<PrizeInfo>()
        prizesInfo.add(PrizeInfo(context.getString(R.string.free_spins), prize.count.toString()))

        if (prize.days > 1) {
            val numberOfTimes = prize.days
            val numberOfSpins = Math.floor(prize.count.toDouble() / prize.days)
            val value = formatSpins(numberOfTimes, numberOfSpins)
            prizesInfo.add(PrizeInfo(context.getString(R.string.free_spins_calculation), value))
        }

        if (prize.games.isNotEmpty()) {
            val gameList = prize.games.replace(",", ", ")
            prizesInfo.add(PrizeInfo(context.getString(R.string.free_spins_in_games), gameList))
        }

        if (prize.wager > 0) {
            prizesInfo.add(PrizeInfo(context.getString(R.string.wager_on_free_spins), prize.wager.toString()))
        }

        return prizesInfo
    }

    private fun getBonusGiftSpinsPrize(context: Context, prize: FragmentBonusPrize.AsBonusGiftSpinsPrize): ArrayList<PrizeInfo> {
        val prizesInfo = ArrayList<PrizeInfo>()
        prizesInfo.add(PrizeInfo(context.getString(R.string.free_spins), prize.count.toString()))

        if (prize.days > 1) {
            val numberOfTimes = prize.days
            val numberOfSpins = Math.floor(prize.count.toDouble() / prize.days)
            val value = formatSpins(numberOfTimes, numberOfSpins)
            prizesInfo.add(PrizeInfo(context.getString(R.string.free_spins_calculation), value))
        }

        if (prize.games.isNotEmpty()) {
            val gameList = prize.games.replace(",", ", ")
            prizesInfo.add(PrizeInfo(context.getString(R.string.free_spins_in_games), gameList))
        }

        if (prize.wager > 0) {
            prizesInfo.add(PrizeInfo(context.getString(R.string.wager_on_free_spins), prize.wager.toString()))
        }

        return prizesInfo
    }

    private fun formatSpins(numberOfTimes: Int, numberOfSpins: Double): String {
        return "$numberOfTimes ${getSpinsAddition(numberOfTimes)}  ${numberOfSpins.toInt()}"
    }

    private fun getSpinsAddition(count: Int): String? {
        return when (count) {
            2, 3, 4, 22, 23, 24 -> "раза по"
            else -> "раз по"
        }
    }

    private fun getBonusPointsPrize(context: Context, prize: FragmentBonusPrize.AsBonusPointsPrize): ArrayList<PrizeInfo> {
        val prizesInfo = ArrayList<PrizeInfo>()
        prizesInfo.add(PrizeInfo(context.getString(R.string.bonus_points), prize.score.toString()))
        return prizesInfo;
    }

    private fun getBonusX2Prize(context: Context, prize: FragmentBonusPrize.AsBonusX2Prize): ArrayList<PrizeInfo> {
        val prizesInfo = ArrayList<PrizeInfo>()
        val value = prize.minutes.toString() + " " + getMinutesAddition(prize.minutes.toString().toInt())
        prizesInfo.add(PrizeInfo(context.getString(R.string.x2_points_on), value))
        return prizesInfo;
    }

    private fun getBonusX3Prize(context: Context, prize: FragmentBonusPrize.AsBonusX3Prize): ArrayList<PrizeInfo> {
        val prizesInfo = ArrayList<PrizeInfo>()
        val value = prize.minutes.toString() + " " + getMinutesAddition(prize.minutes.toString().toInt())
        prizesInfo.add(PrizeInfo(context.getString(R.string.x3_points_on), value))
        return prizesInfo;
    }

    private fun getMinutesAddition(minutes: Int): String {
        return if (minutes in 11..19) {
            "минут"
        } else {
            when (minutes % 10) {
                1 -> "минуту"
                2, 3, 4 -> "минуты"
                else -> "минут"
            }
        }
    }

    private fun getBonusLotteryTicketsPrize(context: Context, prize: FragmentBonusPrize.AsBonusLotteryTicketsPrize): ArrayList<PrizeInfo> {
        val prizesInfo = ArrayList<PrizeInfo>()
        if (prize.count == 1) {
            prizesInfo.add(PrizeInfo(context.getString(R.string.lottery_ticket), prize.count.toString()))
        } else {
            prizesInfo.add(PrizeInfo(context.getString(R.string.lottery_tickets), prize.count.toString()))
        }
        return prizesInfo;
    }

    private fun getBonusWheelFortuneSpinPrize(context: Context, prize: FragmentBonusPrize.AsBonusWheelFortuneSpinPrize): ArrayList<PrizeInfo> {
        val prizesInfo = ArrayList<PrizeInfo>()
        prizesInfo.add(PrizeInfo(context.getString(R.string.wheel_rotation_fortune), prize.count.toString()))
        return prizesInfo;
    }

    private fun getBonusTalismanPrize(context: Context, prize: FragmentBonusPrize.AsBonusTalismanPrize): ArrayList<PrizeInfo> {
        val prizesInfo = ArrayList<PrizeInfo>()
        val value = "${prize.days} ${getDayAddition(prize.days)}"
        prizesInfo.add(PrizeInfo(context.getString(R.string.talisman, prize.name), value))
        return prizesInfo;
    }

    private fun getDayAddition(days: Int): String {
        return if (days in 11..19) {
            "дней"
        } else {
            when (days % 10) {
                1 -> "день"
                2, 3, 4 -> "дня"
                else -> "дней"
            }
        }
    }
}
