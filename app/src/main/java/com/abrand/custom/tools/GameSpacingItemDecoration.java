package com.abrand.custom.tools;

import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

import com.abrand.custom.data.entity.GameListItem;
import com.abrand.custom.data.entity.GameRoomItem;
import com.abrand.custom.data.entity.Screen;

import org.jetbrains.annotations.NotNull;

public class GameSpacingItemDecoration extends RecyclerView.ItemDecoration {
    private int    spanCount;
    private int    startEndMargin;
    private int    leftRightSpacing;
    private int    bottomSpacing;
    private Screen screen;

    public GameSpacingItemDecoration(int spanCount, int startEndMargin, int leftRightSpacing,
                                     int bottomSpacing, Screen screen) {
        this.spanCount = spanCount;
        this.startEndMargin = startEndMargin;
        this.leftRightSpacing = leftRightSpacing;
        this.bottomSpacing = bottomSpacing;
        this.screen = screen;
    }

    @Override
    public void getItemOffsets(Rect outRect, @NotNull View view, RecyclerView parent, @NotNull RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        if ( position >= 0 ) {
            int viewType = parent.getAdapter().getItemViewType(position);

            if ( isGameItem(viewType) ) {
                if ( isLeftItem(screen, position) ) {
                    outRect.left = startEndMargin;
                } else {
                    outRect.left = leftRightSpacing / 2;
                }

                if ( isRightItem(screen, position) ) {
                    outRect.right = startEndMargin;
                } else {
                    outRect.right = leftRightSpacing / 2;
                }

                if ( Screen.HOME.equals(screen) && position <= spanCount ) {
                    outRect.top = startEndMargin;
                }

                outRect.bottom = bottomSpacing;
            }
        }
    }

    private boolean isGameItem(int viewType) {
        return ((Screen.HOME.equals(screen) || Screen.GAME_SEARCH.equals(screen)) && viewType == GameListItem.ItemType.GAME.getId()) ||
                (Screen.GAME_ROOM.equals(screen) && viewType == GameRoomItem.ItemViewType.GAME.getId());
    }

    private boolean isLeftItem(Screen screen, int position) {
        if ( Screen.HOME.equals(screen) || Screen.GAME_SEARCH.equals(screen) ) {
            return position % spanCount == 1;
        } else {
            return position % spanCount == 0;
        }
    }

    private boolean isRightItem(Screen screen, int position) {
        if ( Screen.HOME.equals(screen) || Screen.GAME_SEARCH.equals(screen) ) {
            return position % spanCount == 0;
        } else {
            return position % spanCount == 2;
        }
    }
}
