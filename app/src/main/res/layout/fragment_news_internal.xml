<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_color"
    tools:context=".ui.news.NewsInternalFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/scrollContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/ivBg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:alpha="0.3"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="64dp"
            android:background="@drawable/bg_internal_news_image_bottom"
            app:layout_constraintBottom_toBottomOf="@+id/ivBg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingStart="@dimen/size_16"
            android:paddingLeft="@dimen/size_16"
            android:paddingEnd="@dimen/size_16"
            android:paddingRight="@dimen/size_16"
            android:paddingBottom="@dimen/not_organic_user_footer_padding_bottom">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="100dp"
                android:textColor="#FFFFFF"
                android:textFontWeight="500"
                android:textSize="24sp" />

            <TextView
                android:id="@+id/tvCreatedAt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textAllCaps="true"
                android:textColor="#80FFFFFF"
                android:textFontWeight="500"
                android:textSize="10sp" />

            <WebView
                android:id="@+id/wvContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="50dp"
                android:visibility="gone" />

            <include
                android:id="@+id/l_footer"
                layout="@layout/view_footer"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
