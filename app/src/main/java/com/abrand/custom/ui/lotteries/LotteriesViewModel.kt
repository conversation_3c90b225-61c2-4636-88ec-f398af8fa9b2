package com.abrand.custom.ui.lotteries

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.data.entity.lottery.*
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException

class LotteriesViewModel : ViewModel() {
    private val _lotteriesLiveData = MutableLiveData<Resource<LotteriesResponseFiltered, ServerError, ApolloException>>()
    val lotteriesLiveData: LiveData<Resource<LotteriesResponseFiltered, ServerError, ApolloException>> = _lotteriesLiveData

    companion object {
        const val INIT_AMOUNT = 5
        const val LOAD_STEP = 10
    }

    init {
        getLotteries(0, INIT_AMOUNT)
    }

    fun getLotteries(offset: Int, loadAmount: Int) {
        ApolloProcessorKt.getLotteries(offset, loadAmount, object : GenericTarget<LotteriesResponse> {
            override fun onSuccess(t: LotteriesResponse?) {
                val lotteriesResponseFiltered = t?.let { getFilteredLotteriesResponse(it) }

                if (offset == 0) {
                    _lotteriesLiveData.postValue(Resource.success(lotteriesResponseFiltered))
                } else {
                    val newActiveLotteryItems: MutableList<LotteryItem> = mutableListOf()
                    newActiveLotteryItems.addAll(_lotteriesLiveData.value?.data?.activeLotteryItems ?: arrayListOf())
                    newActiveLotteryItems.addAll(lotteriesResponseFiltered?.activeLotteryItems ?: arrayListOf())

                    val newCompletedLotteryItems: MutableList<LotteryItem> = mutableListOf()
                    newCompletedLotteryItems.addAll(_lotteriesLiveData.value?.data?.completedLotteryItems ?: arrayListOf())
                    newCompletedLotteryItems.addAll(lotteriesResponseFiltered?.completedLotteryItems ?: arrayListOf())

                    val newLotteriesResponseFiltered = LotteriesResponseFiltered(newActiveLotteryItems,
                        newCompletedLotteryItems, t?.total ?: 0, t?.offset ?: 0)
                    _lotteriesLiveData.postValue(Resource.success(newLotteriesResponseFiltered))
                }
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _lotteriesLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _lotteriesLiveData.postValue(Resource.failure(e))
            }
        })
    }

    fun getFilteredLotteriesResponse(lotteriesResponse: LotteriesResponse) : LotteriesResponseFiltered {
        val activeLotteryItems = mutableListOf<LotteryItem>()
        val completedLotteryItems = mutableListOf<LotteryItem>()
        for (item in lotteriesResponse.lotteryList) {
            if (LocalLotteryStatus.IN_PROCESS == item.status) {
                activeLotteryItems.add(LotteryItem(LotteryItem.ItemType.ACTIVE_LOTTERY, item))
            } else if (LocalLotteryStatus.COMPLETED == item.status) {
                completedLotteryItems.add(LotteryItem(LotteryItem.ItemType.COMPLETED_LOTTERY, item))
            }
        }

        return LotteriesResponseFiltered(activeLotteryItems, completedLotteryItems,
            lotteriesResponse.total, lotteriesResponse.offset)
    }
}
