#import "./fragments.graphql"

query getTournamentsByStatus(
    $tournamentStatus: [TournamentStatus]!,
    $offset: Int,
    $limit: PaginationAmount
) {
    tournaments (
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        statusList: $tournamentStatus
        offset: $offset
        limitPagination: $limit
    ) {
        items {
            ... on Tournament {
                ...FragmentTournamentsItem
            }
        },
        totalCount
    }
}

query getTournamentById($id: Int!) {
    tournament (id: $id) {
        ...FragmentFullTournamentsItem
    }
}

mutation joinTournament($tournamentId: Int!) {
    joinTournament(tournamentId: $tournamentId) {
        ...FragmentFullTournamentsItem
    }
}

subscription subscribeTournamentResult($tournamentId: ID!) {
    viewerTournamentResult(tournamentId: $tournamentId){
        ...TournamentViewerProgress
    }
}

fragment FragmentFullTournamentsItem on Tournament {
    id
    title
    dateStart
    dateEnd
    prizeFund
    status
    mobText
    mobBanner
    mobTextContents
    minBetLimit
    qualificationRounds
    isDynamicPrizeFund
    minBaseLoyaltyStatus {
        id
    }
    maxBaseLoyaltyStatus {
        id
    }
    participants{
        ...TournamentParticipant
    }
    winners{
        ...TournamentParticipant
    }
    prizes{
        ...TournamentPrize
    }
    gameList {
        ...GameItem
    }
    viewerProgress{
        ...TournamentViewerProgress
    }
}

fragment TournamentPrize on TournamentPrize{
    place_number
    sum
}

fragment TournamentParticipant on TournamentParticipant{
    user{
        ...User
    }
    place
    score
}

fragment User on User{
    id
    formattedUserName
}

fragment TournamentViewerProgress on TournamentViewerProgress{
    place
    score
    totalBets
}


