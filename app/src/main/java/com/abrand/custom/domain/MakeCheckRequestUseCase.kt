package com.abrand.custom.domain

import android.util.Log
import com.abrand.custom.data.entity.appconfig.LocalConfig
import com.abrand.custom.data.repositories.GeneratorRepositoryImpl
import com.abrand.custom.tools.UrlEncoder
import java.util.Random

class MakeCheckRequestUseCase(
    private val smenGiRepository: GeneratorRepository = GeneratorRepositoryImpl(),
    private val encoder: UrlEncoder = UrlEncoder(),
) {
    suspend operator fun invoke(appConfig: LocalConfig): LocalConfig {
        val customerToken = appConfig.customerToken ?: ""
        val result = smenGiRepository.check(
            urlParams = constructUrlParams(customerToken, appConfig.token),
            domains = appConfig.domains
        )
        Log.d("MakeCheckRequestUseCase", "result: $result") // TODO: remove when use result info
        return appConfig
    }

    private fun constructUrlParams(userToken: String, appToken: String): String {
        return encoder.encode(
            encoder.encodeParam("c", (Random().nextInt(80 - 65) + 65).toString()),
            encoder.encodeParam("f", appToken),
            encoder.encodeParam("g", userToken)
        )
    }
}
