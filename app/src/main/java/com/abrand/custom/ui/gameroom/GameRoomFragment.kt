package com.abrand.custom.ui.gameroom

import android.graphics.drawable.BitmapDrawable
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.*
import com.abrand.custom.databinding.FragmentGameRoomBinding
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.tools.GameSpacingItemDecoration
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.type.GameListOrderField
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment

class GameRoomFragment : Fragment() {
    private lateinit var adapter: GameRoomAdapter
    private lateinit var viewModel: GameRoomViewModel
    private val COLS_COUNT = 3
    private var TAG = "GameRoomFragment"
    private var selectedGameType = GameRoomAdapter.GameType.SLOT
    private var selectedGameOrder = GameListOrderField.rating
    private var rootView: View? = null // TODO: This is memory leak. Need to be refactored.
    private var binding: FragmentGameRoomBinding? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        if (rootView == null || adapter.getGameCount() == 0) { //TODO: Need to discuss this condition
            binding = FragmentGameRoomBinding.inflate(inflater, container, false)
            rootView = binding?.root

            viewModel = ViewModelProvider(this).get(GameRoomViewModel::class.java)

            val layoutManager = GridLayoutManager(context, COLS_COUNT)
            layoutManager.spanSizeLookup = object : SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (adapter.getItemViewType(position)) {
                        GameRoomItem.ItemViewType.HEADER.id, GameRoomItem.ItemViewType.GAME_LOAD.id,
                        GameRoomItem.ItemViewType.FOOTER.id -> COLS_COUNT
                        else -> 1
                    }
                }
            }

            val rvGamesList = rootView!!.findViewById<RecyclerView>(R.id.rvGamesList)
            rvGamesList.setItemViewCacheSize(30)
            rvGamesList.layoutManager = layoutManager
            adapter = GameRoomAdapter(gameRoomListener, footerListener, GeneralTools.getGameIconSize(activity, Screen.GAME_ROOM))
            adapter.isUserLogged = User.State.PLAYER == Settings.get().userState
            rvGamesList.adapter = adapter

            val gameIconStartEndMargin = requireContext().resources.getDimensionPixelSize(R.dimen.game_icon_start_end_margin)
            val gameIconLeftRightSpacing = requireContext().resources.getDimensionPixelSize(R.dimen.game_icon_left_right_spacing)
            val gameIconBottomSpacing = requireContext().resources.getDimensionPixelSize(R.dimen.game_icon_bottom_spacing)
            rvGamesList.addItemDecoration(GameSpacingItemDecoration(COLS_COUNT, gameIconStartEndMargin,
                gameIconLeftRightSpacing, gameIconBottomSpacing, Screen.GAME_ROOM))

            loadGames(selectedGameType, selectedGameOrder, 0)
        }
        
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupInsets()
        observeViewModel()

        if (User.State.PLAYER == Settings.get().userState) {
            applyLoggedState()
        } else {
            applyNotLoggedState()
        }

        if (GameRoomAdapter.GameType.FAVOURITE == selectedGameType && SessionDataHolder.getInstance().needUpdateFavouriteList) {
            loadGames(selectedGameType, selectedGameOrder, 0)
        }
        SessionDataHolder.getInstance().needUpdateFavouriteList = false
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun observeViewModel() {
        viewModel.gamesLiveData.observe(viewLifecycleOwner, Observer {
            val gameRoomItems: MutableList<GameRoomItem> = mutableListOf()
            for (gameItems in it.gameItems) {
                gameRoomItems.add(GameRoomItem(GameRoomItem.ItemViewType.GAME, gameItems))
            }

            adapter.currentTotalGames = it.total
            if (it.offset == 0) {
                if (it.gameItems.size == 0) {
                    (activity as? MainActivity)?.showMessage(getString(R.string.no_games_here))
                }
                adapter.setList(gameRoomItems)
            } else {
                adapter.appendList(gameRoomItems)
                if (it.gameItems.size == 0) {
                    (activity as? MainActivity)?.showMessage(getString(R.string.no_games_here))
                }
            }
            adapter.setGameLoadingState(false)
        })

        viewModel.gamesLoadApolloExceptionLiveData.observe(viewLifecycleOwner, Observer {
            (activity as MainActivity).showConnectionIssueMessage(it)
            adapter.setGameLoadingState(false)
        })
    }

    private fun setupInsets() {
        binding?.apply {
            ViewCompat.setOnApplyWindowInsetsListener(rvGamesList) { view, insets ->
                view.updatePadding(top = insets.systemWindowInsetTop, bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }

    private val gameRoomListener = object : GameRoomAdapter.GameRoomListener {

        override fun onGameTypeChanged(gameType: GameRoomAdapter.GameType) {
            if (gameType != selectedGameType) {
                selectedGameType = gameType
                loadGames(selectedGameType, selectedGameOrder, 0)
            }
        }

        override fun onGameOrderChanged(gameOrderField: GameListOrderField) {
            selectedGameOrder = gameOrderField
            loadGames(selectedGameType, selectedGameOrder, 0)
        }

        override fun onGameClicked(localGameItem: LocalGameItem, view: View) {
            val dataHolder = SessionDataHolder.getInstance()
            dataHolder.currentGameItem = localGameItem
            val gameIcon = view.findViewById<ImageView>(R.id.iv_item_game_image)
            if (gameIcon.drawable is BitmapDrawable) {
                dataHolder.currentGameImage = (gameIcon.drawable as BitmapDrawable).bitmap
            }
            showPregame(localGameItem, view)
        }

        override fun onLoadMore() {
            loadGames(selectedGameType, selectedGameOrder, adapter.getGameCount())
        }

    }

    private val footerListener = object : FooterListener {
        override fun onClickPaymentSystems() {
            if (User.State.PLAYER == Settings.get().userState) {
                (activity as? MainActivity)?.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
            } else {
                (activity as? MainActivity)?.openEnterScreen(false)
            }
        }
    }

    private fun showPregame(game: LocalGameItem, view: View) {
        if (activity != null) {
            (activity as MainActivity).showPregame(game, view)
        }
    }

    private fun loadGames(type: GameRoomAdapter.GameType, gameOrderField: GameListOrderField, offset: Int) {
        adapter.setGameLoadingState(true)
        if (type == GameRoomAdapter.GameType.SLOT) {
            viewModel.loadSlotGames(offset, gameOrderField)
        } else if (type == GameRoomAdapter.GameType.NEW) {
            viewModel.loadNewGames(offset, gameOrderField)
        } else if (type == GameRoomAdapter.GameType.FAVOURITE) {
            viewModel.loadFavouriteGames(gameOrderField, offset)
        } else if (type == GameRoomAdapter.GameType.TABLES) {
            viewModel.loadTableGames(offset, gameOrderField)
        } else {
            Log.w(TAG, "Load incorrect state: $type")
            adapter.setGameLoadingState(false)
        }
    }

    private fun applyLoggedState() {
        adapter.applyLoggedState()
    }

    fun applyNotLoggedState() {
        adapter.applyNotLoggedState()
    }

}
