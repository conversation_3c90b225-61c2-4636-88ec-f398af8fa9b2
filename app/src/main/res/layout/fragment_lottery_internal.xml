<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootScrollView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient"
    android:clipToPadding="false"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/scrollContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/not_organic_user_footer_padding_bottom">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:background="#000000"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/iv_image"
                android:layout_width="match_parent"
                android:layout_height="251dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="251dp"
                android:background="#cc000000"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="67dp"
                android:layout_marginBottom="@dimen/size_16"
                android:background="@drawable/bg_gradient_black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/tv_days" />

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_16"
                android:layout_marginLeft="@dimen/size_16"
                android:layout_marginEnd="@dimen/size_16"
                android:layout_marginRight="@dimen/size_16"
                android:gravity="start"
                android:paddingTop="71dp"
                android:textColor="@color/white"
                android:textSize="24sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_fund"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="24dp"
                android:textColor="@color/white"
                android:textSize="24sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_name" />

            <TextView
                android:id="@+id/tv_fund_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:alpha="0.6"
                android:text="@string/tournaments_fund"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_fund" />

            <TextView
                android:id="@+id/tv_days"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:textColor="@color/white"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_fund_text" />

            <TextView
                android:id="@+id/tv_days_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:alpha="0.6"
                android:text="@string/lottery_toend"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_days" />

            <Button
                android:id="@+id/btn_header"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginStart="@dimen/size_16"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="@dimen/size_16"
                android:background="@drawable/btn_bg"
                android:text="@string/lottery_get_tickets"
                android:textAllCaps="false"
                android:textSize="16sp"
                android:visibility="visible"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/iv_image" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_tabs_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/size_16"
                android:background="@drawable/categories_bg"
                app:layout_constraintTop_toBottomOf="@id/btn_header">

                <RelativeLayout
                    android:id="@+id/tab_conditions"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    app:layout_constraintEnd_toStartOf="@+id/tab_prizes"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_goneMarginTop="16dp">

                    <ImageView
                        android:id="@+id/iv_conditions"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="20dp"
                        android:src="@drawable/ic_book" />

                    <TextView
                        android:id="@+id/tv_conditions"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_centerHorizontal="true"
                        android:layout_marginBottom="@dimen/size_16"
                        android:text="@string/conditions"
                        android:textColor="@color/white"
                        android:textSize="10sp" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/tab_prizes"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="@+id/tab_conditions"
                    app:layout_constraintEnd_toStartOf="@+id/tab_your_tickets"
                    app:layout_constraintStart_toEndOf="@+id/tab_conditions"
                    app:layout_constraintTop_toTopOf="@+id/tab_conditions">

                    <ImageView
                        android:id="@+id/iv_prizes"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="20dp"
                        android:src="@drawable/ic_prize" />

                    <TextView
                        android:id="@+id/tv_prizes"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_centerHorizontal="true"
                        android:layout_marginBottom="@dimen/size_16"
                        android:text="@string/prizes"
                        android:textColor="@color/white"
                        android:textSize="10sp" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/tab_your_tickets"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="@+id/tab_conditions"
                    app:layout_constraintEnd_toStartOf="@+id/tab_buy_tickets"
                    app:layout_constraintStart_toEndOf="@+id/tab_prizes"
                    app:layout_constraintTop_toTopOf="@+id/tab_conditions">

                    <ImageView
                        android:id="@+id/iv_your_tickets"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="20dp"
                        android:src="@drawable/ic_your_tickets" />

                    <TextView
                        android:id="@+id/tv_your_tickets"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_centerHorizontal="true"
                        android:layout_marginBottom="8dp"
                        android:gravity="center_horizontal"
                        android:text="@string/lottery_your_tickets"
                        android:textColor="@color/white"
                        android:textSize="10sp" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/tab_buy_tickets"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="visible"
                    app:layout_constraintBottom_toBottomOf="@+id/tab_conditions"
                    app:layout_constraintEnd_toStartOf="@+id/tab_ghost1"
                    app:layout_constraintStart_toEndOf="@+id/tab_your_tickets"
                    app:layout_constraintTop_toTopOf="@+id/tab_conditions">

                    <ImageView
                        android:id="@+id/iv_buy_tickets"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_marginTop="20dp"
                        android:src="@drawable/ic_buy_tickets" />

                    <TextView
                        android:id="@+id/tv_buy_tickets"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:layout_centerHorizontal="true"
                        android:layout_marginBottom="8dp"
                        android:gravity="center_horizontal"
                        android:text="@string/lottery_buy_tickets"
                        android:textColor="@color/white"
                        android:textSize="10sp" />

                </RelativeLayout>

                <View
                    android:id="@+id/tab_ghost1"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/tab_conditions"
                    app:layout_constraintLeft_toRightOf="@+id/tab_buy_tickets"
                    app:layout_constraintRight_toLeftOf="@id/tab_ghost2"
                    app:layout_constraintTop_toTopOf="@+id/tab_conditions" />

                <View
                    android:id="@+id/tab_ghost2"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@+id/tab_conditions"
                    app:layout_constraintLeft_toRightOf="@+id/tab_ghost1"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tab_conditions" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <ImageView
                android:id="@+id/iv_loader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/size_16"
                android:src="@drawable/ic_preload"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/cl_tabs_layout" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#000D26"
            android:orientation="horizontal"
            android:paddingTop="24dp"
            app:layout_constraintTop_toBottomOf="@id/header">

            <LinearLayout
                android:id="@+id/body_conditions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingStart="@dimen/size_16"
                android:paddingEnd="@dimen/size_16"
                android:scrollbars="none"
                android:visibility="gone">

                <WebView
                    android:id="@+id/wv_conditions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/body_prizes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="@dimen/size_16"
                android:paddingEnd="@dimen/size_16"
                android:visibility="gone">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_prizes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/body_winners"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:paddingStart="@dimen/size_16"
                    android:paddingEnd="@dimen/size_16"
                    android:paddingBottom="8dp"
                    android:weightSum="100">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="10"
                        android:textFontWeight="10" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="35"
                        android:alpha="0.4"
                        android:text="@string/lottery_player"
                        android:textColor="@color/white" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="25"
                        android:alpha="0.4"
                        android:text="@string/lottery_ticket_column"
                        android:textColor="@color/white" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="30"
                        android:alpha="0.4"
                        android:text="@string/lottery_prize"
                        android:textAlignment="viewEnd"
                        android:textColor="@color/white" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_winners"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:overScrollMode="never" />

            </LinearLayout>

            <include
                android:id="@+id/body_your_tickets"
                layout="@layout/content_your_tickets"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <include
                android:id="@+id/body_buy_tickets"
                layout="@layout/content_buy_tickets"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/bg_gradient_footer"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/body" />

        <include
            android:id="@+id/footer"
            layout="@layout/view_footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/body">

        </include>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
