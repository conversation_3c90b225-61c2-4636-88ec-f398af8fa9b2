<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_gradient"
        tools:context=".ui.loyaltyprogram.NewLoyaltyStatusFragment">

        <TextView
            android:id="@+id/tv_congratulations"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="90dp"
            android:text="@string/congrats_loyalty_status"
            android:textColor="#FFFFFF"
            android:textSize="24sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_status"
            android:layout_width="wrap_content"
            android:layout_height="90dp"
            android:layout_marginTop="40dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_congratulations" />

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:textColor="#0097EC"
            android:textSize="24sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_status" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_new_prizes_privileges"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:overScrollMode="never"
            app:layout_constraintTop_toBottomOf="@+id/tv_status" />

        <Button
            android:id="@+id/btn_home"
            style="@style/ButtonBlueStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="@dimen/size_16"
            android:text="@string/to_winnings"
            app:layout_constraintTop_toBottomOf="@+id/rv_new_prizes_privileges" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
