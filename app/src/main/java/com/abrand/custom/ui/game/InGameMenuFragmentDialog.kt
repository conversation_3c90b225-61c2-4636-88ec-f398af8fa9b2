package com.abrand.custom.ui.game

import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.AnimationUtils
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.FastClickPaymentSystem
import com.abrand.custom.data.entity.User
import com.abrand.custom.databinding.FragmentInGameMenuBinding
import com.abrand.custom.interfaces.InGameListener
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.views.BonusWageringView
import com.squareup.picasso.Picasso

class InGameMenuFragmentDialog : DialogFragment() {
    private var binding: FragmentInGameMenuBinding? = null
    var listener: InGameListener? = null
    private val viewModel: InGameMenuViewModel by viewModels()
    private val activityViewModel: GameViewModel by activityViewModels()
    private val inGameMenuMessagesFragment = InGameMenuMessagesFragment()
    private var menuState : MenuState = MenuState.CENTER
    private val menuStateKey = "menuState"
    private var gameFragment : GameFragment? = null

    companion object {
        const val TAG = "InGameMenuFragment"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, android.R.style.Theme_DeviceDefault_NoActionBar)

        dialog?.window?.setFlags(
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        gameFragment = getGameFragment()

        context.let { localContext ->
            if (localContext != null) {
                dialog?.window?.setBackgroundDrawable(
                    ColorDrawable(
                        ContextCompat.getColor(localContext, R.color.in_game_menu_bg)
                    )
                )
            }
        }

        //Window.setDecorFitsSystemWindows(boolean) Added in API level 30
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        observeViewModels()

        val localBinding = FragmentInGameMenuBinding.inflate(inflater, container, false)
        binding = localBinding

        localBinding.btnMenu.setOnClickListener {
            dialog?.dismiss()
        }

        savedInstanceState?.getSerializable(menuStateKey)?.let {
            menuState = it as MenuState
        }

        if (MenuState.CENTER == menuState) {
            showCenterMenuItems()
        } else {
            showSideMenuItems()
            setSideMenuItemsImage(menuState)
        }

        setBonusRefund()
        startBtnMenuAnimation()

        return localBinding.root
    }

    override fun onSaveInstanceState(outState: Bundle) {
        outState.putSerializable(menuStateKey, menuState)
        super.onSaveInstanceState(outState)
    }

    override fun onDestroyView() {
        listener?.onDialogClosed()
        binding = null
        super.onDestroyView()
    }

    fun dismissAndReset() {
        dialog?.dismiss()
        menuState = MenuState.CENTER
    }

    private fun observeViewModels() {
        viewModel.resetBonusBalanceLiveData.observe(
            viewLifecycleOwner
        ) { (status, data, error, failure) ->
            when (status) {
                Status.SUCCESS -> {
                    data?.let {
                        binding?.bonusWagering?.visibility = View.GONE
                        (activity as MainActivity).bonusRefund = null
                    }
                }
                Status.ERROR -> {
                    Log.w(TAG, "${error?.errorMessage}")
                    // Does user need to see this error?
//                    Toast.makeText(context, error?.errorMessage, Toast.LENGTH_SHORT).show()
                }
                Status.FAILURE -> {
                    Log.w(TAG, failure.toString())
                    // Does user need to see this failure?
//                    Toast.makeText(context, failure.toString(), Toast.LENGTH_SHORT).show()
                }
                else -> {
                    Log.e(InGameMenuTournamentFragment.TAG, "tournamentLiveData else branch")
                }
            }
        }

        activityViewModel.viewerMessageLiveData.observe(viewLifecycleOwner) { messageList ->
            updateMessagesCounter(messageList?.size)
        }
    }

    private fun isTournamentAvailable(): Boolean {
        val tournamentId = gameFragment?.tournamentId
        return tournamentId != null && tournamentId > 0
    }

    private fun showCenterMenuItems() {
        menuState = MenuState.CENTER
        showBonusWageringView()
        showTalisman()

        binding?.ivSearch?.visibility = View.GONE
        binding?.tvSearch?.visibility = View.VISIBLE
        binding?.tvSearch?.setOnClickListener {
            showDialogScreen(InGameMenuSearchFragment())
        }

        showCenterMessages()
        binding?.ivCash?.visibility = View.GONE
        if (User.State.NOT_LOGGED == Settings.get().userState) {
            binding?.tvCash?.visibility = View.GONE
        } else {
            binding?.tvCash?.visibility = View.VISIBLE
            binding?.tvCash?.setOnClickListener {
                listener?.openDepositClicked()
            }
        }

        showFastClickPaymentViewOrBottomBtn()

        binding?.ivBonuses?.visibility = View.GONE
        if (User.State.NOT_LOGGED == Settings.get().userState) {
            binding?.tvBonuses?.visibility = View.GONE
        } else {
            binding?.tvBonuses?.apply {
                visibility = View.VISIBLE
                setOnClickListener {
                    showDialogScreen(InGameMenuBonusBalanceFragment())
                }
            }
        }

        binding?.ivTournaments?.visibility = View.GONE
        if (isTournamentAvailable()) {
            binding?.tvTournament?.visibility = View.VISIBLE
            binding?.tvTournament?.setOnClickListener {
                showDialogScreen(InGameMenuTournamentFragment())
            }
        } else {
            binding?.tvTournament?.visibility = View.GONE
        }

        binding?.ivSupport?.visibility = View.GONE
        binding?.tvSupport?.visibility = View.VISIBLE
        binding?.tvSupport?.setOnClickListener {
            listener?.openSupportChatClicked()
        }

        binding?.ivHome?.visibility = View.GONE
        binding?.tvHome?.visibility = View.VISIBLE
        binding?.tvHome?.setOnClickListener {
            dialog?.dismiss()
            listener?.openHomeScreenClicked()
        }

        binding?.requisiteView?.visibility = View.GONE

        binding?.btnMenu?.setOnClickListener {
            dialog?.dismiss()
        }
        binding?.ivMenu?.setImageResource(R.drawable.ic_ingame_menu_close)
    }

    private fun showBonusWageringView() {
        val bonusRefund = (activity as MainActivity).bonusRefund
        if (bonusRefund != null && bonusRefund.bonusRefundSum > 0) {
            binding?.bonusWagering?.visibility = View.VISIBLE
        } else {
            binding?.bonusWagering?.visibility = View.GONE
        }
    }

    private fun showTalisman() {
        val talisman = gameFragment?.talisman
        if (talisman?.img != null) {
            binding?.containerTalisman?.visibility = View.VISIBLE
            Picasso.get().load(ApoloConfig.getFullUrl(talisman.img))
                .into(binding?.ivTalisman)
            binding?.tvTalismanName?.text = talisman.name
        } else {
            binding?.containerTalisman?.visibility = View.GONE
        }
    }

    private fun showCenterMessages() {
        binding?.sideMessages?.visibility = View.GONE
        if (User.State.NOT_LOGGED == Settings.get().userState) {
            binding?.centerMessages?.visibility = View.GONE
        } else {
            binding?.centerMessages?.visibility = View.VISIBLE
            binding?.centerMessages?.setOnClickListener {
                showDialogScreen(inGameMenuMessagesFragment)
            }
        }
    }

    private fun showSideMessages() {
        binding?.centerMessages?.visibility = View.GONE
        if (User.State.NOT_LOGGED == Settings.get().userState) {
            binding?.sideMessages?.visibility = View.GONE
        } else {
            binding?.sideMessages?.visibility = View.VISIBLE
            binding?.sideMessages?.setOnClickListener {
                showDialogScreen(inGameMenuMessagesFragment)
            }
        }
    }

    private fun showFastClickPaymentViewOrBottomBtn() {
        //In future: move a state to ViewModel
        val isDemoGame = gameFragment?.isDemoGame ?: false
        val fastClickPaymentSystem = gameFragment?.fastClickPaymentSystem
        val isFastClickAvailable = fastClickPaymentSystem != null
        val isLogged = User.State.NOT_LOGGED != Settings.get().userState

        if (isDemoGame) {
            if (isLogged) {
                val isReplenishment = gameFragment?.isReplenishment ?: false

                if (isFastClickAvailable && !isReplenishment) {
                    fastClickPaymentSystem?.let { showOneClickBtn(it) }
                } else {
                    showPlayRealGameBtn(true)
                }
            } else {
                showPlayRealGameBtn(false)
            }
        } else {
            if (isFastClickAvailable) {
                fastClickPaymentSystem?.let { showOneClickBtn(it) }
            } else {
                showMakeDepositBtn()
            }
        }
    }

    private fun showPlayRealGameBtn(isLogged: Boolean) { // only in Demo game
        binding?.btnPlayMoney?.visibility = View.VISIBLE
        binding?.fastClickPaymentView?.visibility = View.GONE

        if (isLogged) {
            binding?.btnPlayMoney?.setOnClickListener {
                dialog?.dismiss()
                listener?.loadMoneyGame()
            }
        } else {
            binding?.btnPlayMoney?.setOnClickListener {
                dialog?.dismiss()
                listener?.openRegister()
            }
        }
    }

    private fun showOneClickBtn(fastClickPaymentSystem: FastClickPaymentSystem) {
        binding?.btnPlayMoney?.visibility = View.GONE
        binding?.fastClickPaymentView?.let {
            it.visibility = View.VISIBLE
            it.setFastClickPaymentSystem(fastClickPaymentSystem)
            it.setButtonFastPaymentClickListener { isRebill, amount ->
                if (isRebill) {
                    listener?.makeRebill(amount)
                } else {
                    listener?.getFastPaymentUrl(amount)
                }
            }
        }
    }

    private fun showMakeDepositBtn() { // only in Real game
        binding?.btnPlayMoney?.text = getText(R.string._replenish)

        binding?.btnPlayMoney?.visibility = View.VISIBLE
        binding?.fastClickPaymentView?.visibility = View.GONE

        binding?.btnPlayMoney?.setOnClickListener {
            dialog?.dismiss()
            listener?.openDepositClicked()
        }
    }

    private fun showSideMenuItems() {
        binding?.bonusWagering?.visibility = View.GONE
        binding?.containerTalisman?.visibility = View.GONE
        binding?.tvSearch?.visibility = View.GONE
        binding?.ivSearch?.visibility = View.VISIBLE
        binding?.ivSearch?.setOnClickListener {
            showDialogScreen(InGameMenuSearchFragment())
        }

        showSideMessages()
        binding?.tvCash?.visibility = View.GONE
        binding?.fastClickPaymentView?.visibility = View.GONE
        binding?.btnPlayMoney?.visibility = View.GONE
        if (User.State.NOT_LOGGED == Settings.get().userState) {
            binding?.ivCash?.visibility = View.GONE
            binding?.requisiteView?.visibility = View.GONE
        } else {
            binding?.ivCash?.visibility = View.VISIBLE
            binding?.ivCash?.setOnClickListener {
                listener?.openDepositClicked()
            }

            val fastClickPaymentSystem = gameFragment?.fastClickPaymentSystem
            if (fastClickPaymentSystem != null) {
                binding?.requisiteView?.apply {
                    visibility = View.VISIBLE
                    Settings.get().fastClickPaymentSystem?.logo?.let { setLogo(it) }
                    Settings.get().fastClickPaymentSystem?.requisite?.let { setRequisite(it) }
                    setOnClickListener {
                        openMainMenuScreen()
                    }
                }
            } else {
                binding?.requisiteView?.visibility = View.GONE
            }
        }

        if (User.State.NOT_LOGGED == Settings.get().userState) {
            binding?.ivBonuses?.visibility = View.GONE
        } else {
            binding?.tvBonuses?.visibility = View.GONE
            binding?.ivBonuses?.apply {
                visibility = View.VISIBLE
                setOnClickListener {
                    showDialogScreen(InGameMenuBonusBalanceFragment())
                }
            }
        }

        binding?.tvTournament?.visibility = View.GONE
        if (isTournamentAvailable()) {
            binding?.ivTournaments?.visibility = View.VISIBLE
            binding?.ivTournaments?.setOnClickListener {
                showDialogScreen(InGameMenuTournamentFragment())
            }
        } else {
            binding?.ivTournaments?.visibility = View.GONE
        }

        binding?.tvSupport?.visibility = View.GONE
        binding?.ivSupport?.visibility = View.VISIBLE
        binding?.ivSupport?.setOnClickListener {
            listener?.openSupportChatClicked()
        }

        binding?.tvHome?.visibility = View.GONE
        binding?.ivHome?.visibility = View.VISIBLE
        binding?.ivHome?.setOnClickListener {
            dialog?.dismiss()
            listener?.openHomeScreenClicked()
        }

        binding?.btnMenu?.setOnClickListener {
            openMainMenuScreen()
        }
        binding?.ivMenu?.setImageResource(R.drawable.ic_arrow_back)
    }

    private fun openMainMenuScreen() {
        // popBackStack not working
        // childFragmentManager.popBackStack(null, FragmentManager.POP_BACK_STACK_INCLUSIVE)
        childFragmentManager.beginTransaction()
            .replace(R.id.fragment_ingame_menu_container, Fragment())
            .commit()
        showCenterMenuItems()
    }

    private fun showDialogScreen(fragment: Fragment) {
        val fragments = childFragmentManager.fragments
        val currentFragment = if (fragments.isNotEmpty()) fragments[0] else null
        if (currentFragment == null || currentFragment.javaClass != fragment.javaClass) {
            childFragmentManager.beginTransaction()
                .replace(R.id.fragment_ingame_menu_container, fragment)
                .commit()
            showSideMenuItems()
            setSideMenuItemsImage(fragment)

            when (fragment.javaClass) {
                InGameMenuSearchFragment::class.java -> {
                    menuState = MenuState.SEARCH
                }
                InGameMenuMessagesFragment::class.java -> {
                    menuState = MenuState.MESSAGES
                }
                InGameMenuTournamentFragment::class.java -> {
                    menuState = MenuState.TOURNAMENT
                }
                InGameMenuBonusBalanceFragment::class.java -> {
                    menuState = MenuState.BONUSES
                }
            }
        }
    }

    private fun setSideMenuItemsImage(fragment: Fragment) {
        when (fragment.javaClass) {
            InGameMenuSearchFragment::class.java -> {
                setSideMenuItemsDefaultImage()
                binding?.ivSearch?.setImageResource(R.drawable.ic_search_green)
            }
            InGameMenuMessagesFragment::class.java -> {
                setSideMenuItemsDefaultImage()
                binding?.ivSideMessages?.setImageResource(R.drawable.ic_messages_default_green)
            }
            InGameMenuTournamentFragment::class.java -> {
                setSideMenuItemsDefaultImage()
                binding?.ivTournaments?.setImageResource(R.drawable.ic_tournament_green)
            }
            InGameMenuBonusBalanceFragment::class.java -> {
                setSideMenuItemsDefaultImage()
                binding?.ivBonuses?.setImageResource(R.drawable.ic_bonuses_green)
            }
        }
    }

    private fun setSideMenuItemsImage(menuState: MenuState) {
        when (menuState) {
            MenuState.SEARCH -> {
                setSideMenuItemsDefaultImage()
                binding?.ivSearch?.setImageResource(R.drawable.ic_search_green)
            }
            MenuState.MESSAGES -> {
                setSideMenuItemsDefaultImage()
                binding?.ivSideMessages?.setImageResource(R.drawable.ic_messages_default_green)
            }
            MenuState.TOURNAMENT -> {
                setSideMenuItemsDefaultImage()
                binding?.ivTournaments?.setImageResource(R.drawable.ic_tournament_green)
            }
            MenuState.BONUSES -> {
                setSideMenuItemsDefaultImage()
                binding?.ivBonuses?.setImageResource(R.drawable.ic_bonuses_green)
            }
            else -> {
                // TODO: decide what should u do in another cases
            }
        }
    }

    private fun setSideMenuItemsDefaultImage() {
        binding?.ivSearch?.setImageResource(R.drawable.ic_search_white)
        binding?.ivSideMessages?.setImageResource(R.drawable.ic_messages_default_white)
        binding?.ivBonuses?.setImageResource(R.drawable.ic_bonuses_white)
        binding?.ivTournaments?.setImageResource(R.drawable.ic_tournament_white)
    }

    fun setBonusRefund() {
        val bonusRefund = (activity as MainActivity).bonusRefund
        if (bonusRefund != null && bonusRefund.bonusRefundSum > 0)
            binding?.bonusWagering?.let {
                it.setBetRefundSum(bonusRefund.bonusBetSum, bonusRefund.bonusRefundSum)
                it.setPercentToRefund(bonusRefund.bonusPercentToRefund)
                it.listener = object : BonusWageringView.Listener {
                    override fun onResetBonusBalanceClicked() {
                        viewModel.resetBonusBalance()
                    }
                }
            }
    }

    private fun startBtnMenuAnimation() {
        val enlargeAnimation = AnimationUtils.loadAnimation(context, R.anim.rotate_btn_to_x)
        binding?.ivMenu?.startAnimation(enlargeAnimation)
    }

    fun onKeyboardShow() {
        Handler(Looper.getMainLooper()).postDelayed({
            binding?.requisiteView?.visibility = View.GONE
            binding?.btnMenu?.visibility = View.GONE
        }, 50)
    }

    fun onKeyboardHide() {
        val fragments = childFragmentManager.fragments
        val currentFragment = if (fragments.isNotEmpty()) fragments[0] else null
        if (currentFragment != null &&
            (currentFragment is InGameMenuSearchFragment ||
                    currentFragment is InGameMenuTournamentFragment)
        ) {
            val fastClickPaymentSystem = gameFragment?.fastClickPaymentSystem
            if (fastClickPaymentSystem != null) {
                binding?.requisiteView?.visibility = View.VISIBLE
            }
        }
        binding?.btnMenu?.visibility = View.VISIBLE
    }

    fun showFastClickPaymentLoader() {
        binding?.fastClickPaymentView?.showLoader()
    }

    fun hideFastClickPaymentLoader() {
        binding?.fastClickPaymentView?.hideLoader()
    }

    fun showFastClickPaymentSuccessfulRebillingMessage() {
        binding?.fastClickPaymentView?.showSuccessfulRebillingMessage()
    }

    fun updateFastClickPaymentSystem(fastClickPaymentSystem: FastClickPaymentSystem) {
        binding?.fastClickPaymentView?.setFastClickPaymentSystem(fastClickPaymentSystem)
    }

    fun showPlayMoneyBtn() {
        binding?.fastClickPaymentView?.visibility = View.GONE
        binding?.btnPlayMoney?.visibility = View.VISIBLE
        binding?.btnPlayMoney?.setOnClickListener {
            dialog?.dismiss()
            listener?.loadMoneyGame()
        }
    }

    private fun updateMessagesCounter(messagesCount: Int?) {
        if (messagesCount != null && messagesCount > 0) {
            binding?.centerMessagesCounter?.visibility = View.VISIBLE
            binding?.sideMessagesCounter?.visibility = View.VISIBLE
            binding?.tvCenterMessagesCounter?.text = messagesCount.toString()
            binding?.tvSideMessagesCounter?.text = messagesCount.toString()
        } else {
            binding?.centerMessagesCounter?.visibility = View.INVISIBLE
            binding?.sideMessagesCounter?.visibility = View.INVISIBLE
        }
    }

    private fun getGameFragment(): GameFragment? {
        val currentFragment = (activity as? MainActivity)?.currentFragment
        return if (currentFragment is GameFragment) {
            currentFragment
        } else {
            null
        }
    }

    enum class MenuState {
        CENTER, SEARCH, MESSAGES, BONUSES, TOURNAMENT
    }

}
