package com.abrand.custom.ui.halloffame

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.ResponseException
import com.abrand.custom.data.entity.User
import com.abrand.custom.data.repositories.YearRepository
import com.abrand.custom.databinding.FragmentHallOfFameBinding
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment
import java.util.*

class HallOfFameFragment : Fragment() {
    private var binding: FragmentHallOfFameBinding? = null
    private val viewModel: HallOfFameViewModel by viewModels()
    private var adapter: HallOfFameAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentHallOfFameBinding.inflate(inflater, container, false)

        observeViewModel()
        setupInsets()
        initSpinner()

        binding?.rvPlaces?.layoutManager = LinearLayoutManager(context)
        adapter = HallOfFameAdapter()
        adapter?.listener = object : HallOfFameAdapter.Listener {
            override fun onGameClicked(gameUrl: String) {
                (activity as MainActivity).openPregame(gameUrl)
            }
        }
        binding?.rvPlaces?.adapter = adapter

        binding?.footer?.glPaymentSystemsView?.setOnClickListener {
            if (User.State.PLAYER == Settings.get().userState) {
                (activity as? MainActivity)?.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
            } else {
                (activity as? MainActivity)?.openEnterScreen(false)
            }
        }

        binding?.footer?.tvCopyrightYear?.text = inflater.context.getString(
            R.string.app_copyright_year,
            YearRepository.getCurrentYear()
        )

        return binding?.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun observeViewModel() {
        viewModel.hallOfFameLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    it.data?.let { data -> adapter?.setList(data) }
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    (activity as MainActivity).showConnectionIssueMessage(it.failure
                        ?: ResponseException(exceptionType = ResponseException.ExceptionType.UNKNOWN))
                }
                else -> {}
            }
        }
    }

    private fun setupInsets() {
        binding?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it.llRoot) { view, insets ->
                view.updatePadding(bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }

    private fun initSpinner() {
        val items = viewModel.getMonthList()
        val itemsAdapter = context?.let {
            MonthArrayAdapter(it, R.layout.item_hall_of_fame_spinner_close, items)
        }
        binding?.spinnerMonth?.adapter = itemsAdapter
        binding?.spinnerMonth?.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(adapter: AdapterView<*>, view: View?, position: Int, id: Long) {
                itemsAdapter?.selectedPosition = position

                val calendar: Calendar = Calendar.getInstance()
                calendar.add(Calendar.MONTH, -position)
                val year: Int = calendar.get(Calendar.YEAR)
                val month: Int = calendar.get(Calendar.MONTH) + 1
                viewModel.getHallOfFame(year, month)
            }

            override fun onNothingSelected(parentView: AdapterView<*>?) {}
        }
    }
}
