package com.abrand.custom.tools;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.widget.Toast;

import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.common.api.CommonStatusCodes;
import com.google.android.gms.safetynet.SafetyNet;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

public class ReCaptchaVerify {
    private static final String TAG = "ReCaptchaVerify";

    public static void getReCaptchaResponse(Context context, String reCaptchaId, ReCaptchaResponse reCaptchaResponse) {
        if ( !TextUtils.isEmpty(reCaptchaId) && context != null ) {
            SafetyNet.getClient(context).verifyWithRecaptcha(reCaptchaId)
                    .addOnSuccessListener(recaptchaTokenResponse -> {
                        String token = recaptchaTokenResponse.getTokenResult();
                        if ( !TextUtils.isEmpty(token) ) {
                            reCaptchaResponse.onReCaptchaResponseReceived(token);
                        } else {
                            reCaptchaResponse.onReCaptchaResponseFailure();
                            Toast.makeText(context, "Captcha error", Toast.LENGTH_SHORT).show();
                        }
                    })
                    .addOnFailureListener(e -> {
                        reCaptchaResponse.onReCaptchaResponseFailure();
                        Toast.makeText(context, "Captcha error", Toast.LENGTH_SHORT).show();
                        FirebaseCrashlytics.getInstance().recordException(e);
                        if ( e instanceof ApiException ) {
                            ApiException apiException = (ApiException) e;
                            int          statusCode   = apiException.getStatusCode();
                            Log.w(TAG, "Error: " + CommonStatusCodes.getStatusCodeString(statusCode));
                        } else {
                            // A different, unknown type of error occurred.
                            Log.w(TAG, "Error: " + e.getMessage());
                        }
                    });
        }
    }

    public interface ReCaptchaResponse {
        void onReCaptchaResponseReceived(String reCaptchaResponse);

        void onReCaptchaResponseFailure();
    }
}
