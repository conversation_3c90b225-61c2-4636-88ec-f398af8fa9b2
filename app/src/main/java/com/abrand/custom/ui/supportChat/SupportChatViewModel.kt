package com.abrand.custom.ui.supportChat

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.GetSupportConfigQuery
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessor
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException

class SupportChatViewModel : ViewModel() {
    val supportConfigLiveData = MutableLiveData<GetSupportConfigQuery.YhelperConfig>()
    val serverErrorLiveData = MutableLiveData<ServerError>()
    val apolloExceptionLiveData = SingleLiveEvent<ApolloException?>()

    fun getSupportChatConfig() {
        ApolloProcessorKt.getSupportConfig(object : GenericTarget<GetSupportConfigQuery.YhelperConfig> {

            override fun onSuccess(yhelperConfig: GetSupportConfigQuery.YhelperConfig?) {
                if ( yhelperConfig == null ) {
                    onError("Error: Empty support config", "", null)
                    return
                }

                yhelperConfig?.apply {
                    supportConfigLiveData.postValue(this)
                }
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                serverErrorLiveData.postValue(ServerError(errorMessage, errorCode, fieldsErrors))
            }

            override fun onFailure(e: ApolloException?) {
                apolloExceptionLiveData.postValue(e)
            }
        })
    }

    fun buildSupportChatContentHtml(supportChatConfiguration: SupportChatConfiguration, supportChatHostUrl: String): String {
        return """
                    <!doctype html>
                    <html>
                    <head>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
                    </head>
                    <body>
                    <script src="${supportWidgetScriptUrl(supportChatHostUrl, supportChatConfiguration.isProd)}" async=""></script>
                    <script>
                        (function() {
                            var supportChatEventListenerName = function (data) {
                                    ${SupportChatJavascriptInterface.TAG}.receiveChatEventFromJs(data.event);
                            };
                            var settings = {
                                hide_button: true,
                                'style_scheme': 'blue'
                            };

                            var data = {
                                user_id: "${supportChatConfiguration.userId}",
                                widget_id: "${supportChatConfiguration.widgetId}",
                                signature: "${supportChatConfiguration.signature}",
                                settings: settings,
                                eventsListener: supportChatEventListenerName,
                                hostUrl: "$supportChatHostUrl",
                                debugHostUrl: "$supportChatHostUrl",
                                events: {
                                    initCallback: function () {
                                        console.log('Chat init callback migration')
                                    }
                                }
                            };

                            var loader = setInterval(function() {
                                if (typeof window.YHelperChat !== 'undefined') {
                                    clearInterval(loader);
                                    window.YHelperChat(data);
                                }
                            }, 100);
                        })();
                    </script>
                """.trimIndent()
    }

    private fun supportWidgetScriptUrl(supportChatHostUrl: String, isProd: Boolean) =
            if (isProd) "$supportChatHostUrl/widget.js" else "$supportChatHostUrl/widget.js" //TODO: must be refactored [by Reaver]
}
