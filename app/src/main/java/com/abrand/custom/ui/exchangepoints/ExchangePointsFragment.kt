package com.abrand.custom.ui.exchangepoints

import android.os.Bundle
import android.text.Html
import android.text.InputType
import android.text.TextUtils
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import com.abrand.custom.R
import com.abrand.custom.data.Constants
import com.abrand.custom.data.Settings
import com.abrand.custom.databinding.FragmentExchangePointsBinding
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment
import com.abrand.custom.ui.views.textfield.TextField.UpdateListener

class ExchangePointsFragment : Fragment(R.layout.fragment_exchange_points) {
    private lateinit var viewModel: ExchangePointsViewModel
    private var exchangeRatio: Double = 0.0
    private var binding: FragmentExchangePointsBinding? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentExchangePointsBinding.bind(view)
        viewModel = ViewModelProviders.of(this).get(ExchangePointsViewModel::class.java)

        setupInsets()
        observeViewModel()

        binding?.etPoints?.setInputType(InputType.TYPE_CLASS_NUMBER)
        binding?.etPoints?.setMaxLength(11)
        binding?.etPoints?.subscribeToUpdates(etPointsListener)
        binding?.btnExchange?.setOnClickListener {
            if (getInputedPoints() <= (activity as MainActivity).getLoyaltyPoints()) {
                GeneralTools.hideKeyboard(activity)
                viewModel.loyaltyPointsExchange(getInputedPoints())
            } else {
                binding?.etPoints?.showErrorMessage(getString(R.string.not_enough_points))
                binding?.etPoints?.subscribeToUpdates(etPointsLengthUpdateListener)
            }
        }
        binding?.btnRequestEmailConfirmation?.setOnClickListener {
            viewModel.requestEmailConfirmation()
        }

        setLoyaltyPoints((activity as MainActivity).getLoyaltyPoints())
        setFormattedPoints((activity as MainActivity).getLoyaltyPoints().toString())
        binding?.btnExchange?.isEnabled = false
        context?.let {
            binding?.btnExchange?.setTextColor(ContextCompat.getColor(it, R.color.btn_exchange_points_disable_text))
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.getExchangePointsData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun setupInsets() {
        binding?.apply {
            ViewCompat.setOnApplyWindowInsetsListener(tvTitle) { view, insets ->
                view.updatePadding(top = insets.systemWindowInsetTop)
                insets
            }

            ViewCompat.setOnApplyWindowInsetsListener(containerScroll) { view, insets ->
                view.updatePadding(bottom = insets.systemWindowInsetBottom)

                val insetBottom = insets.systemWindowInsetBottom
                if (insetBottom > 150) {
                    nestedScrollView.post { nestedScrollView.fullScroll(View.FOCUS_DOWN) }
                }

                insets
            }
        }
    }

    private fun observeViewModel() {
        viewModel.viewerLiveData.observe(viewLifecycleOwner, Observer {
            val emailConfirmed = it?.profile?.emailConfirmed
            if (emailConfirmed != null && emailConfirmed) {
                binding?.containerEmailConfirmation?.visibility = View.GONE
                if ((activity as MainActivity).getLoyaltyPoints() > 0) {
                    binding?.btnExchange?.isEnabled = true
                    context?.let { _context ->
                        binding?.btnExchange?.setTextColor(ContextCompat.getColor(_context, R.color.btn_exchange_points_enable_text))
                    }
                }
            } else {
                binding?.containerEmailConfirmation?.visibility = View.VISIBLE
            }

            val exchangeRate = it?.loyaltyProgress?.status?.exchange_rate
            binding?.tvExchangeRate?.text = exchangeRate
            if (!TextUtils.isEmpty(exchangeRate) && exchangeRate!!.contains(":")) {
                exchangeRatio = exchangeRate.split(":")[0].toDouble()
                setMoney(getInputedPoints())
            }

            binding?.tvExchangeRateStatus?.text = it?.loyaltyProgress?.status?.title
        })

        viewModel.loyaltyPointsExchangeLiveData.observe(viewLifecycleOwner, Observer {
            (activity as MainActivity).openPaymentsScreen(PaymentsFragment.Screen.HISTORY)
            //TODO: is negative case processing present here? [by Reaver]
        })

        viewModel.emailConfirmationRequestedLiveData.observe(viewLifecycleOwner, Observer {
            setEmailConfirmationSentState()
            //TODO: is negative case processing present here? [by Reaver]
        })

        viewModel.serverErrorLiveData.observe(viewLifecycleOwner, Observer {
            if (it.errorCode == Constants.SERVER_EMAIL_CONFIRMATION_ALREADY_REQUESTED) {
                it.errorMessage = getString(R.string.email_confirmation_already_requested)
                setEmailConfirmationSentState()
            }
            Toast.makeText(context, it.errorMessage, Toast.LENGTH_SHORT).show()
        })

        viewModel.apolloExceptionLiveData.observe(viewLifecycleOwner, Observer {
            (activity as MainActivity).showConnectionIssueMessage(it)
        })
    }

    private val etPointsListener = UpdateListener { data: String?, valid: Boolean ->
        if (TextUtils.isEmpty(data) || exchangeRatio == 0.0) {
            binding?.tvMoney?.text = GeneralTools.formatBalance(getString(R.string.default_currency_code), 0.0)
        } else {
            val input = GeneralTools.removeSpaces(data)
            setFormattedPoints(input)
            setMoney(input.toInt())
        }
    }

    private fun setFormattedPoints(points: String) {
        setPointsWithoutSubscription(GeneralTools.formatNumber(points))
    }

    private fun setPointsWithoutSubscription(points: String) {
        binding?.etPoints?.unsubscribeFromUpdates(etPointsListener)
        binding?.etPoints?.text = points
        binding?.etPoints?.subscribeToUpdates(etPointsListener)
        binding?.etPoints?.setSelection(points.length)
    }

    private val etPointsLengthUpdateListener: UpdateListener = UpdateListener { data, valid ->
        if (getInputedPoints() <= (activity as MainActivity).getLoyaltyPoints()) {
            binding?.etPoints?.hideErrorMessage()
        } else {
            binding?.etPoints?.showErrorMessage(getString(R.string.not_enough_points))
        }
    }

    fun setLoyaltyPoints(loyaltyPoints: Int) {
        binding?.tvBalancePoints?.text = GeneralTools.formatNumber(loyaltyPoints.toString())
    }

    private fun getInputedPoints(): Int {
        return if (TextUtils.isEmpty(binding?.etPoints?.text)) {
            0
        } else {
            GeneralTools.removeSpaces(binding?.etPoints?.text).toInt()
        }
    }

    private fun setEmailConfirmationSentState() {
        binding?.apply {
            ivEmailConfirmation.setImageDrawable(context?.let { ContextCompat.getDrawable(it, R.drawable.ic_email_confirmation_sent) })
            tvEmailConfirmation.text = getString(R.string.exchange_points_email_confirmation_sent, Settings.get().loggedUserEmail)
            tvEmailChangeSupport.setText(Html.fromHtml(getString(R.string.exchange_points_email_change_support)), TextView.BufferType.SPANNABLE)
            tvEmailChangeSupport.visibility = View.VISIBLE
            tvEmailChangeSupport.setOnClickListener {
                (activity as MainActivity).openSupportChat("", "")
            }
            btnRequestEmailConfirmation.visibility = View.GONE
        }
    }

    private fun setMoney(inputPoint: Int) {
        val money = inputPoint.div(exchangeRatio)
        binding?.tvMoney?.text = GeneralTools.formatExchangeMoney(getString(R.string.default_currency_code), money)
    }
}
