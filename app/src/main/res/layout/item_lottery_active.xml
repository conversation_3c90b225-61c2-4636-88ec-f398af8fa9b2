<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_root"
    android:layout_width="match_parent"
    android:layout_height="@dimen/lottery_active_card_height"
    android:layout_marginLeft="@dimen/lottery_card_side_margin"
    android:layout_marginTop="@dimen/size_16"
    android:layout_marginRight="@dimen/lottery_card_side_margin"
    android:background="@drawable/tournament_default_bg">

    <ImageView
        android:id="@+id/iv_lottery_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0.5" />

    <TextView
        android:id="@+id/tv_lottery_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="@dimen/size_16"
        android:gravity="start"
        android:textColor="@color/white"
        android:textFontWeight="500"
        android:textSize="18sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_lottery_prize"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="16dp"
        android:layout_marginTop="28dp"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintBottom_toTopOf="@id/tv_lottery_prize_title"
        app:layout_constraintLeft_toLeftOf="parent" />

    <TextView
        android:id="@+id/tv_lottery_prize_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="@dimen/size_16"
        android:alpha="0.6"
        android:text="@string/tournaments_fund"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@id/tv_to_end"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_to_end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintBottom_toTopOf="@id/tv_to_end_title"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_to_end_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="@dimen/size_16"
        android:alpha="0.6"
        android:text="@string/tournaments_toend"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/btn_about"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/btn_bg_disable"
        android:minHeight="32dp"
        android:scaleType="center"
        android:text="@string/lottery_about"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
