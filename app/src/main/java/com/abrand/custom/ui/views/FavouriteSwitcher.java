package com.abrand.custom.ui.views;

import android.content.Context;
import android.graphics.PorterDuff;
import android.util.AttributeSet;

import androidx.appcompat.widget.SwitchCompat;
import androidx.core.content.ContextCompat;

import com.abrand.custom.R;

public class FavouriteSwitcher extends SwitchCompat {
    int thumbCheckedColor;
    int thumbUncheckedColor;
    int trackCheckedColor;
    int trackUncheckedColor;

    public FavouriteSwitcher(Context context) {
        super(context);
        init(context);
    }

    public FavouriteSwitcher(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public FavouriteSwitcher(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        thumbCheckedColor = ContextCompat.getColor(context, R.color.favourite_thumb_checked);
        thumbUncheckedColor = ContextCompat.getColor(context, R.color.favourite_thumb_unchecked);
        trackCheckedColor = ContextCompat.getColor(context, R.color.favourite_track_checked);
        trackUncheckedColor = ContextCompat.getColor(context, R.color.favourite_track_unchecked);
    }

    @Override
    public void setChecked(boolean checked) {
        super.setChecked(checked);
        changeColor(checked);
    }

    private void changeColor(boolean isChecked) {
        int thumbColor;
        int trackColor;

        if ( isChecked ) {
            thumbColor = thumbCheckedColor;
            trackColor = trackCheckedColor;
        } else {
            thumbColor = thumbUncheckedColor;
            trackColor = trackUncheckedColor;
        }

        try {
            getThumbDrawable().setColorFilter(thumbColor, PorterDuff.Mode.MULTIPLY);
            getTrackDrawable().setColorFilter(trackColor, PorterDuff.Mode.MULTIPLY);
        } catch ( NullPointerException e ) {
            e.printStackTrace();
        }
    }
}
