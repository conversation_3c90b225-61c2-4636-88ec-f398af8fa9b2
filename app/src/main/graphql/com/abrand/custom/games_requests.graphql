#import "./fragments.graphql"

query getPopularGames(
  $limit: PaginationAmount!,
  $offset: Int!,
  $order: GameListOrder
) {
  gameListPopular (
    deviceInfo: {
      platformType: MOB,
      platform: NATIVE_APP,
      os: ANDROID
    },
    offset: $offset
    limit: $limit
    order: $order
  ) {
    ...GameList
  }
}

query getNewGames(
  $limit: PaginationAmount!,
  $offset: Int!,
  $order: GameListOrder
) {
  gameListNew (
    deviceInfo: {
      platformType: MOB,
      platform: NATIVE_APP,
      os: ANDROID
    },
    offset: $offset
    limit: $limit
    order: $order
  ) {
    ...GameList
  }
}

query getSlotGames(
  $limit: PaginationAmount!,
  $offset: Int!,
  $order: GameListOrder
) {
  gameListSlots (
    deviceInfo: {
      platformType: MOB,
      platform: NATIVE_APP,
      os: ANDROID
    },
    offset: $offset
    limit: $limit
    order: $order
  ) {
    ...GameList
  }
}

query getTableGames(
  $limit: PaginationAmount!,
  $offset: Int!,
  $order: GameListOrder
) {
  gameListTable (
    deviceInfo: {
      platformType: MOB,
      platform: NATIVE_APP,
      os: ANDROID
    },
    offset: $offset
    limit: $limit
    order: $order
  ) {
    ...GameList
  }
}

query getFavouriteGames(
  $limit: PaginationAmount!,
  $offset: Int!,
  $order: GameListOrder
) {
  gameListFavourites (
    deviceInfo: {
      platformType: MOB,
      platform: NATIVE_APP,
      os: ANDROID
    },
    offset: $offset
    limit: $limit
    order: $order
  ) {
    ...GameList
  }
}

query getGamesByName (
  $name: String!,
  $limit: PaginationAmount!,
  $offset: Int!
){
  gameListAll (
    deviceInfo: {
      platformType: MOB,
      platform: NATIVE_APP,
      os: ANDROID
    },
    name : $name,
    limit: $limit,
    offset: $offset
  ) {
    ...GameList
  }
}

query getGameById($gameId: Int!) {
  gameById (
    id : $gameId
  ) {
    isFavourite
    tournament(platformType: MOB) {
      id
    }
  }
}

mutation addGameToFavourites($gameId: Int!) {
  addGameToFavourites(gameId: $gameId) {
    isFavourite
  }
}

mutation removeGameFromFavourites($gameId: Int!) {
  removeGameFromFavourites(gameId: $gameId) {
    isFavourite
  }
}

query getGameByUrl($url: String!) {
  gameByUrl(url: $url) {
    ...GameThumb
  }
}

query hasGameFreeSpins($url: String!) {
  hasGameFreeSpins (
    url: $url
  ) {
    free_spins
  }
}

query getGameListByType(
  $typeSlug: String!,
  $limit: PaginationAmount!,
  $offset: Int!
) {
  gameListByType (
    deviceInfo: {
      platformType: MOB,
      platform: NATIVE_APP,
      os: ANDROID
    },
    typeSlug: $typeSlug
    offset: $offset
    limit: $limit
  ) {
    ...GameList
  }
}

fragment GameList on GameListResult {
  gameList {
    ...GameItem
  },
  totalCount
}

fragment GameThumb on Game {
  id
  name
  url
  isHasDemo
  mobileIcon
  isFavourite
  tournament(platformType: MOB) {
    id
  }
}
