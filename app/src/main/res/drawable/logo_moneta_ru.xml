<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="27dp"
    android:height="24dp"
    android:viewportWidth="27"
    android:viewportHeight="24">
  <path
      android:pathData="M12.1164,19.3721H8.0423V10.6155C8.0423,9.5308 7.5397,8.9752 6.4815,8.9752C5.8201,8.9752 4.7354,9.4779 4.0741,10.0334V19.3721H0V6.1446H4.0741V7.917C5.1323,7.1234 7.2487,5.88 8.6508,5.88C10.5556,5.88 11.3492,6.6737 11.7989,7.8641C13.0423,6.9382 15.2116,5.88 16.5608,5.88C19.5238,5.88 20,7.2028 20,9.9012V19.3721H15.9259V10.6155C15.9259,9.5043 15.5026,8.9752 14.4709,8.9752C13.8889,8.9752 12.7778,9.425 12.1164,9.9012V19.3721Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="19.3721"
          android:startX="20"
          android:endY="5.88"
          android:endX="20"
          android:type="linear">
        <item android:offset="0" android:color="#FF23BCE8"/>
        <item android:offset="1" android:color="#FF2897D0"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24,0h3v24h-3z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="0"
          android:startX="24"
          android:endY="24"
          android:endX="24"
          android:type="linear">
        <item android:offset="0" android:color="#FFD99F2D"/>
        <item android:offset="0.215205" android:color="#FFEDD22F"/>
        <item android:offset="0.499326" android:color="#FFF5E62C"/>
        <item android:offset="0.759455" android:color="#FFDBBF2B"/>
        <item android:offset="1" android:color="#FFC08902"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,12C24.375,12.2761 24.319,12.5 24.25,12.5H26.75C26.681,12.5 26.625,12.2761 26.625,12C26.625,11.7239 26.681,11.5 26.75,11.5H24.25C24.319,11.5 24.375,11.7239 24.375,12Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="11.5"
          android:startX="24.25"
          android:endY="12.5"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,7.875C24.375,8.0821 24.319,8.25 24.25,8.25H26.75C26.681,8.25 26.625,8.0821 26.625,7.875C26.625,7.6679 26.681,7.5 26.75,7.5H24.25C24.319,7.5 24.375,7.6679 24.375,7.875Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="7.5"
          android:startX="24.25"
          android:endY="8.25"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,9.125C24.375,9.3321 24.319,9.5 24.25,9.5H26.75C26.681,9.5 26.625,9.3321 26.625,9.125C26.625,8.9179 26.681,8.75 26.75,8.75H24.25C24.319,8.75 24.375,8.9179 24.375,9.125Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="8.75"
          android:startX="24.25"
          android:endY="9.5"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,10.625C24.375,10.8321 24.319,11 24.25,11H26.75C26.681,11 26.625,10.8321 26.625,10.625C26.625,10.4179 26.681,10.25 26.75,10.25H24.25C24.319,10.25 24.375,10.4179 24.375,10.625Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="10.25"
          android:startX="24.25"
          android:endY="11"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,6.625C24.375,6.8321 24.319,7 24.25,7H26.75C26.681,7 26.625,6.8321 26.625,6.625C26.625,6.4179 26.681,6.25 26.75,6.25H24.25C24.319,6.25 24.375,6.4179 24.375,6.625Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="6.25"
          android:startX="24.25"
          android:endY="7"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,5.375C24.375,5.5821 24.319,5.75 24.25,5.75H26.75C26.681,5.75 26.625,5.5821 26.625,5.375C26.625,5.1679 26.681,5 26.75,5H24.25C24.319,5 24.375,5.1679 24.375,5.375Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="5"
          android:startX="24.25"
          android:endY="5.75"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,4.25C24.375,4.3881 24.319,4.5 24.25,4.5H26.75C26.681,4.5 26.625,4.3881 26.625,4.25C26.625,4.1119 26.681,4 26.75,4H24.25C24.319,4 24.375,4.1119 24.375,4.25Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="4"
          android:startX="24.25"
          android:endY="4.5"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,3.25C24.375,3.3881 24.319,3.5 24.25,3.5H26.75C26.681,3.5 26.625,3.3881 26.625,3.25C26.625,3.1119 26.681,3 26.75,3H24.25C24.319,3 24.375,3.1119 24.375,3.25Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="3"
          android:startX="24.25"
          android:endY="3.5"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,1.5C24.375,1.6381 24.319,1.75 24.25,1.75H26.75C26.681,1.75 26.625,1.6381 26.625,1.5C26.625,1.3619 26.681,1.25 26.75,1.25H24.25C24.319,1.25 24.375,1.3619 24.375,1.5Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="1.25"
          android:startX="24.25"
          android:endY="1.75"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,2.25C24.375,2.3881 24.319,2.5 24.25,2.5H26.75C26.681,2.5 26.625,2.3881 26.625,2.25C26.625,2.1119 26.681,2 26.75,2H24.25C24.319,2 24.375,2.1119 24.375,2.25Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="2"
          android:startX="24.25"
          android:endY="2.5"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,1C24.375,1.1381 24.319,1.25 24.25,1.25H26.75C26.681,1.25 26.625,1.1381 26.625,1C26.625,0.8619 26.681,0.75 26.75,0.75H24.25C24.319,0.75 24.375,0.8619 24.375,1Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="0.75"
          android:startX="24.25"
          android:endY="1.25"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,0.5C24.375,0.6381 24.319,0.75 24.25,0.75H26.75C26.681,0.75 26.625,0.6381 26.625,0.5C26.625,0.3619 26.681,0.25 26.75,0.25H24.25C24.319,0.25 24.375,0.3619 24.375,0.5Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="0.25"
          android:startX="24.25"
          android:endY="0.75"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,18.625C24.375,18.8321 24.319,19 24.25,19H26.75C26.681,19 26.625,18.8321 26.625,18.625C26.625,18.4179 26.681,18.25 26.75,18.25H24.25C24.319,18.25 24.375,18.4179 24.375,18.625Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="18.25"
          android:startX="24.25"
          android:endY="19"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,14.875C24.375,15.0821 24.319,15.25 24.25,15.25H26.75C26.681,15.25 26.625,15.0821 26.625,14.875C26.625,14.6679 26.681,14.5 26.75,14.5H24.25C24.319,14.5 24.375,14.6679 24.375,14.875Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="14.5"
          android:startX="24.25"
          android:endY="15.25"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,13.375C24.375,13.5821 24.319,13.75 24.25,13.75H26.75C26.681,13.75 26.625,13.5821 26.625,13.375C26.625,13.1679 26.681,13 26.75,13H24.25C24.319,13 24.375,13.1679 24.375,13.375Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="13"
          android:startX="24.25"
          android:endY="13.75"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,17.375C24.375,17.5821 24.319,17.75 24.25,17.75H26.75C26.681,17.75 26.625,17.5821 26.625,17.375C26.625,17.1679 26.681,17 26.75,17H24.25C24.319,17 24.375,17.1679 24.375,17.375Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="17"
          android:startX="24.25"
          android:endY="17.75"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,16.125C24.375,16.3321 24.319,16.5 24.25,16.5H26.75C26.681,16.5 26.625,16.3321 26.625,16.125C26.625,15.9179 26.681,15.75 26.75,15.75H24.25C24.319,15.75 24.375,15.9179 24.375,16.125Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="15.75"
          android:startX="24.25"
          android:endY="16.5"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,21.75C24.375,21.8881 24.319,22 24.25,22H26.75C26.681,22 26.625,21.8881 26.625,21.75C26.625,21.6119 26.681,21.5 26.75,21.5H24.25C24.319,21.5 24.375,21.6119 24.375,21.75Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="21.5"
          android:startX="24.25"
          android:endY="22"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,20.75C24.375,20.8881 24.319,21 24.25,21H26.75C26.681,21 26.625,20.8881 26.625,20.75C26.625,20.6119 26.681,20.5 26.75,20.5H24.25C24.319,20.5 24.375,20.6119 24.375,20.75Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="20.5"
          android:startX="24.25"
          android:endY="21"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,22.5C24.375,22.6381 24.319,22.75 24.25,22.75H26.75C26.681,22.75 26.625,22.6381 26.625,22.5C26.625,22.3619 26.681,22.25 26.75,22.25H24.25C24.319,22.25 24.375,22.3619 24.375,22.5Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="22.25"
          android:startX="24.25"
          android:endY="22.75"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,19.75C24.375,19.8881 24.319,20 24.25,20H26.75C26.681,20 26.625,19.8881 26.625,19.75C26.625,19.6119 26.681,19.5 26.75,19.5H24.25C24.319,19.5 24.375,19.6119 24.375,19.75Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="19.5"
          android:startX="24.25"
          android:endY="20"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,23.75C24.375,23.8881 24.319,24 24.25,24H26.75C26.681,24 26.625,23.8881 26.625,23.75C26.625,23.6119 26.681,23.5 26.75,23.5H24.25C24.319,23.5 24.375,23.6119 24.375,23.75Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="23.5"
          android:startX="24.25"
          android:endY="24"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.375,23.25C24.375,23.3881 24.319,23.5 24.25,23.5H26.75C26.681,23.5 26.625,23.3881 26.625,23.25C26.625,23.1119 26.681,23 26.75,23H24.25C24.319,23 24.375,23.1119 24.375,23.25Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="23"
          android:startX="24.25"
          android:endY="23.5"
          android:endX="24.25"
          android:type="linear">
        <item android:offset="0" android:color="#FFDAA92E"/>
        <item android:offset="1" android:color="#FFF0E834"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
