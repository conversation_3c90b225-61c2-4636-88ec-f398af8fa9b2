package com.abrand.custom.data

import org.json.JSONObject

class JsonDataGenerator {
    private val pushSubscriptionEventName = "push_subscription";

    fun getPushSubscriptionAnalyticsDataJson(data: Int): String {
        return JSONObject()
                .put(pushSubscriptionEventName, data)
                .toString()
    }

    fun getPushSubscriptionAuthDataJson(appId: String, pushToken: String, deviceId: String): String {
        return JSONObject()
                .put("xAppName", appId)
                .put("xAppToken", pushToken)
                .put("xDeviceId", deviceId)
                .toString()
    }

    fun getLoginData<PERSON>son(loginType: String, userId: String): String {
        return JSONObject()
            .put("login_type", loginType)
            .put("user_id", userId)
            .toString()
    }

    fun getBiometryAuthDataJson(biometryOn: Boolean, userId: String): String {
        var biometry = if (biometryOn) 1 else 0
        return JSONObject()
            .put("biometry", biometry)
            .put("user_id", userId)
            .toString()
    }
}
