package com.abrand.custom.ui.noconnection

import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.Navigation
import com.abrand.custom.R
import com.abrand.custom.databinding.FragmentNoConnectionBinding
import com.abrand.custom.ui.activitymain.MainActivity

class NoConnectionFragment : Fragment(R.layout.fragment_no_connection) {
    private var type: String? = null
    private var binding: FragmentNoConnectionBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            type = it.getString(KEY_TYPE)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentNoConnectionBinding.bind(view)
        if (VALUE_NO_INTERNET == type) {
            binding?.tvNoConnection?.text = getString(R.string.no_internet_connection)
        } else {
            binding?.tvNoConnection?.text = getString(R.string.unstable_internet_connection)
        }

        binding?.btnUpdate?.setOnClickListener {
            update()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    public fun update() {
        val fragmentView = view
        if (fragmentView != null) {
            val mainActivity = activity
            if (mainActivity is MainActivity) {
                mainActivity.initialViewerData
                mainActivity.balanceSubscribe() // restore subscriptions in activity
                mainActivity.realTimeMessagesSubscribe()
                mainActivity.messagesSubscribe()
                mainActivity.onlineUsersSubscribe()
            }
            Navigation.findNavController(fragmentView).navigateUp()
        }
    }

    companion object {
        const val KEY_TYPE = "type"
        const val VALUE_NO_INTERNET = "NoInternet"
        const val VALUE_UNSTABLE_CONNECTION = "UnstableConnection"
    }
}
