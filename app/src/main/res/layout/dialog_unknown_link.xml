<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#FFFFFF">

    <ImageButton
        android:id="@+id/ib_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:padding="@dimen/size_16"
        android:src="@drawable/ic_unknown_link_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:paddingLeft="32dp"
        android:paddingRight="32dp"
        android:text="@string/unknown_link_dialog_message"
        android:textColor="#000000"
        android:textSize="12sp"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/btn_support_chat"
        style="@style/ButtonStyle"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginLeft="32dp"
        android:layout_marginTop="20dp"
        android:layout_marginRight="32dp"
        android:layout_marginBottom="36dp"
        android:text="@string/support_chat_button_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_message" />

</androidx.constraintlayout.widget.ConstraintLayout>
