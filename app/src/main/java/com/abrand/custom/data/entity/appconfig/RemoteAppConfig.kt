package com.abrand.custom.data.entity.appconfig

import com.google.gson.annotations.SerializedName

class RemoteAppConfig(
    @SerializedName("download_code") val downloadCode: String,
    @SerializedName("domains") val domains: List<String>?,
    @SerializedName("updater_domains") val updaterDomains: List<String>?,
    @SerializedName("params") val params: RemoteParams?,
    @SerializedName("ref") val ref: String?,
    @SerializedName("uuid") val uuid: String?,
    @SerializedName("affdata") val affData: Map<String, String>?,
    @SerializedName("salt") val salt: String?,
    @SerializedName("appInstallUuid") val appInstallUuid: String?,
    @SerializedName("postfix") val postfix: String?,
)

data class RemoteParams(
    @SerializedName(value = "f_flags") val fFlags: String?,  // parse as JSON
)

internal fun RemoteAppConfig.mergeWithLocalConfig(currentConfig: LocalConfig): LocalConfig {
    return currentConfig.copy(
        downloadCode = this.downloadCode,
        domains = this.domains.takeIf { !it.isNullOrEmpty() } ?: currentConfig.domains,
        updaterDomains = this.updaterDomains.takeIf { !it.isNullOrEmpty() }
            ?: currentConfig.updaterDomains,
        ref = this.ref.takeIf { !it.isNullOrBlank() } ?: currentConfig.ref,
        uuid = this.uuid.takeIf { !it.isNullOrBlank() } ?: currentConfig.uuid,
        affData = this.affData.takeIf { !it.isNullOrEmpty() } ?: currentConfig.affData,
        salt = this.salt.takeIf { !it.isNullOrBlank() } ?: currentConfig.salt,
        appInstallUuid = this.appInstallUuid.takeIf { !it.isNullOrBlank() }
            ?: currentConfig.appInstallUuid,
    )
}
