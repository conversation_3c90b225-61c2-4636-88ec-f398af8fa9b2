package com.abrand.custom.data.entity

import com.google.gson.annotations.SerializedName

enum class AnalyticsEvent(@SerializedName("eventName") val eventName: String,
                          @SerializedName("eventToken") val eventToken: String) {
    FIRST_OPEN("1st_open", "nfwc2p"),
    RETURN_TO_APP("return_toApp", "y6t2zg"),
    OPEN_DEP("open_deposit", "nm3kwb"),
    OPEN_PAYMENT("open_withdrawal", "xy5goc"),
    CLOSE_APP("close_app", "41ss88"),
    OUR_APP("our_app", "wozxj9"),
    PUSH_SUBSCRIPTION("push_subscription", "xeuosk"),
    MENU_CASHIER("menu_cashier", "ghrw3k"),
    STICKY_CASHIER("sticky_cashier", "ko7cqm"),
    LOGIN_APP("login_app", "lgnapp"),
    BIOMETRY_AUTH("biometry_auth", " b1ometr9")
}
