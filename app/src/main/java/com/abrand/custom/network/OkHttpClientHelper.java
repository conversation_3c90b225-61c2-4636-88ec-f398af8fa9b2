package com.abrand.custom.network;

import com.abrand.custom.BuildConfig;

import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;

public class OkHttpClientHelper {

    private static volatile OkHttpClient okHttpClient;

    public static OkHttpClient getOkHttpClient() {
        OkHttpClient localOkHttpClient = okHttpClient;

        if ( localOkHttpClient == null ) {
            synchronized (OkHttpClientHelper.class) {
                localOkHttpClient = okHttpClient;
                if ( localOkHttpClient == null ) {
                    OkHttpClient.Builder okHttpBuilder = new OkHttpClient.Builder();
                    WebkitCookieManagerProxy proxy = new WebkitCookieManagerProxy();

                    okHttpBuilder.cookieJar(proxy);
                    okHttpBuilder.addInterceptor(new UserAgentInterceptor());

                    if ( BuildConfig.DEBUG ) {
                        HttpLoggingInterceptor logger = new HttpLoggingInterceptor();

                        logger.level(HttpLoggingInterceptor.Level.BODY);
                        okHttpBuilder.addInterceptor(logger);
                    }

                    okHttpBuilder.pingInterval(10, TimeUnit.SECONDS);
                    okHttpBuilder.retryOnConnectionFailure(true);

                    okHttpClient = localOkHttpClient = okHttpBuilder.build();
                }
            }
        }

        return localOkHttpClient;
    }

    public static OkHttpClient.Builder getOkHttpBuilder() {
        return getOkHttpClient().newBuilder();
    }

}
