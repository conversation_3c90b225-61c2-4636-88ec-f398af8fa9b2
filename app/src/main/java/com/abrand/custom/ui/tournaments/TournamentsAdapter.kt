package com.abrand.custom.ui.tournaments

import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.CountDownTimer
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.*
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.data.repositories.YearRepository
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.setHtmlText
import com.squareup.picasso.Picasso
import com.squareup.picasso.Picasso.LoadedFrom
import com.squareup.picasso.Target
import jp.wasabeef.blurry.Blurry
import java.util.*
import java.util.concurrent.TimeUnit

class TournamentsAdapter() : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
        private var tournamentsListener: TournamentsListener? = null
        var footerListener: FooterListener? = null
         var items = mutableListOf<TournamentsItem>()
        var tournamentsLoadVH: TournamentsLoadVH? = null
        var isUserLogged = false
        var spinnerPosition = 0
        var currentTotalTournaments = 0
        var isTournamentLoading = false
        var tournamentWidth = 0
        var tournamentHeight = 0
        var isCurrentSingle = true

    init {
        items.add(TournamentsItem(itemType = TournamentsItem.ItemType.TITLE_CURRENT))
        items.add(TournamentsItem(itemType = TournamentsItem.ItemType.TITLE_ENDED))
        items.add(TournamentsItem(itemType = TournamentsItem.ItemType.TOURNAMENTS_LOAD))
        items.add(TournamentsItem(itemType = TournamentsItem.ItemType.FOOTER))
    }

    private fun refreshList() {
        items = mutableListOf()
        items.add(TournamentsItem(itemType = TournamentsItem.ItemType.TITLE_CURRENT))
        items.add(TournamentsItem(itemType = TournamentsItem.ItemType.TITLE_ENDED))
        items.add(TournamentsItem(itemType = TournamentsItem.ItemType.TOURNAMENTS_LOAD))
        items.add(TournamentsItem(itemType = TournamentsItem.ItemType.FOOTER))
        notifyDataSetChanged()
    }

        fun setList(newItems: MutableList<TournamentsItem>) {
            refreshList()
            var current = 0
            for(item in newItems){
                if(item.status.equals("in_process"))
                    current++
            }
            when (current) {
                0 -> {
                    items.removeAt(0)
                    addElements(newItems)
                    notifyDataSetChanged()
                }
                1 -> {
                    addElements(newItems)
                    isCurrentSingle = true
                    notifyDataSetChanged()
                }
                else -> {
                    addElements(newItems)
                    isCurrentSingle = false
                    notifyDataSetChanged()
                }
            }
        }

        fun appendList(newItems: MutableList<TournamentsItem>) {
            items.addAll(items.size - 2, newItems)
            notifyDataSetChanged()
        }

    fun addElements(newItems: MutableList<TournamentsItem>) {
        for (item in newItems) {
            if (item.status.equals("in_process")) {
                items.add(items.size - 3, item)
            } else {
                items.add(items.size - 2, item)
            }
        }
    }

    fun cleanList() {
        items = mutableListOf<TournamentsItem>()
        notifyDataSetChanged()
    }

        constructor(tournamentsListener: TournamentsListener, tournamentWidth: Int, tournamentHeight: Int) : this() {
            this.tournamentsListener = tournamentsListener
            this.tournamentHeight = tournamentHeight
            this.tournamentWidth = tournamentWidth
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            val inflater = LayoutInflater.from(parent.context)
            return when (viewType) {
                TournamentsItem.ItemType.TITLE_CURRENT.id -> TitleCurrentVH(inflater, parent)
                TournamentsItem.ItemType.TOURNAMENT_CURRENT.id -> CurrentTournamentVH(inflater, parent)
                TournamentsItem.ItemType.TITLE_ENDED.id -> TitleEndedVH(inflater, parent)
                TournamentsItem.ItemType.TOURNAMENT_ENDED.id -> EndedTournamentVH(inflater, parent)
                TournamentsItem.ItemType.TOURNAMENTS_LOAD.id -> TournamentsLoadVH(inflater,parent)
                else -> FooterVH(inflater, parent)
            }
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            if (holder is EndedTournamentVH) {
                holder.bind(items[position])
            } else if (holder is CurrentTournamentVH) {
                holder.bind(items[position])
            } else if (holder is TournamentsLoadVH) {
                tournamentsLoadVH = holder
                holder.bind()
            } else if (holder is FooterVH) {
                holder.bind()
            }
            setFirstItemAppBarPadding(position, holder)
        }

        override fun getItemCount(): Int = items.size


        fun getTournamentsCount() = items.count {
            TournamentsItem.ItemType.TOURNAMENT_ENDED == it.itemType ||
                    TournamentsItem.ItemType.TOURNAMENT_CURRENT == it.itemType
        }

    override fun getItemViewType(position: Int): Int {
        return items[position].itemType.id
    }

        fun setTournamentLoadingState(isTournamentLoading: Boolean) {
            this.isTournamentLoading = isTournamentLoading
            tournamentsLoadVH?.setTournamentLoadingState(isTournamentLoading)
        }

        fun applyLoggedState() {
            isUserLogged = true
        }

        fun applyNotLoggedState() {
            isUserLogged = false
        }


    private fun setFirstItemAppBarPadding(position: Int, holder: RecyclerView.ViewHolder) {
        if (position == 0) {
            val padding: Int = GeneralTools.getActionBarHeight(holder.itemView.context)
            holder.itemView.setPadding(
                    holder.itemView.paddingLeft,
                    padding,
                    holder.itemView.paddingRight,
                    holder.itemView.paddingBottom
            )
        } else {
            holder.itemView.setPadding(
                    holder.itemView.paddingLeft,
                    0,
                    holder.itemView.paddingRight,
                    holder.itemView.paddingBottom
            )
        }
    }

    class TitleCurrentVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_tournaments_current_title, parent, false))

    class TitleEndedVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_tournaments_ended_title, parent, false))

    inner class CurrentTournamentVH(inflater: LayoutInflater, parent: ViewGroup) :
                RecyclerView.ViewHolder(inflater.inflate(if(isCurrentSingle) R.layout.item_tournament_current_single else R.layout.item_tournament_current_multiple, parent, false)) {

            private val ivItemTournamentImage = itemView.findViewById<ImageView>(R.id.iv_item_tournament_image)
            private val ivItemTournamentImageBlur = itemView.findViewById<ImageView>(R.id.iv_item_tournament_image_blur)
            private val tvItemTournamentName = itemView.findViewById<TextView>(R.id.tv_item_tournament_name)
            private val tvItemTournamentPrize = itemView.findViewById<TextView>(R.id.tv_item_tournament_prize)
            private val tvItemTournamentDay = itemView.findViewById<TextView>(R.id.tv_item_tournament_day)
            private val btnItemTournamentAbout = itemView.findViewById<TextView>(R.id.btn_about)

        var currentBannerTarget: Target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: LoadedFrom?) {
                if (!isCurrentSingle) {
                    setupImageViewBeforeDisplayImage()
                    val roundedBitmapDrawable =
                        RoundedBitmapDrawableFactory.create(parent.context.resources, bitmap)
                    roundedBitmapDrawable.cornerRadius =
                        parent.context.resources.getDimensionPixelSize(R.dimen.news_card_corner_radius)
                            .toFloat()
                    ivItemTournamentImage.background = roundedBitmapDrawable
                } else {
                    setupImageViewBeforeDisplayImage()
                    val roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(parent.context.resources, bitmap)
                    roundedBitmapDrawable.cornerRadius = 0f
                    ivItemTournamentImage.background = roundedBitmapDrawable
                    ivItemTournamentImageBlur.background = roundedBitmapDrawable
                    Blurry.with(parent.context).radius(80).from(bitmap).into(ivItemTournamentImageBlur)
                }
            }

            override fun onBitmapFailed(e: Exception?, errorDrawable: Drawable?) {}

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) {}
        }

            fun bind(tournamentsItem: TournamentsItem?) {
                ivItemTournamentImage.clearAnimation()
                val animation = AnimationUtils.loadAnimation(itemView.context, R.anim.rotation_loader)
                ivItemTournamentImage.scaleType = ImageView.ScaleType.CENTER_INSIDE
                if(isCurrentSingle){
                    ivItemTournamentImage.setImageResource(R.drawable.ic_preload_black)
                }else{
                    ivItemTournamentImage.setImageResource(R.drawable.ic_preload)
                }
                ivItemTournamentImage.startAnimation(animation)

                if (tournamentsItem != null) {
                    tvItemTournamentName.text = tournamentsItem.title
                    tvItemTournamentPrize.setHtmlText(GeneralTools.getTournamentFormattedBalance(tournamentsItem.prizeFund.toDouble()))
                    val endTime = DateTools.getServerDate(itemView.context,tournamentsItem.dateEnd).time
                    val currentTime = Calendar.getInstance().timeInMillis
                    val count = (endTime-currentTime)/(1000*60*60*24)
                    if(count<2){
                        val timer = object: CountDownTimer(endTime-currentTime, 1000) {
                            var hms : String = ""
                            override fun onTick(millisUntilFinished: Long) {
                                hms = java.lang.String.format("%02d:%02d:%02d", TimeUnit.MILLISECONDS.toHours(millisUntilFinished),
                                        TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished) - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(millisUntilFinished)),
                                        TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)))
                                tvItemTournamentDay.text = hms
                            }

                            override fun onFinish() {
                                tournamentsListener?.onRefresh()
                            }
                        }
                        timer.start()
                    }else {
                        tvItemTournamentDay.text = itemView.context.resources.getQuantityString(R.plurals.days, count.toInt(), count.toInt())
                    }
                    try {
                        setTournamentBackground(tournamentsItem.bannerMob, itemView.context)
                    }catch(e:Exception){}
                }

                btnItemTournamentAbout.setOnClickListener {
                    tournamentsItem?.let { tournamentThumb -> tournamentsListener?.onTournamentClicked(tournamentThumb) }
                }

            }

            private fun setupImageViewBeforeDisplayImage() {
                ivItemTournamentImage.clearAnimation()
                ivItemTournamentImage.setImageResource(0)
                ivItemTournamentImage.scaleType = ImageView.ScaleType.CENTER_INSIDE
            }

            private fun setTournamentBackground(mobileIcon: String?, context: Context) {
                if (!TextUtils.isEmpty(mobileIcon)) {
                    Picasso.get()
                            .load(ApoloConfig.getFullUrl(mobileIcon))
                            .resize(tournamentWidth, context.resources.getDimensionPixelSize(R.dimen.tournaments_big_card_height))
                            .centerCrop()
                            .into(currentBannerTarget)
                } else {
                    if(!isCurrentSingle){
                        setupImageViewBeforeDisplayImage()
                    }else{
                        setupImageViewBeforeDisplayImage()
                        ivItemTournamentImage.background = context.getDrawable(R.drawable.bg_gradient)
                        ivItemTournamentImageBlur.background = context.getDrawable(R.drawable.bg_gradient)
                    }
                }
            }
        }

    inner class EndedTournamentVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_tournament_ended, parent, false)) {

        private val ivItemTournamentImage = itemView.findViewById<ImageView>(R.id.iv_item_tournament_image)
        private val tvItemTournamentName = itemView.findViewById<TextView>(R.id.tv_item_tournament_name)
        private val tvItemTournamentPrize = itemView.findViewById<TextView>(R.id.tv_item_tournament_prize)
        private val tvItemTournamentDay = itemView.findViewById<TextView>(R.id.tv_item_tournament_day)
        private val tvItemTournamentMonth = itemView.findViewById<TextView>(R.id.tv_item_tournament_month)

        var endedBannerTarget: Target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: LoadedFrom?) {
                val roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(parent.context.resources, bitmap)
                roundedBitmapDrawable.cornerRadius = parent.context.resources.getDimensionPixelSize(R.dimen.news_card_corner_radius).toFloat()
                ivItemTournamentImage.background = roundedBitmapDrawable
            }

            override fun onBitmapFailed(e: Exception?, errorDrawable: Drawable?) {}

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) {}
        }

        //bad code style, need to proper format and refactor method
        fun bind(tournamentsItem: TournamentsItem) {
            tvItemTournamentName.text = tournamentsItem.title
            tvItemTournamentPrize.setHtmlText(GeneralTools.getTournamentFormattedBalance(tournamentsItem.prizeFund.toDouble()))
            tvItemTournamentDay.text = DateTools.getDay(itemView.context, tournamentsItem.dateEnd.toString().replace(tournamentsItem.dateStart.toString().substring(tournamentsItem.dateStart.toString().indexOf(".")),"Z"))
            val serverDateEnd = tournamentsItem.dateEnd.toString().replace(tournamentsItem.dateStart.toString().substring(tournamentsItem.dateStart.toString().indexOf(".")),"Z")
            tvItemTournamentMonth.text = DateTools.getMonth(itemView.context, serverDateEnd)
            setTournamentBackground(tournamentsItem.bannerMob, itemView.context)

            itemView.setOnClickListener {
                tournamentsItem.let { tournamentsItem -> tournamentsListener?.onTournamentClicked(tournamentsItem) }
            }
        }

        private fun setTournamentBackground(mobileIcon: String?, context: Context) {
            if (!TextUtils.isEmpty(mobileIcon)) {
                Picasso.get()
                        .load(ApoloConfig.getFullUrl(mobileIcon))
                        .resize(tournamentWidth, tournamentHeight)
                        .centerCrop()
                        .into(endedBannerTarget)
            } else {
                ivItemTournamentImage.background = null
            }
        }
    }

    inner class TournamentsLoadVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_news_load, parent, false)) {
        private val btnLoadMore = itemView.findViewById<TextView>(R.id.btn_load_more)
        private val ivLoader = itemView.findViewById<ImageView>(R.id.iv_loader)

        init {
            setLoadMoreView()
        }

        fun bind() {
            btnLoadMore.setOnClickListener {
                tournamentsListener?.onLoadMoreClicked()
            }
        }

        fun setTournamentLoadingState(isLoading: Boolean) {
            if (isLoading) {
                ivLoader.visibility = View.VISIBLE
                val animation = AnimationUtils.loadAnimation(itemView.context, R.anim.rotation_loader)
                ivLoader.startAnimation(animation)
                btnLoadMore.visibility = View.INVISIBLE
            } else {
                ivLoader.clearAnimation()
                ivLoader.visibility = View.GONE
                setLoadMoreView()
            }
        }

        private fun setLoadMoreView() {
            if (currentTotalTournaments > getTournamentsCount()) {
                if (currentTotalTournaments - getTournamentsCount() >= TournamentsFragment.LOAD_STEP) {
                    btnLoadMore.text = itemView.context.resources.getQuantityString(R.plurals.more_tournaments, TournamentsFragment.LOAD_STEP, TournamentsFragment.LOAD_STEP)
                } else {
                    val btnTournamentsCount = currentTotalTournaments - getTournamentsCount()
                    btnLoadMore.text = itemView.context.resources.getQuantityString(R.plurals.more_tournaments, btnTournamentsCount, btnTournamentsCount)
                }
                btnLoadMore.visibility = View.VISIBLE
            } else {
                btnLoadMore.visibility = View.GONE
            }
        }
    }

    inner class FooterVH(inflater: LayoutInflater, parent: ViewGroup) :
                RecyclerView.ViewHolder(inflater.inflate(R.layout.view_footer, parent, false)) {

            fun bind() {
                val paddingBottom: Int = itemView.context.resources.getDimensionPixelSize(R.dimen.not_organic_user_footer_padding_bottom)
                itemView.setPadding(0, 0, 0, paddingBottom)
                itemView.findViewById<View>(R.id.gl_payment_systems_view).setOnClickListener {
                    footerListener?.onClickPaymentSystems()
                }
                itemView.findViewById<TextView>(R.id.tv_copyright_year)?.let {
                    it.text = itemView.context.getString(
                        R.string.app_copyright_year,
                        YearRepository.getCurrentYear()
                    )
                }
            }
        }

    interface TournamentsListener {
        fun onTournamentClicked(tournamentsItem: TournamentsItem)
        fun onLoadMoreClicked()
        fun onRefresh()
    }
}
