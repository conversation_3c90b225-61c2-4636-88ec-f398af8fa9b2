package com.abrand.custom.data.mappers

import com.abrand.custom.GetTransactionQuery
import com.abrand.custom.GetTransactionsQuery
import com.abrand.custom.data.entity.HistoryFilter
import com.abrand.custom.data.entity.Transaction
import com.abrand.custom.fragment.BaseTransaction
import com.abrand.custom.type.PaymentCompoundOperation
import com.abrand.custom.type.TransactionStatus

fun List<GetTransactionsQuery.Transaction?>?.mapToLocalTransactions(): List<Transaction> {
    val result = arrayListOf<Transaction>()

    if (this != null) {
        for (item in this) {
            if (item != null) {
                result.add(item.mapToLocalTransaction())
            }
        }
    }

    return result
}

fun GetTransactionsQuery.Transaction.mapToLocalTransaction(): Transaction {
    return if (this.asCompoundTransaction is GetTransactionsQuery.AsCompoundTransaction) {
        val serverBaseTransaction = this.asCompoundTransaction.fragments.baseTransaction
        val baseTransaction = serverBaseTransaction?.mapTopLocalBaseTransaction()
        val children = this.asCompoundTransaction.children?.let {
            getChildrenTransactions(it.filterNotNull().toMutableList())
        }
        Transaction(baseTransaction, children)
    } else if (this.asTransaction is GetTransactionsQuery.AsTransaction) {
        val serverBaseTransaction = this.asTransaction.fragments.baseTransaction
        val baseTransaction = serverBaseTransaction?.mapTopLocalBaseTransaction()
        Transaction(baseTransaction)
    } else {
        Transaction()
    }
}

private fun getChildrenTransactions(transactionChildren: MutableList<GetTransactionsQuery.Child>): MutableList<Transaction.LocalBaseTransaction> {
    val children: MutableList<Transaction.LocalBaseTransaction> = mutableListOf()
    for (transaction in transactionChildren) {
        transaction.fragments.baseTransaction?.let { children.add(it.mapTopLocalBaseTransaction()) }
    }
    return children
}

fun GetTransactionQuery.Transaction.mapToLocalTransaction(): Transaction {
    return if (this.asCompoundTransaction is GetTransactionQuery.AsCompoundTransaction) {
        val serverBaseTransaction = this.asCompoundTransaction.fragments.baseTransaction
        val baseTransaction = serverBaseTransaction?.mapTopLocalBaseTransaction()
        val children = this.asCompoundTransaction.children?.let {
            getChildrenTransaction(it.filterNotNull().toMutableList())
        }
        Transaction(baseTransaction, children)
    } else if (this.asTransaction is GetTransactionQuery.AsTransaction) {
        val serverBaseTransaction = this.asTransaction.fragments.baseTransaction
        val baseTransaction = serverBaseTransaction?.mapTopLocalBaseTransaction()
        Transaction(baseTransaction)
    } else {
        Transaction()
    }
}

private fun getChildrenTransaction(transactionChildren: MutableList<GetTransactionQuery.Child>): MutableList<Transaction.LocalBaseTransaction> {
    val children: MutableList<Transaction.LocalBaseTransaction> = mutableListOf()
    for (transaction in transactionChildren) {
        transaction.fragments.baseTransaction?.let { children.add(it.mapTopLocalBaseTransaction()) }
    }
    return children
}

fun BaseTransaction.mapTopLocalBaseTransaction(): Transaction.LocalBaseTransaction {
    return Transaction.LocalBaseTransaction(
        this.id,
        this.direction,
        this.amount,
        Transaction.LocalBaseTransaction.Currency(this.currency?.code),
        this.date,
        this.status,
        this.operationType,
        this.paymentSystemName,
        this.userComment,
        this.rejectionEnable
    )
}

fun List<HistoryFilter.Operation>.toListPaymentCompoundOperation(): List<PaymentCompoundOperation> {
    val result = arrayListOf<PaymentCompoundOperation>()

    for (item in this) {
        when (item) {
            HistoryFilter.Operation.IN -> result.add(PaymentCompoundOperation.IN)
            HistoryFilter.Operation.OUT -> result.add(PaymentCompoundOperation.OUT)
            HistoryFilter.Operation.BONUSES_PRIZES -> {
                result.add(PaymentCompoundOperation.BONUSES)
                result.add(PaymentCompoundOperation.PRIZES)
            }
            HistoryFilter.Operation.POINTS_EXCHANGE -> result.add(PaymentCompoundOperation.POINTS_EXCHANGE)
            HistoryFilter.Operation.OTHERS -> result.add(PaymentCompoundOperation.OTHERS)
            else -> {}
        }
    }

    return result
}

fun List<HistoryFilter.Status>.toListTransactionStatus(): List<TransactionStatus> {
    val result = arrayListOf<TransactionStatus>()

    for (item in this) {
        when (item) {
            HistoryFilter.Status.NEW -> result.add(TransactionStatus.NEW)
            HistoryFilter.Status.SUCCESS -> result.add(TransactionStatus.SUCCESS)
            HistoryFilter.Status.USER_CANCELLED -> result.add(TransactionStatus.USER_CANCELLED)
            HistoryFilter.Status.FAIL -> result.add(TransactionStatus.FAIL)
            else -> {}
        }
    }

    return result
}

