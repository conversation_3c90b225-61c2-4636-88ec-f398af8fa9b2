package com.abrand.custom.ui.lotteryinternal

import android.app.Dialog
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.os.*
import android.text.*
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.*
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.UrlSource
import com.abrand.custom.data.entity.User
import com.abrand.custom.data.entity.lottery.*
import com.abrand.custom.databinding.FragmentLotteryInternalBinding
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.readTextFromAsset
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment
import com.abrand.custom.ui.views.textfield.TextField
import com.squareup.picasso.Picasso
import com.squareup.picasso.Target
import java.util.*
import java.util.concurrent.TimeUnit

class LotteryInternalFragment : Fragment() {
    private var binding: FragmentLotteryInternalBinding? = null
    private val viewModel: LotteryInternalViewModel by viewModels()
    private var lottery: LocalLottery? = null
    private var lotteryId: Int? = null
    private var isNeedOpenResults: Boolean? = null
    private var selectedTabType = TabType.CONDITIONS
    private var ticketsPackageAdapter: TicketsPackageAdapter? = null
    private var ticketsAdapter: TicketsAdapter? = null

    companion object {
        val COLS_COUNT = 3
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentLotteryInternalBinding.inflate(inflater, container, false)

        arguments?.let {
            lottery = it.getSerializable(MainActivity.ITEM) as LocalLottery?
            lotteryId = it.getInt(MainActivity.LOTTERY_ID)
            isNeedOpenResults = it.getBoolean(MainActivity.IS_NEED_OPEN_RESULTS)
        }

        setupInsets()
        observeViewModel()

        lottery?.id?.let {
            viewModel.getLottery(User.State.PLAYER == Settings.get().userState, it)
            fillHeader()
        }
        lotteryId?.let {
            if (it > 0) {
                viewModel.getLottery(User.State.PLAYER == Settings.get().userState, it)
            }
        }

        return binding?.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun setupInsets() {
        binding?.rootScrollView?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it) { view, insets ->
                view.updatePadding(
                    top = insets.systemWindowInsetTop,
                    bottom = insets.systemWindowInsetBottom
                )

                val insetBottom = insets.systemWindowInsetBottom
                if (insetBottom > 150) {
                    scrollToBuyTicketsButton()
                }

                insets
            }
        }
    }

    private fun observeViewModel() {
        viewModel.lotteryLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    it.data?.apply {
                        lottery = this
                        fillHeader()
                        fillConditions(this.textMobile)
                        fillPrizes(this)
                        if (isNeedOpenResults == true) {
                            binding?.tabPrizes?.performClick()
                        }
                        fillTickets(this.userTickets)
                        fillBuyTickets()
                    }
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(it.failure)
                    }
                }
                else -> {}
            }
        }

        viewModel.buyTicketsLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    it.data?.apply {
                        lottery?.userTickets = this
                        fillTickets(lottery?.userTickets)
                        openTicketsTab()
                    }
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(it.failure)
                    }
                }
                else -> {}
            }
        }

        viewModel.getPrizeLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    (activity as MainActivity).openPaymentsScreenWithoutClearStack(PaymentsFragment.Screen.HISTORY)
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    (activity as MainActivity).showConnectionIssueMessage(it.failure)
                }
                else -> {}
            }
        }
    }

    private fun fillHeader() {
        context?.let { setBackground(lottery?.bannerMobile, it) }

        binding?.tvName?.text = lottery?.name
        setPrizeSum()
        setTime()

        var localLotteryWinner : LocalLotteryWinner? = null
        lottery?.winners?.apply {
            for (winner in this) {
                if (Settings.get().userId == winner.userId) {
                    localLotteryWinner = winner
                }
            }
        }

        if (LocalLotteryStatus.IN_PROCESS == lottery?.status) {
            binding?.btnHeader?.text = getString(R.string.lottery_get_tickets)
            binding?.btnHeader?.visibility = View.VISIBLE
            binding?.btnHeader?.setOnClickListener {
                if (User.State.PLAYER == Settings.get().userState) {
                    openBuyTicketsTab()
                    scrollToBuyTicketsButton()
                } else {
                    openRegisterScreen()
                }
            }
        } else if (LocalLotteryStatus.COMPLETED == lottery?.status &&
            localLotteryWinner?.id != null && localLotteryWinner?.distributed == false) {
            binding?.btnHeader?.text = getString(R.string.lottery_get_prize)
            binding?.btnHeader?.visibility = View.VISIBLE
            binding?.btnHeader?.setOnClickListener {
                localLotteryWinner?.id?.let {
                    viewModel.getPrize(it)
                }
            }
        } else {
            binding?.btnHeader?.visibility = View.GONE
        }

        setTabs()
    }

    private fun setPrizeSum() {
        val prizeFund = if (lottery?.prizeFundByString.isNullOrEmpty()) {
            val formattedPrizeSum =
                GeneralTools.formatBalance(Settings.get().userCurrencyCode, lottery?.prizesSum)
            val spannableString = SpannableStringBuilder(formattedPrizeSum)
            spannableString.setSpan(
                StyleSpan(Typeface.BOLD),
                0,
                formattedPrizeSum.length - 2,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            spannableString
        } else {
            lottery?.prizeFundByString
        }

        binding?.tvFund?.text = prizeFund
    }

    private fun setBackground(imageUrl: String?, context: Context) {
        if (!TextUtils.isEmpty(imageUrl)) {
            Picasso.get()
                .load(ApoloConfig.getFullUrl(imageUrl))
                .resize(
                    GeneralTools.getScreenWidth(context),
                    context.resources.getDimensionPixelSize(R.dimen.news_internal_image_bg_height)
                )
                .centerCrop()
                .into(picassoBackgroundTarget)
        }
    }

    private val picassoBackgroundTarget = object : Target {
        override fun onBitmapLoaded(bitmap: Bitmap, from: Picasso.LoadedFrom) {
            val roundedBitmapDrawable =
                context?.let { RoundedBitmapDrawableFactory.create(it.resources, bitmap) }
            roundedBitmapDrawable?.cornerRadius = 0f
            binding?.ivImage?.background = roundedBitmapDrawable
        }

        override fun onBitmapFailed(e: Exception, errorDrawable: Drawable?) {}

        override fun onPrepareLoad(placeHolderDrawable: Drawable?) {}
    }

    private fun setTime() {
        if (LocalLotteryStatus.COMPLETED == lottery?.status) {
            binding?.tvDays?.text = getString(
                R.string.lottery_period_value,
                DateTools.getDayMonth(context, lottery?.startAt),
                DateTools.getDayMonth(context, lottery?.finishAt)
            )
            binding?.tvDaysText?.text = this.resources.getString(R.string.lottery_period_label)
        } else {
            val endTime = DateTools.getServerDate(context, lottery?.finishAt).time
            val currentTime = Calendar.getInstance().timeInMillis
            val days = (endTime - currentTime) / (DateTools.MILLIS_IN_DAY)
            if (days < 2) {
                val timer = object : CountDownTimer(endTime - currentTime, 1000) {
                    var hms: String = ""
                    override fun onTick(millisUntilFinished: Long) {
                        hms = String.format(
                            "%02d:%02d:%02d", TimeUnit.MILLISECONDS.toHours(millisUntilFinished),
                            TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished) - TimeUnit.HOURS.toMinutes(
                                TimeUnit.MILLISECONDS.toHours(millisUntilFinished)
                            ),
                            TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) - TimeUnit.MINUTES.toSeconds(
                                TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)
                            )
                        )
                        if (binding?.tvDays != null)
                            binding?.tvDays?.text = hms
                    }

                    override fun onFinish() {
                    }
                }
                timer.start()
            } else {
                binding?.tvDays?.text =
                    this.resources.getQuantityString(R.plurals.days, days.toInt(), days.toInt())
            }
        }
    }

    private fun setTabs() {
        binding?.tabConditions?.setOnClickListener {
            selectTab(TabType.CONDITIONS)
            showTabBody(TabType.CONDITIONS)
        }

        binding?.tabPrizes?.setOnClickListener {
            selectTab(TabType.PRIZES)
            showTabBody(TabType.PRIZES)
        }

        binding?.tabYourTickets?.setOnClickListener {
            selectTab(TabType.YOUR_TICKETS)
            showTabBody(TabType.YOUR_TICKETS)
        }
        binding?.tabBuyTickets?.setOnClickListener {
            selectTab(TabType.BUY_TICKETS)
            showTabBody(TabType.BUY_TICKETS)
        }

        if (User.State.PLAYER != Settings.get().userState) {
            binding?.tabYourTickets?.visibility = View.GONE
            binding?.tabBuyTickets?.visibility = View.GONE
            binding?.tabGhost1?.visibility = View.VISIBLE
            binding?.tabGhost2?.visibility = View.VISIBLE
        }

        if (LocalLotteryStatus.COMPLETED == lottery?.status) {
            binding?.tabBuyTickets?.visibility = View.GONE
            binding?.tabGhost2?.visibility = View.VISIBLE
        }

        binding?.tabConditions?.performClick()
    }

    private fun showTabBody(tabType: TabType) {
        binding?.bodyConditions?.visibility = View.GONE
        binding?.bodyPrizes?.visibility = View.GONE
        binding?.bodyWinners?.visibility = View.GONE
        binding?.bodyYourTickets?.root?.visibility = View.GONE
        binding?.bodyBuyTickets?.root?.visibility = View.GONE

        when (tabType) {
            TabType.CONDITIONS -> binding?.bodyConditions?.visibility = View.VISIBLE
            TabType.PRIZES -> {
                if (LocalLotteryStatus.IN_PROCESS == lottery?.status) {
                    binding?.bodyPrizes?.visibility = View.VISIBLE
                    binding?.bodyWinners?.visibility = View.GONE
                } else {
                    binding?.bodyPrizes?.visibility = View.GONE
                    binding?.bodyWinners?.visibility = View.VISIBLE
                }
            }
            TabType.YOUR_TICKETS -> binding?.bodyYourTickets?.root?.visibility = View.VISIBLE
            TabType.BUY_TICKETS -> binding?.bodyBuyTickets?.root?.visibility = View.VISIBLE
        }
    }

    private fun selectTab(tabType: TabType) {
        when (tabType) {
            TabType.CONDITIONS -> selectTab(
                TabType.CONDITIONS,
                R.drawable.ic_book,
                binding?.ivConditions,
                binding?.tvConditions,
                binding?.tabConditions
            )
            TabType.PRIZES -> selectTab(
                TabType.PRIZES,
                R.drawable.ic_prize,
                binding?.ivPrizes,
                binding?.tvPrizes,
                binding?.tabPrizes
            )
            TabType.YOUR_TICKETS -> selectTab(
                TabType.YOUR_TICKETS,
                R.drawable.ic_your_tickets,
                binding?.ivYourTickets,
                binding?.tvYourTickets,
                binding?.tabYourTickets
            )
            else -> {
                selectTab(
                    TabType.BUY_TICKETS,
                    R.drawable.ic_buy_tickets,
                    binding?.ivBuyTickets,
                    binding?.tvBuyTickets,
                    binding?.tabBuyTickets
                )
            }
        }
    }

    private fun selectTab(
        tabType: TabType,
        drawableId: Int,
        imageView: ImageView?,
        textView: TextView?,
        bg: RelativeLayout?
    ) {
        val ICON_SELECTED_GRADIENT_START = "#FF6ED200"
        val ICON_SELECTED_GRADIENT_END = "#FF008E00"
        val TEXT_SELECTED_COLOR = "#FF008E00"
        setTabUnSelected(selectedTabType)
        selectedTabType = tabType

        val bitmap = GeneralTools.getBitmapFromVectorDrawable(context, drawableId)
        imageView?.setImageBitmap(
            GeneralTools.addGradient(
                bitmap,
                ICON_SELECTED_GRADIENT_START,
                ICON_SELECTED_GRADIENT_END
            )
        )
        textView?.setTextColor(Color.parseColor(TEXT_SELECTED_COLOR))
        bg?.background = context?.let { ContextCompat.getDrawable(it, R.drawable.bg_lottery_tab) }
    }

    private fun setTabUnSelected(selectedGameType: TabType) {
        val TEXT_DEFAULT_COLOR = Color.WHITE
        val TEXT_DEFAULT_BG = Color.TRANSPARENT
        when (selectedGameType) {
            TabType.CONDITIONS -> {
                binding?.ivConditions?.setImageDrawable(context?.let { ContextCompat.getDrawable(it, R.drawable.ic_book) })
                binding?.tvConditions?.setTextColor(TEXT_DEFAULT_COLOR)
                binding?.tabConditions?.setBackgroundColor(TEXT_DEFAULT_BG)
            }
            TabType.PRIZES -> {
                binding?.ivPrizes?.setImageDrawable(context?.let { ContextCompat.getDrawable(it, R.drawable.ic_prize) })
                binding?.tvPrizes?.setTextColor(TEXT_DEFAULT_COLOR)
                binding?.tabPrizes?.setBackgroundColor(TEXT_DEFAULT_BG)
            }
            TabType.YOUR_TICKETS -> {
                binding?.ivYourTickets?.setImageDrawable(context?.let { ContextCompat.getDrawable(it, R.drawable.ic_your_tickets) })
                binding?.tvYourTickets?.setTextColor(TEXT_DEFAULT_COLOR)
                binding?.tabYourTickets?.setBackgroundColor(TEXT_DEFAULT_BG)
            }
            TabType.BUY_TICKETS -> {
                binding?.ivBuyTickets?.setImageDrawable(context?.let {
                    ContextCompat.getDrawable(it, R.drawable.ic_buy_tickets) })
                binding?.tvBuyTickets?.setTextColor(TEXT_DEFAULT_COLOR)
                binding?.tabBuyTickets?.setBackgroundColor(TEXT_DEFAULT_BG)
            }
        }
    }

    private fun fillConditions(htmlConditions: String?) {
        val CSS_ASSET_FILE_NAME = "lottery_conditions.css"
        binding?.wvConditions?.setBackgroundColor(Color.TRANSPARENT)
        binding?.wvConditions?.settings?.apply {
            javaScriptEnabled = true
        }
        binding?.wvConditions?.webViewClient = object : WebViewClient() {

            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                (activity as MainActivity).processDeepLinkUrl(url, UrlSource.NEWS)
                return true
            }

            @RequiresApi(Build.VERSION_CODES.KITKAT)
            override fun onPageFinished(view: WebView, url: String) {
                val css = context?.readTextFromAsset(CSS_ASSET_FILE_NAME)
                val js = "var style = document.createElement('style'); style.innerHTML = `$css`; document.head.appendChild(style);"
                binding?.wvConditions?.evaluateJavascript(js, null)
                binding?.wvConditions?.visibility = View.VISIBLE
                super.onPageFinished(view, url)
            }
        }

        binding?.wvConditions?.loadDataWithBaseURL(ApoloConfig.BASE_URL,
            htmlConditions ?: "", "text/html", "UTF-8", "")
    }

    private fun fillPrizes(localLottery: LocalLottery) {
        if (LocalLotteryStatus.IN_PROCESS == localLottery.status) {
            localLottery.prizes?.apply {
                binding?.rvPrizes?.layoutManager = LinearLayoutManager(context)
                binding?.rvPrizes?.adapter = PrizesAdapter(this)
            }
        } else {
            localLottery.winners?.apply {
                binding?.rvWinners?.layoutManager = LinearLayoutManager(context)
                binding?.rvWinners?.adapter = WinnersAdapter(this)
            }
        }
    }

    private fun fillTickets(tickets: List<LocalLotteryTicket>?) {
        val ticketsCount = tickets?.size ?: 0
        if (ticketsCount > 0) {
            binding?.bodyYourTickets?.tvLotteryTicketsCount?.text =
                this.resources.getQuantityString(R.plurals.have_lottery_tickets, ticketsCount, ticketsCount)
            binding?.bodyYourTickets?.tvLotteryNoTicketsDescription?.visibility = View.GONE
        } else {
            binding?.bodyYourTickets?.tvLotteryTicketsCount?.text =
                context?.getString(R.string.lottery_no_tickets_title)
            binding?.bodyYourTickets?.tvLotteryNoTicketsDescription?.visibility = View.VISIBLE
        }

        tickets?.apply {
            if (this.isEmpty()) {
                return@apply
            }

            val numberItemsToDisplay = if (this.size > LotteryInternalViewModel.TICKETS_LOAD_STEP) {
                LotteryInternalViewModel.TICKETS_LOAD_STEP
            } else {
                this.size
            }
            val ticketItems = mutableListOf<LotteryTicketItem>()
            repeat(numberItemsToDisplay) {
                ticketItems.add(LotteryTicketItem(LotteryTicketItem.ItemType.TICKET, this[it]))
            }
            ticketItems.add(LotteryTicketItem(LotteryTicketItem.ItemType.TICKETS_LOAD))
            ticketsAdapter = TicketsAdapter(ticketItems)
            ticketsAdapter?.currentTotalTickets = ticketsCount
            ticketsAdapter?.listener = object : TicketsAdapter.Listener {
                override fun onLoadMoreClicked() {
                    val currentTicketsCount = ticketsAdapter?.getTicketsCount()
                    if (currentTicketsCount != null) {
                        ticketsAdapter?.setTicketsLoadingState(true)
                        var nextTicketsCount = currentTicketsCount + LotteryInternalViewModel.TICKETS_LOAD_STEP
                        if (nextTicketsCount > ticketsCount) {
                            nextTicketsCount = ticketsCount
                        }
                        val nextTicketItems = mutableListOf<LotteryTicketItem>()
                        for (index in currentTicketsCount until nextTicketsCount) {
                            nextTicketItems.add(LotteryTicketItem(LotteryTicketItem.ItemType.TICKET, tickets[index]))
                        }

                        ticketsAdapter?.appendList(nextTicketItems)
                        ticketsAdapter?.setTicketsLoadingState(false)
                    }
                }
            }

            val layoutManager = GridLayoutManager(context, COLS_COUNT)
            layoutManager.spanSizeLookup = object : SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return when (ticketsAdapter?.getItemViewType(position)) {
                        LotteryTicketItem.ItemType.TICKETS_LOAD.id -> COLS_COUNT
                        else -> 1
                    }
                }
            }
            binding?.bodyYourTickets?.rvYourTickets?.layoutManager = layoutManager
            binding?.bodyYourTickets?.rvYourTickets?.adapter = ticketsAdapter
        }
    }

    private fun fillBuyTickets() {
        if (LocalLottery.RedemptionType.NO_REDEMPTION == lottery?.redemptionType) {
            binding?.btnHeader?.visibility = View.GONE
            binding?.tabBuyTickets?.visibility = View.GONE
            binding?.tabGhost2?.visibility = View.VISIBLE
            return
        }

        lottery?.ticketPackages?.apply {
            binding?.bodyBuyTickets?.rvTicketPackages?.layoutManager = GridLayoutManager(context, 2)
            ticketsPackageAdapter = TicketsPackageAdapter(this)
            ticketsPackageAdapter?.listener = object : TicketsPackageAdapter.Listener {
                override fun onPackageSelected(isSelected: Boolean) {
                    binding?.bodyBuyTickets?.btnBuyPackage?.isEnabled = isSelected
                }
            }
            binding?.bodyBuyTickets?.rvTicketPackages?.adapter = ticketsPackageAdapter

            binding?.bodyBuyTickets?.btnBuyPackage?.setOnClickListener {
                val selectedPosition = ticketsPackageAdapter?.selectedPosition
                if (selectedPosition != null && selectedPosition >= 0) {
                    val lotteryTicketPackage = lottery?.ticketPackages?.get(selectedPosition)
                    val ticketCount = lotteryTicketPackage?.ticketCount
                    val priceWithDiscount = lotteryTicketPackage?.priceWithDiscount
                    val lotteryId = lottery?.id
                    if (lotteryId != null && ticketCount != null && priceWithDiscount != null) {
                        if (isEnoughFunds(priceWithDiscount)) {
                            viewModel.buyTicketsPackage(lotteryId, ticketCount)
                        } else {
                            showNotEnoughFundsDialog()
                        }
                    }
                }
            }
        }

        if (lottery?.ticketPackages?.isEmpty() == true) {
            binding?.bodyBuyTickets?.tvTicketPackagesLabel?.visibility = View.GONE
            binding?.bodyBuyTickets?.rvTicketPackages?.visibility = View.GONE
            binding?.bodyBuyTickets?.btnBuyPackage?.visibility = View.GONE

            val params: ViewGroup.MarginLayoutParams =
                binding?.bodyBuyTickets?.tvBuyQuantityLabel?.layoutParams as ViewGroup.MarginLayoutParams
            params.topMargin = 0
        }

        binding?.bodyBuyTickets?.apply {
            etTickets.setInputType(InputType.TYPE_CLASS_NUMBER)
            etTickets.setMaxLength(8)
            etTickets.subscribeToUpdates(etTicketsListener)
            etTickets.subscribeToAfterTextChangedUpdates(zeroInputBlock)
            etTickets.text = LotteryInternalViewModel.DEFAULT_NUMBER_OF_TICKETS_TO_BUY
            etTickets.setTextInfo(getString(R.string.lottery_tickets_quantity_short))
            etPrice.setFieldNotEditable()
            etPrice.setTextInfo(Settings.get().userCurrencySymbol)
            btnBuyQuantity.setOnClickListener {
                val tickets = if (etTickets.text.isEmpty()) {
                    0
                } else {
                    etTickets.text.toInt()
                }

                val lotteryId = lottery?.id
                val ticketPrice = lottery?.ticketPrice
                if (lotteryId != null && ticketPrice != null && tickets > 0) {
                    if (isEnoughFunds(ticketPrice, tickets)) {
                        viewModel.buyTickets(lotteryId, tickets)
                        GeneralTools.hideKeyboard(activity)
                    } else {
                        showNotEnoughFundsDialog()
                    }
                }
            }

            ivBuyQuantityInfo.setOnClickListener {
                (activity as? MainActivity)?.showMessage(getString(R.string.lottery_buy_tickets_info))
            }
        }
    }

    private val etTicketsListener = TextField.UpdateListener { data: String?, _: Boolean ->
        val tickets = if (data.isNullOrEmpty()) {
            0
        } else {
            try {
                data.toLong()
            } catch (e: NumberFormatException) {
                // That will block buy button and block ability to process incorrect amount of tickets
                0
            }
        }
        binding?.bodyBuyTickets?.btnBuyQuantity?.isEnabled = tickets > 0
        binding?.bodyBuyTickets?.etPrice?.text = lottery?.ticketPrice?.toLong()?.times(tickets).toString()
    }

    private val zeroInputBlock = TextField.UpdateListener { data: String?, _: Boolean ->
        data?.let {input ->
            if (input.length > 1 && input.startsWith("0")) {
                // TODO: Here can be more than one zero. Can be optimized
                val fixedInput = input.substring(1)
                binding?.bodyBuyTickets?.etTickets?.setTextKeepState(fixedInput)
            }
        }
    }

    private fun openTicketsTab() {
        binding?.tabYourTickets?.performClick()
    }

    private fun openBuyTicketsTab() {
        binding?.tabBuyTickets?.performClick()
    }

    private fun scrollToBuyTicketsButton() {
        binding?.rootScrollView?.post {
            binding?.bodyBuyTickets?.btnBuyQuantity?.top?.let { scrollPosition ->
                binding?.rootScrollView?.smoothScrollTo(0, scrollPosition)
            }
        }
    }

    private fun isEnoughFunds(ticketPrice: Double, tickets: Int): Boolean {
        return ticketPrice * tickets <= (activity as MainActivity).balance
    }

    private fun isEnoughFunds(priceWithDiscount: Double): Boolean {
        return priceWithDiscount <= (activity as MainActivity).balance
    }

    private fun showNotEnoughFundsDialog() {
        val dialog = context?.let { Dialog(it) }
        dialog?.setContentView(R.layout.dialog_lottery_not_enough_funds)
        dialog?.findViewById<View>(R.id.ib_close)
            ?.setOnClickListener { v: View? -> dialog.cancel() }

        val btnReplenishAccount = dialog?.findViewById<Button>(R.id.btn_replenish_account)
        btnReplenishAccount?.setOnClickListener {
            (activity as? MainActivity)?.openPaymentsScreenWithoutClearStack(PaymentsFragment.Screen.DEPOSIT)
            dialog.cancel()
        }

        val dialogView = dialog?.window?.decorView
        dialogView?.setBackgroundResource(android.R.color.transparent)

        dialog?.show()
    }

    private fun openRegisterScreen() {
        if (activity != null && activity is MainActivity) {
            (activity as MainActivity).showEnterScreenFromLottery(false, lottery)
        }
    }
}

enum class TabType {
    CONDITIONS, PRIZES, YOUR_TICKETS, BUY_TICKETS
}
