<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:orientation="vertical">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="#ECEFF1"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:background="@drawable/country_search_bg">

            <EditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="@dimen/size_16"
                android:layout_toLeftOf="@+id/btn_search_clear"
                android:background="@null"
                android:hint="@string/country_search"
                android:textColor="#000000"
                android:textColorHint="#9e9e9e" />

            <ImageButton
                android:id="@+id/btn_search_clear"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@null"
                android:src="@drawable/ic_close" />
        </RelativeLayout>
    </FrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_countries"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</LinearLayout>