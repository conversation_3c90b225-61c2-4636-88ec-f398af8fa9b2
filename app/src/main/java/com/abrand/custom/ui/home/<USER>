package com.abrand.custom.ui.home

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.*
import com.abrand.custom.interfaces.*
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException

class HomeViewModel : ViewModel() {
    val slidesLiveData = SingleLiveEvent<Resource<MutableList<Banner>, ServerError, ApolloException>>()
    val gamesLiveData = MutableLiveData<Resource<LoadGamesResult, ServerError, ApolloException>>()

    companion object {
        const val LIVE_CASINO_TYPE_SLUG = "live"
    }

    private val _homeAdditionalBlocksLiveData = SingleLiveEvent<Resource<HomeAdditionalBlocks, ServerError, ApolloException>>()
    val homeAdditionalBlocksLiveData: LiveData<Resource<HomeAdditionalBlocks, ServerError, ApolloException>> = _homeAdditionalBlocksLiveData

    fun getSlides(refCode: String) {
        ApolloProcessorKt.getSlides(object : GetSlidesTarget {
            override fun onSuccess(banners: MutableList<Banner>) {
                slidesLiveData.postValue(Resource.success(banners))
            }

            override fun onFailure(e: ApolloException) {
                slidesLiveData.postValue(Resource.failure(e))
            }
        }, viewModelScope)
    }

    //unused, temporary left here as example
//    fun loadGamesByType(type: GameTypeField, loadAmount: Int, offset: Int) {
//        ApolloProcessorKt.loadGamesByType(type, loadAmount, offset, null, null,
//                object : LoadTarget {
//                    override fun onSuccess(gameThumbs: MutableList<GameThumb>, total: Int, offset: Int) {
//                        gamesLiveData.postValue(Resource.success(LoadGamesResult(gameThumbs, total, offset)))
//                    }
//
//                    override fun onFailure(e: ApolloException) {
//                        gamesLiveData.postValue(Resource.failure(e))
//                    }
//                }, viewModelScope)
//    }

//    fun loadByAttribute(attribute: GameAttributeType, loadAmount: Int, offset: Int) {
//        ApolloProcessorKt.loadByAttribute(attribute, loadAmount, offset, null, null, object : LoadTarget {
//            override fun onSuccess(gameThumbs: MutableList<GameThumb>, total: Int, offset: Int) {
//                gamesLiveData.postValue(Resource.success(LoadGamesResult(gameThumbs, total, offset)))
    fun loadPopularGames(loadAmount: Int, offset: Int) {
        ApolloProcessorKt.loadPopularGames(loadAmount, offset, null, null, object:LoadTarget {
            override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                gamesLiveData.postValue(Resource.success(LoadGamesResult(localGameItems, total, offset)))
            }

            override fun onFailure(e: ApolloException) {
                gamesLiveData.postValue(Resource.failure(e))
            }
        }, viewModelScope)
    }

    fun loadNewGames(loadAmount: Int, offset: Int) {
        ApolloProcessorKt.loadNewGames(loadAmount, offset, null, null, object:LoadTarget {
            override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                gamesLiveData.postValue(Resource.success(LoadGamesResult(localGameItems, total, offset)))
            }

            override fun onFailure(e: ApolloException) {
                gamesLiveData.postValue(Resource.failure(e))
            }
        }, viewModelScope)
    }

    fun loadSlotGames(loadAmount: Int, offset: Int) {
        ApolloProcessorKt.loadSlotGames(loadAmount, offset, null, null, object:LoadTarget {
            override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                gamesLiveData.postValue(Resource.success(LoadGamesResult(localGameItems, total, offset)))
            }

            override fun onFailure(e: ApolloException) {
                gamesLiveData.postValue(Resource.failure(e))
            }
        }, viewModelScope)
    }

    fun loadTableGames(loadAmount: Int, offset: Int) {
        ApolloProcessorKt.loadTableGames(loadAmount, offset, null, null, object:LoadTarget {
            override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                gamesLiveData.postValue(Resource.success(LoadGamesResult(localGameItems, total, offset)))
            }

            override fun onFailure(e: ApolloException) {
                gamesLiveData.postValue(Resource.failure(e))
            }
        }, viewModelScope)
    }

    fun loadFavouriteGames(loadAmount: Int, offset: Int) {
        ApolloProcessorKt.loadFavouriteGames(null, null, loadAmount, offset,
                object : LoadTarget {
                    override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                        gamesLiveData.postValue(Resource.success(LoadGamesResult(localGameItems, total, offset)))
                    }

                    override fun onFailure(e: ApolloException) {
                        gamesLiveData.postValue(Resource.failure(e))
                    }
                }, viewModelScope)
    }

    fun loadLiveCasinoGames(loadAmount: Int, offset: Int) {
        ApolloProcessorKt.loadGamesByType(LIVE_CASINO_TYPE_SLUG, loadAmount, offset,
            object : LoadTarget {
                override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                    gamesLiveData.postValue(Resource.success(LoadGamesResult(localGameItems, total, offset)))
                }

                override fun onFailure(e: ApolloException) {
                    gamesLiveData.postValue(Resource.failure(e))
                }
            }, viewModelScope)
    }

    fun getHomeAdditionalBlocks() {
        ApolloProcessorKt.getHomeAdditionalBlocks(object : GenericTarget<HomeAdditionalBlocks> {
            override fun onSuccess(t: HomeAdditionalBlocks?) {
                _homeAdditionalBlocksLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _homeAdditionalBlocksLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _homeAdditionalBlocksLiveData.postValue(Resource.failure(e))
            }
        })
    }
}
