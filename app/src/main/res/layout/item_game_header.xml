<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager_banner"
        android:layout_width="match_parent"
        android:layout_height="@dimen/home_banner_height"
        android:overScrollMode="never"
        app:layout_constraintTop_toTopOf="parent" />

    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="@+id/view_pager_banner">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_tabs_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/view_pager_banner">

            <View
                android:id="@+id/v_selector"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/selector_bg"
                app:layout_constraintBottom_toBottomOf="@+id/ib_main_best_btn"
                app:layout_constraintLeft_toLeftOf="@+id/ib_main_best_btn"
                app:layout_constraintRight_toRightOf="@+id/ib_main_best_btn"
                app:layout_constraintTop_toTopOf="@+id/ib_main_best_btn" />

            <RelativeLayout
                android:id="@+id/ib_main_best_btn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/home_tab_height"
                android:background="@drawable/categories_bg"
                android:minWidth="@dimen/home_tab_min_width"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginTop="16dp">

                <ImageView
                    android:id="@+id/iv_best"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/tv_best"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_best" />

                <TextView
                    android:id="@+id/tv_best"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/size_16"
                    android:paddingLeft="@dimen/home_tab_padding_left"
                    android:paddingRight="@dimen/home_tab_padding_right"
                    android:text="@string/best_games"
                    android:textAllCaps="true"
                    android:textColor="@color/white" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ib_main_new_btn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/home_tab_height"
                android:background="@drawable/categories_bg"
                android:minWidth="@dimen/home_tab_min_width"
                app:layout_constraintBottom_toBottomOf="@+id/ib_main_best_btn"
                app:layout_constraintLeft_toRightOf="@+id/ib_main_best_btn"
                app:layout_constraintTop_toTopOf="@+id/ib_main_best_btn">

                <ImageView
                    android:id="@+id/iv_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/tv_new"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_new" />

                <TextView
                    android:id="@+id/tv_new"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/size_16"
                    android:paddingLeft="@dimen/home_tab_padding_left"
                    android:paddingRight="@dimen/home_tab_padding_right"
                    android:text="@string/new_games"
                    android:textAllCaps="true"
                    android:textColor="@color/white" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ib_main_favourites_btn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/home_tab_height"
                android:background="@drawable/categories_bg"
                android:minWidth="@dimen/home_tab_min_width"
                app:layout_constraintBottom_toBottomOf="@+id/ib_main_best_btn"
                app:layout_constraintLeft_toRightOf="@+id/ib_main_new_btn"
                app:layout_constraintTop_toTopOf="@+id/ib_main_best_btn">

                <ImageView
                    android:id="@+id/iv_favourites"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/tv_favourites"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_favourite" />

                <TextView
                    android:id="@+id/tv_favourites"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/size_16"
                    android:paddingLeft="@dimen/home_tab_padding_left"
                    android:paddingRight="@dimen/home_tab_padding_right"
                    android:text="@string/favorite_games"
                    android:textAllCaps="true"
                    android:textColor="@color/white" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ib_main_slots_btn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/home_tab_height"
                android:background="@drawable/categories_bg"
                android:minWidth="@dimen/home_tab_min_width"
                app:layout_constraintBottom_toBottomOf="@+id/ib_main_best_btn"
                app:layout_constraintLeft_toRightOf="@+id/ib_main_favourites_btn"
                app:layout_constraintTop_toTopOf="@+id/ib_main_best_btn">

                <ImageView
                    android:id="@+id/iv_slots"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/tv_slots"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_cherries" />

                <TextView
                    android:id="@+id/tv_slots"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/size_16"
                    android:paddingLeft="@dimen/home_tab_padding_left"
                    android:paddingRight="@dimen/home_tab_padding_right"
                    android:text="@string/slots"
                    android:textAllCaps="true"
                    android:textColor="@color/white" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ib_main_tables_btn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/home_tab_height"
                android:background="@drawable/categories_bg"
                android:minWidth="@dimen/home_tab_min_width"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/ib_main_best_btn"
                app:layout_constraintLeft_toRightOf="@+id/ib_main_slots_btn"
                app:layout_constraintTop_toTopOf="@+id/ib_main_best_btn">

                <ImageView
                    android:id="@+id/iv_tables"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/tv_tables"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_roulette" />

                <TextView
                    android:id="@+id/tv_tables"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/size_16"
                    android:paddingLeft="@dimen/home_tab_padding_left"
                    android:paddingRight="@dimen/home_tab_padding_right"
                    android:text="@string/table_games"
                    android:textAllCaps="true"
                    android:textColor="@color/white" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ib_main_live_casino_btn"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/home_tab_height"
                android:background="@drawable/categories_bg"
                android:minWidth="@dimen/home_tab_min_width"
                app:layout_constraintBottom_toBottomOf="@+id/ib_main_best_btn"
                app:layout_constraintLeft_toRightOf="@+id/ib_main_tables_btn"
                app:layout_constraintTop_toTopOf="@+id/ib_main_best_btn">

                <ImageView
                    android:id="@+id/iv_live_casino"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/tv_live_casino"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_live_casino" />

                <TextView
                    android:id="@+id/tv_live_casino"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/size_16"
                    android:paddingLeft="@dimen/home_tab_padding_left"
                    android:paddingRight="@dimen/home_tab_padding_right"
                    android:text="@string/live_casino"
                    android:textAllCaps="true"
                    android:textColor="@color/white" />

            </RelativeLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </HorizontalScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
