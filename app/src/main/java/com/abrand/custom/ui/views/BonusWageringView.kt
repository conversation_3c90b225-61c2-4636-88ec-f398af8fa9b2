package com.abrand.custom.ui.views

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.abrand.custom.R
import com.abrand.custom.tools.GeneralTools

open class BonusWageringView : ConstraintLayout {
    var listener: Listener? = null
    var tvBetRefundSum: TextView? = null
    var tvPercentToRefund: TextView? = null
    var progress: ProgressBar? = null

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {
        val rootView = inflate(context, getLayoutId(), this)

        val tvTitle = rootView.findViewById<TextView>(R.id.tv_title)
        val tvQuestion = rootView.findViewById<TextView>(R.id.tv_reset_question)
        val btnNo = rootView.findViewById<Button>(R.id.btn_no)
        val btnYes = rootView.findViewById<Button>(R.id.btn_yes)
        val ivResetBonusBalance = rootView.findViewById<ImageView>(R.id.iv_reset_bonus_balance)
        progress = rootView.findViewById(R.id.progress)
        tvBetRefundSum = rootView.findViewById(R.id.tv_bet_refund_sum)
        tvPercentToRefund = rootView.findViewById(R.id.tv_percent_to_refund)
        ivResetBonusBalance.setOnClickListener {
            tvTitle.visibility = View.INVISIBLE
            tvQuestion.visibility = View.VISIBLE
            ivResetBonusBalance.visibility = View.GONE
            if (isHideProgressForReset()) {
                progress?.visibility = View.GONE
            }
            tvBetRefundSum?.visibility = View.INVISIBLE
            tvPercentToRefund?.visibility = View.GONE
            btnNo.visibility = VISIBLE
            btnYes.visibility = VISIBLE
        }
        btnNo.setOnClickListener {
            tvTitle.visibility = View.VISIBLE
            tvQuestion.visibility = View.INVISIBLE
            ivResetBonusBalance.visibility = View.VISIBLE
            progress?.visibility = View.VISIBLE
            tvBetRefundSum?.visibility = View.VISIBLE
            tvPercentToRefund?.visibility = View.VISIBLE
            btnNo.visibility = GONE
            btnYes.visibility = GONE
        }
        btnYes.setOnClickListener {
            listener?.onResetBonusBalanceClicked()
        }
    }

    open fun getLayoutId(): Int {
        return R.layout.view_bonus_wagering
    }

    open fun isHideProgressForReset(): Boolean {
        return true
    }

    fun setBetRefundSum(betSum: Double, refundSum: Double) {
        val formattedBetSum = GeneralTools.formatNumber(betSum.toInt().toString())
        val formattedRefundSum = GeneralTools.formatNumber(refundSum.toInt().toString())
        tvBetRefundSum?.text = formattedBetSum.plus(" / ").plus(formattedRefundSum)
    }

    fun setPercentToRefund(percentToRefund: Double) {
        tvPercentToRefund?.text = percentToRefund.toString().plus("%")
        post {
            progress?.progress = percentToRefund.toInt()
        }
    }

    interface Listener {
        fun onResetBonusBalanceClicked()
    }

}
