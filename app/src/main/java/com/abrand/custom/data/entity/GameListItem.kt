package com.abrand.custom.data.entity

class GameListItem {
    var id: Int? = null
    var itemType: ItemType
    var gameItem: LocalGameItem? = null

    constructor(itemType: ItemType) {
        this.itemType = itemType
        this.id = itemType.id
    }

    constructor(itemType: ItemType, gameItem: LocalGameItem) {
        this.itemType = itemType
        this.id = gameItem.id
        this.gameItem = gameItem
    }

    enum class ItemType(val id: Int) {
        HEADER(0), GAME(1), GAME_LOAD(2), HOME_ADDITIONAL_BLOCK(3), FOOTER(4)
    }
}
