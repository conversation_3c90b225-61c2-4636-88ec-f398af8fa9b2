package com.abrand.custom.ui.messages

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.MessageItem
import com.abrand.custom.data.entity.Screen
import com.abrand.custom.data.entity.UrlSource
import com.abrand.custom.databinding.FragmentMessagesBinding
import com.abrand.custom.tools.DateTools
import com.abrand.custom.ui.activitymain.MainActivity

class MessagesFragment : Fragment() {
    private var binding: FragmentMessagesBinding? = null
    private var adapter: MessagesAdapter? = null
    private val viewModel: MessagesViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val localBinding = FragmentMessagesBinding.inflate(inflater, container, false)
        binding = localBinding

        setupInsets()
        observeViewModel()

        localBinding.rvMessages.layoutManager = LinearLayoutManager(context)

        adapter = MessagesAdapter(Screen.MESSAGES_MAIN, mutableListOf())
        adapter?.listener = messageListener
        localBinding.rvMessages.adapter = adapter

        viewModel.getMessages()

        binding?.btnBottom?.setOnClickListener {
            viewModel.markAllMessagesAsRead()
        }

        return localBinding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun setupInsets() {
        binding?.btnBottom?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it) { view, insets ->
                val param = view.layoutParams as ViewGroup.MarginLayoutParams
                param.setMargins(0, 0, 0, insets.systemWindowInsetBottom)
                view.layoutParams = param
                insets
            }
        }

        binding?.rvMessages?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it) { view, insets ->
                val btnMarkAllHeight =
                    context?.resources?.getDimensionPixelSize(R.dimen.btn_mark_all_height)
                val rvMessagesBottomPadding =
                    if (btnMarkAllHeight != null) {
                        insets.systemWindowInsetBottom + btnMarkAllHeight
                    } else {
                        insets.systemWindowInsetBottom
                    }
                view.updatePadding(bottom = rvMessagesBottomPadding)
                insets
            }
        }
    }

    private fun observeViewModel() {
        viewModel.messagesLiveData.observe(
            viewLifecycleOwner, { (status, data, error, failure) ->
                when (status) {
                    Status.SUCCESS -> {
                        data?.unreadCount?.let {
                            if (it > 0) {
                                binding?.btnBottom?.visibility = View.VISIBLE
                            }
                        }

                        val messages = data?.items
                        if (messages?.isEmpty() == false) {
                            val messageItems = mutableListOf<MessageItem>()
                            for (message in messages) {
                                val formattedDate = DateTools.getFormattedDate(
                                    context,
                                    message?.date,
                                    DateTools.FormatDateType.MESSAGES
                                )
                                val messageItem = MessageItem(message, formattedDate)
                                messageItems.add(messageItem)
                            }
                            adapter?.setList(messageItems)
                            binding?.containerNoMessages?.visibility = View.INVISIBLE
                        } else {
                            binding?.containerNoMessages?.visibility = View.VISIBLE
                        }
                    }
                    Status.ERROR -> {
                        (activity as? MainActivity)?.showMessage(error?.errorMessage ?: "")
                    }
                    Status.FAILURE -> {
                        (activity as? MainActivity)?.showConnectionIssueMessage(failure)
                    }
                    Status.VIEWER_EMPTY -> {
                        navigateUp()
                    }
                }
            })

        viewModel.deleteMessageLiveData.observe(
            viewLifecycleOwner,
            { (status, data, error, failure) ->
                when (status) {
                    Status.SUCCESS -> {
                        val itemId = data?.first?.id
                        val itemPosition = data?.first?.itemPosition
                        if (itemId != null && itemPosition != null) {
                            adapter?.removeItem(itemId, itemPosition)
                        }

                        val unreadCount = data?.second
                        if (unreadCount != null) {
                            (activity as? MainActivity)?.setMessagesCounter(unreadCount)
                        }
                    }
                    Status.ERROR -> {
                        (activity as? MainActivity)?.showMessage(error?.errorMessage ?: "")
                    }
                    Status.FAILURE -> {
                        (activity as? MainActivity)?.showConnectionIssueMessage(failure)
                    }
                    Status.VIEWER_EMPTY -> {
                        navigateUp()
                    }
                }
            })

        viewModel.readMessageLiveData.observe(
            viewLifecycleOwner,
            { (status, data, error, failure) ->
                when (status) {
                    Status.SUCCESS -> {
                        val itemId = data?.first?.id
                        val itemPosition = data?.first?.itemPosition
                        if (itemId != null && itemPosition != null) {
                            adapter?.markItemAsRead(itemId, itemPosition)
                        }

                        val unreadCount = data?.second
                        if (unreadCount != null) {
                            (activity as? MainActivity)?.setMessagesCounter(unreadCount)
                        }
                    }
                    Status.ERROR -> {
                        (activity as? MainActivity)?.showMessage(error?.errorMessage ?: "")
                    }
                    Status.FAILURE -> {
                        (activity as? MainActivity)?.showConnectionIssueMessage(failure)
                    }
                    Status.VIEWER_EMPTY -> {
                        navigateUp()
                    }
                }
            })

        viewModel.readAllMessagesLiveData.observe(
            viewLifecycleOwner,
            { (status, _, error, failure) ->
                when (status) {
                    Status.SUCCESS -> {
                        binding?.btnBottom?.visibility = View.GONE
                        adapter?.markAllAsRead()
                        (activity as? MainActivity)?.resetMessagesCounter()
                    }
                    Status.ERROR -> {
                        (activity as? MainActivity)?.showMessage(error?.errorMessage ?: "")
                    }
                    Status.FAILURE -> {
                        (activity as? MainActivity)?.showConnectionIssueMessage(failure)
                    }
                    Status.VIEWER_EMPTY -> {
                        navigateUp()
                    }
                }
            })
    }

    private val messageListener = object : MessagesAdapter.Listener {
        override fun onItemDelete(id: String?, itemPosition: Int) {
            if (id != null) {
                viewModel.deleteMessage(id, itemPosition)
            }
        }

        override fun onMarkItemAsRead(id: String?, itemPosition: Int) {
            if (id != null) {
                viewModel.markMessageAsRead(id, itemPosition)
            }
        }

        override fun onDeepLinkClicked(url: String) {
            (activity as? MainActivity)?.processDeepLinkUrl(url, UrlSource.SUBSCRIPTION_MESSAGE)
        }

        override fun onMessagesListEmpty() {
            binding?.containerNoMessages?.visibility = View.VISIBLE
            binding?.btnBottom?.visibility = View.INVISIBLE
        }
    }

    private fun navigateUp() {
        view?.let {
            activity?.runOnUiThread { Navigation.findNavController(it).navigateUp() }
        }
    }

    fun addMessageItem(messageItem: MessageItem) {
        adapter?.appendList(messageItem)
        if (binding?.containerNoMessages?.visibility == View.VISIBLE) {
            binding?.containerNoMessages?.visibility = View.INVISIBLE
        }
    }
}
