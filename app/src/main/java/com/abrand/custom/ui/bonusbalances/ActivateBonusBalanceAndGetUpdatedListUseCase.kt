package com.abrand.custom.ui.bonusbalances

import com.abrand.custom.data.repositories.BonusBalancesRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ActivateBonusBalanceAndGetUpdatedListUseCase(
    private val bonusBalanceRepo: IBonusBalancesRepo = BonusBalancesRepository,
    private val getBonusesBalancesUseCase: GetBonusesBalancesUseCase = GetBonusesBalancesUseCase(),
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO,
) {
    suspend fun invoke(bonusId: Int): BonusActivateResult = withContext(dispatcher) {
        if (bonusBalanceRepo.activateBonus(bonusId)) {
            return@withContext BonusActivateResult.Success(getBonusesBalancesUseCase.invoke())
        }

        return@withContext BonusActivateResult.Error(getBonusesBalancesUseCase.invoke())
    }
}

sealed interface BonusActivateResult {
    data class Success(val list: List<BonusBalance>) : BonusActivateResult
    data class Error(val list: List<BonusBalance>) : BonusActivateResult
}
