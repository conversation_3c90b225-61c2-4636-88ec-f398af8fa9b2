<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:app="http://schemas.android.com/apk/res-auto"
android:id="@+id/card_root"
android:layout_width="match_parent"
android:layout_height="@dimen/tournaments_card_height"
android:layout_marginLeft="@dimen/tournaments_card_side_margin"
android:layout_marginTop="@dimen/size_16"
android:layout_marginRight="@dimen/tournaments_card_side_margin"
    android:background="@drawable/tournament_default_bg">


<ImageView
    android:id="@+id/iv_item_tournament_image"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:alpha="0.5" />

<TextView
    android:id="@+id/tv_item_tournament_name"
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_marginStart="16dp"
    android:layout_marginLeft="16dp"
    android:layout_marginTop="16dp"
    android:layout_marginEnd="@dimen/size_16"
    android:layout_marginRight="@dimen/size_16"
    android:gravity="start"
    android:textColor="#FFFFFF"
    android:textFontWeight="500"
    android:textSize="18sp"
    app:layout_constraintEnd_toStartOf="@+id/tv_item_tournament_month"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toTopOf="parent" />

<TextView
    android:id="@+id/tv_item_tournament_day"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="16dp"
    android:textColor="@color/white"
    android:textSize="24sp"
    android:textStyle="bold"
    app:layout_constraintStart_toStartOf="@+id/tv_item_tournament_month"
    app:layout_constraintEnd_toEndOf="@+id/tv_item_tournament_month"
    app:layout_constraintTop_toTopOf="parent" />

<TextView
    android:id="@+id/tv_item_tournament_month"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="16dp"
    android:layout_marginRight="16dp"
    android:textAllCaps="false"
    android:textColor="@color/white"
    android:textSize="10sp"
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintTop_toBottomOf="@+id/tv_item_tournament_day" />


<TextView
    android:id="@+id/tv_item_tournament_prize"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:gravity="center"
    android:textColor="@color/white"
    android:textSize="24sp"
    android:layout_marginBottom="16dp"
    android:layout_marginStart="16dp"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintBottom_toBottomOf="parent" />


<ImageView
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    android:layout_width="24dp"
    android:layout_height="24dp"
    android:layout_marginBottom="16dp"
    android:layout_marginEnd="24dp"
    android:layout_gravity="center_horizontal"
    android:scaleType="center"
    android:src="@drawable/ic_arrow_right" />
</androidx.constraintlayout.widget.ConstraintLayout>