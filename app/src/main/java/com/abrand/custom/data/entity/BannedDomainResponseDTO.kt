package com.abrand.custom.data.entity

import com.google.gson.annotations.SerializedName

data class BannedDomainResponseDTO(
    @SerializedName("status") val status: <PERSON><PERSON><PERSON>,
    @SerializedName("message") val message: String,
    @SerializedName("data") val data: BannedDomainsData
)

data class BannedDomainsData(
    @SerializedName("domains") val domains: List<String>,
    @SerializedName("updater_domains") val updaterDomains: List<String>,
)
