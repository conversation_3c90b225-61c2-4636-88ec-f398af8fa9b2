package com.abrand.custom.data.datasources

import com.abrand.custom.data.entity.BannedDomainDTO
import com.abrand.custom.data.entity.BannedDomainEntity

internal class BannedDomainsLocalDataSourceImpl(
    private val bannedDomainDao: BannedDomainDao = BannedDomainDatabaseProvider.getDao(),
    private val bannedDomainsPreferencesDataStore: BannedDomainsPreferencesDataStore = BannedDomainsPreferencesDataStore()
) : BannedDomainsLocalDataSource {

    override suspend fun getBannedDomainList(): List<BannedDomainDTO> {
        return bannedDomainDao.getBannedDomainList().map { it.toBannedDomainDTO() }
    }

    override suspend fun saveBannedDomain(domain: BannedDomainDTO) {
        return bannedDomainDao.insertBannedDomain(domain.toBannedDomainEntity())
    }

    override suspend fun removeBannedDomainById(id: Long) {
        return bannedDomainDao.deleteBannedDomainById(id)
    }

    override suspend fun deleteAllBannedDomains() {
        return bannedDomainDao.deleteAllBannedDomains()
    }

    override suspend fun saveLastFailedRequestTimestamp(lastTime: Long) {
        bannedDomainsPreferencesDataStore.saveLastFailedBannedDomainsRequestTimestamp(lastTime)
    }

    override suspend fun getLastFailedRequestTimestamp(): Long {
        return bannedDomainsPreferencesDataStore.getLastFailedBannedDomainsRequestTimestamp()
    }

    private fun BannedDomainEntity.toBannedDomainDTO() = BannedDomainDTO(
        id = id,
        domain = domain,
        reason = reason,
        code = code,
        action = action,
        time = time
    )

    private fun BannedDomainDTO.toBannedDomainEntity() = BannedDomainEntity(
        id = id,
        domain = domain,
        reason = reason,
        code = code,
        action = action,
        time = time
    )
}
