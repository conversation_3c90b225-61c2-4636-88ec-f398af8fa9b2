package com.abrand.custom.ui.enter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.abrand.custom.ui.activitymain.MainActivity;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.Robolectric;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;

import static android.os.Build.VERSION_CODES.M;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.robolectric.Shadows.shadowOf;

@RunWith(RobolectricTestRunner.class)
@Config(sdk = {M})
public class EnterFragmentTest {
    private MainActivity  activity;
    private EnterFragment fragment;

    @Before
    public void setUp() {
        ActivityController<MainActivity> controller = Robolectric.buildActivity(MainActivity.class);
        activity = spy(controller.get());
        activity.onCreate(null);

        fragment = spy(new EnterFragment());
        startFragment(fragment);
    }

    @Test
    // Outdated
    public void processAuthenticationOnSuccess() {
//        String userId    = "userId";
//        String userEmail = "<EMAIL>";
//
//        shadowOf(Looper.getMainLooper()).idle();
//        when(fragment.isResumed()).thenReturn(true);
//        when(fragment.getActivity()).thenReturn(activity);
//        doNothing().when(fragment).invokeBackPress(activity);
//
//        fragment.onSuccess(userId, userEmail);
//
//        Assert.assertEquals(userId, Settings.get().getUserId());
//        Assert.assertEquals(userEmail, Settings.get().getLoggedUserEmail());
//
//        verify(activity).getInitialViewerData();
//        verify(activity).balanceSubscribe();
    }

    private void startFragment(Fragment fragment) {
        FragmentManager     fragmentManager     = activity.getSupportFragmentManager();
        FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();
        fragmentTransaction.add(fragment, null);
        fragmentTransaction.commit();
    }
}
