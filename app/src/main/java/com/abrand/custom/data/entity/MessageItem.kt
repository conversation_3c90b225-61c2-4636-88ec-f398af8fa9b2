package com.abrand.custom.data.entity

import com.abrand.custom.GetMessagesQuery
import com.abrand.custom.SubscribeMessagesSubscription

class MessageItem(var itemType: ItemType) {
    var id: String? = null
    var date: String? = null
    var title: String? = null
    var image: String? = null
    var text: String? = null
    var buttonText: String? = null
    var buttonUrl: String? = null
    var buttonText2: String? = null
    var buttonUrl2: String? = null
    var isRead: Boolean? = null
    var itemPosition: Int? = null
    var serverObjectDate: Any? = null

    constructor(message: GetMessagesQuery.Item?, formattedDate: String) : this(ItemType.MESSAGE) {
        this.id = message?.id
        this.date = formattedDate
        this.title = message?.title
        this.image = message?.imgMob
        this.text = message?.message
        this.buttonText = message?.buttonText
        this.buttonUrl = message?.buttonUrl
        this.buttonText2 = message?.buttonText2
        this.buttonUrl2 = message?.buttonUrl2
        this.isRead = message?.isRead
    }

    constructor(message: SubscribeMessagesSubscription.ViewerMessagesV2, formattedDate: String = "") : this(ItemType.MESSAGE) {
        this.id = message.id
        this.date = formattedDate
        this.title = message.title
        this.image = message.imgMob
        this.text = message.message
        this.buttonText = message.buttonText
        this.buttonUrl = message.buttonUrl
        this.buttonText2 = message.buttonText2
        this.buttonUrl2 = message.buttonUrl2
        this.isRead = message.isRead
        this.serverObjectDate = message.date
    }

    constructor(id: String?, itemPosition: Int?) : this(ItemType.MESSAGE) {
        this.id = id
        this.itemPosition = itemPosition
    }

    enum class ItemType(val id: Int) {
        TITLE(0),
        MESSAGE(1)
    }

    override fun toString(): String {
        return "MessageItem(itemType=$itemType, id=$id, date=$date, title=$title, image=$image, text=$text, buttonText=$buttonText, buttonUrl=$buttonUrl, buttonText2=$buttonText2, buttonUrl2=$buttonUrl2, isRead=$isRead, itemPosition=$itemPosition, serverObjectDate=$serverObjectDate)"
    }
}
