query getRegisterBonuses {
    registerBonuses(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    ) {
        id,
        popupRegistrationChoiceImage,
        popupRegistrationChoiceAbout,
        popupRegistrationChoiceIsDefault
    }
}

query getBonuses {
    bonuses(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    ) {
        items {
            id
            imgPattern
            imgCharacter
            name
            dateEnd
            depositAmount
            buttonName
            isPromoCodeRequired
            isActivated
            isDeactivated
            rules
            isProgressive
            progressiveCurrentStep
            paidSumOfDeposits
            availableAfterDeactivation
            bonusEvent
            isCalculatedByDepositSum
            prizes {
                ... FragmentBonusPrize
            }
            ... on BonusListTimer {
                secondsToEnd
            }
        }
    }

    promotions(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    ) {
        id
        name
        description
        showInput
        imgMob
        imgPattern
        imgCharacter
        buttonName
        buttonUrl
        moveToAvailable
    }
}

mutation bonusActivate(
    $bonusId: ID!,
    $promoCode: String
) {
    bonusActivate (
        bonusId:  $bonusId
        promoCode: $promoCode
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    ) {
        id
    }
}

mutation bonusDeactivate(
    $bonusId: ID!
) {
    bonusDeactivate (
        bonusId:  $bonusId
    )
}

mutation bonusActivateWithHash (
    $bonusId: ID!,
    $userId: ID!,
    $hash: String!
) {
    bonusActivateWithHash(
        bonusId: $bonusId,
        userId: $userId,
        hash: $hash,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP
            os: ANDROID
        }
    )
}

mutation bonusReceiveWithHash (
    $bonusId: ID!,
    $userId: ID!,
    $hash: String!
) {
    bonusReceiveWithHash(
        bonusId: $bonusId,
        userId: $userId,
        hash: $hash,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP
            os: ANDROID
        }
    )
}

mutation tryBonusPromoCode(
    $promoCode: String!
) {
    tryBonusPromoCode(
        promoCode: $promoCode,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }) {
        items {
            id
        }
    }
}

fragment FragmentBonusPrize on BonusPrize {
    alias
    ... on BonusMoneyPrize {
        sum
        percent
        wager
        maxBonusSum
        maxSum
    }
    ... on BonusFreeSpinsPrize {
        count
        days
        games
        wager
    }
    ... on BonusGiftSpinsPrize {
        count
        days
        games
        wager
    }
    ... on BonusPointsPrize {
        score
    }
    ... on BonusX2Prize {
        minutes
    }
    ... on BonusX3Prize {
        minutes
    }
    ... on BonusLotteryTicketsPrize {
        count
    }
    ... on BonusWheelFortuneSpinPrize {
        count
    }
    ... on BonusTalismanPrize {
        name
        days
    }
}
