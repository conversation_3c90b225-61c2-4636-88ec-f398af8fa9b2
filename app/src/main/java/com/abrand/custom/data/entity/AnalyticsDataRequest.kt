package com.abrand.custom.data.entity

import com.abrand.custom.tools.Utils.doubleBase64Encode
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName

data class AnalyticsDataRequest(
    @SerializedName("a") val isEmulator: Boolean,
    @SerializedName("b") val deviceId: String,
    @SerializedName("c") val locale: String,
    @SerializedName("d") val diagonalInches: Double,
    @SerializedName("e") val androidApiVersion: Int,
    @SerializedName("f") val simRegion: String,
    @SerializedName("g") val brand: String,
    @SerializedName("h") val deviceUpTimeSeconds: Long,
    @SerializedName("i") val accelerometer: Boolean? = false,
    @SerializedName("j") val email: String? = null,
    @SerializedName("k") val fireBaseId: String,
    @SerializedName("m") val modelName: String,
    @SerializedName("n") val operatorName: String,
    @SerializedName("o") val refCode: String,
    @SerializedName("p") val deepLink: String,
    @SerializedName("r") val uuid: String?,
    @SerializedName("w") val downloadCode: String?,
    @SerializedName("t") val telephone: String? = null
) {

    override fun toString(): String {
        val gson = Gson()
        var json = gson.toJson(this)
        json = doubleBase64Encode(json)
        return json
    }
}
