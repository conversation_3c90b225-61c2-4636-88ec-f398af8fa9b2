package com.abrand.custom.data.entity

import com.google.gson.annotations.SerializedName

data class BannedDomainDataDTO(
    @SerializedName("domain") val domain: String,
    @SerializedName("actionName") val actionName: String,
    @SerializedName("reason") val reason: String,
    @SerializedName("statusCode") val statusCode: Int?,
    @SerializedName("errorDatetime") val errorDatetime: Long,
    @SerializedName("device") val device: String?,
    @SerializedName("country") val country: String?,
    @SerializedName("packageId") val packageId: String
)
