package com.abrand.custom.data.datasources

import android.content.Context
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.abrand.custom.AApp
import kotlinx.coroutines.flow.first

val Context.dataStore by preferencesDataStore("settings")

internal class BannedDomainsPreferencesDataStore(
//    private val dataStore: DataStore<Preferences> //inject when add DI
) {
    companion object {
        val LAST_FAILED_BANNED_DOMAINS_TIMESTAMP =
            longPreferencesKey("last-failed-banned-domains-request-timestamp")
    }

    suspend fun saveLastFailedBannedDomainsRequestTimestamp(lastTime: Long) {
        AApp.getContext().dataStore.edit { preferences ->
            preferences[LAST_FAILED_BANNED_DOMAINS_TIMESTAMP] = lastTime
        }
    }

    suspend fun getLastFailedBannedDomainsRequestTimestamp(): Long {
        return AApp.getContext().dataStore.data.first()[LAST_FAILED_BANNED_DOMAINS_TIMESTAMP] ?: 0L
    }
}
