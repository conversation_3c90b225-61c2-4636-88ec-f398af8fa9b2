package com.abrand.custom.ui.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.data.entity.ResponseException
import com.abrand.custom.domain.CaptchaType
import com.abrand.custom.domain.GetRecaptchaUseCase
import com.abrand.custom.domain.RecaptchaResponse
import com.abrand.custom.type.Gender
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class ProfileViewModelKt : ViewModel() {
    private val getRecaptchaUseCase = GetRecaptchaUseCase<Unit>()
    private val _eventChannel = Channel<ProfileEffects>(capacity = Channel.BUFFERED)
    val eventFlow: Flow<ProfileEffects> = _eventChannel.receiveAsFlow()

    fun saveProfile(
        userName: String,
        phone: String,
        gender: Gender,
        birthday: String,
        jViewmodel: ProfileViewModel
    ) {
        viewModelScope.launch {
            val result = getRecaptchaUseCase(CaptchaType.PROFILE) { solvedCaptcha ->
                jViewmodel.saveProfile(
                    userName,
                    phone,
                    gender,
                    birthday,
                    solvedCaptcha?.gCaptchaResponse
                )
            }

            when (result) {
                is RecaptchaResponse.CaptchaNotSolved -> {
                    _eventChannel.send(ProfileEffects.CaptchaStatusError(result.message))
                }

                is RecaptchaResponse.NetworkError -> {
                    _eventChannel.send(ProfileEffects.CaptchaStatusNetworkError(result.e))
                }

                is RecaptchaResponse.Executed<Unit> -> {
                    //NOP saving result processed via profileRepo LiveData
                }
            }
        }
    }
}

sealed interface ProfileEffects {
    data class CaptchaStatusError(val message: String) : ProfileEffects
    data class CaptchaStatusNetworkError(val e: ResponseException) : ProfileEffects
}
