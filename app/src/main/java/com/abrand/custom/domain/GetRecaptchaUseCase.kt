package com.abrand.custom.domain

import com.abrand.custom.BuildConfig
import com.abrand.custom.data.entity.ResponseException
import com.abrand.custom.data.repositories.ReCaptchaRepositoryImpl
import com.abrand.custom.domain.interfaces.CaptchaRepository
import com.abrand.custom.domain.interfaces.ReCaptchaEnterpriseRepository

class GetRecaptchaUseCase<T>(
    private val captchaRepository: CaptchaRepository = ReCaptchaRepositoryImpl,
    private val enterpriseRepo: ReCaptchaEnterpriseRepository = ReCaptchaRepositoryImpl,
) {
    suspend operator fun invoke(
        captchaType: CaptchaType,
        executeAtEnd: suspend (SolvedCaptcha?) -> T
    ): RecaptchaResponse<T> {
        val response = captchaRepository.getRecaptcha(captchaType, BuildConfig.APPLICATION_ID)

        return when (response) {
            is CaptchaRepository.ReCaptchaResult.Success -> {
                solveCaptcha(captchaType, executeAtEnd, response)
            }

            is CaptchaRepository.ReCaptchaResult.ResponseError -> {
                RecaptchaResponse.CaptchaNotSolved(response.serverError.errorMessage)
            }

            is CaptchaRepository.ReCaptchaResult.NetworkError -> {
                RecaptchaResponse.NetworkError(response.e)
            }
        }
    }

    private suspend fun solveCaptcha(
        captchaType: CaptchaType,
        executeAtEnd: suspend (SolvedCaptcha?) -> T,
        response: CaptchaRepository.ReCaptchaResult.Success,
    ): RecaptchaResponse<T> {
        val captchaStatus = response.captchaStatus
        return if (captchaStatus.enabled && captchaStatus.captchaId != null) {
            // Need solve
            var token: String? = null
            enterpriseRepo.solve(captchaType).onSuccess { recaptchaToken ->
                token = recaptchaToken
            }.onFailure { _ ->
                // process exception
                return RecaptchaResponse.CaptchaNotSolved("Fail to solve captcha")
            }

            if (!token.isNullOrBlank()) {
                //Solved
                RecaptchaResponse.Executed(
                    executeAtEnd.invoke(SolvedCaptcha(BuildConfig.APPLICATION_ID, token))
                )
            } else {
                // Problem with solve captcha
                RecaptchaResponse.CaptchaNotSolved("Problem with solving captcha, result token is null")
            }

        } else { // Don't need to solve
            RecaptchaResponse.Executed(executeAtEnd.invoke(null))
        }
    }
}

sealed class RecaptchaResponse<out T> {
    data class Executed<T>(val data: T) : RecaptchaResponse<T>()
    data class CaptchaNotSolved(val message: String) : RecaptchaResponse<Nothing>()
    data class NetworkError(val e: ResponseException) : RecaptchaResponse<Nothing>()
}

data class SolvedCaptcha(val appId: String, val gCaptchaResponse: String?)
