package com.abrand.custom.ui.newpassword

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessor
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException

class NewPasswordViewModel : ViewModel() {
    val resetPasswordLiveData = MutableLiveData<Resource<String?, ServerError, ApolloException>>()

    fun resetUserPasswordByToken(resetPasswordToken: String, newPassword: String, refCode: String, affData: String?) {
        ApolloProcessorKt.resetUserPasswordByToken(resetPasswordToken, newPassword,
                refCode, affData, object : GenericTarget<String> {
            override fun onSuccess(viewerId: String?) {
                resetPasswordLiveData.postValue(Resource.success(viewerId))
            }

            override fun onFailure(e: ApolloException) {
                resetPasswordLiveData.postValue(Resource.failure(e))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                resetPasswordLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }
        })
    }
}
