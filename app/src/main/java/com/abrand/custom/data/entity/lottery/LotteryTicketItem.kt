package com.abrand.custom.data.entity.lottery

class LotteryTicketItem {
    var itemType: ItemType
    var ticket: LocalLotteryTicket? = null

    constructor(itemType: ItemType) {
        this.itemType = itemType
    }

    constructor(itemType: ItemType, lottery: LocalLotteryTicket) {
        this.itemType = itemType
        this.ticket = lottery
    }

    enum class ItemType(val id: Int) {
        TICKET(0),
        TICKETS_LOAD(1)
    }
}
