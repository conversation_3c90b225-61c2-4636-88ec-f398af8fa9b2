package com.abrand.custom.ui.exchangepoints

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.GetExchangePointsDataQuery
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessor
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException

class ExchangePointsViewModel : ViewModel() {
    val viewerLiveData = MutableLiveData<GetExchangePointsDataQuery.Viewer?>()
    val loyaltyPointsExchangeLiveData = MutableLiveData<Boolean>()
    val emailConfirmationRequestedLiveData = MutableLiveData<Boolean>()
    val serverErrorLiveData = MutableLiveData<ServerError>()
    val apolloExceptionLiveData = SingleLiveEvent<ApolloException>()

    fun getExchangePointsData() {
        ApolloProcessorKt.getExchangePointsData(object : GenericTarget<GetExchangePointsDataQuery.Viewer> {
            override fun onSuccess(viewer: GetExchangePointsDataQuery.Viewer?) {
                viewerLiveData.postValue(viewer)
            }

            override fun onFailure(e: ApolloException) {
                apolloExceptionLiveData.postValue(e)
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                serverErrorLiveData.postValue(ServerError(errorMessage, errorCode, fieldsErrors))
            }
        })
    }

    fun loyaltyPointsExchange(points: Int) {
        ApolloProcessorKt.loyaltyPointsExchange(points, object : GenericTarget<Boolean> {
            override fun onSuccess(isSuccess: Boolean?) {
                loyaltyPointsExchangeLiveData.postValue(isSuccess == true)
            }

            override fun onFailure(e: ApolloException) {
                apolloExceptionLiveData.postValue(e)
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                serverErrorLiveData.postValue(ServerError(errorMessage, errorCode, fieldsErrors))
            }
        })
    }

    fun requestEmailConfirmation() {
        ApolloProcessorKt.requestEmailConfirmation(object : GenericTarget<Boolean> {
            override fun onSuccess(isSuccess: Boolean?) {
                emailConfirmationRequestedLiveData.postValue(isSuccess == true)
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                serverErrorLiveData.postValue(ServerError(errorMessage, errorCode, fieldsErrors))
            }

            override fun onFailure(e: ApolloException) {
                apolloExceptionLiveData.postValue(e)
            }
        })
    }
}
