package com.abrand.custom.data.entity

import com.abrand.custom.GetBonusesQuery
import com.abrand.custom.fragment.FragmentBonusPrize
//import com.abrand.custom.fragment.FragmentBonusPrize.FragmentBonusPrizeBonusPrize

class PromotionItem {
    var itemType: ItemType
    var id: Int? = null
    var title: String? = null
    var bonus: Bonus? = null
    var permanentPromotion: PermanentPromotion? = null

    constructor(itemType: ItemType) {
        this.itemType = itemType
        this.id = itemType.id
    }

    constructor(itemType: ItemType, title: String) {
        this.itemType = itemType
        this.id = itemType.id
        this.title = title
    }

    constructor(itemType: ItemType, bonus: Bonus) {
        this.itemType = itemType
        this.id = bonus.id
        this.bonus = bonus
    }

    constructor(itemType: ItemType, permanentPromotion: PermanentPromotion) {
        this.itemType = itemType
        this.id = permanentPromotion.id
        this.permanentPromotion = permanentPromotion
    }

    enum class ItemType(val id: Int) {
        TITLE(0), NO_ACTIVE_BONUSES(1), BONUS(2), PERMANENT_PROMOTION(3), FOOTER(4)
    }

    enum class BonusEvent(val event: String) {
        MIN_DEPOSIT("min_deposit"), FIRST_DEPOSIT("first_deposit"),
        END_ACTIVATION_TIME("end_activation_time"), EMAIL_CONFIRMATION("email_confirmation"),
        PHONE_CONFIRMATION("phone_confirmation");

        companion object {
            fun getByValue(value: String): BonusEvent? = values().find { it.event == value }
        }
    }
}
