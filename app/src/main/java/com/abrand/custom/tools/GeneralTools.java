package com.abrand.custom.tools;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Point;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Shader;
import android.graphics.drawable.Drawable;
import android.net.ConnectivityManager;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.webkit.CookieManager;
import android.widget.EditText;

import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;

import com.abrand.custom.R;
import com.abrand.custom.data.ApoloConfig;
import com.abrand.custom.data.entity.Country;
import com.abrand.custom.data.entity.Screen;
import com.google.android.gms.ads.identifier.AdvertisingIdClient;
import com.google.android.gms.common.GooglePlayServicesNotAvailableException;
import com.google.android.gms.common.GooglePlayServicesRepairableException;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Currency;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

public class GeneralTools {
    private static final String TAG = "GeneralTools";

    public static void hideKeyboard(Activity activity) {
        View view = activity.getCurrentFocus();
        if ( view != null ) {
            InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
            if ( imm != null ) {
                imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
            }
        }
    }

    public static void hideKeyboard(Context context, EditText editText) {
        InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        if ( imm != null ) {
            imm.hideSoftInputFromWindow(editText.getWindowToken(), 0);
        }
    }

    public static void showKeyboard(Context context, EditText editText) {
        InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        if ( imm != null ) {
            imm.showSoftInput(editText, 0);
        }
    }

    public static void showCookie() {
        CookieManager cookieManager = android.webkit.CookieManager.getInstance();
        String        currentCookie = cookieManager.getCookie(ApoloConfig.BASE_URL);
        if ( !TextUtils.isEmpty(currentCookie) ) {
            Log.d("CurrentCookie", currentCookie);
        } else {
            Log.d("CurrentCookie", "cookie is empty");
        }
    }

    public static String formatBalance(String currencyCode, Double value) {
        return formatBalance(currencyCode, value, 0);
    }

    public static String formatBalance(String currencyCode, Double value, int maximumFractionDigits) {
        try {
            NumberFormat format = NumberFormat.getCurrencyInstance(new Locale("ru"));
            format.setMaximumFractionDigits(maximumFractionDigits);
            format.setCurrency(Currency.getInstance(currencyCode));
            return format.format(value);
        } catch ( Exception e ) {
            Log.e("formatBalance", e.getMessage());
            return "0";
        }
    }

    public static String formatBalance(String currencyCode, Double value, int maximumFractionDigits, boolean removeEndZero) {
        if ( removeEndZero ) {
            String valueWithoutEndZero = removeEndZero(value);
            value = Double.valueOf(valueWithoutEndZero);
            String[] valueArr = valueWithoutEndZero.split("\\.");
            int digitsAfterDot = 0;
            if ( valueArr.length > 1 ) {
                digitsAfterDot = valueArr[1].length();
            }
            if ( maximumFractionDigits > digitsAfterDot ) {
                maximumFractionDigits = digitsAfterDot;
            }
            return formatBalance(currencyCode, value, maximumFractionDigits);
        } else {
            return formatBalance(currencyCode, value, maximumFractionDigits);
        }
    }

    public static String removeEndZero(Double value) {
        DecimalFormat format = new DecimalFormat("0.#####");
        return format.format(value).replace(",", ".");
    }

    public static String formatExchangeMoney(String currencyCode, Double value) {
        try {
            NumberFormat format = NumberFormat.getCurrencyInstance(new Locale("ru"));
            format.setMaximumFractionDigits(2);
            format.setCurrency(Currency.getInstance(currencyCode));
            return format.format(value);
        } catch ( Exception e ) {
            Log.e("formatBalance", e.getMessage());
            return "0";
        }
    }

    public static String removeSpaces(String string) {
        return string.replaceAll("\\s+", "");
    }

    @SuppressLint("HardwareIds")
    public static String getAndroidId(Context context) {
        return Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
    }

    public static String loadJSONFromAsset(Context context, String fileName) {
        String json = null;
        try {
            InputStream is     = context.getAssets().open(fileName);
            int         size   = is.available();
            byte[]      buffer = new byte[size];
            is.read(buffer);
            is.close();
            json = new String(buffer, "UTF-8");
        } catch (IOException ex) {
            Log.e(TAG, ex.toString());
            return null;
        }
        return json;
    }

    /**
     *
     * @param context
     * @return List<Country> (with ISO, country name and phone code)
     */
    public static List<Country> getCountries(Context context) {
        final String  PHONE_CODES_MAP_FILE_NAME = "phone_codes_map.json";
        List<Country> countries                 = new ArrayList<>();
        String        json                      = GeneralTools.loadJSONFromAsset(context, PHONE_CODES_MAP_FILE_NAME);
        if ( !TextUtils.isEmpty(json) ) {
            try {
                JSONObject jo = new JSONObject(json);
                for ( Iterator<String> it = jo.keys(); it.hasNext(); ) {
                    String countryISO       = it.next();
                    String countryPhoneCode = "+" + jo.get(countryISO);
                    Locale locale           = new Locale("", countryISO);
                    String countryName      = locale.getDisplayCountry();
                    countries.add(new Country(countryISO, countryName, countryPhoneCode));
                }
            } catch ( JSONException e ) {
                e.printStackTrace();
            }
        }
        return countries;
    }

    public static Drawable getDrawableByCountryCode(Context context, String countryCode) {
        Drawable flagDrawable = null;
        String   drawableName = "ic_flag_" + countryCode.toLowerCase();
        int      resourceId   = context.getResources().getIdentifier(drawableName, "drawable", context.getPackageName());
        try {
            flagDrawable = ContextCompat.getDrawable(context, resourceId);
        } catch ( Resources.NotFoundException exception ) {
            Log.e(TAG, "drawable for" + countryCode + " not found");
        }
        return flagDrawable;
    }

    public static int getStatusBarHeight(Context context) {
        int resourceId = context.getResources().getIdentifier("status_bar_height", "dimen", "android");
        if ( resourceId > 0 ) {
            return context.getResources().getDimensionPixelSize(resourceId);
        } else {
            return 0;
        }
    }

    public static int getNavBarHeight(Context context) {
        Resources resources  = context.getResources();
        int       resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android");
        if ( resourceId > 0 ) {
            return resources.getDimensionPixelSize(resourceId);
        } else {
            return 0;
        }
    }

    public static int getActionBarHeight(Context context) {
        return context.getResources().getDimensionPixelSize(R.dimen.action_bar_height);
    }

    public static int getScreenHeight(Context context) {
        WindowManager wm      = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display       display = wm.getDefaultDisplay();
        Point         size    = new Point();
        display.getSize(size);
        return size.y;
    }

    public static int getScreenWidth(Context context) {
        WindowManager wm      = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display       display = wm.getDefaultDisplay();
        Point         size    = new Point();
        display.getSize(size);
        return size.x;
    }

    public static boolean isNetworkConnected(Context context) {
        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        return cm != null && cm.getActiveNetworkInfo() != null && cm.getActiveNetworkInfo().isConnected();
    }

    public static void getInstalledApps(Context context) {
        final PackageManager pm = context.getPackageManager();
        List<ApplicationInfo> packages = pm.getInstalledApplications(PackageManager.GET_META_DATA);
        for (ApplicationInfo packageInfo : packages) {
            Log.d(TAG, "Installed package: " + packageInfo.packageName);
        }
    }

    public static boolean isNotificationEnables(Context context) {
        return NotificationManagerCompat.from(context).areNotificationsEnabled();
    }

    public static int getGeneralChannelImportance(Context context) {
        if ( Build.VERSION.SDK_INT >= Build.VERSION_CODES.O ) {
            NotificationManager notificationManager = ((NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE));
            NotificationChannel channel             = notificationManager.getNotificationChannel(context.getString(R.string.general_notification_channel_id));
            if ( channel != null ) {
                return channel.getImportance();
            } else {
                return -1;
            }
        } else {
            return -1;
        }
    }

    public static Bitmap getBitmapFromVectorDrawable(Context context, int drawableId) {
        Drawable drawable = ContextCompat.getDrawable(context, drawableId);
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            drawable = (DrawableCompat.wrap(drawable)).mutate();
        }

        Bitmap bitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(),
                drawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        drawable.draw(canvas);

        return bitmap;
    }

    public static Bitmap addGradient(Bitmap originalBitmap, String startColorString, String endColorString) {
        int width = originalBitmap.getWidth();
        int height = originalBitmap.getHeight();
        Bitmap updatedBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas        = new Canvas(updatedBitmap);

        canvas.drawBitmap(originalBitmap, 0, 0, null);

        Paint paint = new Paint();
        LinearGradient shader = new LinearGradient(0, 0, 0, height,
                Color.parseColor(startColorString), Color.parseColor(endColorString),
                Shader.TileMode.CLAMP);
        paint.setShader(shader);
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
        canvas.drawRect(0, 0, width, height, paint);

        return updatedBitmap;
    }

    public static int getGameIconSize(Context context, Screen screen) {
        int screenWidth              = getScreenWidth(context);
        int gameIconLeftRightSpacing = context.getResources().getDimensionPixelSize(R.dimen.game_icon_left_right_spacing);
        if ( Screen.INGAME_MENU_SEARCH_LANDSCAPE == screen ) {
            int gameIconStartMargin = context.getResources().getDimensionPixelSize(R.dimen.ingame_menu_games_margin_start_landscape);
            int gameIconEndMargin   = context.getResources().getDimensionPixelSize(R.dimen.ingame_menu_games_margin_end_landscape);
            return (screenWidth - gameIconStartMargin - gameIconEndMargin - gameIconLeftRightSpacing * 3) / 4;
        } else {
            int gameIconStartEndMargin = context.getResources().getDimensionPixelSize(R.dimen.game_icon_start_end_margin);
            return (screenWidth - gameIconStartEndMargin * 2 - gameIconLeftRightSpacing * 2) / 3;
        }
    }

    public static Drawable getLoyaltyStatusDrawable(Context context, int loyaltyStatusId, boolean isOpenStatus) {
        switch ( loyaltyStatusId ) {
            case 1:
                if ( isOpenStatus ) {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_1_open);
                } else {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_1_close);
                }
            case 2:
                if ( isOpenStatus ) {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_2_open);
                } else {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_2_close);
                }
            case 3:
                if ( isOpenStatus ) {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_3_open);
                } else {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_3_close);
                }
            case 4:
                if ( isOpenStatus ) {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_4_open);
                } else {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_4_close);
                }
            case 5:
                if ( isOpenStatus ) {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_5_open);
                } else {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_5_close);
                }
            case 6:
                if ( isOpenStatus ) {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_6_open);
                } else {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_6_close);
                }
            default:
                if ( isOpenStatus ) {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_7_open);
                } else {
                    return ContextCompat.getDrawable(context, R.drawable.ic_loyalty_status_7_close);
                }
        }
    }

    public static String formatNumber(String number) {
        int  SPACE_POSITION = 3;
        char space          = ' ';

        StringBuilder inputBuilder = new StringBuilder(number);
        for ( int i = inputBuilder.length() - SPACE_POSITION; i > 0; i -= SPACE_POSITION ) {
            inputBuilder.insert(i, space);
        }
        return inputBuilder.toString();
    }

    public static String formatDoubleNumber(String number) {
        double inputDouble   = Double.parseDouble(number);
        double roundedDouble = new BigDecimal(inputDouble).setScale(1, RoundingMode.HALF_UP).doubleValue();
        String roundedNumber = Double.toString(roundedDouble);

        String[] numberArr       = roundedNumber.split("\\.");
        String   wholeNumberPart = numberArr[0];
        String   decimalPart     = numberArr[1];
        if ( decimalPart.equals("0") ) {
            return formatNumber(wholeNumberPart);
        } else {
            return formatNumber(wholeNumberPart) + "." + decimalPart;
        }
    }

    public static Locale getLocale(Context context) {
        return android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N ?
                context.getResources().getConfiguration().getLocales().get(0) :
                context.getResources().getConfiguration().locale;
    }

    public static String getMD5(final String s) {
        final String MD5 = "MD5";
        try {
            // Create MD5 Hash
            MessageDigest digest = java.security.MessageDigest.getInstance(MD5);
            digest.update(s.getBytes());
            byte[] messageDigest = digest.digest();

            // Create Hex String
            StringBuilder hexString = new StringBuilder();
            for (byte aMessageDigest : messageDigest) {
                StringBuilder h = new StringBuilder(Integer.toHexString(0xFF & aMessageDigest));
                while (h.length() < 2)
                    h.insert(0, '0');
                hexString.append(h);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String getBase64(String string) {
        return Base64.encodeToString(string.getBytes(), Base64.NO_WRAP);
    }

    public static DecimalFormat getTournamentResultDecimalFormat() {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        decimalFormat.setDecimalFormatSymbols(DecimalFormatSymbols.getInstance(Locale.ENGLISH));
        return decimalFormat;
    }

    public static String getTournamentFormattedBalance(double prizeFund) {
        String formattedBalance = GeneralTools.formatBalance(com.abrand.custom.data.Settings.get().getUserCurrencyCode(), prizeFund);
        return "<b>" + formattedBalance.substring(0, formattedBalance.length() - 2) + "</b> " +
                formattedBalance.substring(formattedBalance.length() - 1);
    }
}
