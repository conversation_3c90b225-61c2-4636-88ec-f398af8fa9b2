package com.abrand.custom.data.repositories

import com.abrand.custom.data.Settings
import com.abrand.custom.data.datasources.ConfigRemoteDatasource
import com.abrand.custom.data.datasources.GeneratorRemoteDataSourceImpl
import com.abrand.custom.data.entity.appconfig.LocalConfig
import com.abrand.custom.data.entity.appconfig.mergeWithLocalConfig

object ConfigRepository {
    private val settings = Settings
    private val configRemoteDatasource: ConfigRemoteDatasource = GeneratorRemoteDataSourceImpl()

    fun isConfigDataFetched(): Boolean {
        return settings.isConfigDataFetched
    }

    fun setConfigDataFetched() {
        settings.isConfigDataFetched = true
    }

    suspend fun fetchAppConfig(currentConfig: LocalConfig): LocalConfig {
        return currentConfig.hash?.let { hash ->
            configRemoteDatasource.fetchAppConfig(
                token = currentConfig.token,
                domains = currentConfig.domains,
                hash = hash
            ).mergeWithLocalConfig(currentConfig).also { newConfig ->
                saveLocalConfig(newConfig)
            }
        } ?: currentConfig
    }

    private fun saveLocalConfig(localConfig: LocalConfig) {
        settings.localConfig = localConfig
    }
}
