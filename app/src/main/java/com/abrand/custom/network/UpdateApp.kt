package com.abrand.custom.network

import android.Manifest
import android.app.Activity
import android.app.ProgressDialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.abrand.custom.BuildConfig
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.repositories.SettingsRepository
import com.abrand.custom.tools.Crypt
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.text.NumberFormat
import androidx.core.net.toUri
import com.abrand.custom.data.entity.BannedDomainDTO
import com.abrand.custom.data.repositories.BannedDomainsRepositoryImpl
import com.abrand.custom.domain.BannedDomainsRepository
import kotlinx.coroutines.withContext

class UpdateApp(
    listener: Listener,
) {
    private val settingsRepository: SettingsRepository = SettingsRepository
    private val versionName = BuildConfig.VERSION_NAME
    private var updateImportance = UpdateImportance.UNKNOWN
    private var appUrl: String? = null
    private var downloadDialog: ProgressDialog? = null
    private val TAG = "UpdateApk"
    private val fileLoader = FileLoader()
    val updateListener: Listener = listener
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val bannedDomainsRepository: BannedDomainsRepository = BannedDomainsRepositoryImpl

    companion object {
        const val REQUEST_WRITE_EXTERNAL_STORAGE = 101
        const val REQUEST_APP_SETTINGS = 102
    }

    fun getUpdateImportance(activity: Activity) {
        val scheme = "https"
        val path = "check-application-version"

        scope.launch {
            fileLoader.deleteApkFile(activity)

            val localConfig = settingsRepository.getLocalConfig()
            val domains = localConfig.updaterDomains ?: emptyList()
            val updateToken = localConfig.token
            val builder = Uri.Builder().apply {
                scheme(scheme)
                appendPath(path)
            }

            domains.forEach { domain ->
                val url = builder.authority(domain).build().toString()
                val response = OkHttpProcessor.getUpdateImportance(versionName, url, updateToken)

                when (response) {
                    is CheckUpdateResponse.Success -> {
                        processUpdateImportanceResponse(activity, response.updateImportanceCode, response.appUrl)
                        return@launch
                    }

                    is CheckUpdateResponse.ApplicationError -> {
                        handleBannedDomains(
                            domain = domain,
                            reason = response.error,
                            code = null
                        )
                        Log.e(
                            TAG,
                            "Error check update on domain: $domain reason: ${response.error}"
                        )
                    }
                }
            }

            // not received success response from any of domains
            updateListener.updateImportancePassed()
        }
    }

    private suspend fun processUpdateImportanceResponse(
        activity: Activity,
        pUpdateImportanceCode: Int,
        baseDownloadUrl: String
    ) = withContext(Dispatchers.Main.immediate) {
        val paramsMap = prepareDownloadParamsMap()

        appUrl = prepareDownloadUrl(baseDownloadUrl, paramsMap)
        updateImportance = UpdateImportance.getByCode(pUpdateImportanceCode)
        when (updateImportance) {
            UpdateImportance.REQUIRED -> showUpdateRequiredDialog(activity)
            UpdateImportance.RECOMMENDED -> showUpdateRecommendedDialog(activity)
            UpdateImportance.NEWEST -> if (Settings.get().newestImportanceUpdateLvl) {
                showUpdateRecommendedDialog(activity)
            } else {
                updateListener.updateImportancePassed()
            }
            UpdateImportance.NOT_REQUIRED -> updateListener.updateImportancePassed()
            else -> {
                Log.e(TAG, "wrong updateImportanceCode: $pUpdateImportanceCode")
                updateListener.updateImportancePassed()
            }
        }
    }

    private fun prepareDownloadParamsMap(): Map<String, String> {
        val appConfig = settingsRepository.getLocalConfig()
        val parameters = mutableMapOf<String, String>()

        parameters["referrer"] = appConfig.ref.orEmpty()
        parameters["uuid"] = appConfig.uuid.orEmpty()
        parameters["appInstallUuid"] = appConfig.appInstallUuid.orEmpty()

        appConfig.affData?.forEach { affData ->
            parameters["affdata[${affData.key}]"] = affData.value
        }

        return parameters.filterValues { it.isEmpty().not() }
    }

    private fun prepareDownloadUrl(url: String, downloadParams: Map<String, String>): String {
        val builder = url.toUri().buildUpon()

        downloadParams.forEach { param ->
            builder.appendQueryParameter(param.key, param.value)
        }

        return builder.toString()
    }

    fun startDownloading(activity: Activity) {
        Thread {
            fileLoader.download(activity, appUrl, object : FileLoader.DownloadListener {
                override fun onProgress(progress: Int) {
                    activity.runOnUiThread {
                        setIndeterminateDownloadDialog(false)
                        downloadDialog?.progress = progress
                    }
                }

                override fun onDownloadCancelled() {
                    updateListener.updateImportancePassed()
                }

                override fun onFinish(isSuccess: Boolean, fileUri: Uri) {
                    hideDownloadDialog()
                    if (isSuccess) {
                        installApp(activity, fileUri)
                    } else {
                        activity.runOnUiThread {
                            Toast.makeText(activity, R.string.error_file_download, Toast.LENGTH_LONG).show()
                            if (UpdateImportance.RECOMMENDED === updateImportance || UpdateImportance.NEWEST === updateImportance) {
                                updateListener.updateImportancePassed()
                            }
                        }
                    }
                }
            })
        }.start()
        showDownloadDialog(activity)
    }

    private fun showUpdateRequiredDialog(activity: Activity) {
        if (!activity.isFinishing) {
            val builder = AlertDialog.Builder(activity)
            builder.setCancelable(false)
            builder.setMessage(R.string.update_app_required_message)
                .setPositiveButton(R.string.update) { dialog: DialogInterface?, id: Int -> downloadNewApk(activity) }.show()
        }
    }

    private fun showUpdateRecommendedDialog(activity: Activity) {
        if (!activity.isFinishing) {
            val builder = AlertDialog.Builder(activity)

            builder.setCancelable(false)
            builder.setMessage(R.string.update_app_recommended_message)
                .setPositiveButton(R.string.update) { dialog, id -> downloadNewApk(activity) }
                .setNegativeButton(R.string.cancel) { dialog, id -> updateListener.updateImportancePassed() }
                .show()
        }
    }

    private fun showDownloadDialog(context: Context) {
        downloadDialog = ProgressDialog(context)
        downloadDialog?.setMessage(context.getString(R.string.file_downlod_message))
        setIndeterminateDownloadDialog(true)
        downloadDialog?.max = 100
        downloadDialog?.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL)
        downloadDialog?.setCancelable(false)
        if (UpdateImportance.RECOMMENDED === updateImportance || UpdateImportance.NEWEST === updateImportance) {
            downloadDialog?.setButton(DialogInterface.BUTTON_NEGATIVE,
                    context.getString(R.string.cancel))
            { dialog: DialogInterface?, which: Int -> fileLoader.cancelDownload() }
        }
        downloadDialog?.show()
    }

    private fun downloadNewApk(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startDownloading(activity);
        } else {
            val permissionCheck = ContextCompat.checkSelfPermission(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE)
            if (permissionCheck != PackageManager.PERMISSION_GRANTED) {
                requestWritePermissionWithRationale(activity)
            } else {
                startDownloading(activity)
            }
        }
    }

    private fun requestWritePermissionWithRationale(activity: Activity) {
        if (ActivityCompat.shouldShowRequestPermissionRationale(activity, Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
            showWritePermissionRationaleDialog(activity)
        } else {
            requestWriteExtStorage(activity)
        }
    }

    fun showWritePermissionRationaleDialog(activity: Activity) {
        val builder = AlertDialog.Builder(activity)
        builder.setCancelable(false)
        builder.setMessage(R.string.write_permission_denied_message)
                .setPositiveButton(R.string.grant) { dialog: DialogInterface?, id: Int -> requestWriteExtStorage(activity) }.show()
    }

    private fun requestWriteExtStorage(activity: Activity) {
        ActivityCompat.requestPermissions(activity, arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE), REQUEST_WRITE_EXTERNAL_STORAGE)
    }

    private fun installApp(activity: Activity, fileUri: Uri) {
        val installIntent = Intent(Intent.ACTION_VIEW)
        installIntent.putExtra(Intent.EXTRA_NOT_UNKNOWN_SOURCE, true)
        installIntent.setDataAndType(fileUri, "application/vnd.android.package-archive")
        installIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
        installIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        activity.startActivity(installIntent)
        activity.finish()
    }

    fun showWritePermissionDeniedDialog(activity: Activity) {
        val builder = AlertDialog.Builder(activity)
        builder.setCancelable(false)
        val message: String = activity.getString(R.string.write_permission_denied_message)
        builder.setMessage(message)
                .setPositiveButton(R.string.go_to_settings) { dialog: DialogInterface?, id: Int ->
                    openApplicationSettings(activity)
                    Toast.makeText(activity, R.string.grant_write_permission_instruction,
                            Toast.LENGTH_LONG).show()
                }.show()
    }

    private fun openApplicationSettings(activity: Activity) {
        val appSettingsIntent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
                Uri.parse("package:" + activity.packageName))
        activity.startActivityForResult(appSettingsIntent, REQUEST_APP_SETTINGS)
    }

    private fun setIndeterminateDownloadDialog(isIndeterminate: Boolean) {
        downloadDialog?.isIndeterminate = isIndeterminate
        if (isIndeterminate) {
            downloadDialog?.setProgressNumberFormat(null)
            downloadDialog?.setProgressPercentFormat(null)
        } else {
            val percentInstance = NumberFormat.getPercentInstance()
            percentInstance.maximumFractionDigits = 0
            downloadDialog?.setProgressPercentFormat(percentInstance)
        }
    }

    private fun hideDownloadDialog() {
        if (downloadDialog != null && downloadDialog!!.isShowing) {
            downloadDialog?.dismiss()
        }
    }

    fun onDestroy() {
        hideDownloadDialog()
        fileLoader.cancelDownload()
        scope.cancel()
    }

    private suspend fun handleBannedDomains(
        domain: String,
        reason: String,
        code: Int?
    ) {
        bannedDomainsRepository.saveBannedDomain(
            BannedDomainDTO(
                domain = domain,
                reason = reason,
                code = code,
                action = "updater",
                time = System.currentTimeMillis()
            )
        )
    }

    enum class UpdateImportance(val code: Int) {
        REQUIRED(0), RECOMMENDED(1), NEWEST(2), NOT_REQUIRED(3), UNKNOWN(-1);

        companion object {
            fun getByCode(code: Int): UpdateImportance {
                for (updateImportance in values()) {
                    if (updateImportance.code == code) {
                        return updateImportance
                    }
                }
                return UNKNOWN
            }
        }
    }

    interface Listener {
        fun updateImportancePassed()
    }

    sealed interface CheckUpdateResponse {
        data class Success(val updateImportanceCode: Int, val appUrl: String) : CheckUpdateResponse
        data class ApplicationError(val error: String) : CheckUpdateResponse
//        data class TransportError(val error: String) : CheckUpdateResponse
    }
}
