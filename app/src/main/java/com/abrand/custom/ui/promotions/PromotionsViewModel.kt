package com.abrand.custom.ui.promotions

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.BonusesResponse
import com.abrand.custom.data.entity.PromotionItem
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.network.ProfileRepository
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException

class PromotionsViewModel : ViewModel() {
    private val profileRepository: ProfileRepository = ProfileRepository()
    val bonusesLiveData = MutableLiveData<BonusesResponse?>()
    val bonusActivateLiveData = MutableLiveData<Int?>()
    val bonusDeactivateLiveData = MutableLiveData<Boolean>()
    val makeRebillLiveData = profileRepository.makeRebillLiveData
    val fastPaymentRebillingLiveData = MutableLiveData<Pair<Boolean, Int>>()
    val fastPaymentUrlLiveData: LiveData<String> = profileRepository.fastPaymentUrlLiveData

    private val _tryBonusPromoCodeLiveData = MutableLiveData<Resource<Boolean, Pair<Int, ServerError>, ApolloException>>()
    val tryBonusPromoCodeLiveData: LiveData<Resource<Boolean, Pair<Int, ServerError>, ApolloException>> = _tryBonusPromoCodeLiveData

    val serverErrorLiveData = MutableLiveData<ServerError>()
    val apolloExceptionLiveData = SingleLiveEvent<ApolloException?>()

    var isRebilling = false
    var isBonusActivateProcessing = false

    private val PROMOCODE_MIN_LENGTH = 3

    companion object {
        val INVALID_PROMOCODE = "invalid_promocode"
    }

    fun getBonuses() {
        ApolloProcessorKt.getBonuses(object : GenericTarget<BonusesResponse> {

            override fun onSuccess(bonuses: BonusesResponse?) {
                bonusesLiveData.postValue(bonuses)
                Handler(Looper.getMainLooper()).postDelayed({
                    isBonusActivateProcessing = false
                }, 500)
            }

            override fun onFailure(e: ApolloException?) {
                apolloExceptionLiveData.postValue(e)
                isBonusActivateProcessing = false
            }

            override fun onError(errorMessage: String?, errorCode: String?, fieldsErrors: FieldsErrorsHolder?) {
                serverErrorLiveData.postValue(ServerError(errorMessage!!, errorCode!!, fieldsErrors))
                isBonusActivateProcessing = false
            }
        })
    }

    fun bonusActivate(bonusId: String, promoCode: String) {
        if (!isBonusActivateProcessing) {
            isBonusActivateProcessing = true
            ApolloProcessorKt.bonusActivate(bonusId, promoCode, object : GenericTarget<Int> {
                override fun onSuccess(bonusId: Int?) {
                    bonusActivateLiveData.postValue(bonusId)
                }

                override fun onFailure(e: ApolloException?) {
                    apolloExceptionLiveData.postValue(e)
                    isBonusActivateProcessing = false
                }

                override fun onError(errorMessage: String?, errorCode: String?, fieldsErrors: FieldsErrorsHolder?) {
                    serverErrorLiveData.postValue(ServerError(errorMessage!!, errorCode!!, fieldsErrors))
                    isBonusActivateProcessing = false
                }
            })
        }
    }

    fun bonusDeactivate(bonusId: String) {
        ApolloProcessorKt.bonusDeactivate(bonusId, object : GenericTarget<Boolean> {
            override fun onSuccess(isDeactivated: Boolean?) {
                bonusDeactivateLiveData.postValue(isDeactivated == true)
            }

            override fun onFailure(e: ApolloException?) {
                apolloExceptionLiveData.postValue(e)
            }

            override fun onError(errorMessage: String?, errorCode: String?, fieldsErrors: FieldsErrorsHolder?) {
                serverErrorLiveData.postValue(ServerError(errorMessage!!, errorCode!!, fieldsErrors))
            }
        })
    }

    fun tryBonusPromoCode(promotionId: Int, promoCode: String) {
        if (promoCode.length < PROMOCODE_MIN_LENGTH) {
            _tryBonusPromoCodeLiveData.postValue(Resource.error(Pair(promotionId, ServerError("", INVALID_PROMOCODE, null))))
            return
        }

        ApolloProcessorKt.tryBonusPromoCode(promoCode, object : GenericTarget<Boolean> {
            override fun onSuccess(t: Boolean?) {
                _tryBonusPromoCodeLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _tryBonusPromoCodeLiveData.postValue(Resource.error(Pair(promotionId, ServerError(errorMessage, errorCode, fieldsErrors))))
            }

            override fun onFailure(e: ApolloException) {
                _tryBonusPromoCodeLiveData.postValue(Resource.failure(e))
            }

        })
    }

    fun getFormattedPromotions(bonusesResponse: BonusesResponse?, activeBonusesTitle: String,
                               availableBonusesTitle : String, permanentPromotionsTitle : String) : MutableList<PromotionItem> {
        val activatedBonuses = mutableListOf<PromotionItem>()
        val availableBonuses = mutableListOf<PromotionItem>()
        bonusesResponse?.let {
            for (bonus in bonusesResponse.bonuses) {
                if (bonus.isActivated == true) {
                    activatedBonuses.add(PromotionItem(PromotionItem.ItemType.BONUS, bonus))
                } else {
                    availableBonuses.add(PromotionItem(PromotionItem.ItemType.BONUS, bonus))
                }
            }
        }

        val permanentPromotions = mutableListOf<PromotionItem>()
        bonusesResponse?.let {
            for (permanentPromotion in bonusesResponse.permanentPromotions) {
                if (permanentPromotion.moveToAvailable == true) {
                    availableBonuses.add(PromotionItem(PromotionItem.ItemType.PERMANENT_PROMOTION, permanentPromotion))
                } else {
                    permanentPromotions.add(PromotionItem(PromotionItem.ItemType.PERMANENT_PROMOTION, permanentPromotion))
                }
            }
        }

        val promotionItems = mutableListOf<PromotionItem>()
        promotionItems.add(PromotionItem(PromotionItem.ItemType.TITLE, activeBonusesTitle))
        if (activatedBonuses.isEmpty()) {
            promotionItems.add(PromotionItem(PromotionItem.ItemType.NO_ACTIVE_BONUSES))
        } else {
            promotionItems.addAll(activatedBonuses)
        }

        if (availableBonuses.isNotEmpty()) {
            promotionItems.add(PromotionItem(PromotionItem.ItemType.TITLE, availableBonusesTitle))
            promotionItems.addAll(availableBonuses)
        }

        if (permanentPromotions.isNotEmpty()) {
            promotionItems.add(PromotionItem(PromotionItem.ItemType.TITLE, permanentPromotionsTitle))
            promotionItems.addAll(permanentPromotions)
        }

        return promotionItems
    }

    fun makeRebill(bonusId: Int, sum: Int) {
        profileRepository.makeRebill(sum)
        fastPaymentRebillingLiveData.postValue(Pair(true.also { isRebilling = it }, bonusId))
    }

    fun getFastPaymentUrl(sum: Int) {
        profileRepository.getFastPaymentUrl(sum)
    }
}
