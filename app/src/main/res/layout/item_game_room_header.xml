<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_tabs_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/categories_bg"
        android:paddingTop="@dimen/game_room_header_padding">

        <RelativeLayout
            android:id="@+id/tab_slots"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_marginTop="32dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tab_new"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginTop="16dp">

            <ImageView
                android:id="@+id/iv_slots"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/tv_slots"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="8dp"
                android:src="@drawable/ic_cherries" />

            <TextView
                android:id="@+id/tv_slots"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/size_16"
                android:text="@string/slots"
                android:textColor="@color/white" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/tab_new"
            android:layout_width="0dp"
            android:layout_height="80dp"
            app:layout_constraintBottom_toBottomOf="@+id/tab_slots"
            app:layout_constraintLeft_toRightOf="@+id/tab_slots"
            app:layout_constraintRight_toLeftOf="@+id/tab_favorite"
            app:layout_constraintTop_toTopOf="@+id/tab_slots">

            <ImageView
                android:id="@+id/iv_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/tv_new"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="8dp"
                android:src="@drawable/ic_new" />

            <TextView
                android:id="@+id/tv_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/size_16"
                android:text="@string/new_games"
                android:textColor="@color/white" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/tab_favorite"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/tab_slots"
            app:layout_constraintLeft_toRightOf="@+id/tab_new"
            app:layout_constraintRight_toLeftOf="@+id/tab_tables"
            app:layout_constraintTop_toTopOf="@+id/tab_slots">

            <ImageView
                android:id="@+id/iv_favorite"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/tv_favorite"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="8dp"
                android:src="@drawable/ic_favourite" />

            <TextView
                android:id="@+id/tv_favorite"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/size_16"
                android:text="@string/favorite_games"
                android:textColor="@color/white" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/tab_tables"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/tab_slots"
            app:layout_constraintLeft_toRightOf="@+id/tab_favorite"
            app:layout_constraintRight_toRightOf="@+id/tab_ghost"
            app:layout_constraintTop_toTopOf="@+id/tab_slots">

            <ImageView
                android:id="@+id/iv_tables"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/tv_tables"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="8dp"
                android:src="@drawable/ic_roulette" />

            <TextView
                android:id="@+id/tv_tables"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/size_16"
                android:text="@string/table_games"
                android:textColor="@color/white" />

        </RelativeLayout>

        <View
            android:id="@+id/tab_ghost"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tab_slots"
            app:layout_constraintLeft_toRightOf="@+id/tab_tables"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tab_slots" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#1affffff" />

    <Spinner
        android:id="@+id/spinner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/size_16"
        android:layout_marginTop="8dp"
        android:layout_marginRight="@dimen/size_16"
        android:background="@android:color/transparent"
        android:popupBackground="@drawable/game_room_spinner_bg" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="@dimen/size_16"
        android:background="#1affffff" />

</LinearLayout>
