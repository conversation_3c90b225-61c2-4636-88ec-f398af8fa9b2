package com.abrand.custom.ui.bonusbalances

import com.abrand.custom.ui.game.BonusBalanceWon
import kotlinx.coroutines.flow.Flow

interface IBonusBalancesRepo {
    suspend fun obtainBonusBalances(): List<BonusBalance>
    suspend fun obtainBonusBalancesCount(): Int
    suspend fun activateBonus(bonusId: Int): Boolean
    suspend fun deleteBonus(bonusId: Int): Boolean
    fun getBonusBalanceWonSubscription(): Flow<BonusBalanceWon>
}
