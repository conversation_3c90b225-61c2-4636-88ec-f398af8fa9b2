<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_root"
    android:layout_width="match_parent"
    android:layout_height="@dimen/news_card_height"
    android:layout_marginLeft="@dimen/news_card_side_margin"
    android:layout_marginTop="@dimen/size_16"
    android:layout_marginRight="@dimen/news_card_side_margin"
    android:background="@drawable/news_card_default_bg">

    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0.5" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="@dimen/size_16"
        android:layout_marginRight="@dimen/size_16"
        android:gravity="start"
        android:textColor="#FFFFFF"
        android:textFontWeight="500"
        android:textSize="18sp"
        app:layout_constraintEnd_toStartOf="@+id/tv_month_name"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_month_day"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="31dp"
        android:textColor="@color/white"
        android:textSize="23sp"
        app:layout_constraintEnd_toEndOf="@+id/tv_month_name"
        app:layout_constraintStart_toStartOf="@+id/tv_month_name"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_month_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:layout_marginRight="8dp"
        android:textAllCaps="true"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_month_day" />

</androidx.constraintlayout.widget.ConstraintLayout>