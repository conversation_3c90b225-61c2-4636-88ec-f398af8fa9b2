package com.abrand.custom.ui.gameroom

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.data.entity.LoadGamesResult
import com.abrand.custom.data.entity.LocalGameItem
import com.abrand.custom.interfaces.LoadTarget
import com.abrand.custom.network.ApolloProcessor
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.tools.SingleLiveEvent
import com.abrand.custom.type.*
import com.apollographql.apollo3.exception.ApolloException

class GameRoomViewModel : ViewModel() {
    val gamesLiveData = SingleLiveEvent<LoadGamesResult>()
    val gamesLoadApolloExceptionLiveData = SingleLiveEvent<ApolloException?>()
    val LOAD_STEP = 21

    fun loadNewGames(offset: Int, gameOrder: GameListOrderField) {
        ApolloProcessorKt.loadNewGames(LOAD_STEP, offset, gameOrder, getOrderDirection(gameOrder), object:LoadTarget {
            override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                gamesLiveData.postValue(LoadGamesResult(localGameItems, total, offset))
            }

            override fun onFailure(e: ApolloException) {
                gamesLoadApolloExceptionLiveData.postValue(e)
            }

        }, viewModelScope)
    }

    // TODO: remove after refactor as unused
//    fun loadByAttribute(attribute: GameAttributeType, gameOrder: GameOrderField, offset: Int) {
//        ApolloProcessorKt.loadByAttribute(attribute, LOAD_STEP, offset, gameOrder,
//                getOrderDirection(gameOrder), object : LoadTarget {
//            override fun onSuccess(gameThumbs: MutableList<GameThumb>, total: Int, offset: Int) {
//                gamesLiveData.postValue(LoadGamesResult(gameThumbs, total, offset))
//            }
//
//            override fun onFailure(e: ApolloException?) {
//                gamesLoadApolloExceptionLiveData.postValue(e)
//            }
//
//        }, viewModelScope)
//    }

    fun loadFavouriteGames(gameOrder: GameListOrderField, offset: Int) {
        ApolloProcessorKt.loadFavouriteGames(gameOrder, getOrderDirection(gameOrder), LOAD_STEP, offset,
                object : LoadTarget {
                    override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                        gamesLiveData.postValue(LoadGamesResult(localGameItems, total, offset))
                    }

                    override fun onFailure(e: ApolloException?) {
                        gamesLoadApolloExceptionLiveData.postValue(e)
                    }
        }, viewModelScope)
    }

    fun loadSlotGames(offset: Int, gameOrder: GameListOrderField) {
        ApolloProcessorKt.loadSlotGames(LOAD_STEP, offset, gameOrder, getOrderDirection(gameOrder), object:LoadTarget {
            override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                gamesLiveData.postValue(LoadGamesResult(localGameItems, total, offset))
            }

            override fun onFailure(e: ApolloException) {
                gamesLoadApolloExceptionLiveData.postValue(e)
            }
        }, viewModelScope)
    }

    fun loadTableGames(offset: Int, gameOrder: GameListOrderField) {
        ApolloProcessorKt.loadTableGames(LOAD_STEP, offset, gameOrder, getOrderDirection(gameOrder), object:LoadTarget {
            override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                gamesLiveData.postValue(LoadGamesResult(localGameItems, total, offset))
            }

            override fun onFailure(e: ApolloException) {
                gamesLoadApolloExceptionLiveData.postValue(e)
            }
        }, viewModelScope)
    }

    private fun getOrderDirection(gameOrderField: GameListOrderField): OrderDirection {
        return when (gameOrderField) {
            GameListOrderField.alphabet, GameListOrderField.payout -> OrderDirection.asc
            else -> OrderDirection.desc
        }
    }
}
