package com.abrand.custom.ui.game

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.abrand.custom.data.Resource
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessor
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException

class InGameMenuViewModel : ViewModel() {
    val resetBonusBalanceLiveData =
        MutableLiveData<Resource<Boolean, ServerError, ApolloException>>()

    fun resetBonusBalance() {
        ApolloProcessorKt.resetBonusBalance(object : GenericTarget<Boolean> {
            override fun onSuccess(t: Boolean?) {
                resetBonusBalanceLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String,
                                 fieldsErrors: FieldsErrorsHolder?) {
                resetBonusBalanceLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                resetBonusBalanceLiveData.postValue(Resource.failure(e))
            }
        })
    }
}
