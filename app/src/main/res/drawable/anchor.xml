<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="56dp"
    android:height="71dp"
    android:viewportWidth="56"
    android:viewportHeight="71">
  <group>
    <clip-path
        android:pathData="M0,0h56v71h-56z"/>
    <path
        android:pathData="M55.288,65.296C55.027,64.604 54.409,64.288 53.432,64.345C54.051,64.571 54.377,64.976 54.409,65.557C54.181,65.169 53.79,64.862 53.235,64.636C53.821,65.121 54.083,65.622 54.018,66.138C53.953,66.655 53.347,67.033 52.199,67.268C53.23,62.145 37.787,62.042 29.012,64.119C20.24,66.196 2.689,69.677 2.168,64.772C1.647,59.866 14.285,53.907 17.727,41.754C18.272,39.828 18.667,37.86 18.949,35.908C18.632,35.612 18.354,35.257 18.148,34.827C17.202,32.859 17.202,32.147 18.148,32.689C17.236,31.139 17.252,30.346 18.196,30.315C17.022,28.795 16.959,27.925 18.007,27.705C17.228,26.322 17.291,25.461 18.196,25.128C17.616,24.077 18.461,23.568 19.459,23.377C19.311,20.336 19.051,17.779 18.68,15.708L18.154,8.307L20.488,5.235C22.352,8.123 23.231,14.496 23.127,24.357C23.346,25.479 24.204,32.285 24.165,34.831C24.149,35.965 23.342,36.659 22.309,36.878C19.754,54.207 10.408,58.229 7.25,62.213C4.97,65.09 7.104,65.638 13.651,63.861C29.908,62.859 40.333,61.954 44.928,61.146C51.819,59.934 55.093,62.988 55.679,63.521C56.07,63.876 55.941,64.468 55.288,65.296Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="1.428"
            android:startY="64.948"
            android:endX="1.428"
            android:endY="2.796"
            android:type="linear">
          <item android:offset="0" android:color="#FF4F2A29"/>
          <item android:offset="1" android:color="#FF6D3E38"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M15.921,53.382C15.732,53.636 15.541,53.881 15.35,54.12C15.107,54.105 14.87,54.054 14.642,53.96C13.625,53.544 12.984,52.408 12.593,51.357C12.763,51.142 12.932,50.927 13.099,50.706C13.422,51.729 13.985,52.95 14.907,53.327C15.216,53.454 15.556,53.469 15.923,53.382H15.921Z"
        android:strokeAlpha="0.1"
        android:fillColor="#900040"
        android:fillAlpha="0.1"/>
    <path
        android:pathData="M23.123,21.925C23.127,22.172 23.127,22.424 23.129,22.676C22.993,22.726 22.856,22.766 22.712,22.79C21.642,22.963 20.516,22.396 19.309,21.07C19.275,20.649 19.24,20.25 19.205,19.869C20.473,21.502 21.625,22.277 22.598,22.113C22.782,22.084 22.956,22.014 23.123,21.924V21.925Z"
        android:strokeAlpha="0.1"
        android:fillColor="#900040"
        android:fillAlpha="0.1"/>
    <path
        android:pathData="M21.634,40.487C21.584,40.711 21.532,40.937 21.48,41.158C21.287,41.145 21.095,41.125 20.898,41.077C19.902,40.836 19.014,40.179 18.296,39.467C18.356,39.197 18.417,38.929 18.473,38.656C19.166,39.416 20.069,40.172 21.063,40.411C21.258,40.457 21.449,40.481 21.636,40.487H21.634Z"
        android:strokeAlpha="0.1"
        android:fillColor="#900040"
        android:fillAlpha="0.1"/>
    <path
        android:pathData="M3.473,64.334C2.985,64.327 2.565,64.248 2.181,64.128C2.207,63.907 2.251,63.687 2.323,63.46C2.613,63.558 2.935,63.628 3.31,63.644C4.304,63.692 5.489,63.455 6.694,63.082C6.555,63.383 6.509,63.633 6.544,63.838C5.502,64.137 4.476,64.334 3.566,64.334C3.534,64.334 3.505,64.334 3.475,64.334H3.473Z"
        android:strokeAlpha="0.1"
        android:fillColor="#900040"
        android:fillAlpha="0.1"/>
    <path
        android:pathData="M19.168,47.912C19.036,48.199 18.903,48.482 18.767,48.756C18.111,48.913 17.308,48.95 16.692,48.482C16.16,48.078 15.884,47.386 15.845,46.415C16.14,45.856 16.416,45.28 16.672,44.688C16.305,46.777 16.687,47.614 17.113,47.938C17.686,48.372 18.615,48.124 19.17,47.908L19.168,47.912Z"
        android:strokeAlpha="0.1"
        android:fillColor="#900040"
        android:fillAlpha="0.1"/>
    <path
        android:pathData="M18.447,12.445L18.384,11.553C19.492,12.408 20.642,13.043 21.623,13.032C21.953,13.025 22.242,12.934 22.502,12.769C22.535,13.003 22.567,13.242 22.597,13.488C22.302,13.622 21.985,13.709 21.636,13.716C21.618,13.716 21.599,13.716 21.582,13.716C20.601,13.716 19.507,13.188 18.447,12.445Z"
        android:strokeAlpha="0.1"
        android:fillColor="#900040"
        android:fillAlpha="0.1"/>
    <path
        android:pathData="M18.146,32.691C17.833,32.158 17.67,31.748 17.599,31.428C20.412,34.067 23.398,33.241 24.102,32.993C24.121,33.272 24.134,33.534 24.145,33.784C22.203,34.608 20.205,34.244 18.148,32.691H18.146Z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.856"
            android:startY="59.209"
            android:endX="16.856"
            android:endY="-4.378"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF900040"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M53.41,66.83C53.055,67.029 52.653,67.174 52.197,67.268C53.228,62.145 37.785,62.042 29.011,64.119C20.238,66.196 2.687,69.677 2.166,64.772C3.34,67.978 7.736,59.368 12.689,54.812C17.642,50.256 22.159,37.821 20.867,37.36C19.574,36.898 18.601,38.009 18.595,38.002C18.727,37.304 18.847,36.605 18.947,35.91C18.63,35.614 18.352,35.259 18.146,34.829C17.961,34.44 17.825,34.122 17.727,33.854C20.238,36.554 22.367,36.652 24.113,34.144C24.125,34.139 24.139,34.132 24.156,34.12C24.163,34.378 24.169,34.621 24.165,34.827C24.149,35.962 23.342,36.655 22.309,36.874C19.754,54.203 10.408,58.225 7.25,62.21C4.97,65.086 7.104,65.634 13.651,63.858C28.074,62.969 38.041,62.134 43.554,61.356C44.01,61.66 44.483,61.856 44.976,61.919C51.422,62.73 53.028,65.326 53.41,66.828V66.83Z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="1.428"
            android:startY="75.635"
            android:endX="1.428"
            android:endY="-0.883"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF900040"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.782,36.955C18.84,36.607 18.897,36.258 18.947,35.91C18.94,35.903 18.932,35.894 18.925,35.884C19.268,36.129 19.628,36.293 19.954,36.25C20.674,36.159 20.587,35.259 19.693,33.554C21.962,32.805 21.962,31.959 19.693,31.016C21.54,30.383 21.532,29.573 19.676,28.588C22.064,27.76 22.001,26.884 19.485,25.961C21.519,26.095 21.885,25.444 20.585,24.004C21.577,22.68 19.138,14.031 18.372,11.426L18.15,8.307L20.407,5.338C20.457,5.345 20.507,5.353 20.557,5.36C20.993,6.158 23.264,10.979 23.123,24.357C23.342,25.479 24.201,32.285 24.162,34.831C24.145,35.965 23.338,36.659 22.305,36.878C21.842,40.019 21.154,42.72 20.321,45.067C22.046,37.816 22.231,37.35 20.726,36.786C19.218,36.219 18.78,36.959 18.78,36.957L18.782,36.955Z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="17.412"
            android:startY="67.266"
            android:endX="17.412"
            android:endY="-14.77"
            android:type="linear">
          <item android:offset="0.2" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF900040"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.196,30.315C17.768,29.759 17.538,29.322 17.434,28.974C18.988,30.385 21.067,30.756 23.67,30.088C23.715,30.031 23.767,29.96 23.831,29.873C23.867,30.219 23.9,30.565 23.934,30.907C21.851,31.737 19.939,31.54 18.198,30.315H18.196Z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.689"
            android:startY="58.327"
            android:endX="16.689"
            android:endY="-4.543"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF900040"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.196,25.128C22.139,26.921 24.288,24.337 22.333,24.304C21.03,24.282 20.071,23.973 19.457,23.377C19.381,21.813 19.266,20.428 19.147,19.273L18.68,13.091L18.471,12.769L18.154,8.307L20.488,5.235C22.352,8.123 23.231,14.496 23.127,24.357C23.179,24.624 23.268,25.218 23.372,25.992C23.277,26.007 23.177,26.027 23.067,26.051C20.711,26.59 19.088,26.283 18.196,25.13V25.128Z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="17.523"
            android:startY="65.5"
            android:endX="17.523"
            android:endY="-14.2"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="0.7" android:color="#FF900040"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.005,27.707C17.716,27.191 17.583,26.776 17.544,26.437C20.09,28.174 22.344,27.885 23.531,27.258C23.563,27.517 23.594,27.784 23.628,28.06C23.544,28.1 23.47,28.131 23.375,28.183C21.356,29.261 19.565,29.101 18.005,27.707Z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.8"
            android:startY="56.193"
            android:endX="16.8"
            android:endY="-3.587"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF900040"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M51.915,52.711C50.873,58.593 45.953,63.792 37.157,68.309L37.442,55.981C41.748,53.09 44.292,50.191 45.073,47.281C45.864,44.338 43.943,39.677 42.608,36.955C42.465,37.987 42.477,39.004 42.501,39.583C42.512,39.852 42.297,40.073 42.028,40.073H41.44C41.221,40.073 41.034,39.922 40.984,39.71C39.762,34.602 36.699,30.037 36.163,29.264C36.109,29.185 36.083,29.097 36.083,29.003V28.181C36.083,27.881 36.361,27.653 36.656,27.718C41.457,28.764 46.449,31.586 47.643,32.288C47.806,32.384 47.895,32.568 47.869,32.756L47.715,33.892C47.671,34.223 47.3,34.398 47.007,34.233C46.178,33.764 45.497,33.585 44.941,33.617C48.575,37.235 53.424,44.208 51.917,52.711H51.915Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="46.228"
            android:startY="24.464"
            android:endX="54.8"
            android:endY="49.54"
            android:type="linear">
          <item android:offset="0" android:color="#FF817E95"/>
          <item android:offset="0.9" android:color="#FF66627A"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M37.157,68.309L37.264,63.644C42.671,61.7 45.825,58.604 47.32,56.541C50.691,51.889 52.46,45.089 49.315,39.319C51.396,42.946 52.831,47.542 51.915,52.711C50.873,58.593 45.953,63.792 37.157,68.309Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="36.437"
            android:startY="84.096"
            android:endX="36.437"
            android:endY="41.662"
            android:type="linear">
          <item android:offset="0.2" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M37.442,55.981C38.736,55.11 40.033,54.041 41.331,52.777H41.364C39.172,54.756 38.379,56.522 38.981,58.076C41.783,65.298 55.416,51.556 49.315,39.32C51.396,42.948 52.831,47.544 51.915,52.713C50.873,58.595 45.953,63.793 37.157,68.311L37.442,55.983V55.981Z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="47.377"
            android:startY="71.699"
            android:endX="33.65"
            android:endY="68.337"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M47.867,32.754L47.735,33.723C47.162,34.768 44.967,32.141 44.553,32.06C44.064,31.963 43.022,32.254 41.946,31.834C41.153,31.523 38.034,29.013 36.456,27.719C36.519,27.705 36.585,27.701 36.654,27.716C41.455,28.762 46.447,31.584 47.641,32.287C47.761,32.358 47.841,32.478 47.863,32.61C47.871,32.656 47.873,32.704 47.867,32.752V32.754Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="35.714"
            android:startY="37.597"
            android:endX="35.714"
            android:endY="15.782"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M36.648,70.139C36.383,70.884 35.465,71.167 34.826,70.695C32.997,69.339 29.459,66.058 26.591,59.423C26.155,58.413 25.947,57.319 25.973,56.221C26.018,54.365 26.148,51.201 26.513,48.197C26.609,47.406 26.58,46.606 26.428,45.823C25.606,41.581 22.812,27.526 20.726,21.388C18.504,20.073 16.95,17.412 15.847,14.48C14.219,10.15 15.3,2.652 20.529,1.843C24.673,1.201 27.51,4.139 29.074,8.987C30.468,13.308 29.61,19.251 26.606,21.416C27.014,25.433 28.427,38.193 30.516,44.616C30.789,45.453 31.201,46.231 31.742,46.928C32.986,48.537 35.554,51.972 36.914,54.622C37.388,55.546 37.646,56.563 37.704,57.598C37.85,60.287 37.995,66.337 36.647,70.141L36.648,70.139ZM20.021,13.869C20.779,16.44 21.994,18.668 23.998,18.475C26.216,18.263 26.973,13.722 26.185,10.606C25.456,7.727 24.134,5.982 22.202,6.364C19.765,6.844 19.261,11.297 20.019,13.869H20.021Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="14.742"
            android:startY="-41.975"
            android:endX="47.462"
            android:endY="69.397"
            android:type="linear">
          <item android:offset="0" android:color="#FF908EA3"/>
          <item android:offset="1" android:color="#FF6D6783"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M27.774,61.146C27.62,61.029 27.416,60.87 27.16,60.674C26.969,60.271 26.778,59.855 26.593,59.423C26.157,58.413 25.949,57.319 25.975,56.221C26.02,54.365 26.149,51.201 26.515,48.197C26.611,47.406 26.581,46.606 26.429,45.823C26.374,45.534 26.307,45.197 26.235,44.824C26.55,45.111 26.733,45.778 26.722,46.972C26.672,52.26 27.154,55.562 28.165,56.879L27.774,61.145V61.146Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="2.373"
            android:startY="-38.792"
            android:endX="35.112"
            android:endY="72.634"
            android:type="linear">
          <item android:offset="0.2" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M28.165,56.879C32.222,60.284 33.822,64.354 32.968,69.094L27.319,59.631L28.167,56.879H28.165Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="32.932"
            android:startY="59.596"
            android:endX="36.806"
            android:endY="72.746"
            android:type="linear">
          <item android:offset="0.5" android:color="#FF002EAE"/>
          <item android:offset="1" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M34.826,70.695C34.462,70.426 34.028,70.075 33.548,69.643C36.19,71.664 35.135,61.338 34.874,58.386C34.612,55.431 29.921,50.675 28.488,46.606C27.054,42.539 23.47,22.816 22.428,22.751C21.386,22.687 21.612,24.302 21.612,24.302C21.308,23.215 21.009,22.225 20.726,21.386C18.504,20.071 16.95,17.41 15.847,14.478C15.773,14.281 15.706,14.077 15.645,13.867C17.369,17.5 21.729,22.762 25.817,20.423C30.364,17.822 28.797,8.229 28.782,8.141C28.885,8.415 28.983,8.695 29.076,8.984C30.47,13.304 29.611,19.247 26.607,21.412C27.015,25.429 28.428,38.189 30.518,44.612C30.791,45.449 31.202,46.227 31.744,46.924C32.988,48.534 35.556,51.968 36.916,54.618C37.39,55.542 37.648,56.559 37.705,57.595C37.852,60.284 37.997,66.334 36.648,70.137C36.383,70.882 35.465,71.165 34.826,70.693V70.695Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="14.482"
            android:startY="-42.434"
            android:endX="48.244"
            android:endY="72.491"
            android:type="linear">
          <item android:offset="0.5" android:color="#FF002EAE"/>
          <item android:offset="1" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M32.968,69.096C22.598,70.647 13.896,68.708 6.859,63.278C-2.883,55.763 0.612,45.44 4.282,38.482C4.493,38.081 4.059,37.645 3.649,37.845C2.75,38.283 2.014,38.792 1.589,39.112C1.368,39.278 1.057,39.223 0.907,38.993L0.261,38.003C0.171,37.865 0.156,37.691 0.228,37.542C3.33,31.091 12.509,24.929 14.354,23.735C14.558,23.603 14.827,23.649 14.986,23.833C15.815,24.795 16.001,26.281 16.038,26.719C16.045,26.802 16.029,26.879 15.991,26.954C13.514,31.981 13.557,38.491 13.594,39.828C13.598,39.986 13.523,40.13 13.392,40.222L12.25,41.018C12.003,41.191 11.652,41.095 11.541,40.818C11.389,40.439 11.228,40.097 11.057,39.784C10.842,39.39 10.241,39.502 10.175,39.944C9.564,43.989 9.062,49.465 10.28,51.937C12.333,56.105 24.549,58.141 28.167,56.881C30.791,60.048 32.391,64.121 32.97,69.096H32.968Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="25.886"
            android:startY="36.788"
            android:endX="39.752"
            android:endY="83.974"
            android:type="linear">
          <item android:offset="0" android:color="#FF908EA3"/>
          <item android:offset="1" android:color="#FF6D6783"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M15.315,24.304C15.862,25.223 16.004,26.349 16.038,26.719C16.045,26.802 16.029,26.879 15.991,26.954C13.514,31.981 13.557,38.491 13.594,39.828C13.598,39.986 13.523,40.13 13.392,40.222C11.862,41.255 11.061,32.353 9.791,32.498C8.521,32.643 8.276,32.16 9.791,29.638C11.302,27.122 15.298,24.317 15.315,24.306V24.304Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="8.048"
            android:startY="48.081"
            android:endX="8.048"
            android:endY="2.538"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M0.226,37.542C1.285,35.342 3.05,33.177 4.99,31.229C4.964,31.258 0.897,36.041 1.044,37.44C1.19,38.846 4.734,35.654 7.543,34.334C10.353,33.011 9.87,33.572 11.541,40.816C11.389,40.439 11.228,40.095 11.057,39.782C10.842,39.389 10.241,39.501 10.175,39.942C9.965,41.325 9.772,42.876 9.652,44.416C9.843,38.991 9.157,36.375 7.592,36.569C5.246,36.859 2.754,46.991 3.438,52.13C4.122,57.269 13.994,64.345 25.723,65.219C28.011,65.39 30.147,65.274 32.131,64.961C32.495,66.221 32.793,67.603 32.968,69.096C22.598,70.647 13.896,68.708 6.859,63.278C-2.883,55.763 0.612,45.44 4.282,38.482C4.493,38.081 4.059,37.645 3.649,37.845C2.75,38.283 2.014,38.792 1.589,39.112C1.368,39.278 1.057,39.223 0.907,38.993L0.261,38.003C0.171,37.865 0.156,37.691 0.228,37.542H0.226Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="-0.501"
            android:startY="74.936"
            android:endX="-0.501"
            android:endY="38.241"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M4.092,38.844C4.155,38.723 4.217,38.601 4.28,38.482C4.491,38.081 4.057,37.645 3.647,37.845C3.071,38.127 2.574,38.432 2.166,38.702C5.743,36.192 7.936,35.013 8.741,35.163C9.948,35.39 11.311,40.288 11.315,40.303C11.23,40.12 11.144,39.946 11.054,39.782C10.838,39.389 10.238,39.501 10.171,39.942C10.152,40.06 10.136,40.183 10.117,40.303C10.076,37.766 9.401,36.445 8.094,36.341C6.154,36.184 4.139,38.782 4.091,38.844H4.092Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="1.428"
            android:startY="40.319"
            android:endX="1.428"
            android:endY="30.239"
            android:type="linear">
          <item android:offset="0.5" android:color="#FF002EAE"/>
          <item android:offset="1" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M42.61,36.955C42.362,35.903 42.467,34.58 42.923,33.837C43.228,33.342 43.899,33.267 44.939,33.615C45.16,33.833 45.384,34.071 45.612,34.314C44.967,33.83 44.086,33.401 43.444,33.966C42.684,34.636 42.406,35.631 42.61,36.951V36.955Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="41.778"
            android:startY="36.971"
            android:endX="41.778"
            android:endY="30.368"
            android:type="linear">
          <item android:offset="0.5" android:color="#FF002EAE"/>
          <item android:offset="1" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M14.985,23.833C15.111,23.978 15.216,24.138 15.315,24.302C9.188,28.698 5.832,31.742 5.246,33.436C4.61,35.274 6.52,34.733 7.63,34.292C7.601,34.306 7.573,34.317 7.543,34.332C4.734,35.654 1.19,38.846 1.044,37.439C0.897,36.037 4.964,31.258 4.99,31.227C8.737,27.466 13.138,24.521 14.352,23.733C14.556,23.601 14.825,23.647 14.985,23.831V23.833Z"
        android:strokeAlpha="0.7"
        android:fillAlpha="0.7">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="1.187"
            android:startY="22.974"
            android:endX="15.317"
            android:endY="22.974"
            android:type="linear">
          <item android:offset="0" android:color="#FF000000"/>
          <item android:offset="1" android:color="#FF9A77F6"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M28.165,56.879C29.841,58.91 31.163,61.603 32.131,64.96C30.147,65.27 28.009,65.386 25.723,65.217C13.994,64.345 4.122,57.267 3.438,52.128C2.754,46.989 5.246,36.857 7.592,36.567C7.944,36.523 8.237,36.672 8.48,36.953C5.444,43.45 4.691,48.961 6.223,53.485C8.521,60.273 26.552,64.198 28.556,63.035C29.891,62.259 29.762,60.208 28.165,56.879Z"
        android:strokeAlpha="0.7"
        android:fillAlpha="0.7">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="3.746"
            android:startY="32.907"
            android:endX="36.788"
            android:endY="53.6"
            android:type="linear">
          <item android:offset="0" android:color="#FF9A77F6"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M43.708,59.596C43.042,59.702 42.603,59.577 42.532,59.193C42.48,58.917 42.469,58.649 42.482,58.387C52.89,52.325 46.367,40.305 46.361,40.297C44.963,36.512 42.788,35.563 43.606,34.582C43.728,34.435 43.878,34.354 44.043,34.314C45.84,36.547 47.453,39.455 48.883,43.038C53.183,53.813 43.708,59.594 43.706,59.594L43.708,59.596Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="39.849"
            android:startY="28.657"
            android:endX="52.156"
            android:endY="46.593"
            android:type="linear">
          <item android:offset="0" android:color="#FF9A77F6"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M36.456,27.719C36.958,28.131 37.616,28.665 38.306,29.213C35.588,27.153 40.431,35.84 40.984,35.938C41.536,36.035 42.345,33.758 42.345,33.758C42.13,34.566 42.072,36.03 41.85,37.829C41.622,39.67 41.333,40.299 40.984,39.71C39.762,34.602 36.699,30.037 36.163,29.264C36.109,29.185 36.083,29.097 36.083,29.003V28.181C36.083,27.951 36.248,27.765 36.458,27.719H36.456Z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="36.548"
            android:startY="27.057"
            android:endX="42.352"
            android:endY="27.057"
            android:type="linear">
          <item android:offset="0" android:color="#FF9A77F6"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M15.647,13.871C14.41,9.751 15.402,3.449 19.657,2.055C22.179,3.057 22.483,4.792 22.029,6.419C20.677,6.808 19.963,8.459 19.76,10.273C19.732,10.304 19.709,10.33 19.693,10.347C18.955,8.537 18.065,7.804 17.021,8.147C15.458,8.662 15.645,13.873 15.645,13.873L15.647,13.871Z"
        android:strokeAlpha="0.7"
        android:fillAlpha="0.7">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="20.842"
            android:startY="-2.98"
            android:endX="24.04"
            android:endY="7.942"
            android:type="linear">
          <item android:offset="0" android:color="#FF9A77F6"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M36.648,70.139C36.383,70.884 35.465,71.167 34.826,70.695C34.321,70.319 33.682,69.791 32.966,69.096C22.597,70.647 13.894,68.708 6.857,63.278C2.437,59.866 0.745,55.879 0.556,51.887C0.829,53.033 0.896,58.558 15.7,62.909C18.729,64.102 22.137,64.948 25.721,65.215C27.125,65.32 28.464,65.305 29.752,65.215C30.565,65.158 31.362,65.077 32.13,64.954C31.862,64.031 31.562,63.179 31.247,62.395C31.236,62.366 31.223,62.336 31.212,62.307C31.128,62.103 31.045,61.901 30.959,61.707C33.698,59.999 34.585,57.845 33.615,55.242C36.367,58.115 37.546,58.158 37.155,55.369C37.14,55.268 37.132,55.176 37.123,55.086C37.461,55.88 37.657,56.728 37.704,57.593C37.85,60.282 37.995,66.332 36.647,70.135L36.648,70.139Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="32.08"
            android:startY="35.923"
            android:endX="41.762"
            android:endY="68.835"
            android:type="linear">
          <item android:offset="0.3" android:color="#FF002EAE"/>
          <item android:offset="0.9" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M20.727,21.388C23.058,22.808 25.018,22.817 26.609,21.416C26.494,24.237 24.532,24.228 20.727,21.388Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="26.442"
            android:startY="24.335"
            android:endX="19.236"
            android:endY="22.579"
            android:type="linear">
          <item android:offset="0.5" android:color="#FF002EAE"/>
          <item android:offset="1" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M28.631,46.961C28.584,46.843 28.532,46.724 28.49,46.606C27.056,42.539 23.472,22.816 22.43,22.751C21.388,22.687 21.614,24.302 21.614,24.302C21.31,23.215 21.011,22.225 20.727,21.386C20.062,20.993 19.463,20.465 18.91,19.858L20.594,19.937C22.229,21.024 24.049,21.436 25.817,20.424C25.938,20.355 26.053,20.277 26.166,20.2L27.75,20.275C27.41,20.719 27.032,21.109 26.607,21.416C27.015,25.433 28.428,38.193 30.518,44.616C30.637,44.984 30.789,45.337 30.954,45.672C28.731,42.512 28.632,46.739 28.629,46.961H28.631Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="10.013"
            android:startY="-41.736"
            android:endX="44.177"
            android:endY="74.553"
            android:type="linear">
          <item android:offset="0.5" android:color="#FFFFFFFF"/>
          <item android:offset="0.8" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M8.48,36.955C8.422,36.891 8.363,36.833 8.302,36.782C9.874,38.049 9.652,44.414 9.653,44.416C9.425,47.381 9.479,50.307 10.28,51.935C10.616,52.615 11.224,53.237 12.018,53.798C10.462,52.93 8.079,51.858 8.177,53.519C8.322,55.952 10.434,58.01 10.473,58.049C8.393,56.734 6.803,55.202 6.223,53.485C4.691,48.961 5.444,43.45 8.48,36.953V36.955Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="4.914"
            android:startY="19.038"
            android:endX="19.347"
            android:endY="68.157"
            android:type="linear">
          <item android:offset="0.2" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M48.295,49.913L48.29,49.962C48.292,49.949 48.293,49.933 48.295,49.913Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="48.968"
            android:startY="49.669"
            android:endX="49.005"
            android:endY="49.777"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M43.708,59.596C43.042,59.702 42.603,59.577 42.532,59.193C42.48,58.917 42.469,58.649 42.482,58.387C52.89,52.325 46.367,40.305 46.361,40.297C44.963,36.512 42.788,35.563 43.606,34.582C43.728,34.435 43.878,34.354 44.043,34.314C45.84,36.547 47.453,39.455 48.883,43.038C53.183,53.813 43.708,59.594 43.706,59.594L43.708,59.596Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="39.849"
            android:startY="28.657"
            android:endX="52.156"
            android:endY="46.593"
            android:type="linear">
          <item android:offset="0" android:color="#FF9A77F6"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M41.104,52.994C41.305,52.803 41.503,52.608 41.703,52.406C41.714,52.395 41.726,52.382 41.737,52.371C41.931,52.172 42.122,51.972 42.312,51.764C42.321,51.755 42.33,51.744 42.338,51.734C43.565,50.382 44.655,48.835 45.073,47.283C45.499,45.697 45.136,43.617 44.503,41.607C44.233,40.746 43.916,39.902 43.58,39.098C43.417,38.71 43.254,38.334 43.092,37.979C43.087,37.967 43.081,37.957 43.076,37.944C42.916,37.595 42.758,37.262 42.608,36.955C42.465,37.987 42.477,39.004 42.501,39.583C42.512,39.852 42.297,40.073 42.028,40.073H41.44C41.221,40.073 41.034,39.922 40.984,39.71C41.333,40.297 41.622,39.67 41.85,37.829C42.191,35.066 42.143,33.077 42.875,33.077C43.365,33.077 44.053,33.256 44.939,33.615C45.13,33.806 45.328,34.01 45.525,34.22C45.61,34.312 45.697,34.406 45.785,34.499C45.889,34.613 45.994,34.731 46.098,34.849C46.371,35.156 46.643,35.478 46.927,35.827C46.764,35.669 45.076,34.065 44.045,34.31C43.967,34.328 43.893,34.358 43.808,34.406C43.736,34.45 43.667,34.505 43.608,34.578C43.561,34.636 43.526,34.691 43.498,34.748C43.489,34.766 43.483,34.785 43.476,34.803C43.461,34.841 43.448,34.882 43.441,34.921C43.437,34.943 43.435,34.965 43.433,34.985C43.431,35.024 43.431,35.064 43.437,35.104C43.439,35.125 43.441,35.147 43.444,35.167C43.454,35.217 43.469,35.268 43.487,35.322C43.491,35.334 43.495,35.346 43.498,35.358C43.73,35.951 44.503,36.727 45.306,38.077C45.312,38.086 45.317,38.097 45.323,38.108C45.399,38.235 45.473,38.368 45.549,38.504C45.831,39.019 46.109,39.607 46.365,40.299C48.023,43.356 48.668,46.531 48.301,49.821C48.362,49.025 48.488,45.089 44.437,49.498C43.374,50.881 42.262,52.045 41.103,52.994C41.101,52.993 41.101,52.991 41.101,52.991L41.104,52.994Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="41.574"
            android:startY="16.738"
            android:endX="54.362"
            android:endY="60.259"
            android:type="linear">
          <item android:offset="0.4" android:color="#FF002EAE"/>
          <item android:offset="1" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M46.927,35.831C47.756,36.857 48.575,38.027 49.315,39.319C53.003,46.716 49.477,54.657 45.493,58.189C48.006,55.873 51.954,50.728 48.885,43.036C46.74,37.659 44.045,34.312 44.045,34.312C45.078,34.069 46.764,35.671 46.927,35.829V35.831Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="45.375"
            android:startY="27.848"
            android:endX="54.002"
            android:endY="53.035"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M28.491,46.608C29.491,49.446 32.074,52.617 33.633,55.279L33.724,55.634C30.626,52.027 28.619,48.716 27.709,45.699C26.344,41.184 23.246,24.539 22.802,23.299C24.149,26.498 27.197,42.935 28.491,46.61V46.608Z"
        android:strokeAlpha="0.7"
        android:fillAlpha="0.7">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="13.203"
            android:startY="8.222"
            android:endX="41.447"
            android:endY="49.417"
            android:type="linear">
          <item android:offset="0" android:color="#FF9A77F6"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M24.451,8.145L22.802,2.652C22.982,2.169 23.507,2.086 24.378,2.404C25.686,2.882 27.514,7.242 27.069,13.186C26.772,17.15 25.749,18.914 23.998,18.475L24.451,8.143V8.145Z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="3.449"
            android:startY="-75.415"
            android:endX="33.52"
            android:endY="26.932"
            android:type="linear">
          <item android:offset="0.2" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF002EAE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M23.998,18.476C23.131,18.559 22.413,18.19 21.814,17.535C23.496,16.554 24.019,12.666 23.318,9.898C22.947,8.432 22.422,7.264 21.742,6.517C21.888,6.451 22.04,6.399 22.203,6.366C24.136,5.985 25.458,7.729 26.187,10.608C26.975,13.724 26.218,18.265 24,18.476H23.998Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="22.993"
            android:startY="-1.416"
            android:endX="28.474"
            android:endY="17.186"
            android:type="linear">
          <item android:offset="0" android:color="#FF000000"/>
          <item android:offset="1" android:color="#FF5B507B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M20.388,7.961C20.946,6.145 20.714,4.773 19.695,3.846C18.673,2.919 17.703,3.068 16.783,4.289C16.574,3.25 16.774,2.404 17.382,1.753C17.99,1.102 18.762,1.045 19.695,1.584C19.889,0.57 20.444,0.109 21.356,0.193C22.268,0.28 22.847,0.668 23.092,1.357C23.477,1.056 23.963,1.013 24.549,1.229C25.428,1.552 26.7,4.17 25.788,6.98C25.18,8.855 24.184,9.5 22.802,8.919C22.482,9.156 22.127,9.048 21.742,8.595C20.891,8.932 20.44,8.719 20.39,7.959L20.388,7.961Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="23.772"
            android:startY="-6.199"
            android:endX="29.18"
            android:endY="12.164"
            android:type="linear">
          <item android:offset="0.4" android:color="#FF6D3E38"/>
          <item android:offset="1" android:color="#FF4F2A29"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M16.781,4.291C17.08,3.252 17.79,2.717 18.91,2.684C20.59,2.636 21.421,5.737 20.931,7.53C20.442,9.324 23.489,5.963 19.693,1.584C21.007,2.299 21.745,3.373 21.909,4.803C22.153,6.947 21.894,8.176 22.682,8.022C23.472,7.869 24.66,4.83 23.09,1.356C24.1,2.303 24.442,3.747 24.113,5.686C23.62,8.594 24.252,8.496 24.937,7.957C25.619,7.418 26.504,4.483 25.363,2.062C25.988,3.116 26.429,4.996 25.786,6.979C25.178,8.853 24.182,9.499 22.801,8.917C22.48,9.155 22.126,9.046 21.74,8.594C20.889,8.93 20.438,8.717 20.388,7.957C20.946,6.142 20.714,4.77 19.695,3.842C18.673,2.915 17.703,3.064 16.783,4.286L16.781,4.291Z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.077"
            android:startY="9.197"
            android:endX="16.077"
            android:endY="1.821"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#FF900040"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M19.695,3.848C20.802,4.996 21.263,4.944 21.078,3.692C23.255,4.764 24.056,4.332 23.485,2.393C24.686,4.85 25.53,5.371 26.018,3.955C26.164,4.858 26.136,5.904 25.788,6.982C25.18,8.857 24.184,9.502 22.802,8.921C22.482,9.158 22.127,9.05 21.742,8.597C20.891,8.934 20.44,8.72 20.39,7.961C20.948,6.145 20.716,4.773 19.696,3.846L19.695,3.848Z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="18.951"
            android:startY="11.478"
            android:endX="18.951"
            android:endY="-4.635"
            android:type="linear">
          <item android:offset="0" android:color="#FF900040"/>
          <item android:offset="1" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
