package com.abrand.custom.ui.views

import android.content.Context
import android.util.AttributeSet
import android.view.animation.AnimationUtils
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.User
import com.abrand.custom.ui.home.HomeFragment

class GameLoadView : LinearLayout {
    private var currentLoaderState = LoaderState.HIDE
    var ivLoader: ImageView? = null
    var btnLoadMore: Button? = null

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {
        val rootView = inflate(context, R.layout.item_game_load, this)
        ivLoader = rootView.findViewById(R.id.iv_loader)
        btnLoadMore = rootView.findViewById(R.id.btn_load_more)
    }

    fun setGameLoadingState(isLoading: Boolean, totalGames: Int, loadedGamesCount: Int, loadStep: Int) {
        if (isLoading) {
            if (LoaderState.SHOW != currentLoaderState) {
                ivLoader?.visibility = VISIBLE
                val animation = AnimationUtils.loadAnimation(context, R.anim.rotation_loader)
                ivLoader?.startAnimation(animation)
                currentLoaderState = LoaderState.SHOW
            }
            btnLoadMore?.visibility = INVISIBLE
        } else if (!isLoading) {
            if (LoaderState.HIDE != currentLoaderState) {
                ivLoader?.clearAnimation()
                ivLoader?.visibility = GONE
                currentLoaderState = LoaderState.HIDE
            }
            setLoadMoreView(totalGames, loadedGamesCount, loadStep)
        }
    }

    private fun setLoadMoreView(totalGames: Int, loadedGamesCount: Int, loadStep: Int) {
        if (totalGames > loadedGamesCount &&
                User.State.ORGANIC != Settings.get().userState) {
            if (totalGames - loadedGamesCount >= loadStep) {
                btnLoadMore?.text = resources.getQuantityString(R.plurals.more_games, loadStep, loadStep) //why second loadstep is here?
            } else {
                val gameCount = totalGames - loadedGamesCount
                btnLoadMore?.text = resources.getQuantityString(R.plurals.more_games, gameCount, gameCount)
            }
            btnLoadMore?.visibility = VISIBLE
        } else {
            btnLoadMore?.visibility = GONE
        }
    }

    enum class LoaderState {
        SHOW, HIDE
    }
}
