package com.abrand.custom.ui.tournamentinternal

import android.app.Activity
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.text.Html
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.*
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.RoundedBitmapDrawableFactory
import androidx.core.view.ViewCompat
import androidx.core.view.size
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.*
import com.abrand.custom.databinding.FragmentTournamentInternalBinding
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.readTextFromAsset
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.interfaces.GameClickListener
import com.squareup.picasso.Picasso
import java.util.*
import androidx.fragment.app.viewModels
import com.abrand.custom.data.repositories.YearRepository
import com.abrand.custom.ui.payments.PaymentsFragment
import java.util.concurrent.TimeUnit

class TournamentInternalFragment : Fragment(R.layout.fragment_tournament_internal), GameClickListener {
    var tournamentsItem: TournamentsItem? = null

    private val viewModel: TournamentsInternalViewModel by viewModels()
    private var binding: FragmentTournamentInternalBinding? = null
    private var selectedTabType = TabType.CONDITIONS
    private var isJoined = false
    private var autoJoined = false
    private val GAMES_LOAD = 12
    private var gamesLeft = 0
    var isUserLogged = false


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        arguments?.let {
            tournamentsItem = it.getSerializable(MainActivity.ITEM) as TournamentsItem
            autoJoined = it.getBoolean(MainActivity.AUTO_JOIN,false)
        }
        val localBinding = FragmentTournamentInternalBinding.inflate(inflater, container, false)
        binding = localBinding
        return localBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        tournamentsItem?.let { loadTournament(it.id) }

        fillHeader()
        setupInsets()
        observeViewModel()
        isUserLogged = User.State.PLAYER == Settings.get().userState


        if (User.State.PLAYER == Settings.get().userState) {
            applyLoggedState()
        } else {
            applyNotLoggedState()
        }

        binding?.footer?.glPaymentSystemsView?.setOnClickListener {
            if (User.State.PLAYER == Settings.get().userState) {
                (activity as? MainActivity)?.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
            } else {
                (activity as? MainActivity)?.openEnterScreen(false)
            }
        }

        binding?.footer?.tvCopyrightYear?.text = context?.getString(
            R.string.app_copyright_year,
            YearRepository.getCurrentYear()
        )
    }

    private fun observeViewModel() {
        viewModel.tournamentLiveData.observe(viewLifecycleOwner, Observer {
            val tournamentsItem: TournamentsItem = it.tournament
            val dataHolder = SessionDataHolder.getInstance()
            dataHolder.currentTournamentItem = tournamentsItem
            setHtmlContent(tournamentsItem)
            binding?.tvName?.text = tournamentsItem.title
            fillPrizes(tournamentsItem.tournamentPrizes, tournamentsItem.isDynamicPrizeFund)
            fillLeaders(tournamentsItem.participants, tournamentsItem.tournamentPrizes,
                tournamentsItem.isDynamicPrizeFund, tournamentsItem.prizeFund.toDouble())
            fillGames(tournamentsItem.gameList)
            fillResult(tournamentsItem)
            isJoined = tournamentsItem.tournamentViewerProgress?.toString() != "null"
        })

        viewModel.joinLiveData.observe(viewLifecycleOwner) {
            val tournamentsItem: TournamentsItem = it.tournament
            SessionDataHolder.getInstance().currentTournamentItem = tournamentsItem
            fillResult(tournamentsItem)
            binding?.tabSlots?.performClick()
        }

        viewModel.tournamentLoadApolloExceptionLiveData.observe(viewLifecycleOwner) {
            (activity as MainActivity).showConnectionIssueMessage(it)
            binding?.btn?.isEnabled = true
        }

        viewModel.joinErrorLiveData.observe(viewLifecycleOwner) {
            binding?.tvError?.visibility = View.VISIBLE
        }

        viewModel.tournamentErrorLiveData.observe(viewLifecycleOwner) {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
        }
    }

    private fun setHtmlContent(tournamentsItem: TournamentsItem) {
        val CSS_ASSET_FILE_NAME = "news.css"
        binding?.tvAbout?.setBackgroundColor(Color.TRANSPARENT)
        binding?.tvAbout?.settings?.apply {
            javaScriptEnabled = true
        }
        binding?.tvAbout?.webViewClient = object : WebViewClient() {

            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                (activity as MainActivity).processDeepLinkUrl(url, UrlSource.NEWS)
                return true
            }

            @RequiresApi(Build.VERSION_CODES.KITKAT)
            override fun onPageFinished(view: WebView, url: String) {
                val css = context?.readTextFromAsset(CSS_ASSET_FILE_NAME)
                val js = "var style = document.createElement('style'); style.innerHTML = `$css`; document.head.appendChild(style);"
                binding?.tvAbout?.evaluateJavascript(js, null)
                binding?.tvAbout?.visibility = View.VISIBLE
                super.onPageFinished(view, url)
            }
        }

        binding?.tvAbout?.loadDataWithBaseURL(ApoloConfig.BASE_URL, tournamentsItem.mobText ?: "", "text/html", "UTF-8", "")
    }

    private fun setupInsets() {
        binding?.rvTournamentInternalList?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it) { view, insets ->
                view.updatePadding(top = insets.systemWindowInsetTop, bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }


    private fun loadTournament(id: Int){
        viewModel.loadById(id)
    }

    private fun fillPrizes(list : List<TournamentsItem.TournamentPrize>, isDynamic : Boolean){
        binding?.listPrizes?.layoutManager = LinearLayoutManager(context)
        binding?.listPrizes?.adapter = PrizesAdapter(list,isDynamic)
    }

    private fun fillLeaders(list: List<TournamentsItem.TournamentParticipant>,
                            prizes: List<TournamentsItem.TournamentPrize>, isDynamic: Boolean,
                            sum: Double) {
        binding?.listLeaders?.layoutManager = LinearLayoutManager(context)
        binding?.listLeaders?.adapter = LeadersAdapter(list, prizes, isDynamic, sum)
    }

    private fun fillGames(list: List<LocalGameItem>) {
        binding?.listSlots?.layoutManager = GridLayoutManager(context, 3)
        if (list.size > GAMES_LOAD) {
            binding?.listSlots?.adapter = GamesAdapter(list.subList(0,GAMES_LOAD),this)
            gamesLeft = list.size-GAMES_LOAD
            binding?.btnLoadMore?.visibility = View.VISIBLE
            binding?.btnLoadMore?.text = resources.getQuantityString(R.plurals.more_games, gamesLeft, gamesLeft)
            binding?.btnLoadMore?.setOnClickListener{
                val n = binding?.listSlots?.size?.plus(GAMES_LOAD)
                if(n != null && n > list.size){
                    binding?.listSlots?.adapter = GamesAdapter(list.subList(0,list.size),this)
                    binding?.btnLoadMore?.visibility = View.GONE
                }else if(n != null){
                    binding?.listSlots?.adapter = GamesAdapter(list.subList(0,n),this)
                    gamesLeft-= GAMES_LOAD
                    binding?.btnLoadMore?.text = resources.getQuantityString(R.plurals.more_games, gamesLeft, gamesLeft)
                }
            }
        }else{
            binding?.listSlots?.adapter = GamesAdapter(list,this)
        }
    }

    private fun fillResult(tournamentsItem: TournamentsItem) {
        val progress = tournamentsItem.tournamentViewerProgress
        if (progress.toString() != "null" && !tournamentsItem.status.equals("completed")) {
            binding?.btn?.visibility = View.GONE
            binding?.viewResult?.visibility = View.VISIBLE
            binding?.viewResultTop?.visibility = View.VISIBLE
            binding?.viewResultBot?.visibility = View.VISIBLE
            binding?.tvPlace?.text = getString(R.string.tournament_place, progress?.place)
            binding?.tvResult?.text = GeneralTools.formatNumber(progress?.score?.toInt().toString())
            binding?.tvMinbet?.text = tournamentsItem.minBetLimit.toString().split(".")[0]
            var bets = tournamentsItem.qualificationRounds
            if (progress?.totalBets != null)
                bets -= progress.totalBets
            if (bets > 0) {
                binding?.tvJoinbet?.text = bets.toString()
            } else {
                binding?.tvJoinbetText?.visibility = View.GONE
                binding?.tvJoinbet?.visibility = View.GONE
            }
        } else {
            binding?.btn?.visibility = View.VISIBLE
            binding?.viewResult?.visibility = View.INVISIBLE
            binding?.viewResultTop?.visibility = View.GONE
            binding?.viewResultBot?.visibility = View.GONE
            if (autoJoined) {
                binding?.btn?.performClick()
            }
        }
    }

    private fun fillHeader(){
        binding?.tabConditions?.setOnClickListener {
            selectTab(TabType.CONDITIONS)
            binding?.bodyConditions?.visibility = View.VISIBLE
            binding?.bodySlots?.visibility = View.GONE
            binding?.bodyPrizes?.visibility = View.GONE
            binding?.bodyLeaders?.visibility = View.GONE
        }
        binding?.tabSlots?.setOnClickListener {
            selectTab(TabType.SLOTS)
            binding?.bodyConditions?.visibility = View.GONE
            binding?.bodySlots?.visibility = View.VISIBLE
            binding?.bodyPrizes?.visibility = View.GONE
            binding?.bodyLeaders?.visibility = View.GONE
        }
        binding?.tabPrizes?.setOnClickListener {
            selectTab(TabType.PRIZES)
            binding?.bodyConditions?.visibility = View.GONE
            binding?.bodySlots?.visibility = View.GONE
            binding?.bodyPrizes?.visibility = View.VISIBLE
            binding?.bodyLeaders?.visibility = View.GONE
        }
        binding?.tabLeaders?.setOnClickListener {
            selectTab(TabType.LEADERS)
            binding?.bodyConditions?.visibility = View.GONE
            binding?.bodySlots?.visibility = View.GONE
            binding?.bodyPrizes?.visibility = View.GONE
            binding?.bodyLeaders?.visibility = View.VISIBLE
        }


        binding?.tvName?.text = tournamentsItem?.title
        var s = GeneralTools.formatBalance(Settings.get().userCurrencyCode, tournamentsItem?.prizeFund?.toDouble())
        s = "<b>" + s.substring(0,s.length-2) + "</b> " + s.substring(s.length-1)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N){
            binding?.tvFund?.text = Html.fromHtml(s, Html.FROM_HTML_MODE_LEGACY)
        } else {
            binding?.tvFund?.text = Html.fromHtml(s)
        }
        context?.let { setBackground(tournamentsItem?.bannerMob, it) }
        if (tournamentsItem?.status.equals("completed")) {
            binding?.tabSlots?.visibility = View.GONE
            binding?.tabPrizes?.visibility = View.GONE
            binding?.tvLeaders?.text = context?.getString(R.string.results)
            binding?.tabLeaders?.performClick()
            binding?.tabGhost1?.visibility = View.VISIBLE
            binding?.tabGhost2?.visibility = View.VISIBLE
            binding?.tvDays?.text = DateTools.getDayMonth(context,tournamentsItem?.dateStart) + " - " + DateTools.getDayMonth(context,tournamentsItem?.dateEnd)
            binding?.tvDaysText?.text = this.resources.getString(R.string.tournaments_period)
            binding?.btn?.text = this.resources.getString(R.string.tournaments_ended)
            binding?.btn?.isEnabled = false
        } else {
            binding?.tabSlots?.visibility = View.VISIBLE
            binding?.tabPrizes?.visibility = View.VISIBLE
            binding?.tvLeaders?.text = context?.getString(R.string.leaders)
            binding?.tabGhost1?.visibility = View.GONE
            binding?.tabGhost2?.visibility = View.GONE
            val endTime = DateTools.getServerDate(context,tournamentsItem?.dateEnd).time
            val currentTime = Calendar.getInstance().timeInMillis
            val count = (endTime-currentTime)/(1000*60*60*24)
            if(count<2){
                val timer = object: CountDownTimer(endTime-currentTime, 1000) {
                    var hms : String = ""
                    override fun onTick(millisUntilFinished: Long) {
                        hms = java.lang.String.format("%02d:%02d:%02d", TimeUnit.MILLISECONDS.toHours(millisUntilFinished),
                                TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished) - TimeUnit.HOURS.toMinutes(TimeUnit.MILLISECONDS.toHours(millisUntilFinished)),
                                TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) - TimeUnit.MINUTES.toSeconds(TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)))
                        if(binding?.tvDays != null)
                            binding?.tvDays?.text = hms
                    }

                    override fun onFinish() {
                    }
                }
                timer.start()
            }else {
                binding?.tvDays?.text = this.resources.getQuantityString(R.plurals.days,count.toInt(),count.toInt())
            }
            binding?.btn?.setOnClickListener{
                if (User.State.NOT_LOGGED == Settings.get().userState) {
                    val activity: Activity? = activity
                    if (activity is MainActivity) {
                        activity.showEnterScreenFromTournaments(true, tournamentsItem)
                    }
                }else {
                    tournamentsItem?.let { it -> viewModel.joinTournament(it.id) }
                    binding?.btn?.isEnabled = false
                    isJoined = true
                }
            }
            binding?.tabConditions?.performClick()
        }


    }

    private fun selectTab(tabType: TabType) {
            when (tabType) {
                TabType.CONDITIONS -> selectTab(TabType.CONDITIONS, R.drawable.ic_book, binding?.ivConditions, binding?.tvConditions, binding?.tabConditions)
                TabType.SLOTS -> selectTab(TabType.SLOTS, R.drawable.ic_slot, binding?.ivSlots, binding?.tvSlots, binding?.tabSlots)
                TabType.PRIZES -> selectTab(TabType.PRIZES, R.drawable.ic_prize, binding?.ivPrizes, binding?.tvPrizes, binding?.tabPrizes)
                else -> if (TournamentsItem.STATUS_COMPLETED == tournamentsItem?.status) {
                    selectTab(
                        TabType.LEADERS, R.drawable.ic_prize, binding?.ivLeaders,
                        binding?.tvLeaders, binding?.tabLeaders
                    )
                } else {
                    selectTab(
                        TabType.LEADERS, R.drawable.ic_medal, binding?.ivLeaders,
                        binding?.tvLeaders, binding?.tabLeaders
                    )
                }
            }
    }

    private fun selectTab(tabType: TabType, drawableId: Int, imageView: ImageView?, textView: TextView?, bg: RelativeLayout?) {
        val ICON_SELECTED_GRADIENT_START = "#FF6ED200"
        val ICON_SELECTED_GRADIENT_END = "#FF008E00"
        val TEXT_SELECTED_COLOR = "#FF008E00"
        val BG_COLOR = "#000D26"
        setTabUnSelected(selectedTabType)
        selectedTabType = tabType
            //tournamentInternalListener?.onGameTypeChanged(selectedGameType)

        val bitmap = GeneralTools.getBitmapFromVectorDrawable(context, drawableId)
        imageView?.setImageBitmap(GeneralTools.addGradient(bitmap, ICON_SELECTED_GRADIENT_START, ICON_SELECTED_GRADIENT_END))
        textView?.setTextColor(Color.parseColor(TEXT_SELECTED_COLOR))
        bg?.setBackgroundColor(Color.parseColor(BG_COLOR))
    }

    private fun setTabUnSelected(selectedGameType: TabType) {
        val TEXT_DEFAULT_COLOR = Color.WHITE
        val TEXT_DEFAULT_BG = Color.TRANSPARENT
        when (selectedGameType) {
                TabType.CONDITIONS -> {
                    binding?.ivConditions?.setImageDrawable(context?.let { ContextCompat.getDrawable(it, R.drawable.ic_book) })
                    binding?.tvConditions?.setTextColor(TEXT_DEFAULT_COLOR)
                    binding?.tabConditions?.setBackgroundColor(TEXT_DEFAULT_BG)
                }
                TabType.SLOTS -> {
                    binding?.ivSlots?.setImageDrawable(context?.let { ContextCompat.getDrawable(it, R.drawable.ic_slot) })
                    binding?.tvSlots?.setTextColor(TEXT_DEFAULT_COLOR)
                    binding?.tabSlots?.setBackgroundColor(TEXT_DEFAULT_BG)
                }
                TabType.PRIZES -> {
                    binding?.ivPrizes?.setImageDrawable(context?.let { ContextCompat.getDrawable(it, R.drawable.ic_prize) })
                    binding?.tvPrizes?.setTextColor(TEXT_DEFAULT_COLOR)
                    binding?.tabPrizes?.setBackgroundColor(TEXT_DEFAULT_BG)
                }
                TabType.LEADERS -> {
                    if (TournamentsItem.STATUS_COMPLETED == tournamentsItem?.status) {
                        binding?.ivLeaders?.setImageDrawable(context?.let {
                            ContextCompat.getDrawable(it, R.drawable.ic_prize) })
                    } else {
                        binding?.ivLeaders?.setImageDrawable(context?.let {
                            ContextCompat.getDrawable(it, R.drawable.ic_medal) })
                    }
                    binding?.tvLeaders?.setTextColor(TEXT_DEFAULT_COLOR)
                    binding?.tabLeaders?.setBackgroundColor(TEXT_DEFAULT_BG)
                }
        }
    }

    private fun setBackground(mobileIcon: String?, context: Context) {
        if (!TextUtils.isEmpty(mobileIcon)) {
            Picasso.get()
                    .load(ApoloConfig.getFullUrl(mobileIcon))
                    .resize(GeneralTools.getScreenWidth(context),context.resources.getDimensionPixelSize(R.dimen.news_internal_image_bg_height))
                    .centerCrop()
                    .into(object : com.squareup.picasso.Target {
                        override fun onPrepareLoad(placeHolderDrawable: Drawable?) {

                        }

                        override fun onBitmapFailed(e: Exception?, errorDrawable: Drawable?) {}

                        override fun onBitmapLoaded(bitmap: Bitmap, from: Picasso.LoadedFrom?) {
                            val roundedBitmapDrawable = RoundedBitmapDrawableFactory.create(context.resources, bitmap)
                            roundedBitmapDrawable.cornerRadius = 0f
                            binding?.ivImage?.background = roundedBitmapDrawable
                        }
                    })
        }
    }

    fun applyLoggedState() {
        isUserLogged = true
    }

    fun applyNotLoggedState() {
        isUserLogged = false
    }

    override fun onGameClick(game: LocalGameItem, view: View) {
        val dataHolder = SessionDataHolder.getInstance()
        dataHolder.currentGameItem = game
        val gameIcon = view.findViewById<ImageView>(R.id.iv_item_game_image)
        if (gameIcon?.drawable is BitmapDrawable) {
            dataHolder.currentGameImage = (gameIcon.drawable as BitmapDrawable).bitmap
        }

        (activity as MainActivity).showPregame(game, view)

    }

}



enum class TabType {
    CONDITIONS, SLOTS, PRIZES, LEADERS
}
