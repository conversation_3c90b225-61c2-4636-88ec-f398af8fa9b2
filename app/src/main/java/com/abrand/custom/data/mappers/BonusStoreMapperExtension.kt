package com.abrand.custom.data.mappers

import com.abrand.custom.GetBonusStoreProductsQuery
import com.abrand.custom.data.entity.LocalBonusProductForPoints
import com.abrand.custom.fragment.StoreProductFragment

fun List<GetBonusStoreProductsQuery.BonusStoreProduct?>?.mapToListLocalBonusProductForPoints(): List<LocalBonusProductForPoints> {
    val result = arrayListOf<LocalBonusProductForPoints>()

    if (this != null) {
        for (item in this) {
            if (item != null) {
                val localBonusProductForPoints = item.fragments.storeProductFragment?.mapToLocalBonusProductForPoints()
                if (localBonusProductForPoints != null) {
                    result.add(localBonusProductForPoints)
                }
            }
        }
    }

    return result
}

fun StoreProductFragment.mapToLocalBonusProductForPoints() : LocalBonusProductForPoints {
    return LocalBonusProductForPoints(
        this.id,
        this.name,
        this.description,
        this.image,
        this.price,
        this.isActive,
        this.buttonTooltip
    )
}
