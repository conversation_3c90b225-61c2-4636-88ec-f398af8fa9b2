package com.abrand.custom.ui.gameroom

import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.*
import android.widget.AdapterView.OnItemSelectedListener
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.entity.GameRoomItem
import com.abrand.custom.data.entity.LocalGameItem
import com.abrand.custom.data.repositories.YearRepository
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.presenter.GameRoomItemDiffCallback
import com.abrand.custom.tools.CircleTransform
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.type.GameListOrderField
import com.abrand.custom.ui.views.GameLoadView
import com.squareup.picasso.Picasso
import com.squareup.picasso.Picasso.LoadedFrom
import com.squareup.picasso.Picasso.get
import com.squareup.picasso.Target

class GameRoomAdapter() : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    private var gameRoomListener: GameRoomListener? = null
    private var footerListener: FooterListener? = null
    private var items = mutableListOf<GameRoomItem>()
    private var selectedGameType = GameType.SLOT
    private var gameIconSize = 0
    private var gameLoadVH: GameLoadVH? = null
    var isUserLogged = false
    var isGameLoading = true
    private var headerVH: HeaderVH? = null
    var spinnerPosition = 0
    var currentTotalGames = 0

    init {
        items.add(GameRoomItem(GameRoomItem.ItemViewType.HEADER))
        items.add(GameRoomItem(GameRoomItem.ItemViewType.GAME_LOAD))
        items.add(GameRoomItem(GameRoomItem.ItemViewType.FOOTER))
    }

    constructor(gameRoomListener: GameRoomListener, footerListener: FooterListener, gameIconSize: Int) : this() {
        this.gameRoomListener = gameRoomListener
        this.footerListener = footerListener
        this.gameIconSize = gameIconSize
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            GameRoomItem.ItemViewType.HEADER.id -> HeaderVH(inflater, parent)
            GameRoomItem.ItemViewType.FOOTER.id -> FooterVH(inflater, parent)
            GameRoomItem.ItemViewType.GAME.id -> GameVH(inflater, parent)
            GameRoomItem.ItemViewType.GAME_LOAD.id -> GameLoadVH(GameLoadView(parent.context))
            else -> HeaderVH(inflater, parent)
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is HeaderVH -> {
                headerVH = holder
                holder.bind()
            }
            is GameVH -> {
                holder.bind(items[position].gameItem)
            }
            is GameLoadVH -> {
                gameLoadVH = holder
                gameLoadVH?.bind()
            }
            is FooterVH -> {
                holder.bind()
            }
        }
    }

    override fun getItemCount(): Int = items.size

    fun getGameCount() = items.count {
        GameRoomItem.ItemViewType.GAME == it.itemViewType
    }

    override fun getItemViewType(position: Int): Int {
        return items[position].itemViewType.id
    }

    fun setGameLoadingState(isGameLoading: Boolean) {
        this.isGameLoading = isGameLoading
        gameLoadVH?.setGameLoadingState(isGameLoading)
    }

    fun setList(newList: MutableList<GameRoomItem>) {
        newList.add(0, GameRoomItem(GameRoomItem.ItemViewType.HEADER))
        newList.add(GameRoomItem(GameRoomItem.ItemViewType.GAME_LOAD))
        newList.add(GameRoomItem(GameRoomItem.ItemViewType.FOOTER))

        val diffResult = DiffUtil.calculateDiff(GameRoomItemDiffCallback(items, newList))
        items = newList
        diffResult.dispatchUpdatesTo(this)
    }

    fun appendList(newList: MutableList<GameRoomItem>) {
        val old = ArrayList(items)
        //Add new items before GAME_LOAD and FOOTER item
        items.addAll(items.size - 2, newList)

        val diffResult = DiffUtil.calculateDiff(GameRoomItemDiffCallback(old, items))
        diffResult.dispatchUpdatesTo(this)
    }

    fun applyLoggedState() {
        isUserLogged = true
        headerVH?.initTabs()
    }

    fun applyNotLoggedState() {
        isUserLogged = false
        headerVH?.initTabs()
        if (GameType.FAVOURITE == selectedGameType) {
            headerVH?.selectSlotsTab()
        }
    }

    inner class HeaderVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_game_room_header, parent, false)) {

        private val tabSlots = itemView.findViewById<RelativeLayout>(R.id.tab_slots)
        private val ivSlots = itemView.findViewById<ImageView>(R.id.iv_slots)
        private val tvSlots = itemView.findViewById<TextView>(R.id.tv_slots)
        private val tabNew = itemView.findViewById<RelativeLayout>(R.id.tab_new)
        private val ivNew = itemView.findViewById<ImageView>(R.id.iv_new)
        private val tvNew = itemView.findViewById<TextView>(R.id.tv_new)
        private val tabFavorite = itemView.findViewById<RelativeLayout>(R.id.tab_favorite)
        private val ivFavorite = itemView.findViewById<ImageView>(R.id.iv_favorite)
        private val tvFavorite = itemView.findViewById<TextView>(R.id.tv_favorite)
        private val tabTables = itemView.findViewById<RelativeLayout>(R.id.tab_tables)
        private val ivTables = itemView.findViewById<ImageView>(R.id.iv_tables)
        private val tvTables = itemView.findViewById<TextView>(R.id.tv_tables)
        private val tabGhost = itemView.findViewById<View>(R.id.tab_ghost)
        private val spinner = itemView.findViewById<Spinner>(R.id.spinner)

        fun bind() {
            initTabs()
            initSpinner()
            setGameLoadingState(isGameLoading)
        }

        fun initTabs() {
            if (isUserLogged) {
                applyLoggedState()
            } else {
                applyNotLoggedState()
            }

            setupSelector()
        }

        fun selectSlotsTab() {
            tabSlots.performClick()
        }

        private fun applyLoggedState() {
            tabFavorite.visibility = View.VISIBLE
            tabGhost.visibility = View.GONE
        }

        private fun applyNotLoggedState() {
            tabFavorite.visibility = View.GONE
            tabGhost.visibility = View.VISIBLE
        }

        private fun setupSelector() {
            tabSlots.setOnClickListener {
                selectTab(GameType.SLOT)
            }
            tabNew.setOnClickListener {
                selectTab(GameType.NEW)
            }
            tabFavorite.setOnClickListener {
                selectTab(GameType.FAVOURITE)
            }
            tabTables.setOnClickListener {
                selectTab(GameType.TABLES)
            }
            selectTab(selectedGameType)
        }

        private fun selectTab(gameType: GameType) {
            when (gameType) {
                GameType.SLOT -> selectTab(GameType.SLOT, R.drawable.ic_cherries, ivSlots, tvSlots)
                GameType.NEW -> selectTab(GameType.NEW, R.drawable.ic_new, ivNew, tvNew)
                GameType.FAVOURITE -> selectTab(GameType.FAVOURITE, R.drawable.ic_favourite, ivFavorite, tvFavorite)
                else -> selectTab(GameType.TABLES, R.drawable.ic_roulette, ivTables, tvTables)
            }
        }

        private fun selectTab(gameType: GameType, drawableId: Int, imageView: ImageView, textView: TextView) {
            setTabUnSelected(selectedGameType)
            selectedGameType = gameType
            gameRoomListener?.onGameTypeChanged(selectedGameType)

            val bitmap = GeneralTools.getBitmapFromVectorDrawable(itemView.context, drawableId)
            imageView.setImageBitmap(GeneralTools.addGradient(bitmap, ICON_SELECTED_GRADIENT_START, ICON_SELECTED_GRADIENT_END))
            textView.setTextColor(Color.parseColor(TEXT_SELECTED_COLOR))
        }

        private fun setTabUnSelected(selectedGameType: GameType) {
            when (selectedGameType) {
                GameType.SLOT -> {
                    ivSlots.setImageDrawable(ContextCompat.getDrawable(itemView.context, R.drawable.ic_cherries))
                    tvSlots.setTextColor(Color.parseColor(TEXT_DEFAULT_COLOR))
                }
                GameType.NEW -> {
                    ivNew.setImageDrawable(ContextCompat.getDrawable(itemView.context, R.drawable.ic_new))
                    tvNew.setTextColor(Color.parseColor(TEXT_DEFAULT_COLOR))
                }
                GameType.FAVOURITE -> {
                    ivFavorite.setImageDrawable(ContextCompat.getDrawable(itemView.context, R.drawable.ic_favourite))
                    tvFavorite.setTextColor(Color.parseColor(TEXT_DEFAULT_COLOR))
                }
                GameType.TABLES -> {
                    ivTables.setImageDrawable(ContextCompat.getDrawable(itemView.context, R.drawable.ic_roulette))
                    tvTables.setTextColor(Color.parseColor(TEXT_DEFAULT_COLOR))
                }
            }
        }

        private fun initSpinner() {
            val spinnerAdapter = ArrayAdapter.createFromResource(itemView.context,
                    R.array.games_filter_array, R.layout.item_game_room_spinner_close)
            spinnerAdapter.setDropDownViewResource(R.layout.item_game_room_spinner_open)
            spinner.adapter = spinnerAdapter

            spinner.onItemSelectedListener = null
            spinner.setSelection(spinnerPosition, false)
            spinner.onItemSelectedListener = object : OnItemSelectedListener {
                override fun onItemSelected(adapter: AdapterView<*>, view: View, position: Int, id: Long) {
                    spinnerPosition = position
                    when (position) {
                        0 -> gameRoomListener?.onGameOrderChanged(GameListOrderField.rating)
                        1 -> gameRoomListener?.onGameOrderChanged(GameListOrderField.payout)
                        2 -> gameRoomListener?.onGameOrderChanged(GameListOrderField.alphabet)
                        3 -> gameRoomListener?.onGameOrderChanged(GameListOrderField.position)
                    }
                }

                override fun onNothingSelected(parentView: AdapterView<*>?) {}
            }
        }

    }

    inner class GameVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_game, parent, false)) {

        private val ivItemGameImage = itemView.findViewById<ImageView>(R.id.iv_item_game_image)
        private val tvItemGameName = itemView.findViewById<TextView>(R.id.tv_item_game_name)
        var picasso: Picasso = get()
        private val target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: LoadedFrom) {
                setupImageViewBeforeDisplayImage()
                ivItemGameImage.setImageBitmap(bitmap)
            }

            override fun onBitmapFailed(e: Exception, errorDrawable: Drawable?) {
                setupImageViewBeforeDisplayImage()
                ivItemGameImage.setImageResource(R.drawable.default_thumb_rounded)
            }

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) {
                //do nothing
            }
        }

        fun bind(localGameItem: LocalGameItem?) {
            ivItemGameImage.clearAnimation()
            val animation = AnimationUtils.loadAnimation(itemView.context, R.anim.rotation_loader)
            ivItemGameImage.setImageResource(R.drawable.ic_preload)
            ivItemGameImage.startAnimation(animation)
            setGameIconImageViewSize()

            if (localGameItem != null) {
                tvItemGameName.text = localGameItem.name
                if (!TextUtils.isEmpty(localGameItem.mobileIcon)) {
                    val iconCornerRadius: Int = itemView.context.resources.getDimensionPixelSize(R.dimen.game_icon_corner_radius)
                    picasso.load(ApoloConfig.getFullUrl(localGameItem.mobileIcon))
                            .resize(gameIconSize, gameIconSize)
                            .transform(CircleTransform(iconCornerRadius))
                            .into(target)
                } else {
                    setupImageViewBeforeDisplayImage()
                    ivItemGameImage.setImageResource(R.drawable.default_thumb_rounded)
                }
            }

            itemView.setOnClickListener {
                localGameItem?.let { localGameItem -> gameRoomListener?.onGameClicked(localGameItem, itemView) }
            }

        }

        private fun setGameIconImageViewSize() {
            val params: ViewGroup.LayoutParams = ivItemGameImage.layoutParams
            params.height = gameIconSize
            params.width = gameIconSize
            ivItemGameImage.layoutParams = params
        }

        private fun setupImageViewBeforeDisplayImage() {
            ivItemGameImage.clearAnimation()
            ivItemGameImage.scaleType = ImageView.ScaleType.CENTER_INSIDE
        }
    }

    inner class GameLoadVH(private val gameLoadView: GameLoadView) :
        RecyclerView.ViewHolder(gameLoadView) {

        fun bind() {
            setGameLoadingState(isGameLoading)
        }

        fun setGameLoadingState(isLoading: Boolean) {
            gameLoadView.setGameLoadingState(isLoading, currentTotalGames, getGameCount(), LOAD_STEP)
        }

        init {
            val btnLoadMore = gameLoadView.btnLoadMore
            btnLoadMore?.setOnClickListener { _: View? -> gameRoomListener?.onLoadMore() }
        }
    }

    inner class FooterVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.view_footer, parent, false)) {

        fun bind() {
            val paddingBottom: Int = itemView.context.resources.getDimensionPixelSize(R.dimen.not_organic_user_footer_padding_bottom)
            itemView.setPadding(0, 0, 0, paddingBottom)
            itemView.findViewById<View>(R.id.gl_payment_systems_view).setOnClickListener {
                footerListener?.onClickPaymentSystems()
            }
            itemView.findViewById<TextView>(R.id.tv_copyright_year)?.let {
                it.text = itemView.context.getString(
                    R.string.app_copyright_year,
                    YearRepository.getCurrentYear()
                )
            }
        }
    }

    interface GameRoomListener {
        fun onGameTypeChanged(gameType: GameType)
        fun onGameOrderChanged(gameOrderField: GameListOrderField)
        fun onGameClicked(localGameItem: LocalGameItem, view: View)
        fun onLoadMore()
    }

    enum class GameType {
        SLOT, NEW, FAVOURITE, TABLES
    }

    companion object {
        private const val ICON_SELECTED_GRADIENT_START = "#FF6ED200"
        private const val ICON_SELECTED_GRADIENT_END = "#FF008E00"
        private const val TEXT_SELECTED_COLOR = "#FF008E00"
        private const val TEXT_DEFAULT_COLOR = "#FFFFFF"
        private const val LOAD_STEP = 21
    }
}
