package com.abrand.custom.ui.bonusbalances

import com.abrand.custom.data.repositories.BonusBalancesRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class DeleteBonusBalanceUseCase(
    private val bonusBalanceRepo: IBonusBalancesRepo = BonusBalancesRepository,
    private val getBonusesBalancesUseCase: GetBonusesBalancesUseCase = GetBonusesBalancesUseCase(),
    private val dispatcher: CoroutineDispatcher = Dispatchers.IO,
) {
    suspend fun invoke(bonusId: Int): BonusDeleteResult = withContext(dispatcher) {
        if (bonusBalanceRepo.deleteBonus(bonusId)) {
            return@withContext BonusDeleteResult.Success(getBonusesBalancesUseCase.invoke())
        }

        return@withContext BonusDeleteResult.Error(getBonusesBalancesUseCase.invoke())
    }
}

sealed interface BonusDeleteResult {
    data class Success(val list: List<BonusBalance>) : BonusDeleteResult
    data class Error(val list: List<BonusBalance>) : BonusDeleteResult
}
