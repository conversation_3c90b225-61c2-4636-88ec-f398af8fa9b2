package com.abrand.custom.social

import android.app.Activity
import android.content.Context
import com.yandex.authsdk.YandexAuthOptions
import com.yandex.authsdk.YandexAuthSdk

class YandexNetwork {

    companion object {
        const val REQUEST_CODE_YA_LOGIN = 1001;
    }

    fun getYandexSdk(context: Context): YandexAuthSdk {
        val yandexAuthOptions = YandexAuthOptions.Builder(context).enableLogging().build()
        return YandexAuthSdk(context, yandexAuthOptions)
    }

    fun auth(activity: Activity) {
        val sdk = getYandexSdk(activity)
        val scopes: Set<String>? = null
        activity.startActivityForResult(sdk.createLoginIntent(activity, scopes), REQUEST_CODE_YA_LOGIN)
    }
}
