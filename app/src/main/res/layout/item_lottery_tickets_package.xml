<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="24dp">

    <RadioButton
        android:id="@+id/radio_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:buttonTint="#FDBB2C"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_package"
        android:layout_width="88dp"
        android:layout_height="64dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="17dp"
        android:src="@drawable/tickets_package_bg"
        app:layout_constraintStart_toEndOf="@+id/radio_button"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginTop="4dp"
        android:orientation="horizontal"
        android:rotation="-15.5"
        app:layout_constraintBottom_toBottomOf="@+id/iv_package"
        app:layout_constraintEnd_toEndOf="@+id/iv_package"
        app:layout_constraintStart_toStartOf="@+id/iv_package"
        app:layout_constraintTop_toTopOf="@+id/iv_package">

        <TextView
            android:id="@+id/tv_tickets_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#B3000000"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:text="шт."
            android:textColor="#B3000000"
            android:textSize="10sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_discount"
        android:layout_width="42dp"
        android:layout_height="24dp"
        android:background="@drawable/bg_lottery_package_discount"
        android:gravity="center"
        android:textColor="#CC000000"
        android:textSize="10sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/iv_package"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_full_price"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#99FFFFFF"
        android:textSize="14sp"
        app:layout_constraintBottom_toTopOf="@+id/tv_price_with_discount"
        app:layout_constraintEnd_toEndOf="@+id/iv_package" />

    <TextView
        android:id="@+id/tv_price_with_discount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="11dp"
        android:textColor="@color/white"
        android:textSize="20sp"
        app:layout_constraintEnd_toEndOf="@+id/iv_package"
        app:layout_constraintTop_toBottomOf="@+id/iv_package" />


</androidx.constraintlayout.widget.ConstraintLayout>
