package com.abrand.custom.network

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.abrand.custom.BuildConfig
import com.abrand.custom.R
import com.abrand.custom.data.JsonDataGenerator
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.User
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.ui.SplashActivity
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import java.util.*

class MyFirebaseMessagingService : FirebaseMessagingService() {
    private val TAG = "MyFirebaseMsgService"

    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")
        Settings.get().pushToken = token
        sendTokenToServer()
        sentPostponedAnalyticsEvents()
    }

    private fun sentPostponedAnalyticsEvents() {
        val postponedAnalyticsEvents = Settings.get().postponedAnalyticsEvents
        if (postponedAnalyticsEvents != null && postponedAnalyticsEvents.isNotEmpty()) {
            for (postponeAnalyticsEvents in postponedAnalyticsEvents) {
                OkHttpProcessor.sendAnalytics(this@MyFirebaseMessagingService,
                        postponeAnalyticsEvents, null, null);
            }
            Settings.get().postponedAnalyticsEvents = ArrayList();
        }
    }

    private fun sendTokenToServer() {
        if (User.State.PLAYER == Settings.get().userState) {
            val context = applicationContext
            if (context != null) {
                val pushSubscriptionData = JsonDataGenerator().getPushSubscriptionAuthDataJson(BuildConfig.APPLICATION_ID,
                        Settings.get().pushToken, GeneralTools.getAndroidId(context))
                PushTokenRepository().updateMobileAppSubscription(pushSubscriptionData, object : PushTokenRepository.PushUpdateListener {
                    override fun onSuccess() {
                        Settings.get().isPushTokenMustBeUpdate = false
                    }

                    override fun onFail() {
                        Settings.get().isPushTokenMustBeUpdate = true
                    }
                })
            } else {
                Settings.get().isPushTokenMustBeUpdate = true
            }
        }
    }

    override fun onMessageReceived(message: RemoteMessage) {
        val notification: RemoteMessage.Notification? = message.notification
        if (notification != null) {
            Log.d(TAG, "title: " + notification.title + " | body: " + notification.body)
            showNotification(notification.title, notification.body, message.data);
        }
    }

    //TODO 1. Prepare small icon
    //TODO 2. Channel name
    //TODO 3. Channel id
    //TODO 4. Behaviour showing (change or add)
    private fun showNotification(messageTitle: String?, messageBody: String?,
                                 data: Map<String, String>) {

        val intent = Intent(this, SplashActivity::class.java)

        data.forEach { (key, value) ->
            intent.putExtra(key, value)
        }

        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        val pendingIntent = PendingIntent.getActivity(this, 0,
            intent,
            PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
        )

        val channelId = getString(R.string.general_notification_channel_id)
        val notificationBuilder = NotificationCompat.Builder(this, channelId)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle(messageTitle)
                .setContentText(messageBody)
                .setAutoCancel(true)
                .setContentIntent(pendingIntent)

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(channelId,
                    getString(R.string.general_notification_channel_name),
                            NotificationManager.IMPORTANCE_HIGH)
            notificationManager.createNotificationChannel(channel)
        }

        notificationManager.notify(0, notificationBuilder.build())
    }
}

