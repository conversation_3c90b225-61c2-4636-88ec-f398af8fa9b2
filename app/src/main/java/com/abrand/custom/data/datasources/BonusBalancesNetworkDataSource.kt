package com.abrand.custom.data.datasources

import com.abrand.custom.AApp
import com.abrand.custom.ActivateBonusBalanceMutation
import com.abrand.custom.DeleteBonusBalanceMutation
import com.abrand.custom.GetBonusBalancesCountQuery
import com.abrand.custom.GetBonusBalancesQuery
import com.abrand.custom.SubscribeBonusBalanceWonSubscription
import com.abrand.custom.data.entity.DataResult
import com.abrand.custom.network.ApolloProcessor
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.ui.game.BonusBalanceWon
import com.abrand.custom.ui.bonusbalances.BonusBalance
import com.apollographql.apollo3.exception.ApolloException
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.mapNotNull

suspend fun getBonusBalances(): DataResult<List<BonusBalance>> {
    try {
        val response = AApp.getApolloClient().query(GetBonusBalancesQuery()).execute()

        if (!response.hasErrors()
            && response.data?.viewer?.wallet?.bonusBalances != null
        ) {
            response.data?.viewer?.wallet?.bonusBalances?.let {
                val balancesList = mutableListOf<BonusBalance>()

                for ( balance in it ) {
                    balance?.mapToBonusBalance()?.let { mappedBalance ->
                        balancesList.add(mappedBalance)
                    }
                }

                return DataResult.Success(balancesList)
//                return DataResult.Success(createTestEmptySetOfBonusBalances()) // Test
//                return DataResult.Success(createTestSetOfBonusBalances()) // Test
            }
        }

        return DataResult.Failure(
            ApolloProcessor.getErrorMessage(response),
            ApolloProcessor.getErrorCode(response),
            FieldsErrorsHolder.parce(response.errors)
        )
    } catch(e: ApolloException) {
        return DataResult.Failure(e.message, null, null)
    }
}

suspend fun getBonusBalancesCount(): DataResult<Int> {
    try {
        val response = AApp.getApolloClient().query(GetBonusBalancesCountQuery()).execute()

        if (!response.hasErrors()
            && response.data?.viewer?.wallet?.bonusBalances != null
        ) {
            response.data?.viewer?.wallet?.bonusBalances?.let {
                val count = it.size

                return DataResult.Success(count)
            }
        }

        return DataResult.Failure(
            ApolloProcessor.getErrorMessage(response),
            ApolloProcessor.getErrorCode(response),
            FieldsErrorsHolder.parce(response.errors)
        )
    } catch(e: ApolloException) {
        return DataResult.Failure(e.message, null, null)
    }
}

suspend fun activateBonuses(bonusId: Int): DataResult<Boolean> {
    try {
        val response = AApp.getApolloClient().mutation(ActivateBonusBalanceMutation(bonusId)).execute()

        if (!response.hasErrors()
            && response.data?.walletBonusBalanceActivate == true) {
            return DataResult.Success(true)
        }

        return DataResult.Failure(
            ApolloProcessor.getErrorMessage(response),
            ApolloProcessor.getErrorCode(response),
            FieldsErrorsHolder.parce(response.errors)
        )
    } catch (e: ApolloException) {
        return DataResult.Failure(e.message, null, null)
    }
}

suspend fun deleteBonuses(bonusId: Int): DataResult<Boolean> {
    try {
        val response = AApp.getApolloClient().mutation(DeleteBonusBalanceMutation(bonusId)).execute()

        if (!response.hasErrors()
            && response.data?.walletBonusBalanceReset == true) {
            return DataResult.Success(true)
        }

        return DataResult.Failure(
            ApolloProcessor.getErrorMessage(response),
            ApolloProcessor.getErrorCode(response),
            FieldsErrorsHolder.parce(response.errors)
        )
    } catch (e: ApolloException) {
        return DataResult.Failure(e.message, null, null)
    }
}

fun subscribeBonusBalancesWon(): Flow<BonusBalanceWon> {
    return AApp.getApolloClient().subscription(SubscribeBonusBalanceWonSubscription()).toFlow()
        .mapNotNull {
            it.data?.bonusBalanceWageringWon?.mapToDomainModel()
        }
}

fun GetBonusBalancesQuery.BonusBalance.mapToBonusBalance(): BonusBalance {
    val wageringTarget  = this.wageringTarget.toString().toFloat()
    val wageringCurrent = this.wageringCurrent.toString().toFloat()

    return BonusBalance(
        id = this.id,
        wager = this.wager,
        isActive = this.isActive,
        amount = this.amount.toString().toFloat(),
        wageringTarget = wageringTarget,
        wageringCurrent = wageringCurrent,
        wageringCompletePercent = (this.wageringCompletePercent?.div(100))
            ?: (wageringCurrent / wageringTarget.toDouble()),
        wageringExpiredAt = this.wageringExpiredAt?.toString(),
        wageringMaxTransferAmount = this.wageringMaxTransferAmount.toString().toFloatOrNull()
    )
}

fun SubscribeBonusBalanceWonSubscription.BonusBalanceWageringWon.mapToDomainModel(): BonusBalanceWon {
    return BonusBalanceWon(this.balanceId, this.amount.toString().toFloat())
}

fun createTestSetOfBonusBalances(): List<BonusBalance> {
    return mutableListOf(
        BonusBalance(
            id = 566908,
            wager = 40,
            isActive = false,
            amount = 7000.00000000f,
            wageringTarget = 280000.00f,
            wageringCurrent = 0.000f,
            wageringExpiredAt = "2025-02-06T19:44:31.000Z",
            wageringMaxTransferAmount = 14000.000f,
            wageringCompletePercent = 0.0
        ), BonusBalance(
            id = 566907,
            wager = 1,
            isActive = false,
            amount = 5000.00000000f,
            wageringTarget = 5000.00f,
            wageringCurrent = 0.000f,
            wageringExpiredAt = "2025-12-31T19:44:15.000Z",
            wageringMaxTransferAmount = 5000.000f,
            wageringCompletePercent = 0.0
        ), BonusBalance(
            id = 566906,
            wager = 40,
            isActive = false,
            amount = 6000.00000000f,
            wageringTarget = 24000.00f,
            wageringCurrent = 0.000f,
            wageringExpiredAt = "2025-02-05T19:43:56.000Z",
            wageringMaxTransferAmount = 12000.000f,
            wageringCompletePercent = 0.0
        ), BonusBalance(
            id = 566905,
            wager = 0,
            isActive = false,
            amount = 1000.00000000f,
            wageringTarget = 4000.00f,
            wageringCurrent = 0.000f,
            wageringExpiredAt = "2025-02-05T23:43:40.000Z",
            wageringMaxTransferAmount = null,
            wageringCompletePercent = 0.0
        ), BonusBalance(
            id = 566904,
            wager = 40,
            isActive = true,
            amount = 500.00000000f,
            wageringTarget = 2000.00f,
            wageringCurrent = 0.000f,
            wageringExpiredAt = "2025-02-28T16:42:50.000Z",
            wageringMaxTransferAmount = 1500.000f,
            wageringCompletePercent = 0.0
        ), BonusBalance(
            id = 566977,
            wager = 40,
            isActive = false,
            amount = 500000.00000000f,
            wageringTarget = 500000.00f,
            wageringCurrent = 186456.000f,
            wageringExpiredAt = "2025-12-27T16:42:50.000Z",
            wageringMaxTransferAmount = 1500.000f,
            wageringCompletePercent = 0.42
        )
    )
}

fun createTestSetOfBonusBalances2(): List<BonusBalance> {
    return mutableListOf(
        BonusBalance(
            id = 566977,
            wager = 40,
            isActive = true,
            amount = 500000.00000000f,
            wageringTarget = 500000.00f,
            wageringCurrent = 186456.000f,
            wageringExpiredAt = "2025-12-27T16:42:50.000Z",
            wageringMaxTransferAmount = 1500.000f,
            wageringCompletePercent = 0.42
        ), BonusBalance(
            id = 566908,
            wager = 40,
            isActive = false,
            amount = 7000.00000000f,
            wageringTarget = 280000.00f,
            wageringCurrent = 0.000f,
            wageringExpiredAt = "2025-02-01T19:44:31.000Z",
            wageringMaxTransferAmount = 14000.000f,
            wageringCompletePercent = 0.0
        ), BonusBalance(
            id = 566907,
            wager = 1,
            isActive = false,
            amount = 5000.00000000f,
            wageringTarget = 5000.00f,
            wageringCurrent = 0.000f,
            wageringExpiredAt = "2024-12-31T19:44:15.000Z",
            wageringMaxTransferAmount = 5000.000f,
            wageringCompletePercent = 0.0
        ), BonusBalance(
            id = 566906,
            wager = 40,
            isActive = false,
            amount = 6000.00000000f,
            wageringTarget = 24000.00f,
            wageringCurrent = 0.000f,
            wageringExpiredAt = "2025-02-01T19:43:56.000Z",
            wageringMaxTransferAmount = 12000.000f,
            wageringCompletePercent = 0.0
        ), BonusBalance(
            id = 566905,
            wager = 40,
            isActive = false,
            amount = 1000.00000000f,
            wageringTarget = 4000.00f,
            wageringCurrent = 0.000f,
            wageringExpiredAt = "2025-01-28T19:43:40.000Z",
            wageringMaxTransferAmount = 5000.000f,
            wageringCompletePercent = 0.0
        ), BonusBalance(
            id = 566904,
            wager = 40,
            isActive = false,
            amount = 500.00000000f,
            wageringTarget = 2000.00f,
            wageringCurrent = 0.000f,
            wageringExpiredAt = "2025-02-31T16:42:50.000Z",
            wageringMaxTransferAmount = 1500.000f,
            wageringCompletePercent = 0.0
        ),
    )
}

fun createTestEmptySetOfBonusBalances(): List<BonusBalance> {
    return mutableListOf()
}
