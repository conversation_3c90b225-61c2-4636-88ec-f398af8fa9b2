<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/parent"
    android:layout_width="match_parent"
    android:layout_height="32dp"
    android:orientation="horizontal"
    android:paddingStart="@dimen/size_16"
    android:paddingEnd="@dimen/size_16"
    android:weightSum="100">

    <TextView
        android:id="@+id/tv_number"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="10"
        android:textColor="#40ffffff"
        android:textSize="14sp" />

    <LinearLayout
        android:id="@+id/ll_game_user"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="38"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_game"
            android:layout_width="@dimen/size_16"
            android:layout_height="@dimen/size_16"
            android:layout_gravity="center_vertical" />

        <TextView
            android:id="@+id/tv_user"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#ffffff"
            android:textSize="14sp" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_loyalty_status"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="24"
        android:maxLines="1"
        android:textColor="#ffffff"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_prize"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="28"
        android:gravity="end"
        android:maxLines="1"
        android:textColor="@color/hall_of_fame_prize_text"
        android:textSize="14sp" />

</LinearLayout>
