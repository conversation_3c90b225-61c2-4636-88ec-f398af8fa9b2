<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="@drawable/bg_gradient">

    <LinearLayout
        android:id="@+id/card_recovery"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/bg_recovery_card"
        android:orientation="vertical"
        android:padding="32dp">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_keyhole"
            tools:ignore="ContentDescription" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="@string/_password_recovery"
            android:textColor="#000"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/tv_recovery_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:lineHeight="20sp"
            android:text="@string/enter_email"
            android:textColor="#000"
            android:textSize="12sp"
            tools:ignore="UnusedAttribute" />

        <com.abrand.custom.ui.views.textfield.TextField
            android:id="@+id/et_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:foreground="@drawable/fg_text_field_transparent"
            app:hint="Email" />

        <Button
            android:id="@+id/btn_recover"
            style="@style/ButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:text="@string/_recover"
            android:textSize="16sp" />

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@id/card_recovery"
        android:layout_alignEnd="@id/card_recovery"
        android:contentDescription="@string/_close"
        android:padding="16dp"
        android:src="@drawable/ic_close"
        android:tint="#000" />

    <TextView
        android:id="@+id/tv_resend_email_question"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/card_recovery"
        android:layout_centerHorizontal="true"
        android:gravity="center"
        android:text="@string/resend_email_question"
        android:textColor="#8DA1BD"
        android:visibility="gone"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_resend_email"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_resend_email_question"
        android:layout_centerHorizontal="true"
        android:text="@string/resend_email"
        android:textColor="#0097EC"
        android:visibility="gone"
        tools:visibility="visible" />

</RelativeLayout>
