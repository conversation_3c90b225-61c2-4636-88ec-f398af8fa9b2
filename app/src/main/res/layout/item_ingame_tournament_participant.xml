<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/parent"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <TextView
        android:id="@+id/tv_number"
        android:layout_width="30dp"
        android:layout_height="wrap_content"
        android:alpha="0.8"
        android:gravity="center_vertical"
        android:textColor="#ffffff"
        android:textSize="12sp"
        tools:text="1" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="100">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="60"
            android:alpha="0.8"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAlignment="viewStart"
            android:textColor="#ffffff"
            android:textSize="12sp"
            tools:text="TestUserName" />

        <TextView
            android:id="@+id/tv_score"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="40"
            android:alpha="0.8"
            android:maxLines="1"
            android:textAlignment="viewEnd"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            tools:text="123 500" />
    </LinearLayout>


</LinearLayout>
