package com.abrand.custom.ui.views.textfield;

import android.text.InputType;
import android.text.TextUtils;
import android.util.Patterns;

public class Validator {

    boolean validate(String data, int inputType) {
        switch (inputType) {
            case InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS:
                return isValidEmail(data);
            case InputType.TYPE_CLASS_PHONE:
                return isValidPhoneNumber(data);
            case InputType.TYPE_TEXT_VARIATION_PERSON_NAME:
                return isValidName(data);
            default:
                return true;
        }
    }

    public static boolean isValidEmail(CharSequence target) {
        return !TextUtils.isEmpty(target) && android.util.Patterns.EMAIL_ADDRESS.matcher(target).matches();
    }

    private boolean isValidPhoneNumber(CharSequence target) {
        return !TextUtils.isEmpty(target) && Patterns.PHONE.matcher(target).matches();
    }

    private boolean isValidName(String target) {
        return !TextUtils.isEmpty(target) && Patterns.DOMAIN_NAME.matcher(target).matches();
    }
}
