<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000D26"
    tools:context=".ui.noconnection.NoConnectionFragment">

    <ImageView
        android:id="@+id/iv_no_connection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="160dp"
        android:src="@drawable/ic_no_connection"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvNoConnection"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="34dp"
        android:layout_marginTop="56dp"
        android:layout_marginRight="34dp"
        android:gravity="center_horizontal"
        android:textColor="@color/white"
        android:textSize="22sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_no_connection" />

    <TextView
        android:id="@+id/tv_check_connection_settings"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="@string/check_connection_settings"
        android:textColor="#80B2E3FF"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvNoConnection" />

    <Button
        android:id="@+id/btnUpdate"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:background="@drawable/more_btn_bg"
        android:foreground="?attr/selectableItemBackground"
        android:paddingLeft="46dp"
        android:paddingTop="14dp"
        android:paddingRight="46dp"
        android:paddingBottom="12dp"
        android:text="@string/no_connection_update"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_check_connection_settings" />

</androidx.constraintlayout.widget.ConstraintLayout>