<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/frame_web_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/btn_menu"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginBottom="@dimen/size_16"
        android:background="@drawable/bg_btn_ingame_menu"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/iv_menu"
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_ingame_menu_open" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
