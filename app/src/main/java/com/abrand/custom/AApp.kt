package com.abrand.custom

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.ComponentCallbacks2
import android.content.Context
import android.os.Bundle
import android.util.Log
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.AnalyticsEvent
import com.abrand.custom.data.repositories.ReCaptchaRepositoryImpl
import com.abrand.custom.data.repositories.SettingsRepository
import com.abrand.custom.network.OkHttpClientHelper
import com.abrand.custom.network.OkHttpProcessor
import com.abrand.custom.ui.activitymain.MainActivity
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustConfig
import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.network.okHttpClient
import com.apollographql.apollo3.network.ws.GraphQLWsProtocol
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.squareup.picasso.OkHttp3Downloader
import com.squareup.picasso.Picasso
import okhttp3.Headers
import okhttp3.Request

class AApp : Application() {

    private var currentActivity: Activity? = null

    override fun onCreate() {
        super.onCreate()

        initCoreComponents()
        initThirdPartyLibraries()
        registerActivityLifecycleCallbacks(AdjustLifecycleCallbacks())
    }

    private fun initCoreComponents() {
        context = applicationContext
        Settings.init(applicationContext)
        SettingsRepository.runMigrations(BuildConfig.VERSION_CODE)
    }

    private fun initThirdPartyLibraries() {
        setupCrashlytics()
        initEnterpriseReCaptchaLib()
        setupPicasso()
        setupApollo()
        setupAdjust()
    }

    private fun setupCrashlytics() {
        //Setup Firebase Crashlytics userID, cause it not persistent
        val userId = Settings.get().userId
        if (userId.isNotEmpty()) {
            FirebaseCrashlytics.getInstance().setUserId(userId)
        }
    }

    private fun initEnterpriseReCaptchaLib() {
        ReCaptchaRepositoryImpl.initialize(this)
    }

    private fun setupPicasso() {
        val picasso = Picasso.Builder(context)
            .downloader(OkHttp3Downloader(OkHttpClientHelper.getOkHttpClient()))
            .build()
        Picasso.setSingletonInstance(picasso)

        if (BuildConfig.DEBUG) {
            try {
                Picasso.get().isLoggingEnabled = true
            } catch (e: IllegalStateException) {
                Log.e("Picasso", e.toString())
            }
        }
    }

    private fun setupApollo() {
        setApolloClient(initApollo())

        //        if (BuildConfig.DEBUG) {
        //             ApolloProcessor.generatePersistedQueriesJson()
        //             ApolloProcessor.generateMinifiedPersistedQueriesJson()
        //             ApolloProcessor.generateInvertedPersistedQueriesJson()
        //            ApolloProcessor.generateInvertedMinifiedPersistedQueriesJson()
        //        }
    }

    private fun setupAdjust() {
        val adjustToken = BuildConfig.ADJUST_ID
        val environment = if (BuildConfig.DEBUG) {
            AdjustConfig.ENVIRONMENT_SANDBOX
        } else {
            AdjustConfig.ENVIRONMENT_PRODUCTION
        }
        val config = AdjustConfig(this, adjustToken, environment)
        Adjust.onCreate(config)
    }

    private inner class AdjustLifecycleCallbacks : ActivityLifecycleCallbacks {
        override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
            currentActivity = activity
        }

        override fun onActivityStarted(activity: Activity) {}
        override fun onActivityResumed(activity: Activity) {
            Adjust.onResume()
            if (appWasInBackground) {
                OkHttpProcessor.sendAnalytics(
                    getContext(),
                    AnalyticsEvent.RETURN_TO_APP,
                    null,
                    null
                )
                appWasInBackground = false
            }
        }

        override fun onActivityPaused(activity: Activity) {
            Adjust.onPause()
        }

        override fun onActivityStopped(activity: Activity) {}
        override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
        override fun onActivityDestroyed(activity: Activity) {}
    }

    private fun initApollo(): ApolloClient {
        val okHttpBuilder = OkHttpClientHelper.getOkHttpBuilder()
        val apolloBuilder = ApolloClient.Builder()

        okHttpBuilder.addInterceptor { chain ->
            val request: Request = chain.request()
            val response = chain.proceed(request)

            val headers: Headers = response.headers
            val containsCloudflareHeader = (0 until headers.size).any {
                headers.value(it) == "cloudflare"
            }
            val containsCaptchaHeader = "cf-chl-bypass" in headers.names()

            if ((response.code == 403 || response.code == 503) && containsCloudflareHeader) {
                (currentActivity as? MainActivity)?.resolveCloudflare(containsCaptchaHeader)
            }

            response
        }

        val okHttpClient = okHttpBuilder.build()
        apolloBuilder.okHttpClient(okHttpClient)
        apolloBuilder.serverUrl(ApoloConfig.ENDPOINT)
        //        Enable persistent queries for release builds
        apolloBuilder.enableAutoPersistedQueries(true)
        //        if ( !BuildConfig.DEBUG ) {
        //            okHttpBuilder.addInterceptor(ApoloConfig.getFixPersistentInterceptor());
        //        }
        apolloBuilder.webSocketServerUrl(ApoloConfig.WWS_ENDPOINT)
        apolloBuilder.wsProtocol(GraphQLWsProtocol.Factory())

        return apolloBuilder.build()
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)
        if (level == ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN) {
            appWasInBackground = true
        }
    }

    companion object {
        @SuppressLint("StaticFieldLeak")
        private lateinit var context: Context
        private lateinit var apolloClient: ApolloClient
        private var appWasInBackground = false

        fun getApolloClient(): ApolloClient = apolloClient

        fun setApolloClient(newApolloClient: ApolloClient) {
            apolloClient = newApolloClient
        }

        fun getContext(): Context = context
    }
}
