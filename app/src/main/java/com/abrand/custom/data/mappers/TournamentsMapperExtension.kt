package com.abrand.custom.data.mappers

import com.abrand.custom.GetHomeAdditionalBlocksQuery
import com.abrand.custom.data.entity.LocalGameItem
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.fragment.*

fun FragmentFullTournamentsItem.mapToTournamentsItem(): TournamentsItem {

    return TournamentsItem(
        id = this.id,
        title = this.title,
        dateStart = this.dateStart,
        dateEnd = this.dateEnd,
        prizeFund = this.prizeFund,
        bannerMob = this.mobBanner,
        status = this.status,
        mobText = this.mobText,
        minBaseLoyaltyStatusId = this.minBaseLoyaltyStatus?.id,
        maxBaseLoyaltyStatusId = this.maxBaseLoyaltyStatus?.id,
        tournamentViewerProgress = this.viewerProgress?.fragments?.tournamentViewerProgress?.mapToLocalTournamentViewerProgress(),
        tournamentPrizes = this.prizes.mapToLocalTournamentPrizes(),
        isDynamicPrizeFund = this.isDynamicPrizeFund,
        participants = this.participants.mapToLocalTournamentParticipants(),
        gameList = this.gameList.mapToLocalGameList(),
        minBetLimit = this.minBetLimit,
        qualificationRounds = this.qualificationRounds
    )
}

fun TournamentViewerProgress.mapToLocalTournamentViewerProgress(): TournamentsItem.TournamentViewerProgress {
    return TournamentsItem.TournamentViewerProgress(
        place = this.place,
        score = this.score,
        totalBets = this.totalBets
    )
}

fun List<FragmentFullTournamentsItem.Prize?>?.mapToLocalTournamentPrizes(): MutableList<TournamentsItem.TournamentPrize> {
    val results = mutableListOf<TournamentsItem.TournamentPrize>()

    if (this != null) {
        for (item in this) {
            if (item != null) {
                results.add(item.fragments.tournamentPrize.mapToLocalTournamentPrize())
            }
        }
    }

    return results
}

fun TournamentPrize.mapToLocalTournamentPrize(): TournamentsItem.TournamentPrize {
    return TournamentsItem.TournamentPrize(placeNumber = this.place_number, sum = this.sum)
}

fun List<FragmentFullTournamentsItem.Participant?>?.mapToLocalTournamentParticipants(): MutableList<TournamentsItem.TournamentParticipant> {
    val results = mutableListOf<TournamentsItem.TournamentParticipant>()

    if (this != null) {
        for (item in this) {
            if (item != null) {
                results.add(item.fragments.tournamentParticipant.mapToLocalTournamentParticipant())
            }
        }
    }

    return results
}

fun TournamentParticipant.mapToLocalTournamentParticipant(): TournamentsItem.TournamentParticipant {
    return TournamentsItem.TournamentParticipant(
        user = this.user?.mapToTournamentUser(),
        place = this.place,
        score = this.score
    )
}

fun TournamentParticipant.User.mapToTournamentUser() : TournamentsItem.TournamentUser {
    return TournamentsItem.TournamentUser(id = this.fragments.user.id, formattedUserName = this.fragments.user.formattedUserName)
}

fun List<FragmentFullTournamentsItem.GameList?>?.mapToLocalGameList() : MutableList<LocalGameItem> {
    val results = mutableListOf<LocalGameItem>()

    if (this != null) {
        for (item in this) {
            if (item != null) {
                results.add(item.fragments.gameItem.mapToLocalGameItem())
            }
        }
    }

    return results
}

fun GameItem.mapToLocalGameItem() : LocalGameItem {
    return LocalGameItem(this)
}

fun FragmentTournamentsItem.mapToTournamentsItem(): TournamentsItem {
    return TournamentsItem(
        id = this.id,
        title = this.title,
        dateStart = this.dateStart,
        dateEnd = this.dateEnd,
        prizeFund = this.prizeFund,
        bannerMob = this.mobBanner,
        status = this.status
    )
}

fun List<GetHomeAdditionalBlocksQuery.Item?>.mapToFirstTournamentItem(): TournamentsItem? {
    return if (this.isNotEmpty()) {
        this[0]?.fragments?.fragmentTournamentsItem?.mapToTournamentsItem()
    } else {
        null
    }
}
