<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/coordinator_layout_root"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.activitymain.MainActivity"
    tools:ignore="RtlHardcoded" >

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/abl_appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        android:theme="@style/AppTheme.AppBarOverlay"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/action_bar_height"
            android:background="@android:color/transparent"
            app:popupTheme="@style/AppTheme.PopupOverlay">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_toolbar"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/iv_toolbar_logo"
                    android:layout_width="@dimen/toolbar_logo_w"
                    android:layout_height="@dimen/toolbar_logo_h"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:src="@drawable/ic_logo_header"/>

                <ImageButton
                    android:id="@+id/ib_toolbar_wheel_of_fortune"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:layout_marginBottom="2dp"
                    android:background="@null"
                    android:foreground="?attr/selectableItemBackground"
                    android:padding="8dp"
                    android:src="@drawable/ic_wheel_of_fortune_white"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/ib_toolbar_search"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UnusedAttribute" />

                <ImageButton
                    android:id="@+id/ib_toolbar_sound"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:layout_marginBottom="2dp"
                    android:background="@null"
                    android:foreground="?attr/selectableItemBackground"
                    android:padding="8dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/ib_toolbar_search"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UnusedAttribute" />

                <ImageButton
                    android:id="@+id/ib_toolbar_search"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:layout_marginBottom="2dp"
                    android:background="@null"
                    android:foreground="?attr/selectableItemBackground"
                    android:padding="8dp"
                    android:src="@drawable/ic_search_white"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/b_toolbar_button"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UnusedAttribute" />

                <Button
                    android:id="@+id/b_toolbar_button"
                    style="@style/ButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/btn_heigh_small"
                    android:layout_marginRight="12dp"
                    android:background="@drawable/btn_bg_enter"
                    android:minWidth="0dp"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@string/_login"
                    android:textColor="@color/black"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/ib_toolbar_profile"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="8dp"
                    android:background="@null"
                    android:foreground="?attr/selectableItemBackground"
                    android:padding="8dp"
                    android:src="@drawable/ic_profile_toolbar"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="UnusedAttribute" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <include layout="@layout/content_main" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
