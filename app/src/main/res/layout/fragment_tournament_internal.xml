<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rvTournamentInternalList"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient"
    android:orientation="vertical"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/scrollContainer"
        android:paddingBottom="@dimen/not_organic_user_footer_padding_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header"
        android:animateLayoutChanges="true"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#000000"
        android:orientation="vertical">


        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="match_parent"
            android:layout_height="251dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="251dp"
            android:background="#cc000000"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="67dp"
            android:background="@drawable/bg_gradient_black"
            app:layout_constraintTop_toTopOf="@id/tv_days"
            android:layout_marginBottom="@dimen/size_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginEnd="@dimen/size_16"
            android:layout_marginRight="@dimen/size_16"
            android:gravity="start"
            android:textColor="@color/white"
            android:textSize="24sp"
            android:paddingTop="71dp"/>

        <TextView
            android:id="@+id/tv_fund"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="24dp"
            android:textColor="@color/white"
            android:textSize="24sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_name"/>

        <TextView
            android:id="@+id/tv_fund_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:alpha="0.6"
            android:text="@string/tournaments_fund"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_fund"/>

        <TextView
            android:id="@+id/tv_days"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:textSize="24sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_fund_text"/>

        <TextView
            android:id="@+id/tv_days_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:alpha="0.6"
            android:text="@string/tournaments_toend"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_days"/>

        <TextView
            android:id="@+id/tv_error"
            android:layout_width="match_parent"
            android:layout_height="18dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="@dimen/size_16"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            android:text="@string/tournament_error"
            android:textAlignment="center"
            android:gravity="center"
            android:background="@drawable/bg_btn_error"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_image"/>

        <Button
            android:id="@+id/btn"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/btn_bg"
            android:text="@string/participate"
            android:textAllCaps="false"
            android:textSize="16sp"
            android:layout_marginTop="4dp"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginEnd="@dimen/size_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@id/tv_error"
            />

        <LinearLayout
            android:id="@+id/view_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="48dp"
            android:visibility="invisible"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginEnd="@dimen/size_16"
            android:layout_marginTop="@dimen/size_16"
            android:background="@drawable/btn_bg_disable"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_image"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/view_result_top"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_16"
                android:layout_marginTop="@dimen/size_16">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tournament_result"
                    android:textColor="@color/white"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tv_place"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="@dimen/size_16"
                    android:alpha="0.5"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    tools:text="25 место" />

                <TextView
                    android:id="@+id/tv_result"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginEnd="@dimen/size_16"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    tools:text="1231231" />
            </RelativeLayout>

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:layout_marginTop="8dp"
                android:layout_marginStart="@dimen/size_16"
                android:layout_marginEnd="@dimen/size_16"
                android:background="@color/white"
                android:alpha="0.1"
                />

            <RelativeLayout
                android:id="@+id/view_result_bot"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_marginStart="@dimen/size_16"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="@dimen/size_16"
                android:layout_marginBottom="@dimen/size_16"
                >

                <TextView
                    android:id="@+id/tv_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tournament_min_bet"
                    android:textColor="@color/white"
                    android:layout_alignParentTop="true"
                    android:textSize="10sp" />

                <TextView
                    android:id="@+id/tv_joinbet_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_text"
                    android:layout_alignParentBottom="true"
                    android:layout_marginTop="2dp"
                    android:text="@string/tournament_join_bet"
                    android:textColor="@color/white"
                    android:textSize="10sp" />

                <TextView
                    android:id="@+id/tv_minbet"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentTop="true"
                    android:text="2"
                    android:textColor="@color/white"
                    android:textSize="10sp" />

                <TextView
                    android:id="@+id/tv_joinbet"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_minbet"
                    android:layout_alignParentBottom="true"
                    android:layout_alignParentEnd="true"
                    android:text="135"
                    android:textColor="@color/white"
                    android:textSize="10sp" />

            </RelativeLayout>

        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_tabs_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/size_16"
            app:layout_constraintTop_toBottomOf="@id/view_result"
            android:background="@drawable/categories_bg"
            >

            <RelativeLayout
                android:id="@+id/tab_conditions"
                android:layout_width="0dp"
                android:layout_height="80dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/tab_slots"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginTop="16dp">

                <ImageView
                    android:id="@+id/iv_conditions"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/tv_conditions"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_book" />

                <TextView
                    android:id="@+id/tv_conditions"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/size_16"
                    android:text="@string/conditions"
                    android:textColor="@color/white" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_slots"
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="@+id/tab_conditions"
                app:layout_constraintLeft_toRightOf="@+id/tab_conditions"
                app:layout_constraintRight_toLeftOf="@+id/tab_prizes"
                app:layout_constraintTop_toTopOf="@+id/tab_conditions">

                <ImageView
                    android:id="@+id/iv_slots"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/tv_slots"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_slot" />

                <TextView
                    android:id="@+id/tv_slots"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/size_16"
                    android:text="@string/slots"
                    android:textColor="@color/white" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_prizes"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@+id/tab_conditions"
                app:layout_constraintLeft_toRightOf="@+id/tab_slots"
                app:layout_constraintRight_toLeftOf="@+id/tab_leaders"
                app:layout_constraintTop_toTopOf="@+id/tab_conditions">

                <ImageView
                    android:id="@+id/iv_prizes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/tv_prizes"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_prize" />

                <TextView
                    android:id="@+id/tv_prizes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/size_16"
                    android:text="@string/prizes"
                    android:textColor="@color/white" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/tab_leaders"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="@+id/tab_conditions"
                app:layout_constraintLeft_toRightOf="@+id/tab_prizes"
                app:layout_constraintRight_toRightOf="@+id/tab_ghost1"
                app:layout_constraintTop_toTopOf="@+id/tab_conditions">

                <ImageView
                    android:id="@+id/iv_leaders"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/tv_leaders"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="8dp"
                    android:src="@drawable/ic_medal" />

                <TextView
                    android:id="@+id/tv_leaders"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginBottom="@dimen/size_16"
                    android:text="@string/leaders"
                    android:textColor="@color/white" />

            </RelativeLayout>

            <View
                android:id="@+id/tab_ghost1"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/tab_conditions"
                app:layout_constraintLeft_toRightOf="@+id/tab_leaders"
                app:layout_constraintRight_toLeftOf="@id/tab_ghost2"
                app:layout_constraintTop_toTopOf="@+id/tab_conditions" />

            <View
                android:id="@+id/tab_ghost2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/tab_conditions"
                app:layout_constraintLeft_toRightOf="@+id/tab_ghost1"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tab_conditions" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <ImageView
            android:id="@+id/iv_loader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/size_16"
            android:src="@drawable/ic_preload"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_tabs_layout"
            android:visibility="gone" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/body"
        android:background="#000D26"
        app:layout_constraintTop_toBottomOf="@id/header"
        android:paddingStart="@dimen/size_16"
        android:paddingEnd="@dimen/size_16"
        android:paddingTop="24dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/body_conditions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:scrollbars="none"
            android:orientation="horizontal">

            <WebView
                android:id="@+id/tv_about"
                android:scrollbars="none"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>


        <LinearLayout
            android:id="@+id/body_slots"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:clipToPadding="false"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/list_slots"
                android:overScrollMode="never"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>


            <Button
                android:id="@+id/btn_load_more"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_gravity="center"
                android:background="@drawable/more_btn_bg"
                android:foreground="?attr/selectableItemBackground"
                android:padding="8dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="gone"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/body_prizes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:orientation="vertical">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/list_prizes"
                android:overScrollMode="never"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/body_leaders"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="visible"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:weightSum="100"
                android:paddingBottom="8dp"
                android:orientation="horizontal">

                <TextView
                    android:textFontWeight="10"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="10" />

                <TextView
                    android:id="@+id/textView"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="30"
                    android:alpha="0.4"
                    android:textColor="@color/white"
                    android:text="@string/tournaments_player" />

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="30"
                    android:alpha="0.4"
                    android:textColor="@color/white"
                    android:textAlignment="viewEnd"
                    android:text="@string/tournaments_score" />

                <TextView
                    android:id="@+id/textView3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="30"
                    android:alpha="0.4"
                    android:textColor="@color/white"
                    android:textAlignment="viewEnd"
                    android:text="@string/tournaments_prize" />
            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/list_leaders"
                android:overScrollMode="never"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </LinearLayout>


    </LinearLayout>

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@drawable/bg_gradient_footer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/body"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <include
            android:id="@+id/footer"
            layout="@layout/view_footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/body">

        </include>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
