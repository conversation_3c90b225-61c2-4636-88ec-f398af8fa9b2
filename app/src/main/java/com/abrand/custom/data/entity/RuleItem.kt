package com.abrand.custom.data.entity

class RuleItem {
    var itemType: ItemType
    var text: String? = null
    var number: String? = null

    constructor(itemType: ItemType) {
        this.itemType = itemType
    }

    constructor(itemType: ItemType, text: String) {
        this.itemType = itemType
        this.text = text
    }

    constructor(itemType: ItemType, text: String, number: String) {
        this.itemType = itemType
        this.text = text
        this.number = number
    }

    enum class ItemType(val id: Int) {
        CATEGORY(0),
        FIRST_LEVEL(1),
        SECOND_LEVEL(2),
        THIRD_LEVEL(3),
        EXCHANGE_TABLE(4),
        FOOTER(5)
    }
}
