package com.abrand.custom.data.datasources

import com.abrand.custom.data.entity.GeneratorResponse

interface GeneratorRemoteDataSource {

    suspend fun initCustomer(
        urlParams: String,
        analytics: String,
        domains: List<String>
    ): GeneratorResponse

    suspend fun check(
        urlParams: String,
        domains: List<String>
    ): GeneratorResponse

    suspend fun getInstalled(
        urlParams: String,
        domains: List<String>
    ): GeneratorResponse

    suspend fun sendInstallAnalytics(url: String)
}
