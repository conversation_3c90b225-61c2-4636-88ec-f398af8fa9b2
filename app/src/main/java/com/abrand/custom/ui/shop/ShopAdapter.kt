package com.abrand.custom.ui.shop

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.entity.LocalBonusProductForPoints
import com.abrand.custom.data.entity.ShopItem
import com.abrand.custom.data.repositories.YearRepository
import com.abrand.custom.databinding.ItemShopHeaderBinding
import com.abrand.custom.databinding.ItemShopProductBinding
import com.abrand.custom.databinding.ViewFooterBinding
import com.abrand.custom.interfaces.FooterListener
import com.squareup.picasso.Picasso
import com.squareup.picasso.Target
import java.util.*

class ShopAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    var items = mutableListOf<ShopItem>()
    private var isExpanded: MutableList<Boolean>? = null
    var loyaltyPoints: Int = 0
    var balance: Double = 0.0
    var buttonsClickListener: ButtonsClickListener? = null
    var footerListener: FooterListener? = null

    init {
        items.add(ShopItem(ShopItem.ItemType.HEADER))
        items.add(ShopItem(ShopItem.ItemType.FOOTER))
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return when (viewType) {
            ShopItem.ItemType.HEADER.id -> {
                val view = ItemShopHeaderBinding.inflate(inflater, parent, false)
                HeaderVH(view)
            }
            ShopItem.ItemType.PRODUCT.id -> {
                val view = ItemShopProductBinding.inflate(inflater, parent, false)
                ProductVH(view)
            }
            else -> {
                val view = ViewFooterBinding.inflate(inflater, parent, false)
                FooterVH(view)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val item = items[position]
        when (holder) {
            is ProductVH -> {
                item.product?.let { holder.bind(it) }
            }
        }
    }

    override fun getItemCount() = items.size

    override fun getItemViewType(position: Int): Int {
        return items[position].itemType.id
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setList(newItems: List<ShopItem>) {
        this.items.addAll(1, newItems)
        isExpanded = ArrayList(Collections.nCopies(newItems.size + 1, false))
        notifyDataSetChanged()
    }

    fun updateShopProduct(shopProduct: LocalBonusProductForPoints) {
        items.forEachIndexed { index, element ->
            if (shopProduct.id == element.product?.id) {
                element.product?.isActive = shopProduct.isActive
                element.product?.buttonTooltip = shopProduct.buttonTooltip
                notifyItemChanged(index)
            }
        }
    }

    inner class HeaderVH(val view: ItemShopHeaderBinding) : RecyclerView.ViewHolder(view.root)

    inner class ProductVH(val view: ItemShopProductBinding) :
        RecyclerView.ViewHolder(view.root) {

        private val productImageTarget = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap, from: Picasso.LoadedFrom) {
                view.ivIcon.setImageBitmap(bitmap)
            }

            override fun onBitmapFailed(e: Exception, errorDrawable: Drawable?) { }

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) { }
        }

        fun bind(product: LocalBonusProductForPoints) {
            view.tvName.text = product.name
            setImage(product.image)
            view.tvPoints.text = view.root.context.getString(R.string.shop_product_points, product.price)
            view.tvDescription.text = product.description
            view.root.setOnClickListener {
                changeProductState(adapterPosition)
            }

            val isExpandedItem = isExpanded?.get(adapterPosition)
            if (isExpandedItem == true) {
                view.ivArrowDropdown.setBackgroundResource(R.drawable.ic_shop_arrow_to_top)
                view.root.background = ContextCompat.getDrawable(view.root.context, R.drawable.shop_card_open_bg)

                if (product.isActive) {
                    view.btnBuy.visibility = View.GONE
                    view.btnOpenScreenContainer.visibility = View.VISIBLE
                    view.btnOpenScreen.visibility = View.GONE
                    view.tvInfoDescription.text = product.buttonTooltip
                } else if (loyaltyPoints >= product.price) {
                    view.btnBuy.visibility = View.VISIBLE
                    view.btnOpenScreenContainer.visibility = View.GONE
                    view.btnBuy.setOnClickListener {
                        buttonsClickListener?.onBuyClicked(product.id)
                    }
                } else {
                    view.btnBuy.visibility = View.GONE
                    view.btnOpenScreenContainer.visibility = View.VISIBLE
                    view.btnOpenScreen.visibility = View.VISIBLE
                    view.btnOpenScreen.setOnClickListener {
                        buttonsClickListener?.onActionButtonClicked()
                    }
                    if (balance > 0) {
                        view.btnOpenScreen.text =
                            view.root.context.getString(R.string.shop_begin_to_play)
                    } else {
                        view.btnOpenScreen.text =
                            view.root.context.getString(R.string.shop_to_pay)
                    }
                    view.tvInfoDescription.text =
                        view.root.context.getString(R.string.shop_info_message_not_enough_points)
                }
                view.productHide.visibility = View.VISIBLE
            } else {
                view.ivArrowDropdown.setBackgroundResource(R.drawable.ic_shop_arrow_to_bottom)
                view.root.background = ContextCompat.getDrawable(view.root.context, R.drawable.shop_card_close_bg)
                view.btnBuy.visibility = View.GONE
                view.btnOpenScreenContainer.visibility = View.GONE
                view.productHide.visibility = View.GONE
            }
        }

        private fun setImage(image: String) {
            if (!TextUtils.isEmpty(image)) {
                Picasso.get()
                    .load(ApoloConfig.getFullUrl(image))
                    .into(productImageTarget)
            } else {
                view.ivIcon.background = null
            }
        }

        private fun changeProductState(position: Int) {
            val isExpandedItem = isExpanded?.get(position)
            if (isExpandedItem != null) {
                isExpanded?.set(position, !isExpandedItem)
            }
            notifyItemChanged(position)
        }
    }

    inner class FooterVH(val view: ViewFooterBinding) : RecyclerView.ViewHolder(view.root) {

        init {
            bind()
        }

        fun bind() {
            val paddingBottom: Int = itemView.context.resources.getDimensionPixelSize(R.dimen.not_organic_user_footer_padding_bottom)
            itemView.setPadding(0, 0, 0, paddingBottom)

            itemView.findViewById<View>(R.id.gl_payment_systems_view).setOnClickListener {
                footerListener?.onClickPaymentSystems()
            }
            view.tvCopyrightYear.text = view.tvCopyrightYear.context.getString(
                R.string.app_copyright_year,
                YearRepository.getCurrentYear()
            )
        }
    }

    interface ButtonsClickListener {
        fun onBuyClicked(productId:  Int)
        fun onActionButtonClicked()
    }

}
