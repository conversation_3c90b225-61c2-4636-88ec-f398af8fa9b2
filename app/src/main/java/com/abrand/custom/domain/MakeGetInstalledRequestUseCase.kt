package com.abrand.custom.domain

import com.abrand.custom.data.entity.appconfig.LocalConfig
import com.abrand.custom.data.repositories.GeneratorRepositoryImpl
import com.abrand.custom.tools.UrlEncoder
import com.abrand.custom.tools.WebUrlValidator

class MakeGetInstalledRequestUseCase(
    private val smenGiRepository: GeneratorRepository = GeneratorRepositoryImpl(),
    private val encoder: UrlEncoder = UrlEncoder(),
    private val sendAnalyticsUseCase: SendAnalyticsUseCase = SendAnalyticsUseCase(),
) {
    suspend operator fun invoke(appConfig: LocalConfig): LocalConfig {
        val customerToken = appConfig.customerToken ?: ""
        val analyticUrl = smenGiRepository.getInstalled(
            urlParams = constructUrlParams(customerToken, appConfig.token),
            domains = appConfig.domains
        )

        if (WebUrlValidator.isValidUrl(analyticUrl)) {
            try {
                sendAnalyticsUseCase.invoke(analyticUrl, appConfig)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        return appConfig
    }

    private fun constructUrlParams(userToken: String, appToken: String): String {
        return encoder.encode(
            encoder.encodeParam("a"),
            encoder.encodeParam("f", appToken),
            encoder.encodeParam("g", userToken)
        )
    }
}
