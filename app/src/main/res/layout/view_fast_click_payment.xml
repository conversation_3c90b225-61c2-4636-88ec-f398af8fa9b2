<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="112dp"
    android:background="@drawable/pay_field_bg"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintTop_toBottomOf="@+id/tv_drawer_balance_real_text">

    <RelativeLayout
        android:id="@+id/container_fast_payment_amount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginLeft="@dimen/size_16"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="@+id/fast_payment_divider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/et_fast_payment_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:gravity="end"
                android:inputType="number"
                android:imeOptions="flagNoExtractUi"
                android:maxLength="15"
                android:minWidth="40dp"
                android:textColor="#000000"
                android:textSize="24sp" />

            <TextView
                android:id="@+id/tv_fast_payment_currency"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginLeft="5dp"
                android:textColor="#000000"
                android:textSize="24sp" />
        </LinearLayout>

    </RelativeLayout>

    <View
        android:id="@+id/fast_payment_divider"
        android:layout_width="1dp"
        android:layout_height="24dp"
        android:layout_marginEnd="11dp"
        android:layout_marginRight="11dp"
        android:background="#33000000"
        app:layout_constraintBottom_toBottomOf="@+id/container_fast_payment_amount"
        app:layout_constraintEnd_toStartOf="@+id/view_circle_fast_payment_requisite"
        app:layout_constraintTop_toTopOf="@+id/container_fast_payment_amount" />

    <ImageView
        android:id="@+id/iv_fast_payment_logo"
        android:layout_width="wrap_content"
        android:layout_height="10dp"
        android:layout_marginEnd="24dp"
        android:layout_marginRight="24dp"
        android:scaleType="fitEnd"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/fast_payment_divider" />

    <View
        android:id="@+id/view_circle_fast_payment_requisite"
        android:layout_width="2dp"
        android:layout_height="2dp"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="2dp"
        android:layout_marginRight="2dp"
        android:background="@drawable/circle_fast_payment_requisite"
        app:layout_constraintEnd_toStartOf="@+id/tv_fast_payment_requisite"
        app:layout_constraintTop_toTopOf="@+id/tv_fast_payment_requisite" />

    <TextView
        android:id="@+id/tv_fast_payment_requisite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="24dp"
        android:layout_marginRight="24dp"
        android:textColor="#4F5F8E"
        android:textSize="10sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_fast_payment_logo" />

    <Button
        android:id="@+id/btn_fast_payment"
        style="@style/ButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/btn_bg_enable"
        android:text="@string/to_pay"
        app:layout_constraintBottom_toBottomOf="parent" />

    <RelativeLayout
        android:id="@+id/container_fast_payment_loader"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/pay_field_in_process_bg"
        android:elevation="10dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_loader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginTop="60dp"
            android:src="@drawable/ic_preload" />

        <TextView
            android:id="@+id/tv_fast_payment_successful_transaction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:text="@string/transaction_successful"
            android:visibility="invisible" />

        <ImageView
            android:id="@+id/iv_fast_payment_close_successful_transaction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:paddingStart="18dp"
            android:paddingLeft="18dp"
            android:paddingTop="6dp"
            android:paddingEnd="6dp"
            android:paddingRight="6dp"
            android:paddingBottom="18dp"
            android:src="@drawable/ic_close_white"
            android:visibility="invisible" />

    </RelativeLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
