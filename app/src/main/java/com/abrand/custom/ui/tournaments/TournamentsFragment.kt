package com.abrand.custom.ui.tournaments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.TournamentsItem
import com.abrand.custom.data.entity.User
import com.abrand.custom.databinding.FragmentTournamentsBinding
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.type.TournamentStatus
import com.abrand.custom.ui.activitymain.MainActivity
import androidx.fragment.app.viewModels
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.ui.payments.PaymentsFragment

class TournamentsFragment : Fragment(R.layout.fragment_tournaments) {
    private var adapter: TournamentsAdapter? = null
    private val viewModel: TournamentsViewModel by viewModels()
    private var TAG = "TournamentsFragment"
    private var binding: FragmentTournamentsBinding? = null

    companion object {
        const val INIT_AMOUNT = 5
        const val LOAD_STEP = 10
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        val localBinding = FragmentTournamentsBinding.inflate(inflater, container, false)
        binding = localBinding
        return localBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding?.rvTournaments?.layoutManager = LinearLayoutManager(context)
        binding?.rvTournaments?.setItemViewCacheSize(30)
        adapter = TournamentsAdapter(tournamentsListener,getTournamentWidth(),getTournamentHeight())
        adapter?.footerListener = footerListener
        adapter?.isUserLogged = User.State.PLAYER == Settings.get().userState
        binding?.rvTournaments?.adapter = adapter
        loadTournaments()

        observeViewModel()
        setupInsets()
        if (User.State.PLAYER == Settings.get().userState) {
            applyLoggedState()
        } else {
            applyNotLoggedState()
        }
    }

    private fun observeViewModel() {
        viewModel.tournamentsLiveData.observe(viewLifecycleOwner, Observer {
            adapter?.currentTotalTournaments= it.total
            val tournamentsItems = mutableListOf<TournamentsItem>()
            for (tournament in it.tournamentsList) {
                if (!tournament.status.equals("completed")) {
                    tournamentsItems.add(
                        TournamentsItem(
                            id = tournament.id,
                            itemType = TournamentsItem.ItemType.TOURNAMENT_CURRENT,
                            title = tournament.title,
                            dateStart = tournament.dateStart,
                            dateEnd = tournament.dateEnd,
                            prizeFund = tournament.prizeFund,
                            bannerMob = tournament.bannerMob,
                            status = tournament.status))
                } else {
                    tournamentsItems.add(
                        TournamentsItem(
                            id = tournament.id,
                            itemType = TournamentsItem.ItemType.TOURNAMENT_ENDED,
                            title = tournament.title,
                            dateStart = tournament.dateStart,
                            dateEnd = tournament.dateEnd,
                            prizeFund = tournament.prizeFund,
                            bannerMob = tournament.bannerMob,
                            status = tournament.status))
                }
            }
            if (it.offset == 0) {
                adapter?.setList(tournamentsItems)
            } else {
                adapter?.appendList(tournamentsItems)
            }
            adapter?.setTournamentLoadingState(false)
        })

        viewModel.tournamentsLoadApolloExceptionLiveData.observe(viewLifecycleOwner, Observer {
            (activity as MainActivity).showConnectionIssueMessage(it)
            adapter?.setTournamentLoadingState(false)
        })

        viewModel.tournamentsLiveDataError.observe(viewLifecycleOwner, Observer {
            Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            adapter?.setTournamentLoadingState(false)
        })
    }

    private fun setupInsets() {
        binding?.let {
            ViewCompat.setOnApplyWindowInsetsListener(it.rvTournaments) { view, insets ->
                view.updatePadding(top = insets.systemWindowInsetTop, bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }

    private val tournamentsListener = object : TournamentsAdapter.TournamentsListener {
        override fun onTournamentClicked(tournamentsItem: TournamentsItem) {
            showTournament(tournamentsItem)
        }

        override fun onLoadMoreClicked() {
            adapter?.setTournamentLoadingState(true)
            adapter?.let {
                viewModel.loadByStatus(listOf(TournamentStatus.COMPLETED),
                    it.getTournamentsCount(), LOAD_STEP)
            }
        }

        override fun onRefresh() {
            loadTournaments()
        }

    }

    private val footerListener = object : FooterListener {
        override fun onClickPaymentSystems() {
            if (User.State.PLAYER == Settings.get().userState) {
                (activity as? MainActivity)?.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
            } else {
                (activity as? MainActivity)?.openEnterScreen(false)
            }
        }
    }

    private fun showTournament(tournament: TournamentsItem) {
        if (activity != null) {
            (activity as MainActivity).showTournamentInternal(tournament,false)
        }
    }

    private fun loadTournaments() {
        adapter?.setTournamentLoadingState(true)
        val statusList = listOf(TournamentStatus.IN_PROCESS, TournamentStatus.COMPLETED)
        viewModel.loadByStatus(statusList, 0, INIT_AMOUNT)
        adapter?.setTournamentLoadingState(false)
    }

    private fun applyLoggedState() {
        adapter?.applyLoggedState()
    }

    fun applyNotLoggedState() {
        adapter?.applyNotLoggedState()
    }

    private fun getTournamentWidth(): Int {
        val screenWidth = GeneralTools.getScreenWidth(context)
        return screenWidth - 2 * (context?.resources?.getDimensionPixelSize(R.dimen.tournaments_card_side_margin)
                ?: 0)
    }

    private fun getTournamentHeight() : Int {
        return context?.resources?.getDimensionPixelSize(R.dimen.tournaments_card_height) ?: 0
    }

}
