<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/body_your_tickets"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipToPadding="false"
    android:orientation="vertical"
    android:visibility="gone">

    <TextView
        android:id="@+id/tv_lottery_tickets_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/size_16"
        android:paddingEnd="@dimen/size_16"
        android:textColor="#FFFFFF"
        android:textSize="24sp" />

    <TextView
        android:id="@+id/tv_lottery_no_tickets_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:paddingStart="@dimen/size_16"
        android:paddingEnd="@dimen/size_16"
        android:text="@string/lottery_no_tickets_description"
        android:textColor="@color/text_not_editable_field"
        android:textSize="14sp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_your_tickets"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="24dp"
        android:overScrollMode="never" />

    <Button
        android:id="@+id/btn_load_more"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:background="@drawable/more_btn_bg"
        android:foreground="?attr/selectableItemBackground"
        android:padding="8dp"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone" />

</LinearLayout>
