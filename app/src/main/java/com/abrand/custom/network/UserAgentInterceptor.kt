package com.abrand.custom.network

import com.abrand.custom.BuildConfig
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response

class UserAgentInterceptor : Interceptor {

    companion object {
        private val AGENT_SYSTEM_INFO = System.getProperty("http.agent")
        val NATIVE_APP_ID = "NativeApp"
        private val SYSTEM_IDENTIFIER = "Android"
        private val BANNER_NAME = "intbrowsrm"

        val userAgent = StringBuilder()
                .append(AGENT_SYSTEM_INFO)
                .append(" ")
                .append(NATIVE_APP_ID)
                .append("/")
                .append(SYSTEM_IDENTIFIER)
                .append("/")
                .append(BuildConfig.APPLICATION_ID)
                .append("/")
                .append("v")
                .append(BuildConfig.VERSION_NAME)
                .append("/")
                .append(BANNER_NAME)
                .toString()

        // TODO: should be moved out elsewhere
        val webViewAgentSuffix = StringBuilder()
            .append(" ")
            .append(NATIVE_APP_ID)
            .append("/")
            .append(SYSTEM_IDENTIFIER)
            .append("/")
            .append(BuildConfig.APPLICATION_ID)
            .append("/")
            .append("v")
            .append(BuildConfig.VERSION_NAME)
            .append("/")
            .append(BANNER_NAME)
            .toString()
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest: Request = chain.request()
        val builder = originalRequest.newBuilder()
        builder.addHeader("User-Agent", userAgent)
        return chain.proceed(builder.build())
    }
}
