package com.abrand.custom.ui.newpassword

import android.os.Bundle
import android.text.InputType
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.Navigation
import com.abrand.custom.data.AffDataGenerator
import com.abrand.custom.data.Resource
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.databinding.FragmentNewPasswordBinding
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.ui.activitymain.MainActivity
import com.adjust.sdk.Adjust
import com.apollographql.apollo3.exception.ApolloException

class NewPasswordFragment : Fragment() {
    private lateinit var viewModel: NewPasswordViewModel
    private var binding: FragmentNewPasswordBinding? = null
    private var resetPasswordToken: String? = null

    companion object {
        const val KEY_RESET_PASSWORD_TOKEN = "resetPasswordToken"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            resetPasswordToken = it.getString(KEY_RESET_PASSWORD_TOKEN)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        val localBinding = FragmentNewPasswordBinding.inflate(inflater, container, false)
        binding = localBinding

        viewModel = ViewModelProvider(this).get(NewPasswordViewModel::class.java)
        observeViewModel()

        localBinding.etNewPassword.setInputType(InputType.TYPE_TEXT_VARIATION_WEB_PASSWORD)

        val localResetPasswordToken = resetPasswordToken
        localBinding.btnSave.setOnClickListener {
            if (!localResetPasswordToken.isNullOrBlank()) {
                val affData = AffDataGenerator.getAffData(Adjust.getAdid())
                viewModel.resetUserPasswordByToken(localResetPasswordToken,
                        localBinding.etNewPassword.text, Settings.get().refCode, affData)
            }
        }

        return localBinding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun observeViewModel() {
        viewModel.resetPasswordLiveData.observe(viewLifecycleOwner,
                Observer<Resource<String?, ServerError, ApolloException>> { (status, _, error, failure) ->
                    when (status) {
                        Status.SUCCESS -> {
                            val mainActivity = activity
                            if (mainActivity != null && binding != null) {
                                GeneralTools.hideKeyboard(mainActivity)
                            }
                            navigateUp()
                        }
                        Status.ERROR -> {
                            Toast.makeText(context, error?.errorMessage, Toast.LENGTH_SHORT).show()
                        }
                        Status.FAILURE -> {
                            (activity as MainActivity).showConnectionIssueMessage(failure)
                        }
                        else -> {
                            // TODO: decide what should u do in another cases
                        }
                    }
                })
    }

    private fun navigateUp() {
        view?.let {
            activity?.runOnUiThread { Navigation.findNavController(it).navigateUp() }
        }
    }
}
