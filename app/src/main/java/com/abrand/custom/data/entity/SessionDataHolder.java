package com.abrand.custom.data.entity;

import android.graphics.Bitmap;

import com.abrand.custom.fragment.FragmentFullTournamentsItem;

// TODO: 2019-12-15 refactor -> to just pass
public class SessionDataHolder {
    private static final SessionDataHolder ourInstance = new SessionDataHolder();

    public static SessionDataHolder getInstance() {
        return ourInstance;
    }

    public TournamentsItem currentTournamentItem;
    public LocalGameItem currentGameItem;
    public Bitmap currentGameImage;

    public boolean needUpdateFavouriteList = false;

    private SessionDataHolder() {
    }
}
