package com.abrand.custom.ui.game

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.data.entity.MessageItem
import com.abrand.custom.data.entity.Screen
import com.abrand.custom.data.entity.UrlSource
import com.abrand.custom.databinding.FragmentInGameMenuMessagesBinding
import com.abrand.custom.tools.DateTools
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.messages.MessagesAdapter

class InGameMenuMessagesFragment : Fragment() {
    private var binding: FragmentInGameMenuMessagesBinding? = null
    private var messagesAdapter: MessagesAdapter? = null
    private val activityViewModel: GameViewModel by activityViewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val localBinding = FragmentInGameMenuMessagesBinding.inflate(inflater, container, false)
        binding = localBinding

        messagesAdapter = MessagesAdapter(Screen.MESSAGES_INGAME_MENU, mutableListOf())
        messagesAdapter?.listener = messageListener

        localBinding.rvMessages.layoutManager = LinearLayoutManager(context)
        localBinding.rvMessages.adapter = messagesAdapter

        observeViewModel()

        return localBinding.root
    }

    fun observeViewModel() {
        activityViewModel.viewerMessageLiveData.observe(viewLifecycleOwner) { messagesList ->
            updateMessages(messagesList)
        }
    }

    val messageListener = object : MessagesAdapter.Listener {
        override fun onItemDelete(id: String?, itemPosition: Int) {
            if (id != null) {
                activityViewModel.removeMessageBy(itemPosition - 1)
            }
        }

        override fun onMarkItemAsRead(id: String?, itemPosition: Int) {}

        override fun onDeepLinkClicked(url: String) {
            (activity as? MainActivity)?.processDeepLinkUrl(url, UrlSource.SUBSCRIPTION_MESSAGE)
        }

        override fun onMessagesListEmpty() {
            binding?.containerNoMessages?.visibility = View.VISIBLE
        }
    }

    private fun updateMessages(messageItems: ArrayList<MessageItem>?) {
        if (messageItems != null) {
            setupMessagesDate(messageItems)
            messagesAdapter?.setList(messageItems.toMutableList())
            if (messageItems.size > 0) {
                binding?.containerNoMessages?.visibility = View.GONE
            } else {
                binding?.containerNoMessages?.visibility = View.VISIBLE
            }
        } else {
            binding?.containerNoMessages?.visibility = View.VISIBLE
        }
    }

    private fun setupMessagesDate(messageItems: ArrayList<MessageItem>) {
        for (message in messageItems) {
            if (message.date.isNullOrEmpty() && message.serverObjectDate != null) {
                val formattedDate = DateTools.getFormattedDate(
                    requireContext(),
                    message.serverObjectDate, DateTools.FormatDateType.MESSAGES
                )
                message.date = formattedDate
            }
        }
    }
}
