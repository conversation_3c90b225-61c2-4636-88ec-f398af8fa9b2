package com.abrand.custom.social

import android.app.Activity
import com.abrand.custom.R
import org.telegram.passport.PassportScope
import org.telegram.passport.PassportScopeElementOne
import org.telegram.passport.PassportScopeElementOneOfSeveral
import org.telegram.passport.TelegramPassport

class TelegramNetwork {

    companion object {
        const val REQUEST_CODE_TELEGRAM_LOGIN = 1002;
    }

    fun auth(activity: Activity) {
        val req = TelegramPassport.AuthRequest()
        req.botID = Integer.valueOf(activity.getString(R.string.telegram_bot_id)) /* your bot ID here */
        req.publicKey = activity.getString(R.string.telegram_public_key)  /* your bot public key here */
        req.nonce = "someTestNonce" /* a unique nonce to pass to the bot server */

        // Request either a passport or an ID card with selfie, a driver license, personal details with
        // name as it appears in the documents, address with any address document, and a phone number.
        // You could also pass a raw JSON object here if that's what works better for you
        // (for example, if you already get it from your server in the correct format).
        req.scope = PassportScope(
            PassportScopeElementOne(PassportScope.PERSONAL_DETAILS)
        )
        TelegramPassport.request(activity, req, REQUEST_CODE_TELEGRAM_LOGIN)
    }

}
