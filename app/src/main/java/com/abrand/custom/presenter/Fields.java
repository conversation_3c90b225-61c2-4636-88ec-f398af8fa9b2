package com.abrand.custom.presenter;

import androidx.annotation.Nullable;

public enum Fields {
    EMAIL("email"),                      // Enter, ResetPassword screen
    LOGIN("login"),                      // Enter screen
    PASSWORD("password"),                // Enter screen
    PLAIN_PASSWORD("plainPassword"),     // Enter screen
    CURRENT_PASSWORD("currentPassword"), // Profile screen
    NEW_PASSWORD("newPassword"),         // Profile screen
    USER_NAME("userName"),               // Profile screen
    BIRTHDAY("birthday"),                // Profile screen
    PHONE("phone"),                      // Profile screen
    RECAPTCHA("recaptcha"),              // Login screen
    CAPTCHA("captcha"),                  // Enter screen
    LIMIT_REQUESTS("");                  // Enter screen

    private String strValue;

    Fields(String strValue) {
        this.strValue = strValue;
    }

    public String getStrValue() {
        return strValue;
    }

    @Nullable
    public static Fields getByString(String fieldStr) {
        for ( Fields field : Fields.values() ) {
            if ( field.strValue.equals(fieldStr ) ) {
                return field;
            }
        }

        return null;
    }
}
