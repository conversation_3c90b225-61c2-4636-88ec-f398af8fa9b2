<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/parent"
    android:weightSum="100"
    android:orientation="horizontal"
    android:paddingBottom="8dp"
    android:paddingTop="8dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_number"
        android:layout_weight="10"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:textColor="#40ffffff"
        android:textSize="14sp" />

    <TextView
        android:layout_weight="40"
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAlignment="viewStart"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="#ffffff"
        android:textSize="14sp" />

    <TextView
        android:layout_weight="20"

        android:id="@+id/tv_score"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAlignment="viewEnd"
        android:maxLines="1"
        android:textColor="#ffffff"
        android:textSize="14sp" />

    <TextView
        android:layout_weight="30"
        android:maxLines="1"
        android:id="@+id/tv_fund"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textAlignment="viewEnd"
        android:textColor="#FFFFFF"
        android:textSize="14sp" />

</LinearLayout>
