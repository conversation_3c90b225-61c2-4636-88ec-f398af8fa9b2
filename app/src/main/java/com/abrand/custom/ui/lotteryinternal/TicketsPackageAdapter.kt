package com.abrand.custom.ui.lotteryinternal

import android.graphics.Paint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.lottery.LocalLotteryTicketPackage
import com.abrand.custom.databinding.ItemLotteryTicketsPackageBinding
import com.abrand.custom.tools.GeneralTools

class TicketsPackageAdapter(private val items: List<LocalLotteryTicketPackage>) :
    RecyclerView.Adapter<TicketsPackageAdapter.TicketsPackageViewHolder>() {
    var selectedPosition = 0
    var listener: Listener? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TicketsPackageViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = ItemLotteryTicketsPackageBinding.inflate(inflater, parent, false)
        return TicketsPackageViewHolder(view)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: TicketsPackageViewHolder, position: Int) {
        val ticketsPackage = items[position]
        val context = holder.itemView.context

        holder.view.radioButton.isChecked = (position == selectedPosition)
        holder.view.radioButton.apply {
            setOnClickListener {
                this.isSelected = !this.isSelected
                selectedPosition = if (selectedPosition == holder.adapterPosition) {
                    listener?.onPackageSelected(false)
                    -1
                } else {
                    listener?.onPackageSelected(true)
                    holder.adapterPosition
                }
                notifyDataSetChanged()
            }
        }

        holder.view.tvDiscount.text = context.getString(R.string.lottery_discount, ticketsPackage.discount)
        holder.view.tvTicketsCount.text = ticketsPackage.ticketCount.toString()

        holder.view.tvFullPrice.apply {
            paintFlags = paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
            text = GeneralTools.formatBalance(Settings.get().userCurrencyCode, ticketsPackage.fullPrice)
        }

        holder.view.tvPriceWithDiscount.text =
            GeneralTools.formatBalance(Settings.get().userCurrencyCode, ticketsPackage.priceWithDiscount)
    }

    class TicketsPackageViewHolder(val view: ItemLotteryTicketsPackageBinding) :
        RecyclerView.ViewHolder(view.root)

    interface Listener {
        fun onPackageSelected(isSelected: Boolean)
    }
}
