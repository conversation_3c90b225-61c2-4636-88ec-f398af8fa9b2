package com.abrand.custom.data.entity

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class LoyaltyStatus(
    @SerializedName("statusId") val statusId: Int?,
    @SerializedName("title") val title: String?,
    @SerializedName("prizePoints") var prizePoints: String?,
    @SerializedName("prizeMoney") var prizeMoney: Double?,
    @SerializedName("prizeCurrencySymbol") var prizeCurrencySymbol: String?
) : Parcelable
