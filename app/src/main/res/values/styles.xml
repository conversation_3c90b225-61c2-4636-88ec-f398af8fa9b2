<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/black</item>
    </style>

    <style name="MaterialCalendarTheme" parent="ThemeOverlay.MaterialComponents.MaterialCalendar">
        <item name="materialCalendarStyle">@style/Custom_MaterialCalendar.Fullscreen</item>

        <!-- Header panel -->
        <item name="materialCalendarHeaderLayout">@style/MaterialCalendar.HeaderLayout1</item>
        <!-- Buttons -->
        <item name="buttonBarPositiveButtonStyle">@style/TextButton.Dialog1</item>
        <item name="buttonBarNegativeButtonStyle">@style/TextButton.Dialog1</item>

        <item name="materialCalendarHeaderToggleButton">@style/Widget.AppTheme.MaterialCalendar.HeaderToggleButton</item>

        <item name="colorSurface">#FFFFFF</item>
        <item name="colorOnSurface">#000000</item>
        <item name="android:textColorPrimary">#000000</item>
        <item name="android:windowFullscreen">true</item>

    </style>

    <style name="MaterialCalendar.HeaderLayout1" parent="Widget.MaterialComponents.MaterialCalendar.HeaderLayout">
        <item name="android:background">#FFFFFF</item>
    </style>

    <style name="TextButton.Dialog1" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="backgroundTint">@color/colorPrimaryDark</item>
    </style>

    <style name="Custom_MaterialCalendar.Fullscreen" parent="@style/Widget.MaterialComponents.MaterialCalendar.Fullscreen">
        <item name="android:windowFullscreen">false</item>
    </style>

    <style name="Widget.AppTheme.MaterialCalendar.HeaderToggleButton" parent="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton">
        <item name="android:visibility">gone</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="SplashTheme" parent="AppTheme">
        <item name="android:windowBackground">@drawable/splash_bg</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="ButtonStyle">
        <item name="android:background">@drawable/btn_bg</item>
        <item name="android:foreground">?attr/selectableItemBackground</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:shadowColor">#33000000</item>
        <item name="android:shadowDy">1.0</item>
        <item name="android:shadowRadius">1.0</item>
    </style>

    <style name="ButtonBlueStyle" parent="ButtonStyle">
        <item name="android:background">@drawable/btn_blue_bg</item>
    </style>

    <style name="ButtonGreyStyle" parent="ButtonStyle">
        <item name="android:background">@drawable/btn_grey_bg</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="ButtonTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:shadowColor">#33000000</item>
        <item name="android:shadowDy">1.0</item>
        <item name="android:shadowRadius">1.0</item>
    </style>

    <style name="ButtonFilterHistory">
        <item name="android:textAllCaps">false</item>
        <item name="android:textSize">12sp</item>
        <item name="android:minWidth">42dp</item>
        <item name="android:paddingStart">8dp</item>
        <item name="android:paddingEnd">8dp</item>
        <item name="android:layout_marginEnd">12dp</item>
        <item name="android:layout_marginBottom">12dp</item>
    </style>

    <style name="ButtonFilterHistorySelected" parent="ButtonFilterHistory">
        <item name="android:background">@drawable/bg_btn_filter_transaction_selected</item>
        <item name="android:textColor">@color/btn_filter_history_selected_text</item>
    </style>

    <style name="ButtonFilterHistoryUnselected" parent="ButtonFilterHistory">
        <item name="android:background">@drawable/bg_btn_filter_transaction_unselected</item>
        <item name="android:textColor">@color/btn_filter_history_unselected_text</item>
    </style>

    <style name="CategoryTitle">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">#FFFFFF</item>
    </style>

    <style name="DatePicker" parent="Theme.AppCompat.Light.Dialog">
        <item name="colorAccent">@color/bg_gradient_start</item>
        <item name="colorControlActivated">@color/bg_gradient_start</item>
    </style>

    <style name="DeactivateBonusAlertDialogStyle" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:textColor">#000000</item>
        <item name="android:background">#bdbdbd</item>
    </style>

</resources>
