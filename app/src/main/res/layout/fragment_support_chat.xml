<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <WebView
            android:id="@+id/linkWebView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <include
        android:id="@+id/lPreloader"
        layout="@layout/layout_preloader" />

</androidx.constraintlayout.widget.ConstraintLayout>