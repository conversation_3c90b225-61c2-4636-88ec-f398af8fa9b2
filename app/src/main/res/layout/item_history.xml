<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/transaction_main"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/iv_transaction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_amount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="8dp"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            app:layout_constraintStart_toEndOf="@+id/iv_transaction"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/ll_open_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="@drawable/bg_transaction_status"
            android:orientation="horizontal"
            android:paddingTop="2dp"
            android:paddingEnd="4dp"
            android:paddingBottom="2dp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_amount"
            app:layout_constraintStart_toEndOf="@+id/tv_amount"
            app:layout_constraintTop_toTopOf="@+id/tv_amount">

            <ImageView
                android:id="@+id/iv_open_status_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:src="@drawable/ic_transaction_status_new" />

            <TextView
                android:id="@+id/tv_open_status_new"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:textColor="#8DA1BD"
                android:textSize="10sp" />

            <ImageView
                android:id="@+id/iv_open_status_success"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:src="@drawable/ic_transaction_status_success" />

            <TextView
                android:id="@+id/tv_open_status_success"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:textColor="#8DA1BD"
                android:textSize="10sp" />

            <ImageView
                android:id="@+id/iv_open_status_fail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="4dp"
                android:src="@drawable/ic_transaction_status_fail" />

            <TextView
                android:id="@+id/tv_open_status_fail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:textColor="#8DA1BD"
                android:textSize="10sp" />

        </LinearLayout>

        <ImageView
            android:id="@+id/iv_open_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_amount"
            app:layout_constraintStart_toEndOf="@+id/tv_amount"
            app:layout_constraintTop_toTopOf="@+id/tv_amount" />

        <TextView
            android:id="@+id/tv_operation_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingBottom="8dp"
            android:textColor="#8DA1BD"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@+id/tv_amount"
            app:layout_constraintTop_toBottomOf="@+id/tv_amount" />

        <ImageView
            android:id="@+id/iv_arrow_dropdown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginLeft="@dimen/size_16"
        android:layout_marginRight="@dimen/size_16"
        android:background="#0DFFFFFF" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/transaction_hide"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#0DFFFFFF"
        android:paddingStart="@dimen/size_16"
        android:paddingTop="@dimen/size_16"
        android:paddingEnd="@dimen/size_16"
        android:paddingBottom="8dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_operation_type">

        <Button
            android:id="@+id/btn_question"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_btn_transaction_ask_question"
            android:minHeight="20dp"
            android:paddingStart="6dp"
            android:paddingEnd="6dp"
            android:text="@string/ask_question"
            android:textAllCaps="false"
            android:textColor="#D9FFFFFF"
            android:textSize="10sp"
            app:layout_constraintEnd_toStartOf="@+id/spaceBeforePaymentBtn"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/spaceBeforePaymentBtn"
            android:layout_width="8dp"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toStartOf="@+id/btn_payment"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/btn_payment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_btn_transaction_repeat"
            android:minHeight="20dp"
            android:paddingStart="6dp"
            android:paddingEnd="6dp"
            android:text="@string/repeat_payment"
            android:textAllCaps="false"
            android:textColor="#D9FFFFFF"
            android:textSize="10sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_transaction_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/transaction_id"
            android:textColor="#8DA1BD"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_transaction_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_transaction_id" />

        <ImageView
            android:id="@+id/iv_transaction_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            app:layout_constraintBottom_toBottomOf="@+id/tv_transaction_status"
            app:layout_constraintStart_toEndOf="@+id/tv_transaction_status"
            app:layout_constraintTop_toTopOf="@+id/tv_transaction_status" />

        <TextView
            android:id="@+id/tv_transaction_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="#8DA1BD"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_transaction_status" />

        <TextView
            android:id="@+id/tv_transaction_payment_system"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:paddingBottom="8dp"
            android:textColor="#8DA1BD"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_transaction_date" />

        <TextView
            android:id="@+id/tv_transaction_user_comment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingBottom="8dp"
            android:textColor="#8DA1BD"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_transaction_payment_system" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_children"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_transaction_user_comment" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
