package com.abrand.custom.tools

import android.util.Base64
import kotlin.text.Charsets.UTF_8

object Base64EncodingProviderImpl : Base64EncodingProvider {
    override fun base64Encode(message: String): String {
        return Base64.encodeToString(message.toByteArray(UTF_8), Base64.NO_WRAP)
    }

    override fun base64Decode(message: String): String {
        return String(Base64.decode(message, Base64.NO_WRAP), UTF_8)
    }
}
