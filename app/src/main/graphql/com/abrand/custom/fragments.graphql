fragment GameItem on GameListItem {
    gameId
    name
    url
    isHasDemo
    iconMob
    userRelatedProperties {
        isFavorite
    }
}

fragment FragmentTournamentsItem on Tournament {
    id
    title
    dateStart
    dateEnd
    prizeFund
    mobBanner
    status
}

fragment FragmentLotteriesItem on StandardLottery {
    id
    name
    status
    prizesSum
    prizeFundByString
    startAt
    finishAt
    bannerMobile
}
