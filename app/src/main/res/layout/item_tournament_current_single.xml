<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:app="http://schemas.android.com/apk/res-auto"
android:id="@+id/card_root"
android:layout_width="match_parent"
android:layout_height="@dimen/tournaments_single_card_height"
android:layout_marginLeft="@dimen/tournaments_card_side_margin"
android:layout_marginTop="@dimen/size_16"
android:layout_marginRight="@dimen/tournaments_card_side_margin">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="8dp"

        app:cardElevation="-1dp"
        app:cardBackgroundColor="#ffffff"
        >

        <ImageView
            android:id="@+id/iv_item_tournament_image"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <ImageView
            android:id="@+id/iv_item_tournament_image_blur"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:alpha="0.6"
            android:layout_gravity="bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.cardview.widget.CardView>



    <TextView
        android:id="@+id/tv_item_tournament_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="224dp"
        android:layout_marginStart="@dimen/size_16"
        android:layout_marginEnd="@dimen/size_16"
        android:textStyle="bold"
        android:gravity="start"
        android:textColor="@color/black"
        android:textSize="18sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/size_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_item_tournament_day"
        app:layout_constraintTop_toBottomOf="@id/tv_item_tournament_name"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_item_tournament_prize"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:gravity="center"
            android:textColor="@color/black"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/tv_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="12sp"
            android:alpha="0.6"
            android:text="@string/tournaments_fund"/>


    </LinearLayout>


    <TextView
        android:id="@+id/tv_item_tournament_day"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:layout_marginStart="16dp"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tv_text1"/>

    <TextView
        android:id="@+id/tv_text1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:textColor="@color/black"
        android:textSize="12sp"
        android:alpha="0.6"
        android:text="@string/tournaments_toend"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="@dimen/size_16" />

<Button
    android:id="@+id/btn_about"
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    android:layout_marginEnd="16dp"
    android:textSize="14sp"
    android:layout_gravity="center_horizontal"
    android:minHeight="32dp"
    android:textStyle="bold"
    android:textAllCaps="false"
    android:scaleType="center"
    android:background="@drawable/btn_tournament"
    android:textColor="#000000"
    android:text="@string/tournaments_about"
 />
</androidx.constraintlayout.widget.ConstraintLayout>