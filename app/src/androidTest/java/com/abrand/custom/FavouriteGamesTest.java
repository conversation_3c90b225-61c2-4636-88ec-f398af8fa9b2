package com.abrand.custom;

import android.text.TextUtils;

import androidx.fragment.app.testing.FragmentScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.abrand.custom.data.entity.AuthResponse;
import com.abrand.custom.data.entity.LocalGameItem;
import com.abrand.custom.interfaces.GenericTarget;
import com.abrand.custom.interfaces.LoadTarget;
import com.abrand.custom.network.ApolloProcessor;
import com.abrand.custom.network.ApolloProcessorKt;
import com.abrand.custom.presenter.FieldsErrorsHolder;
import com.abrand.custom.type.GameListOrderField;
import com.abrand.custom.type.OrderDirection;
import com.abrand.custom.ui.enter.EnterFragment;
import com.apollographql.apollo3.exception.ApolloException;

import org.jetbrains.annotations.Nullable;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.List;

@RunWith(AndroidJUnit4.class)
public class FavouriteGamesTest {
    private final String[] error         = {null};
    private final int      RESPONSE_TIME = 5_000;

    @Before
    public void setUp() {
//        FragmentScenario.launch(EnterFragment.class, null, R.style.AppTheme, null);
    }

    private void login() {
        // TODO: 28.10.2022 rewrite to Kt version
//        final String email    = "<EMAIL>";
//        final String password = "qweqwe1";
//
//        ApolloProcessor.login(email, password, "", "", "",
//                new GenericTarget<AuthResponse>() {
//                    @Override
//                    public void onSuccess(@Nullable AuthResponse authResponse) {
//                        if ( TextUtils.isEmpty(authResponse.getUserId()) ) {
//                            error[0] = "success response but empty userId";
//                        }
//                    }
//
//                    @Override
//                    public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                        error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//                    }
//
//                    @Override
//                    public void onFailure(ApolloException e) {
//                        error[0] = e.toString();
//                    }
//                });
    }

    @Test
    public void loadGames() {
        //removed ad unused network call
//        ApolloProcessor.loadByAttribute(GameAttributeType.POPULAR, 20, 0,
//                GameOrderField.POPULARITY, OrderDirection.ASC, new LoadTarget() {
//                    @Override
//                    public void onSuccess(List<LocalGameItem> gameThumbs, int total, int offset) {
//                        if ( gameThumbs.size() > 0 ) {
//                            startIsGameFavourite(gameThumbs.get(0));
//                        } else {
//                            error[0] = "server returned empty game list";
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(ApolloException e) {
//                        error[0] = e.toString();
//                    }
//                });
//
//        waitResponseAndAssertFailIfError();
    }

    private void startIsGameFavourite(LocalGameItem localGameItem) {
        // TODO: 20.10.2022 rewrite to Kt version
//        ApolloProcessor.getGameById(localGameItem.getId(), new GenericTarget<LocalGameItem>() {
//
//            @Override
//            public void onSuccess(@androidx.annotation.Nullable LocalGameItem localGameItem) {
//                if ( localGameItem.isFavorite() ) {
//                    removeGameFromFavourites(localGameItem.getId());
//                } else {
//                    addGameToFavourites(localGameItem.getId());
//                }
//            }
//
//            @Override
//            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = "isGameFavourite:" + e.toString();
//            }
//        });
    }

    private void removeGameFromFavourites(int gameId) {
        ApolloProcessorKt.removeGameFromFavourites(gameId, new GenericTarget<Boolean>() {
            @Override
            public void onSuccess(Boolean isFavourite) {
                if ( !isFavourite ) {
                    endIsGameFavouriteTest(gameId, false);
                    loadFavouriteGamesTest(gameId, false);
                } else {
                    error[0] = "removeGameFromFavourites does not remove game";
                }
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }
        });
    }

    private void addGameToFavourites(int gameId) {
        ApolloProcessorKt.addGameToFavourites(gameId, new GenericTarget<Boolean>() {

            @Override
            public void onSuccess(Boolean isFavourite) {
                if ( isFavourite ) {
                    endIsGameFavouriteTest(gameId, true);
                    loadFavouriteGamesTest(gameId, true);
                } else {
                    error[0] = "addGameToFavourites does not add game";
                }
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }
        });
    }

    private void endIsGameFavouriteTest(int gameId, boolean expectedResultFavourite) {
        // TODO: 20.10.2022 rewrite to Kt version
//        ApolloProcessor.getGameById(gameId, new GenericTarget<LocalGameItem>() {
//
//            @Override
//            public void onSuccess(@androidx.annotation.Nullable LocalGameItem localGameItem) {
//                if ( localGameItem.isFavorite() != expectedResultFavourite ) {
//                    error[0] = "isGameFavourite must be: " + expectedResultFavourite + " but server returned :" + isFavourite;
//                }
//            }
//
//            @Override
//            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
    }

    private void loadFavouriteGamesTest(int gameId, boolean expectedResultFavourite) {
        // TODO: 20.10.2022 rewrite to Kt version
//        ApolloProcessor.loadFavouriteGames(GameListOrderField.POSITION, OrderDirection.ASC,
//                0, 0, new LoadTarget() {
//                    @Override
//                    public void onSuccess(List<LocalGameItem> localGameItems, int total, int offset) {
//                        if ( expectedResultFavourite && !isGameInList(localGameItems, gameId) ) {
//                            error[0] = "loadFavouriteGames not returned gameId: " + gameId + " after addGameToFavourites";
//                        } else if ( !expectedResultFavourite && isGameInList(localGameItems, gameId) ) {
//                            error[0] = "loadFavouriteGames returned gameId: " + gameId + " after removeGameFromFavourites";
//                        }
//                    }
//
//                    @Override
//                    public void onFailure(ApolloException e) {
//                        error[0] = e.toString();
//                    }
//                });
//
//        waitResponseAndAssertFailIfError();
    }

    private boolean isGameInList(List<LocalGameItem> localGameItems, int gameId) {
        for ( LocalGameItem localGameItem : localGameItems ) {
            if ( localGameItem.getId() == gameId ) {
                return true;
            }
        }
        return false;
    }

    private void waitResponseAndAssertFailIfError() {
        try {
            Thread.sleep(RESPONSE_TIME);
            if ( !TextUtils.isEmpty(error[0]) ) {
                Assert.fail(error[0]);
            }
        } catch ( InterruptedException e ) {
            e.printStackTrace();
        }
    }
}
