package com.abrand.custom.tools;

import android.util.Base64;

public class Crypt {
    private static String base64Encode(String message) {
        return Base64.encodeToString(message.getBytes(), 0, message.getBytes().length, Base64.NO_WRAP);
    }

    private static String base64Decode(String message) {
        // FIXME: 15.07.2024 change to NO_WRAP for consistency with encode method
        return new String(Base64.decode(message, Base64.DEFAULT));
    }

    public static String encrypt(String string) {
        return base64Encode(base64Encode(string));
    }

    public static String decrypt(String string) {
        return base64Decode(base64Decode(string));
    }
}

