package com.abrand.custom.ui.lotteryinternal

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.lottery.LocalLotteryWinner
import com.abrand.custom.databinding.ItemLotteryWinnerBinding
import com.abrand.custom.tools.GeneralTools

class WinnersAdapter(private val items: List<LocalLotteryWinner>) :
    RecyclerView.Adapter<WinnersAdapter.WinnerViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WinnerViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = ItemLotteryWinnerBinding.inflate(inflater, parent, false)
        return WinnerViewHolder(view)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON>wHolder, position: Int) {
        val context = holder.itemView.context
        val winner = items[position]

        holder.view.tvNumber.text = winner.place.toString()

        if (winner.userId != Settings.get().userId) {
            holder.view.tvName.text = winner.formattedUserName
        } else {
            if (Settings.get().userName.isNullOrEmpty()) {
                holder.view.tvName.text = Settings.get().loggedUserEmail
            } else {
                holder.view.tvName.text = Settings.get().userName
            }
        }
        if (winner.place < 4) {
            holder.view.tvName.setTextColor(ContextCompat.getColor(context, R.color.lottery_prize_highlighted_text))
        } else {
            holder.view.tvName.setTextColor(ContextCompat.getColor(context, R.color.white))
        }

        holder.view.tvTicketId.text = winner.ticketId.toString()
        if (winner.isTicketGold == true) {
            holder.view.tvTicketId.setTextColor(Color.BLACK)
            holder.view.ivBgTicket.setImageDrawable(ContextCompat.getDrawable(context, R.drawable.bg_ticket_gold))
        } else {
            holder.view.tvTicketId.setTextColor(Color.WHITE)
            holder.view.ivBgTicket.setImageDrawable(ContextCompat.getDrawable(context, R.drawable.bg_ticket_regular))
        }

        holder.view.tvPrize.text = winner.prizeTitle.takeUnless { it.isNullOrBlank() }
            ?: GeneralTools.formatBalance(Settings.get().userCurrencyCode, winner.prizeSum)

        if (position % 4 == 0 || position % 4 == 1) {
            holder.itemView.setBackgroundColor(Color.parseColor("#1455BEF9"))
        } else {
            holder.itemView.setBackgroundColor(Color.TRANSPARENT)
        }
    }

    override fun getItemCount() = items.size

    class WinnerViewHolder(val view: ItemLotteryWinnerBinding) : RecyclerView.ViewHolder(view.root)
}
