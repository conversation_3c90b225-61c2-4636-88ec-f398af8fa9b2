package com.abrand.custom.ui

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.AutologinMutation
import com.abrand.custom.domain.GeneratorCommunicationUseCase
import com.abrand.custom.domain.PrepareAppUseCase
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessorKt
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class SplashViewModel : ViewModel() {
    private val generatorUseCase = GeneratorCommunicationUseCase()
    private val prepareAppUseCase = PrepareAppUseCase()
    private val _eventChannel = Channel<SplashEffects>(capacity = Channel.BUFFERED)
    val eventFlow: Flow<SplashEffects>
        get() = _eventChannel.receiveAsFlow()

    fun autoLogin(
        loginToken: String,
        refCode: String?,
        adjustId: String?,
        target: GenericTarget<AutologinMutation.Viewer>
    ) {
        ApolloProcessorKt.autoLogin(loginToken, refCode, adjustId, target, viewModelScope)
    }

    fun onEvent(event: UIEvents) {
        when (event) {
            UIEvents.StartGeneratorSync -> startGeneratorFlow()
        }
    }

    suspend fun prepareApp() {
        prepareAppUseCase()
    }

    private fun startGeneratorFlow() {
        viewModelScope.launch {
            generatorUseCase().first()
            // TODO: choose result type - just send event for now
            _eventChannel.send(SplashEffects.GeneratorSyncFinish)
        }
    }
}

sealed interface SplashEffects {
    data object GeneratorSyncFinish: SplashEffects
}

sealed interface UIEvents {
    data object StartGeneratorSync: UIEvents
}
