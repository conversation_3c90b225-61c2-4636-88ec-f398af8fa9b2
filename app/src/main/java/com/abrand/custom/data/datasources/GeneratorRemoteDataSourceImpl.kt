package com.abrand.custom.data.datasources

import com.abrand.custom.data.GeneratorRequestProcessor
import com.abrand.custom.data.entity.GeneratorResponse
import com.abrand.custom.data.entity.appconfig.RemoteAppConfig

internal class GeneratorRemoteDataSourceImpl(
    private val apiService: GeneratorApiService = GeneratorApiService.provideService(),
    private val requestProcessor: GeneratorRequestProcessor = GeneratorRequestProcessor()
) : GeneratorRemoteDataSource, ConfigRemoteDatasource {

    override suspend fun initCustomer(
        urlParams: String,
        analytics: String,
        domains: List<String>
    ): GeneratorResponse {
        return requestProcessor.processRequest(
            request = { domain ->
                apiService.initCustomer(domain + urlParams, analytics = analytics)
            },
            domains = domains,
            tag = "initCustomer"
        )
    }

    override suspend fun check(urlParams: String, domains: List<String>): GeneratorResponse {
        return requestProcessor.processRequest(
            request = { domain ->
                apiService.check(domain + urlParams)
            },
            domains = domains,
            tag = "check"
        )
    }

    override suspend fun getInstalled(urlParams: String, domains: List<String>): GeneratorResponse {
        return requestProcessor.processRequest(
            request = { domain ->
                apiService.getInstalled(domain + urlParams)
            },
            domains = domains,
            tag = "getInstalled"
        )
    }

    override suspend fun sendInstallAnalytics(url: String) {
        apiService.sendAnalytics(url)
    }

    override suspend fun fetchAppConfig(
        token: String,
        domains: List<String>,
        hash: String,
    ): RemoteAppConfig {
        return requestProcessor.processRequest(
            request = { domain ->
                apiService.getConfig(
                    url = domain + GET_CONFIG_URL,
                    appToken = token,
                    hash = hash,
                )
            },
            domains = domains,
            tag = "getConfig",
            mapResponse = { serverResponse -> serverResponse.data },
        )
    }

    companion object {
        internal const val GET_CONFIG_URL = "get-config"
    }
}
