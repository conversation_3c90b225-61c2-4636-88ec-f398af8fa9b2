<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="8dp">

    <TextView
        android:id="@+id/tv_tournament_block_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:paddingStart="@dimen/size_16"
        android:paddingEnd="@dimen/size_16"
        android:text="@string/home_current_tournament"
        android:textColor="#FFFFFF"
        android:textSize="24sp" />

    <include
        android:id="@+id/tournament_view"
        layout="@layout/item_tournament_current_multiple"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginStart="@dimen/tournaments_card_side_margin"
        android:layout_marginTop="@dimen/size_16"
        android:layout_marginEnd="@dimen/tournaments_card_side_margin" />

    <TextView
        android:id="@+id/tv_lottery_block_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:paddingStart="@dimen/size_16"
        android:paddingEnd="@dimen/size_16"
        android:text="@string/home_current_lottery"
        android:textColor="#FFFFFF"
        android:textSize="24sp" />

    <include
        android:id="@+id/lottery_view"
        layout="@layout/item_lottery_active"
        android:layout_width="match_parent"
        android:layout_height="@dimen/lottery_active_card_height"
        android:layout_marginStart="@dimen/tournaments_card_side_margin"
        android:layout_marginTop="@dimen/size_16"
        android:layout_marginEnd="@dimen/tournaments_card_side_margin" />

    <TextView
        android:id="@+id/tv_news_block_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:paddingStart="@dimen/size_16"
        android:paddingEnd="@dimen/size_16"
        android:text="@string/home_current_news"
        android:textColor="#FFFFFF"
        android:textSize="24sp" />

    <include
        android:id="@+id/news_view"
        layout="@layout/item_card_news"
        android:layout_width="match_parent"
        android:layout_height="@dimen/news_card_height"
        android:layout_marginStart="@dimen/tournaments_card_side_margin"
        android:layout_marginTop="@dimen/size_16"
        android:layout_marginEnd="@dimen/tournaments_card_side_margin" />

</LinearLayout>
