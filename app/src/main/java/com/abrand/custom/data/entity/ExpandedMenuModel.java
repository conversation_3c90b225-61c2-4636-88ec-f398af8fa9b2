package com.abrand.custom.data.entity;

public class ExpandedMenuModel {
    private MenuItem menuItem;
    private String   title;
    private int      iconId;
    private int      count;

    public ExpandedMenuModel(MenuItem menuItem, String title, int iconId) {
        this.menuItem = menuItem;
        this.title = title;
        this.iconId = iconId;
    }

    public ExpandedMenuModel(MenuItem menuItem, String title) {
        this(menuItem, title, 0);
    }

    public MenuItem getMenuItem() {
        return menuItem;
    }

    public String getTitle() {
        return title;
    }

    public int getIconId() {
        return iconId;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getCount() {
        return count;
    }

    public void increaseCount() {
        this.count++;
    }

    public void resetCount() {
        this.count = 0;
    }

    public enum MenuItem {
        HOME, PROMOTIONS, TOURNAMENTS, GAME_ROOM, CASH_BOX, SHOP, LOTTERY, WHEEL_OF_FORTUNE, RULES,
        NEWS, LOYALTY_PROGRAM, MESSAGES, HALL_OF_FAME, EXIT, CHARITY, EXTRACT, HISTORY
    }
}
