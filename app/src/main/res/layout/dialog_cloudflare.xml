<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/container_reload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginTop="@dimen/size_16"
            android:layout_marginRight="@dimen/size_16"
            android:text="@string/cloudflare_reload_message"
            android:textColor="#000000" />

        <Button
            android:id="@+id/btn_reload"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginTop="@dimen/size_16"
            android:layout_marginRight="@dimen/size_16"
            android:text="@string/cloudflare_reload" />
    </LinearLayout>

    <WebView
        android:id="@+id/wv"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>
