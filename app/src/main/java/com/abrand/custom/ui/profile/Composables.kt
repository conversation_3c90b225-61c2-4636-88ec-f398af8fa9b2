package com.abrand.custom.ui.profile

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.abrand.custom.R
import com.abrand.custom.ui.bonusbalances.BonusBalanceCardContainer
import com.abrand.custom.ui.bonusbalances.BonusBalanceUi
import com.abrand.custom.ui.bonusbalances.BonusBalancesUiEvent
import com.abrand.custom.ui.bonusbalances.BonusBalancesViewModel
import com.abrand.custom.ui.bonusbalances.MediumText
import com.abrand.custom.ui.bonusbalances.NoBonusesView
import com.abrand.custom.ui.bonusbalances.SideEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@Composable
fun BonusesBalancesSection(
    viewModel: BonusBalancesViewModel = viewModel(),
    openPromotions: () -> Unit = {}
) {
    val bonusesState by viewModel.bonusesState.collectAsState()
    val pagerState = rememberPagerState {
        bonusesState.size
    }
    val lifecycle = LocalLifecycleOwner.current.lifecycle
    val context = LocalContext.current

    LaunchedEffect(viewModel, lifecycle) {
        lifecycle.repeatOnLifecycle(state = Lifecycle.State.STARTED) {
            withContext(Dispatchers.Main.immediate) {
                viewModel.sideEffectFlow.collect { sideEffect ->
                    when (sideEffect) {
                        SideEffect.BonusDeleteError -> {
                            Toast.makeText(
                                context,
                                R.string.bonus_balances_deletion_error_text,
                                Toast.LENGTH_LONG
                            ).show()
                        }
                        SideEffect.BonusActivateError -> {
                            Toast.makeText(
                                context,
                                R.string.bonus_balances_activation_error_text,
                                Toast.LENGTH_LONG
                            ).show()
                        }
                    }
                }
            }
        }
    }

    Column {
        BonusesTitleText()
        Spacer(Modifier.height(16.dp))
        BonusBalancesPager(
            pagerState, bonusesState,
            onEvent = viewModel::onEvent,
            getActiveBonusAmount = viewModel::getActiveBonusAmount,
            openPromotions = openPromotions
        )
        Spacer(Modifier.height(16.dp))
        PagerDots(pagerState)
    }
}

@Composable
fun BonusesTitleText() {
    Text(modifier = Modifier.padding(horizontal = 16.dp),
        text = "Бонусы казино",
        color = Color.White,
        fontSize = 24.sp,
        lineHeight = 32.sp,
        fontWeight = FontWeight(400),
        fontFamily = FontFamily(Font(R.font.roboto_regular, FontWeight.Normal))
    )
}

@Composable
fun BonusBalancesPager(
    pagerState: PagerState,
    bonusesState: List<BonusBalanceUi>,
    onEvent: (BonusBalancesUiEvent) -> Unit,
    getActiveBonusAmount: () -> String?,
    openPromotions: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    val scrollToStart = {
        coroutineScope.launch {
            pagerState.animateScrollToPage(0)
        }
    }

    if (bonusesState.isEmpty()) {
        NoBonusesView(
            modifier = Modifier
                .defaultMinSize(minHeight = 212.dp)
                .padding(16.dp)
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .background(color = Color(0x661F1B18)),
            openPromotions = openPromotions
        )
    } else {
        HorizontalPager(
            modifier = Modifier
                .wrapContentHeight()
                .fillMaxWidth(),
            state = pagerState,
            contentPadding = PaddingValues(horizontal = 16.dp),
            pageSpacing = 8.dp
        ) { page ->
            BonusBalanceCardContainer(bonusesState[page], onEvent, scrollToStart, getActiveBonusAmount)
        }
    }
}

@Composable
fun ActiveIndicator(isActive: Boolean) {
    val text = if (isActive) "Активный" else "Не активный"
    val background = if (isActive) Color(0x996BD001) else Color(0x99FDBB2C)

    MediumText(
        modifier = Modifier
            .defaultMinSize(minHeight = 22.dp)
            .clip(RoundedCornerShape((12.dp)))
            .background(background)
            .padding(vertical = 3.dp, horizontal = 8.dp),
        text = text,
        fontSize = 10.sp,
        lineHeight = 16.sp,
    )
}

@Composable
fun PagerDots(
    pagerState: PagerState,
) {
    Row(
        Modifier
            .wrapContentHeight()
            .fillMaxWidth()
            .padding(top = 16.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.Bottom
    ) {
        repeat(
            if (pagerState.pageCount > 1) {
                pagerState.pageCount
            } else {
                0
            }
        ) { iteration ->
            val color = when {
                iteration == pagerState.currentPage ->
                    colorResource(id = R.color.colorPrimary).copy(alpha = 0.8f)
                else ->
                    colorResource(id = R.color.colorPrimary).copy(alpha = 0.2f)
            }

            Box(
                modifier = Modifier
                    .padding(horizontal = 8.dp)
                    .size(8.dp)
                    .clip(CircleShape)
                    .background(color = color)
                    .fillMaxWidth()
                    .height(8.dp)
            )
        }
    }
}

@Preview
@Composable
fun BonusBalancesSectionPreview() {
    BonusesBalancesSection()
}
