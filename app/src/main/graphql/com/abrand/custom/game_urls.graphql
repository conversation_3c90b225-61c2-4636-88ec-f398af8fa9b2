query getDemoGameUrl(
    $gameUrl: String!,
    $host: String!
) {
    demoGameUrl(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        gameUrl: $gameUrl,
        host: $host
    ) {
        url
    }
}

query getGameUrl(
    $gameUrl: String!,
    $host: String!,
    $refCode: String
) {
    gameUrl(
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        },
        gameUrl: $gameUrl,
        host: $host,
        refCode: $refCode
    ) {
        url
    }
}
