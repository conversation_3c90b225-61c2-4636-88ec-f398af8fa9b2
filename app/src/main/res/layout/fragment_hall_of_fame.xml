<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient"
    tools:context=".ui.halloffame.HallOfFameFragment">

    <LinearLayout
        android:id="@+id/ll_root"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_hall_of_fame_label"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="90dp"
            android:layout_marginEnd="@dimen/size_16"
            android:text="@string/hall_of_fame"
            android:textColor="@color/white"
            android:textSize="24sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/size_16"
            android:background="#1AFFFFFF" />

        <Spinner
            android:id="@+id/spinner_month"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginRight="@dimen/size_16"
            android:background="@android:color/transparent"
            android:overlapAnchor="false"
            android:popupBackground="@drawable/hall_of_fame_spinner_bg" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#1AFFFFFF" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/size_16"
            android:orientation="horizontal"
            android:paddingStart="@dimen/size_16"
            android:paddingEnd="@dimen/size_16"
            android:paddingBottom="8dp"
            android:weightSum="100">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="10" />

            <TextView
                android:id="@+id/tv_player_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="38"
                android:alpha="0.4"
                android:paddingStart="24dp"
                android:text="@string/lottery_player"
                android:textColor="@color/white" />

            <TextView
                android:id="@+id/tv_status_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="24"
                android:alpha="0.4"
                android:text="@string/hall_of_fame_loyalty_status"
                android:textColor="@color/white" />

            <TextView
                android:id="@+id/tv_prize_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="28"
                android:alpha="0.4"
                android:text="@string/lottery_prize"
                android:textAlignment="viewEnd"
                android:textColor="@color/white" />
        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_places"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:overScrollMode="never" />

        <include
            android:id="@+id/footer"
            layout="@layout/view_footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/not_organic_user_footer_padding_bottom" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
