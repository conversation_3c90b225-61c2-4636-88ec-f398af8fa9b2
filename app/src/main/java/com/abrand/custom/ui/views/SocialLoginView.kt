package com.abrand.custom.ui.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.core.view.children
import com.abrand.custom.data.entity.SocialNetwork
import com.abrand.custom.databinding.ViewSocialLoginBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

class SocialLoginView : FrameLayout {
    private var binding: ViewSocialLoginBinding? = null
    var listener: Listener? = null

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {
        binding = ViewSocialLoginBinding.inflate(LayoutInflater.from(context), this, true)
    }

    fun showLoader() {
        binding?.loader?.visibility = View.VISIBLE
    }

    fun hideLoader() {
        binding?.loader?.visibility = View.GONE
    }

    fun createAvailableSocialNetworksButtons(socialNetworks: List<String>?) {
        binding?.let { binding ->
            binding.llSocialNetworks.removeAllViews()
            socialNetworks?.forEachIndexed { index, networkValue ->
                SocialNetwork.from(networkValue)?.let { socialNetwork ->
                    val socialNetworkView = SocialNetworkView(context)
                    socialNetworkView.setSocialNetwork(socialNetwork)
                    socialNetworkView.listener = object : SocialNetworkView.Listener {
                        override fun onIconClicked() {
                            listener?.onSocialButtonClicked(socialNetwork)
                        }
                    }
                    binding.llSocialNetworks.addView(socialNetworkView)
                }
            }
        }
    }

    fun showSocialNetworkLoader(socialNetwork: SocialNetwork) {
        binding?.let {
            for (childView in it.llSocialNetworks.children) {
                if (childView is SocialNetworkView) {
                    if (childView.getSocialNetwork() == socialNetwork) {
                        childView.showLoader()
                    }
                }
            }
        }
    }

    fun hideSocialNetworkLoader(socialNetwork: SocialNetwork) {
        binding?.let {
            CoroutineScope(Dispatchers.Main.immediate + SupervisorJob()).launch {
                for (childView in it.llSocialNetworks.children) {
                    if (childView is SocialNetworkView) {
                        if (childView.getSocialNetwork() == socialNetwork) {
                            childView.hideLoader()
                        }
                    }
                }
            }
        }
    }

    interface Listener {
        fun onSocialButtonClicked(socialNetwork: SocialNetwork)
    }
}
