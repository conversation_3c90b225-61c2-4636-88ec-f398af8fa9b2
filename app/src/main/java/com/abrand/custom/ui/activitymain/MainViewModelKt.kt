package com.abrand.custom.ui.activitymain

import android.os.CountDownTimer
import android.text.TextUtils
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.*
import com.abrand.custom.data.Resource
import com.abrand.custom.data.Resource.Companion.error
import com.abrand.custom.data.Resource.Companion.failure
import com.abrand.custom.data.Resource.Companion.success
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.*
import com.abrand.custom.data.repositories.SupportPhoneRepository
import com.abrand.custom.fragment.GameThumb
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.interfaces.GetRegistrationBannerTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.network.ProfileRepository
import com.abrand.custom.network.retrySubscription
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.DateTools
import com.abrand.custom.tools.SynchronizedLiveData
import com.apollographql.apollo3.exception.ApolloException
import com.apollographql.apollo3.exception.ApolloNetworkException
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.*

class MainViewModelKt : ViewModel() {
    private var profileRepository: ProfileRepository = ProfileRepository()
    private val getBonusesBalancesUseCase: GetBonusBalancesCountUseCase = GetBonusBalancesCountUseCase()
    private val initialViewerDataQueryLiveData: LiveData<GetInitialViewerDataQuery.Viewer> =
        profileRepository.initialViewerDataLiveData
    private val balanceLiveData: MutableLiveData<SubscribeBalanceSubscription.Data?> =
        MutableLiveData()
    private val loyaltyPointsLiveData = MutableLiveData<SubscribeLoyaltyPointsSubscription.Data?>()
    private val loyaltyProgressLiveData =
        MutableLiveData<SubscribeLoyaltyProgressSubscription.Data?>()
    private val loyaltyStatusLiveData = MutableLiveData<SubscribeLoyaltyStatusSubscription.Data?>()
    private val loyaltyXOnPointsLiveData =
        MutableLiveData<SubscribeLoyaltyXOnPointsSubscription.Data?>()
    private val realTimeNotificationLiveData =
        SynchronizedLiveData<SubscribeRealTimeNotificationSubscription.Data?>()
    private val messagesLiveData = SynchronizedLiveData<SubscribeMessagesSubscription.Data?>()
    private val onlineUsersLiveData = MutableLiveData<OnlineUsersSubscription.Data?>()
    private val tournamentLiveData =
        MutableLiveData<Resource<TournamentsItem?, ServerError?, ApolloException?>>()
    private var isRebilling = false
    private val fastPaymentRebillingLiveData = MutableLiveData<Boolean>()
    private val socialAuthLiveData = MutableLiveData<Resource<AuthResponse, ServerError, ApolloException>>()
    private val _apiAccessLiveData = MutableLiveData<Boolean>()
    val apiAccessLiveData: LiveData<Boolean> = _apiAccessLiveData
    private val _supportPhoneLiveData = MutableLiveData<SupportPhone?>()
    val supportPhoneLiveData: LiveData<SupportPhone?> = _supportPhoneLiveData
    private val _unvisitedCountLiveData = MutableLiveData<Resource<UnvisitedCount?, ServerError, ApolloException>>()
    val unvisitedCountLiveData: LiveData<Resource<UnvisitedCount?, ServerError, ApolloException>> = _unvisitedCountLiveData
    private val _promotionsCountLiveData = MutableLiveData<Int?>()
    val promotionsCountLiveData: LiveData<Int?> = _promotionsCountLiveData
    private val _tournamentsCountLiveData = MutableLiveData<Int?>()
    val tournamentsCountLiveData: LiveData<Int?> = _tournamentsCountLiveData
    private val _lotteriesCountLiveData = MutableLiveData<Int?>()
    val lotteriesCountLiveData: LiveData<Int?> = _lotteriesCountLiveData

    private val _xOnPointsLiveData = MutableLiveData<XOnPointsEvent>()
    val xOnPointsLiveData: LiveData<XOnPointsEvent> = _xOnPointsLiveData
    private val _bonusRefundLiveData = MutableLiveData<Resource<BonusRefund, ServerError, ApolloException>>()
    val bonusRefundLiveData : LiveData<Resource<BonusRefund, ServerError, ApolloException>> = _bonusRefundLiveData
    private val _bonusResetBalanceLiveData = MutableLiveData<Resource<Boolean, ServerError, ApolloException>>()
    val bonusResetBalanceLiveData : LiveData<Resource<Boolean, ServerError, ApolloException>> = _bonusResetBalanceLiveData

    private val _balanceBonusesCount = MutableStateFlow(0)
    val balanceBonusesCount = _balanceBonusesCount.asStateFlow()

    private val exceptionHandler = CoroutineExceptionHandler { _, e -> Log.e(TAG, e.toString()) }
    private val dispatcher = Dispatchers.Default + exceptionHandler

    private var balanceSubscriptionJob: Job? = null
    private var loyaltyPointsSubscriptionJob: Job? = null
    private var loyaltyProgressSubscriptionJob: Job? = null
    private var loyaltyStatusSubscriptionJob: Job? = null
    private var loyaltyXOnPointsSubscriptionJob: Job? = null
    private var realTimeMessagesSubscriptionJob: Job? = null
    private var onlineUsersSubscriptionJob: Job? = null
    private var messagesSubscriptionJob: Job? = null
    private var promotionsCountSubscriptionJob: Job? = null
    private var tournamentsCountSubscriptionJob: Job? = null
    private var lotteriesCountSubscriptionJob: Job? = null
    private var xOnPointsTimer: CountDownTimer? = null

    init {
        _supportPhoneLiveData.postValue(null)
        viewModelScope.launch(dispatcher) {
            val answer = SupportPhoneRepository.getSupportPhone()
            _supportPhoneLiveData.postValue(answer)
        }
        updateBonusBalancesCount()
    }

    fun balanceSubscribe() {
        balanceSubscriptionJob?.cancel()

        if (User.State.PLAYER != Settings.get().userState) {
            return
        }

        balanceSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeBalance().retrySubscription().collect {
                    FirebaseCrashlytics.getInstance().log("balanceSubscribe: ${it.data.toString()}")

                    val bonusBetSum = it.data?.viewerWallet?.bonusBetSum.toString().toDouble()
                    val bonusRefundSum = it.data?.viewerWallet?.bonusRefundSum.toString().toDouble()
                    val bonusPercentToRefund = it.data?.viewerWallet?.bonusPercentToRefund.toString().toDouble()
                    val bonusRefund = BonusRefund(bonusBetSum, bonusRefundSum, bonusPercentToRefund)
                    _bonusRefundLiveData.postValue(success(bonusRefund))

                    updateFastClickPaymentSystemIfReplenishment(it.data)
                    balanceLiveData.postValue(it.data)
                    if (isRebilling) {
                        isRebilling = false
                        fastPaymentRebillingLiveData.postValue(isRebilling)
                    }
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "$TAG balanceSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun updateBonusBalancesCount() {
        viewModelScope.launch {
            _balanceBonusesCount.emit(getBonusesBalancesUseCase())
        }
    }

    private fun updateFastClickPaymentSystemIfReplenishment(data: SubscribeBalanceSubscription.Data?) {
        if (TextUtils.isEmpty(data?.viewerWallet?.balance.toString())) {
            val subscriptionBalance = data?.viewerWallet?.balance.toString().toDouble()
            val currentBalance: Double? = getBalance()

            if ((currentBalance != null && subscriptionBalance > currentBalance)
                || (currentBalance == null && subscriptionBalance > 0)) {
                getFastClickPaymentSystem()
            }
        }
    }

    private fun getBalance(): Double? {
        return getSubscriptionBalance() ?: getViewerBalance()
    }

    private fun getSubscriptionBalance(): Double? {
        val subscriptionBalanceData = balanceLiveData.value
        return if (subscriptionBalanceData?.viewerWallet?.balance != null) {
            subscriptionBalanceData.viewerWallet.balance.toString().toDouble()
        } else {
            null
        }
    }

    /**
     *
     * @return return balance from last request, but if subscription balance is not null then here is actual balance
     */
    private fun getViewerBalance(): Double? {
        val viewer: GetInitialViewerDataQuery.Viewer? = initialViewerDataQueryLiveData.value
        return if (viewer?.wallet?.balance != null) {
            viewer.wallet.balance.toString().toDouble()
        } else {
            null
        }
    }

    fun getFastClickPaymentSystem() {
        profileRepository.getFastClickPaymentSystem()
    }

    fun getBalanceLiveData(): MutableLiveData<SubscribeBalanceSubscription.Data?> {
        return balanceLiveData
    }

    fun getLoyaltyPointsLiveData(): MutableLiveData<SubscribeLoyaltyPointsSubscription.Data?> {
        return loyaltyPointsLiveData
    }

    fun getLoyaltyProgressLiveData(): MutableLiveData<SubscribeLoyaltyProgressSubscription.Data?> {
        return loyaltyProgressLiveData
    }

    fun getLoyaltyStatusLiveData(): MutableLiveData<SubscribeLoyaltyStatusSubscription.Data?> {
        return loyaltyStatusLiveData
    }

    fun getLoyaltyXOnPointsLiveData(): MutableLiveData<SubscribeLoyaltyXOnPointsSubscription.Data?> {
        return loyaltyXOnPointsLiveData
    }

    fun getRealTimeNotificationLiveData():
            MutableLiveData<SubscribeRealTimeNotificationSubscription.Data?> {
        return realTimeNotificationLiveData
    }

    fun consumeRealTimeNotification() {
        realTimeNotificationLiveData.postValue(null)
    }

    fun getMessagesLiveData(): SynchronizedLiveData<SubscribeMessagesSubscription.Data?> {
        return messagesLiveData
    }

    fun getTournamentLiveData(): MutableLiveData<Resource<TournamentsItem?, ServerError?, ApolloException?>> {
        return tournamentLiveData
    }

    fun resetBalanceLiveData() {
        balanceLiveData.postValue(null)
    }

    fun loyaltyPointsSubscribe() {
        val userState = Settings.get().userState

        loyaltyPointsSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        loyaltyPointsSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeLoyaltyPoints().retrySubscription().collect {
                    loyaltyPointsLiveData.postValue(it.data)
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "loyaltyPointsSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun loyaltyProgressSubscribe() {
        val userState = Settings.get().userState

        loyaltyProgressSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        loyaltyProgressSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeLoyaltyProgress().retrySubscription().collect {
                    loyaltyProgressLiveData.postValue(it.data)
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "loyaltyProgressSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun loyaltyStatusSubscribe() {
        val userState = Settings.get().userState

        loyaltyStatusSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        loyaltyStatusSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeLoyaltyStatus().retrySubscription().collect {
                    loyaltyStatusLiveData.postValue(it.data)
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "loyaltyStatusSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun loyaltyXOnPointsSubscribe() {
        val userState = Settings.get().userState

        loyaltyXOnPointsSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        loyaltyXOnPointsSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeLoyaltyXOnPoints().retrySubscription().collect {
                    loyaltyXOnPointsLiveData.postValue(it.data)
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "loyaltyXOnPointsSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun realTimeMessagesSubscribe() {
        val userState = Settings.get().userState

        realTimeMessagesSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        realTimeMessagesSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeRealtimeMessages().retrySubscription().collect {
                    realTimeNotificationLiveData.postValue(it.data)
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "realTimeMessagesSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun onlineUsersSubscribe() {
        val userState = Settings.get().userState

        onlineUsersSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        onlineUsersSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeOnlineUsers().retrySubscription().collect {
                    onlineUsersLiveData.postValue(it.data)
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "onlineUsersSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun messagesSubscribe() {
        val userState = Settings.get().userState

        messagesSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        messagesSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeMessages().retrySubscription().collect {
                    messagesLiveData.postValue(it.data)
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "$TAG messagesSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun cancelAllSubscriptions() {
        balanceSubscriptionJob?.cancel()
        loyaltyPointsSubscriptionJob?.cancel()
        loyaltyProgressSubscriptionJob?.cancel()
        loyaltyStatusSubscriptionJob?.cancel()
        loyaltyXOnPointsSubscriptionJob?.cancel()
        realTimeMessagesSubscriptionJob?.cancel()
        onlineUsersSubscriptionJob?.cancel()
        messagesSubscriptionJob?.cancel()
        promotionsCountSubscriptionJob?.cancel()
        tournamentsCountSubscriptionJob?.cancel()
        lotteriesCountSubscriptionJob?.cancel()
    }

    fun getGameByUrl(gameUrl: String?, target: GenericTarget<GameThumb?>) {
        ApolloProcessorKt.getGameByUrl(gameUrl, target, viewModelScope)
    }

    fun getTournament(tournamentId: Int) {
        ApolloProcessorKt.loadTournamentById(tournamentId, object : GenericTarget<TournamentsItem> {
            override fun onSuccess(t: TournamentsItem?) {
                tournamentLiveData.postValue(success(t))
            }

            override fun onError(
                errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                tournamentLiveData.postValue(error(ServerError(errorMessage, errorCode,
                    fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                tournamentLiveData.postValue(failure(e))
            }
        }, viewModelScope)
    }

    fun getDrawerRegistrationBanner(target: GetRegistrationBannerTarget) {
        ApolloProcessorKt.getDrawerRegistrationBanner(target, viewModelScope)
    }

    fun getSocialAuthLiveData(
    ): MutableLiveData<Resource<AuthResponse, ServerError, ApolloException>> {
        return socialAuthLiveData
    }

    fun socialLogin(token: String, language: String) {
        ApolloProcessorKt.socialLogin(token, language, object : GenericTarget<AuthResponse> {
            override fun onSuccess(authResponse: AuthResponse?) {
                socialAuthLiveData.postValue(success(authResponse))
            }

            override fun onError(
                errorMessage: String,
                errorCode: String,
                fieldsErrors: FieldsErrorsHolder?
            ) {
                socialAuthLiveData.postValue(
                    error(ServerError(errorMessage, errorCode, fieldsErrors))
                )
            }

            override fun onFailure(e: ApolloException) {
                socialAuthLiveData.postValue(failure(e))
            }
        }, viewModelScope)
    }

    fun checkApiAccess() {
        ApolloProcessorKt.hello(object : GenericTarget<String?> {
            override fun onSuccess(s: String?) {
                _apiAccessLiveData.postValue(true)
            }

            override fun onError(
                errorMessage: String,
                errorCode: String,
                fieldsErrors: FieldsErrorsHolder?
            ) {
                _apiAccessLiveData.postValue(false)
            }

            override fun onFailure(e: ApolloException) {
                _apiAccessLiveData.postValue(false)
            }
        })
    }

    fun setXOnPointsTimeToEnd(endDate: Date?) {
        if (endDate != null) {
            val endTime: Long = endDate.time
            val currentTime = Calendar.getInstance().timeInMillis
            xOnPointsTimer?.cancel()
            xOnPointsTimer = object : CountDownTimer(endTime - currentTime, DateTools.MILLIS_IN_SECOND.toLong()) {
                override fun onTick(millisUntilFinished: Long) {
                    _xOnPointsLiveData.postValue(XOnPointsEvent(DateTools.formatTime(millisUntilFinished), false))
                }

                override fun onFinish() {
                    _xOnPointsLiveData.postValue(XOnPointsEvent("", true))
                }
            }.start()
        }
    }

    fun getUnvisitedCount() {
        val userState = Settings.get().userState
        if (userState == User.State.PLAYER) {
            ApolloProcessorKt.getUnvisitedCount(object : GenericTarget<UnvisitedCount> {
                override fun onSuccess(t: UnvisitedCount?) {
                    _unvisitedCountLiveData.postValue(success(t))
                }

                override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                    _unvisitedCountLiveData.postValue(error(ServerError(errorMessage, errorCode, fieldsErrors)))
                }

                override fun onFailure(e: ApolloException) {
                    _unvisitedCountLiveData.postValue(failure(e))
                }
            })
        }
    }

    fun markPromotionsVisited() {
        ApolloProcessorKt.markBonusesVisited(object : GenericTarget<Boolean> {
            override fun onSuccess(t: Boolean?) {
                //do nothing
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                //do nothing
            }

            override fun onFailure(e: ApolloException) {
                //do nothing
            }
        })
    }

    fun markTournamentsVisited() {
        ApolloProcessorKt.markTournamentsVisited(object : GenericTarget<Boolean> {
            override fun onSuccess(t: Boolean?) {
                //do nothing
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                //do nothing
            }

            override fun onFailure(e: ApolloException) {
                //do nothing
            }
        })
    }

    fun markLotteriesVisited() {
        ApolloProcessorKt.markLotteriesVisited(object : GenericTarget<Boolean> {
            override fun onSuccess(t: Boolean?) {
                //do nothing
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                //do nothing
            }

            override fun onFailure(e: ApolloException) {
                //do nothing
            }
        })
    }

    fun promotionsCountSubscribe() {
        val userState = Settings.get().userState
        promotionsCountSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        promotionsCountSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribePromotionsCount().retrySubscription().collect {
                    _promotionsCountLiveData.postValue(it.data?.viewerBonusesCount)
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "promotionsCountSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun tournamentsCountSubscribe() {
        val userState = Settings.get().userState
        tournamentsCountSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        tournamentsCountSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeTournamentsCount().retrySubscription().collect {
                    _tournamentsCountLiveData.postValue(it.data?.tournamentsCountChange)
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "tournamentsCountSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun lotteriesCountSubscribe() {
        val userState = Settings.get().userState
        lotteriesCountSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        lotteriesCountSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeLotteriesCount().retrySubscription().collect {
                    _lotteriesCountLiveData.postValue(it.data?.lotteriesCountChange)
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()

                crashlytics.setCustomKey("subscription", "lotteriesCountSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun getBonusRefund() {
        ApolloProcessorKt.getBonusRefund(object : GenericTarget<BonusRefund> {
            override fun onSuccess(t: BonusRefund?) {
                _bonusRefundLiveData.postValue(success(t))
            }

            override fun onFailure(e: ApolloException) {
                _bonusRefundLiveData.postValue(failure(e))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _bonusRefundLiveData.postValue(error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }
        })
    }

    fun resetBonusBalance() {
        ApolloProcessorKt.resetBonusBalance(object : GenericTarget<Boolean> {
            override fun onSuccess(t: Boolean?) {
                _bonusResetBalanceLiveData.postValue(success(t))
            }

            override fun onError(errorMessage: String, errorCode: String,
                                 fieldsErrors: FieldsErrorsHolder?) {
                _bonusResetBalanceLiveData.postValue(error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _bonusResetBalanceLiveData.postValue(failure(e))
            }
        })
    }

    companion object {
        private const val TAG = "MainViewModelKt"
    }
}
