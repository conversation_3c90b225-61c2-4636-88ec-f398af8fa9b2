<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/nav_home">

    <fragment
        android:id="@+id/nav_home"
        android:name="com.abrand.custom.ui.home.HomeFragment"
        android:label="@string/menu_home"
        tools:layout="@layout/fragment_home">

        <action
            android:id="@+id/action_nav_home_to_pregame"
            app:destination="@id/nav_pregameFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:popExitAnim="@anim/nav_default_exit_anim" />

        <action
            android:id="@+id/action_nav_home_to_searchGameFragment"
            app:destination="@+id/nav_game_search"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>
    <fragment
        android:id="@+id/nav_profile"
        android:name="com.abrand.custom.ui.profile.ProfileFragment"
        android:label="ProfileFragment"
        tools:layout="@layout/fragment_profile"/>
    <fragment
        android:id="@+id/nav_payments"
        android:name="com.abrand.custom.ui.payments.PaymentsFragment"
        android:label="PayemntsFragment"
        tools:layout="@layout/fragment_payments"/>
    <fragment
        android:id="@+id/nav_terms"
        android:name="com.abrand.custom.ui.terms.TermsFragment"
        android:label="TermsFragment"
        tools:layout="@layout/fragment_terms"/>
    <fragment
        android:id="@+id/nav_pregameFragment"
        android:name="com.abrand.custom.ui.pregame.PregameFragment"
        android:label="PregameFragment"
        tools:layout="@layout/fragment_pregame" />

    <fragment
        android:id="@+id/nav_game"
        android:name="com.abrand.custom.ui.game.GameFragment"
        android:label="ActivityGame"
        tools:layout="@layout/fragment_game">
        <action
            android:id="@+id/action_nav_game_to_nav_pregameFragment"
            app:destination="@id/nav_pregameFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:popExitAnim="@anim/nav_default_exit_anim"/>
    </fragment>

    <fragment
        android:id="@+id/nav_enter"
        android:name="com.abrand.custom.ui.enter.EnterFragment"
        android:label="EnterFragent"
        tools:layout="@layout/fragment_enter">
        <argument
            android:name="is_login"
            app:argType="boolean"
            android:defaultValue="true" />

        <action
            android:id="@+id/action_enter_to_pregame"
            app:destination="@id/nav_pregameFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:popExitAnim="@anim/nav_default_exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/nav_recovery"
        android:name="com.abrand.custom.ui.recovery.RecoveryFragment"
        android:label="RecoveryFragment" />

    <fragment
        android:id="@+id/nav_game_search"
        android:name="com.abrand.custom.ui.search.GameSearchFragment"
        android:label="fragment_game_search"
        tools:layout="@layout/fragment_game_search" >

        <action
            android:id="@+id/action_nav_game_search_to_pregame"
            app:destination="@id/nav_pregameFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:popExitAnim="@anim/nav_default_exit_anim" />
    </fragment>

    <fragment
        android:id="@+id/nav_promotions"
        android:name="com.abrand.custom.ui.promotions.PromotionsFragment"
        android:label="fragment_promotions"
        tools:layout="@layout/fragment_promotions" />

    <fragment
        android:id="@+id/nav_promotions_w_back_icon"
        android:name="com.abrand.custom.ui.promotions.PromotionsFragment"
        android:label="fragment_promotions"
        tools:layout="@layout/fragment_promotions" />

    <fragment
        android:id="@+id/nav_game_room"
        android:name="com.abrand.custom.ui.gameroom.GameRoomFragment"
        android:label="fragment_game_room"
        tools:layout="@layout/fragment_game_room" >

        <action
            android:id="@+id/action_nav_game_room_to_pregame"
            app:destination="@id/nav_pregameFragment"
            app:enterAnim="@anim/nav_default_enter_anim"
            app:popExitAnim="@anim/nav_default_exit_anim" />

    </fragment>

    <fragment
        android:id="@+id/nav_support_chat"
        android:name="com.abrand.custom.ui.supportChat.SupportChatFragment"
        android:label="SupportChatFragment" />

    <fragment
        android:id="@+id/nav_news"
        android:name="com.abrand.custom.ui.news.NewsFragment"
        android:label="fragment_news"
        tools:layout="@layout/fragment_news" />

    <fragment
        android:id="@+id/nav_news_internal"
        android:name="com.abrand.custom.ui.news.NewsInternalFragment"
        android:label="fragment_news_internal"
        tools:layout="@layout/fragment_news_internal" />

    <fragment
        android:id="@+id/nav_loyalty_program"
        android:name="com.abrand.custom.ui.loyaltyprogram.LoyaltyProgramFragment"
        android:label="fragment_loyalty_program"
        tools:layout="@layout/fragment_loyalty_program" />

    <fragment
        android:id="@+id/nav_exchange_points"
        android:name="com.abrand.custom.ui.exchangepoints.ExchangePointsFragment"
        android:label="fragment_exchange_points"
        tools:layout="@layout/fragment_exchange_points" />

    <fragment
        android:id="@+id/nav_no_connection"
        android:name="com.abrand.custom.ui.noconnection.NoConnectionFragment"
        android:label="fragment_no_connection"
        tools:layout="@layout/fragment_no_connection" />

    <fragment
        android:id="@+id/nav_new_password"
        android:name="com.abrand.custom.ui.newpassword.NewPasswordFragment"
        android:label="fragment_new_password"
        tools:layout="@layout/fragment_new_password" />
    <fragment
        android:id="@+id/nav_tournaments"
        android:name="com.abrand.custom.ui.tournaments.TournamentsFragment"
        android:label="TournamentsFragment"
        tools:layout="@layout/fragment_tournaments" >
        <action
            android:id="@+id/action_nav_tournaments_to_tournamentInternalFragment"
            app:destination="@id/nav_tournament_internal" />
    </fragment>
    <fragment
        android:id="@+id/nav_tournament_internal"
        android:name="com.abrand.custom.ui.tournamentinternal.TournamentInternalFragment"
        android:label="fragment_tournament_internal"
        tools:layout="@layout/fragment_tournament_internal" >

    <action
        android:id="@+id/action_tournamentInternal_to_pregame"
        app:destination="@id/nav_pregameFragment"
        app:enterAnim="@anim/nav_default_enter_anim"
        app:popExitAnim="@anim/nav_default_exit_anim" />

        </fragment>

    <fragment
        android:id="@+id/nav_biometric_input_password"
        android:name="com.abrand.custom.ui.biometric.BiometricInputPasswordFragment"
        android:label="fragment_biometric_input_password"
        tools:layout="@layout/fragment_biometric_input_password" />

    <fragment
        android:id="@+id/nav_messages"
        android:name="com.abrand.custom.ui.messages.MessagesFragment"
        android:label="messages_fragment"
        tools:layout="@layout/fragment_messages" />

    <fragment
        android:id="@+id/nav_new_loyalty_status"
        android:name="com.abrand.custom.ui.loyaltyprogram.NewLoyaltyStatusFragment"
        android:label="fragment_new_loyalty_status"
        tools:layout="@layout/fragment_new_loyalty_status" />

    <fragment
        android:id="@+id/nav_wheel_of_fortune"
        android:name="com.abrand.custom.ui.wheeloffortune.WheelOfFortuneFragment"
        android:label="fragment_wheel_of_fortune"
        tools:layout="@layout/fragment_wheel_of_fortune" />

    <fragment
        android:id="@+id/nav_cashback"
        android:name="com.abrand.custom.ui.cashback.CashbackFragment"
        android:label="fragment_cash_back"
        tools:layout="@layout/fragment_cashback" />

    <fragment
        android:id="@+id/nav_lottery_list"
        android:name="com.abrand.custom.ui.lotteries.LotteriesFragment"
        android:label="fragment_lottery"
        tools:layout="@layout/fragment_lottery_list" />

    <fragment
        android:id="@+id/nav_lottery_internal"
        android:name="com.abrand.custom.ui.lotteryinternal.LotteryInternalFragment"
        android:label="fragment_lottery_internal"
        tools:layout="@layout/fragment_lottery_internal" />

    <fragment
        android:id="@+id/nav_hall_of_fame"
        android:name="com.abrand.custom.ui.halloffame.HallOfFameFragment"
        android:label="fragment_hall_of_fame"
        tools:layout="@layout/fragment_hall_of_fame" />

    <fragment
        android:id="@+id/nav_shop"
        android:name="com.abrand.custom.ui.shop.ShopFragment"
        android:label="fragment_shop"
        tools:layout="@layout/fragment_shop" />

</navigation>
