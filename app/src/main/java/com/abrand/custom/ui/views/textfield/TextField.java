package com.abrand.custom.ui.views.textfield;

import android.animation.LayoutTransition;
import android.content.Context;
import android.content.res.TypedArray;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.PasswordTransformationMethod;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.abrand.custom.R;
import com.abrand.custom.presenter.Fields;
import com.abrand.custom.presenter.FieldsErrorsHolder;
import com.abrand.custom.tools.GeneralTools;

import java.util.ArrayList;
import java.util.List;

public class TextField extends FrameLayout {

    protected ViewGroup rootView = null;
    private ViewGroup rootInput = null;
    protected AppCompatEditText editText = null;
    private TextView textInfo = null;
    private TextView hintView = null;
    private TextView errorMsg = null;
    private ImageView passwordVisibilityView = null;

    private boolean hidePassword = false;
    private int inputType = InputType.TYPE_NULL;
    private List<UpdateListener> subscribers = new ArrayList<>();
    final private List<UpdateListener> afterTextChangedSubscribers = new ArrayList<>();
    private FocusChangeListener focusChangeListener;
    private Validator validator = new Validator();
    private int HINT_NOT_EDITABLE_FIELD_COLOR;
    private int HINT_EDITABLE_FIELD_COLOR;
    protected int TEXT_NOT_EDITABLE_COLOR;
    private boolean isFieldEditable = true;
    private boolean hideKeyboardOnFocusLost = false;
    private float hintTextSize = 14;

    public TextField(Context context) {
        super(context);
        init(context, null, -1);
    }

    public TextField(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, -1);
    }

    public TextField(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    public TextField(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        HINT_NOT_EDITABLE_FIELD_COLOR = ContextCompat.getColor(context, R.color.hint_not_editable_field);
        HINT_EDITABLE_FIELD_COLOR = ContextCompat.getColor(context, R.color.hint_editable_field);
        TEXT_NOT_EDITABLE_COLOR = ContextCompat.getColor(context, R.color.text_not_editable_field);

        setBackgroundResource(R.drawable.bg_text_field);
        rootView = inflate(context, getLayoutId(), this).findViewById(R.id.root);
        rootView.getLayoutTransition().enableTransitionType(LayoutTransition.CHANGING);
        String hintStr = null;
        rootInput = rootView.findViewById(R.id.root_input);
        editText = rootView.findViewById(R.id.text);
        textInfo = rootView.findViewById(R.id.text_info);
        hintView = rootView.findViewById(R.id.hint);
        errorMsg = rootView.findViewById(R.id.error);
        passwordVisibilityView = rootView.findViewById(R.id.iv_visibility);
        if ( passwordVisibilityView != null ) {
            passwordVisibilityView.setOnClickListener((v) -> setHidePassword(!hidePassword));
        }

        if ( defStyleAttr != -1 ) {
            TypedArray attrHint = context.obtainStyledAttributes(attrs, R.styleable.TextField, defStyleAttr, 0);
            hintStr = attrHint.getString(R.styleable.TextField_hint);
            hintTextSize = attrHint.getFloat(R.styleable.TextField_hintTextSize, 14);
        } else if ( attrs != null ) {
            TypedArray attrHint = context.obtainStyledAttributes(attrs, R.styleable.TextField);
            hintStr = attrHint.getString(R.styleable.TextField_hint);
            hintTextSize = attrHint.getFloat(R.styleable.TextField_hintTextSize, 14);
        }
        if ( hintStr != null ) {
            hintView.setText(hintStr);
        }
        hintView.setTextSize(TypedValue.COMPLEX_UNIT_SP, hintTextSize);

        setUpFocusManagement(context);
        setUpTextChangeListener();
    }

    protected void setUpFocusManagement(Context context) {
        rootView.setOnClickListener(view -> {
            editText.setVisibility(View.VISIBLE);
            if ( editText.requestFocus() ) {
                InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
                if ( imm != null ) {
                    imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
                }
            }
            if ( !isFieldEditable ) {
                setHintOnTopConfiguration();
            }
        });
        editText.setOnFocusChangeListener((view, isFocused) -> {
            if ( isFocused ) {
                setHintOnTopConfiguration();
            } else if ( editText.getText() == null
                    || TextUtils.isEmpty(editText.getText().toString()) ) {
                setViewDefaultState();
            } else if (hideKeyboardOnFocusLost) {
                GeneralTools.hideKeyboard(context, editText);
            }

            if ( focusChangeListener != null ) {
                focusChangeListener.onFocusChangeListener(isFocused);
            }
        });
    }

    private void setHintOnTopConfiguration() {
        hintView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f);
        if (hintView.getLayoutParams() instanceof ConstraintLayout.LayoutParams){
            ((ConstraintLayout.LayoutParams) hintView.getLayoutParams()).verticalBias = 0f;
        }
        if ( isFieldEditable ) {
            hintView.setTextColor(HINT_EDITABLE_FIELD_COLOR);
        } else {
            hintView.setTextColor(HINT_NOT_EDITABLE_FIELD_COLOR);
        }
    }

    private void setViewDefaultState() {
        editText.setVisibility(View.GONE);
        errorMsg.setVisibility(View.GONE);
        hintView.setTextSize(TypedValue.COMPLEX_UNIT_SP, hintTextSize);
        hintView.setTextColor(HINT_EDITABLE_FIELD_COLOR);
        if (hintView.getLayoutParams() instanceof ConstraintLayout.LayoutParams) {
            ((ConstraintLayout.LayoutParams) hintView.getLayoutParams()).verticalBias = 0.5f;
        }
        if (hideKeyboardOnFocusLost) {
            GeneralTools.hideKeyboard(TextField.this.getContext(), editText);
        }
    }

    private void setUpTextChangeListener() {
        editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                for ( int k = 0; k < subscribers.size(); k++ ) {
                    UpdateListener listener = subscribers.get(k);
                    String data = charSequence.toString().trim();
                    if ( listener != null ) {
                        listener.onUpdate(data, validator.validate(data, inputType));
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {
                for ( UpdateListener subscriber : afterTextChangedSubscribers ) {
                    if ( subscriber != null ) {
                        String newText = editable.toString();
                        subscriber.onUpdate(newText, validator.validate(newText, inputType));
                    }
                }
            }
        });
    }

    public TextField setHint(String hint) {
        hintView.setText(hint);
        return this;
    }

    public TextField setInputType(int inputType) {
        this.inputType = inputType;
        editText.setInputType(inputType);
        setHidePassword(isPassword());
        if ( passwordVisibilityView != null ) {
            if ( isPassword() ) {
                passwordVisibilityView.setVisibility(View.VISIBLE);
            } else {
                passwordVisibilityView.setVisibility(View.GONE);
            }
        }
        return this;
    }

    public void setImeOptions(int imeOptions) {
        editText.setImeOptions(imeOptions);
    }

    public void showErrorMessage(String message) {
        errorMsg.setVisibility(View.VISIBLE);
        errorMsg.setText(message);
        rootInput.setBackgroundResource(R.drawable.bg_text_field_input_error);
        setBackgroundResource(R.drawable.bg_text_field_error);
    }

    public void hideErrorMessage() {
        errorMsg.setVisibility(View.GONE);
        rootInput.setBackgroundResource(isFieldEditable ? R.drawable.bg_text_field : 0);
    }

    public void showErrorIfNeeded(@NonNull FieldsErrorsHolder holder, Fields field) {
        List<String> errors = holder.getErrorsMap().get(field);
        String       error  = null;

        if ( errors != null ) {
            error = errors.get(0);
        }

        if ( TextUtils.isEmpty(error) ) {
            hideErrorMessage();
        } else {
            showErrorMessage(error);
        }
    }

    @NonNull
    public String getText() {
        if ( editText.getText() != null ) {
            return editText.getText().toString();
        } else {
            return "";
        }
    }

    public void clear() {
        Editable editable = editText.getText();
        if (editable != null) {
            editable.clear();
        }
        setViewDefaultState();
    }

    public int getLayoutId() {
        return R.layout.view_text_field;
    }

    public void setText(String value) {
        editText.setText(value);
        if ( editText.getVisibility() != VISIBLE && value != null && !value.isEmpty() ) {
            editText.setVisibility(VISIBLE);
            setHintOnTopConfiguration();
        }
    }

    public void setTextKeepState(String value) {
        editText.setTextKeepState(value);
        if ( editText.getVisibility() != VISIBLE && value != null && !value.isEmpty() ) {
            editText.setVisibility(VISIBLE);
            setHintOnTopConfiguration();
        }
    }

    public void setFieldNotEditable() {
        isFieldEditable = false;
        rootInput.setBackgroundResource(0);
        setBackgroundResource(R.drawable.bg_text_field_not_editable);
        setEditTextNotEditable();
        hintView.setTextColor(HINT_NOT_EDITABLE_FIELD_COLOR);
        editText.setTextColor(TEXT_NOT_EDITABLE_COLOR);
        if ( textInfo != null ) {
            textInfo.setTextColor(TEXT_NOT_EDITABLE_COLOR);
        }
    }

    public void setHideKeyboardOnFocusLost(boolean hideKeyboardOnFocusLost) {
        this.hideKeyboardOnFocusLost = hideKeyboardOnFocusLost;
    }

    public boolean isFieldEditable() {
        return isFieldEditable;
    }

    public void setEditTextNotEditable() {
        editText.setFocusable(false);
        setInputType(InputType.TYPE_NULL);
        editText.setClickable(false);
        editText.setOnClickListener(v -> {
            rootView.performClick();
        });
    }

    public void setSelection(int index) {
        editText.setSelection(index);
    }

    public boolean isValid() {
        return errorMsg.getVisibility() == GONE;
    }

    public boolean isNotEmpty() {
        return editText.getText() != null && !TextUtils.isEmpty(editText.getText().toString());
    }

    public void setHidePassword(boolean isHide) {
        hidePassword = isHide;
        if ( isHide ) {
            editText.setTransformationMethod(new PasswordTransformationMethod());
            if ( passwordVisibilityView != null ) {
                passwordVisibilityView.setImageResource(R.drawable.ic_visibility_off);
            }
        } else {
            editText.setTransformationMethod(null);
            if ( passwordVisibilityView != null ) {
                passwordVisibilityView.setImageResource(R.drawable.ic_visibility);
            }
        }
        editText.setSelection(editText.getText().length());
    }

    public void setTextInfo(String value) {
        if ( textInfo != null ) {
            textInfo.setVisibility(VISIBLE);
            textInfo.setText(value);
        }
    }

    public void setMaxLength(int maxLength) {
        InputFilter[] editFilters = editText.getFilters();
        InputFilter[] newFilters  = new InputFilter[editFilters.length + 1];
        System.arraycopy(editFilters, 0, newFilters, 0, editFilters.length);
        newFilters[editFilters.length] = new InputFilter.LengthFilter(maxLength);
        editText.setFilters(newFilters);
    }

    public void subscribeToUpdates(UpdateListener listener) {
        subscribers.add(listener);
    }

    public boolean isSubscribed(UpdateListener listener) {
        return subscribers.contains(listener);
    }

    public void unsubscribeFromUpdates(UpdateListener listener) {
        subscribers.remove(listener);
    }

    public void subscribeToAfterTextChangedUpdates(UpdateListener listener) {
        afterTextChangedSubscribers.add(listener);
    }

    public boolean isSubscribedToAfterTextChanged(UpdateListener listener) {
        return afterTextChangedSubscribers.contains(listener);
    }

    public void unsubscribeFromAfterTextChangedUpdates(UpdateListener listener) {
        afterTextChangedSubscribers.remove(listener);
    }

    public void setOnFocusChangeListener(FocusChangeListener focusChangeListener) {
        this.focusChangeListener = focusChangeListener;
    }

    public interface UpdateListener {
        void onUpdate(String data, boolean valid);
    }

    public interface FocusChangeListener {
        void onFocusChangeListener(boolean isFocused);
    }

    private boolean isPassword() {
        return inputType == InputType.TYPE_NUMBER_VARIATION_PASSWORD ||
                inputType == InputType.TYPE_TEXT_VARIATION_PASSWORD ||
                inputType == InputType.TYPE_TEXT_VARIATION_WEB_PASSWORD;
    }
}
