package com.abrand.custom.ui.history

import androidx.lifecycle.ViewModel
import com.abrand.custom.data.Resource
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.HistoryFilter
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.data.entity.Transaction
import com.abrand.custom.data.mappers.toListPaymentCompoundOperation
import com.abrand.custom.data.mappers.toListTransactionStatus
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.interfaces.RetryPaymentUrlTarget
import com.abrand.custom.interfaces.TransactionTarget
import com.abrand.custom.interfaces.TransactionsTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException

class HistoryViewModel : ViewModel() {
    val transactionsLiveData = SingleLiveEvent<Resource<List<Transaction>, ServerError, ApolloException>>()
    val oneTransactionLiveData = SingleLiveEvent<Resource<Transaction, ServerError, ApolloException>>()
    val cancelPayoutLiveData = SingleLiveEvent<Resource<Int, ServerError, ApolloException>>()
    val retryPaymentUrlLiveData = SingleLiveEvent<Resource<String, ServerError, ApolloException>>()

    fun getTransactions(operations: List<HistoryFilter.Operation>? = null,
                        statuses: List<HistoryFilter.Status>? = null, dateFrom: String? = null,
                        dateTo: String? = null) {
        ApolloProcessorKt.getTransactions(operations?.toListPaymentCompoundOperation(),
            statuses?.toListTransactionStatus(), dateFrom, dateTo, object : TransactionsTarget {

                override fun onSuccess(transactions: List<Transaction>?) {
                    transactionsLiveData.postValue(Resource.success(transactions))
                }

                override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                    transactionsLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
                }

                override fun onFailure(e: ApolloException) {
                    transactionsLiveData.postValue(Resource.failure(e))
                }

                override fun onViewerNull() {
                    transactionsLiveData.postValue(Resource.viewerEmpty())
                }
        })
    }

    fun cancelPayout(transactionId: Int) {
        ApolloProcessorKt.cancelPayout(transactionId, object : GenericTarget<Boolean> {

            override fun onSuccess(t: Boolean?) {
                cancelPayoutLiveData.postValue(Resource.success(transactionId))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                cancelPayoutLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                cancelPayoutLiveData.postValue(Resource.failure(e))
            }

        })
    }

    fun getTransaction(transactionId: Int) {
        ApolloProcessorKt.getTransaction(transactionId, object : TransactionTarget {

            override fun onSuccess(transaction: Transaction) {
                oneTransactionLiveData.postValue(Resource.success(transaction))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                oneTransactionLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                oneTransactionLiveData.postValue(Resource.failure(e))
            }

            override fun onViewerNull() {
                oneTransactionLiveData.postValue(Resource.viewerEmpty())
            }

        })
    }

    fun onRetryPaymentUrl(transactionId: Int) {
        ApolloProcessorKt.getRetryPaymentUrl(transactionId, Settings.get().refCode, object : RetryPaymentUrlTarget {

            override fun onSuccess(url: String?) {
                retryPaymentUrlLiveData.postValue(Resource.success(url))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                retryPaymentUrlLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                retryPaymentUrlLiveData.postValue(Resource.failure(e))
            }

            override fun onViewerNull() {
                retryPaymentUrlLiveData.postValue(Resource.viewerEmpty())
            }

        })
    }
}
