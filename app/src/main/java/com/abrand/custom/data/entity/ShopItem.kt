package com.abrand.custom.data.entity

class ShopItem {
    var itemType: ItemType
    var product: LocalBonusProductForPoints? = null

    constructor(itemType: ItemType) {
        this.itemType = itemType
    }

    constructor(itemType: ItemType, product: LocalBonusProductForPoints) {
        this.itemType = itemType
        this.product = product
    }

    enum class ItemType(val id: Int) {
        HEADER(0),
        PRODUCT(1),
        FOOTER(2)
    }
}
