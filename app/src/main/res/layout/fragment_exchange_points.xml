<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nestedScrollView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient"
    tools:context=".ui.exchangepoints.ExchangePointsFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/containerScroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="78dp"
            android:paddingLeft="@dimen/size_16"
            android:text="@string/exchange_points_title"
            android:textColor="#FFFFFF"
            android:textFontWeight="300"
            android:textSize="24sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/containerEmailConfirmation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="26dp"
            android:background="#1AB8B8CC"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle">

            <ImageView
                android:id="@+id/ivEmailConfirmation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="12dp"
                android:layout_marginTop="24dp"
                android:src="@drawable/ic_email_confirmation_info"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvEmailConfirmation"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/size_16"
                android:paddingLeft="@dimen/size_16"
                android:paddingTop="@dimen/size_16"
                android:paddingEnd="@dimen/size_16"
                android:paddingRight="@dimen/size_16"
                android:text="@string/exchange_points_email_confirmation_info"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/ivEmailConfirmation"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvEmailChangeSupport"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:lineSpacingExtra="0dp"
                android:paddingStart="@dimen/size_16"
                android:paddingLeft="@dimen/size_16"
                android:paddingEnd="@dimen/size_16"
                android:paddingRight="@dimen/size_16"
                android:paddingBottom="32dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/ivEmailConfirmation"
                app:layout_constraintTop_toBottomOf="@+id/tvEmailConfirmation" />

            <Button
                android:id="@+id/btnRequestEmailConfirmation"
                style="@style/ButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="48dp"
                android:layout_marginTop="@dimen/size_16"
                android:layout_marginRight="48dp"
                android:layout_marginBottom="36dp"
                android:text="@string/confirm_email"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvEmailConfirmation" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:lineSpacingExtra="10dp"
            android:paddingLeft="@dimen/size_16"
            android:paddingRight="@dimen/size_16"
            android:text="@string/exchange_points_description"
            android:textColor="#8DA1BD"
            android:textFontWeight="400"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/containerEmailConfirmation" />

        <TextView
            android:id="@+id/tvBalancePoints"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="start"
            android:paddingLeft="@dimen/size_16"
            android:textColor="#FFFFFF"
            android:textSize="24sp"
            app:layout_constraintEnd_toStartOf="@+id/dividerBalanceExchangeRate"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvDescription"
            tools:text="5 000" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:paddingLeft="@dimen/size_16"
            android:text="@string/points_balance"
            android:textColor="#8DA1BD"
            android:textSize="12sp"
            app:layout_constraintTop_toBottomOf="@+id/tvBalancePoints" />

        <View
            android:id="@+id/dividerBalanceExchangeRate"
            android:layout_width="@dimen/size_16"
            android:layout_height="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvBalancePoints" />

        <TextView
            android:id="@+id/tvExchangeRate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFF"
            android:textSize="24sp"
            app:layout_constraintStart_toEndOf="@+id/dividerBalanceExchangeRate"
            app:layout_constraintTop_toTopOf="@+id/tvBalancePoints"
            tools:text="5:1" />

        <TextView
            android:id="@+id/tvExchangeRateStatusTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/exchange_rate_status_title"
            android:textColor="#8DA1BD"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@+id/tvExchangeRate"
            app:layout_constraintTop_toBottomOf="@+id/tvExchangeRate" />

        <TextView
            android:id="@+id/tvExchangeRateStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@+id/tvExchangeRate"
            app:layout_constraintTop_toBottomOf="@+id/tvExchangeRateStatusTitle"
            tools:text="Мичман" />

        <com.abrand.custom.ui.views.textfield.TextField
            android:id="@+id/etPoints"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginTop="26dp"
            android:layout_marginEnd="@dimen/size_16"
            android:layout_marginRight="@dimen/size_16"
            app:hint="@string/points_hint"
            app:layout_constraintEnd_toStartOf="@+id/ivExchangePointsArrow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvExchangeRateStatus" />

        <ImageView
            android:id="@+id/ivExchangePointsArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_exchange_points_arrow"
            app:layout_constraintBottom_toBottomOf="@+id/etPoints"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/etPoints" />

        <TextView
            android:id="@+id/tvMoney"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginEnd="@dimen/size_16"
            android:layout_marginRight="@dimen/size_16"
            android:textColor="#FFFFFF"
            android:textSize="24sp"
            app:layout_constraintBottom_toBottomOf="@+id/etPoints"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivExchangePointsArrow"
            app:layout_constraintTop_toTopOf="@+id/etPoints" />

        <Button
            android:id="@+id/btnExchange"
            style="@style/ButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginTop="@dimen/size_16"
            android:layout_marginRight="@dimen/size_16"
            android:text="@string/exchange"
            app:layout_constraintTop_toBottomOf="@+id/etPoints" />

        <View
            android:id="@+id/bottom_space"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/size_16"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btnExchange" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>