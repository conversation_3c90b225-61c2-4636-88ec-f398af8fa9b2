package com.abrand.custom.ui.history

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.entity.Transaction
import com.abrand.custom.databinding.ItemChildTransactionBinding
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.setHtmlText
import com.abrand.custom.type.TransactionStatus

class ChildTransactionAdapter(private val context: Context,
                              private var children: List<Transaction.LocalBaseTransaction>,
                              private var listener: Listener) :
        RecyclerView.Adapter<ChildTransactionAdapter.ChildViewHolder>() {
    private val TAG = "ChildTransactionAdapter"

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChildViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        val view = ItemChildTransactionBinding.inflate(inflater, parent, false)
        return ChildViewHolder(view)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON>wHolder, position: Int) {
        val child = children[position]
        val amount: Double = child.amount.toString().toDouble()
        holder.view.tvAmount.text = GeneralTools.formatBalance(child.currency?.code, amount,
            2, true)
        holder.view.tvId.text = context.getString(R.string.transaction_id, child.id)

        initBtnQuestion(holder, child)
        setTransactionStatus(holder, child.status)
        setUserComment(holder, child)
    }

    override fun getItemCount() = children.size

    private fun initBtnQuestion(holder: ChildViewHolder, baseTransaction: Transaction.LocalBaseTransaction) {
        holder.view.btnQuestion.setOnClickListener {
            listener.onOpenSupportChat(baseTransaction)
        }
    }

    private fun setTransactionStatus(holder: ChildViewHolder, transactionStatus: TransactionStatus?) {
        val statusText = getStatusText(transactionStatus)
        holder.view.tvStatus.text = statusText

        when (transactionStatus) {
            TransactionStatus.SUCCESS -> holder.view.ivStatus.setBackgroundResource(R.drawable.ic_transaction_status_success)

            TransactionStatus.NEW,
            TransactionStatus.PROCESS,
            TransactionStatus.PARTIAL,
            TransactionStatus.MANUAL,
            TransactionStatus.USER_AGREEMENT ->
                holder.view.ivStatus.setBackgroundResource(R.drawable.ic_transaction_status_new)

            TransactionStatus.FAIL,
            TransactionStatus.USER_CANCELLED ->
                holder.view.ivStatus.setBackgroundResource(R.drawable.ic_transaction_status_fail)

            else -> holder.view.ivStatus.setBackgroundResource(R.drawable.ic_transaction_status_fail)
        }
    }

    private fun getStatusText(transactionStatus: TransactionStatus?) : String {
        return when (transactionStatus) {
            TransactionStatus.SUCCESS -> context.getString(R.string.transaction_status_success)
            TransactionStatus.NEW -> context.getString(R.string.transaction_status_new)
            TransactionStatus.PROCESS -> context.getString(R.string.transaction_status_in_process)
            TransactionStatus.PARTIAL -> context.getString(R.string.transaction_status_partial)
            TransactionStatus.MANUAL -> context.getString(R.string.transaction_status_manual)
            TransactionStatus.USER_AGREEMENT -> context.getString(R.string.transaction_status_user_agreement)
            TransactionStatus.FAIL -> context.getString(R.string.transaction_status_fail)
            TransactionStatus.USER_CANCELLED -> context.getString(R.string.transaction_status_user_cancelled)
            else -> context.getString(R.string.something_goes_wrong)
        }
    }

    private fun setUserComment(holder: ChildViewHolder, baseTransaction: Transaction.LocalBaseTransaction) {
        val transactionStatus = getStatusText(baseTransaction.status)
        if (baseTransaction.userComment.isNullOrEmpty() || (baseTransaction.userComment == transactionStatus)) {
            holder.view.tvUserComment.visibility = View.GONE
        } else {
            holder.view.tvUserComment.visibility = View.VISIBLE
            holder.view.tvUserComment.setHtmlText(baseTransaction.userComment)
        }
    }

    class ChildViewHolder(val view: ItemChildTransactionBinding) : RecyclerView.ViewHolder(view.root)

    interface Listener {
        fun onOpenSupportChat(baseTransaction: Transaction.LocalBaseTransaction)
    }
}
