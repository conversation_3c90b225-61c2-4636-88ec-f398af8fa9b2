package com.abrand.custom.data.mappers

import com.abrand.custom.GetHallOfFameQuery
import com.abrand.custom.data.entity.HallOfFamePlace

fun GetHallOfFameQuery.HotHallOfFameV2?.mapToListHallOfFamePlace(): MutableList<HallOfFamePlace> {

    val result = mutableListOf<HallOfFamePlace>()

    this?.places?.top?.apply {
        for (top in this) {
            top?.fragments?.maxWinHotRatingPlace?.apply {
                val gameItem = HallOfFamePlace.GameItem(this.gameItem?.url, this.gameItem?.iconMob)
                val user = HallOfFamePlace.User(this.user.id, this.user.formattedUserName, this.user.loyaltyStatus.title)
                val maxWinMoneyAmount = if (this.maxWinMoneyAmount is String) {
                    maxWinMoneyAmount.toDouble()
                } else {
                    0.0
                }
                result.add(HallOfFamePlace(this.position, gameItem, user, maxWinMoneyAmount))
            }
        }
    }

    return result
}
