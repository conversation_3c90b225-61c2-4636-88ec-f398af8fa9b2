query getBonusStoreProducts {
    bonusStoreProducts
    {
        ...StoreProductFragment
    }
}

mutation bonusStoreBuyProduct (
    $productId: Int!
) {
    bonusStoreBuyProduct(
        productId: $productId
    ) {
        ...StoreProductFragment
    }
}

fragment StoreProductFragment on BonusProductForPoints {
    id
    name
    description
    image
    price
    isActive
    buttonTooltip
}
