package com.abrand.custom.data.entity.lottery

class LotteryItem {
    var itemType: ItemType
    var lottery: LocalLottery? = null

    constructor(itemType: ItemType) {
        this.itemType = itemType
    }

    constructor(itemType: ItemType, lottery: LocalLottery) {
        this.itemType = itemType
        this.lottery = lottery
    }

    enum class ItemType(val id: Int) {
        TITLE_ACTIVE_LOTTERY(0),
        ACTIVE_LOTTERY(1),
        TITLE_COMPLETED_LOTTERY(2),
        COMPLETED_LOTTERY(3),
        LOTTERY_LOAD(4),
        FOOTER(5)
    }

    fun isTheSame(other: Any?): <PERSON><PERSON>an {
        return if (other is LotteryItem) {
            if (ItemType.TITLE_ACTIVE_LOTTERY == this.itemType &&
                ItemType.TITLE_ACTIVE_LOTTERY == other.itemType) {
                true
            } else if (ItemType.TITLE_COMPLETED_LOTTERY == this.itemType &&
                ItemType.TITLE_COMPLETED_LOTTERY == other.itemType) {
                true
            } else if (ItemType.LOTTERY_LOAD == this.itemType &&
                ItemType.LOTTERY_LOAD == other.itemType) {
                true
            } else if (ItemType.FOOTER == this.itemType &&
                ItemType.FOOTER == other.itemType) {
                true
            } else {
                val thisLotteryItemId = this.lottery?.id
                val otherLotteryItemId = other.lottery?.id

                thisLotteryItemId != null && otherLotteryItemId != null &&
                        thisLotteryItemId == otherLotteryItemId
            }
        } else {
            false
        }
    }

    override fun equals(other: Any?): Boolean {
        return if (other is LotteryItem) {
            if (ItemType.TITLE_ACTIVE_LOTTERY == this.itemType &&
                ItemType.TITLE_ACTIVE_LOTTERY == other.itemType) {
                true
            } else if (ItemType.TITLE_COMPLETED_LOTTERY == this.itemType &&
                ItemType.TITLE_COMPLETED_LOTTERY == other.itemType) {
                true
            } else if (ItemType.LOTTERY_LOAD == this.itemType &&
                ItemType.LOTTERY_LOAD == other.itemType) {
                true
            } else if (ItemType.FOOTER == this.itemType &&
                ItemType.FOOTER == other.itemType) {
                true
            } else {
                val thisLotteryItemId = this.lottery?.id
                val otherLotteryItemId = other.lottery?.id

                val thisLotteryItemStatus = this.lottery?.status
                val otherLotteryItemStatus = other.lottery?.status

                thisLotteryItemId != null &&
                        otherLotteryItemId != null &&
                        thisLotteryItemId == otherLotteryItemId &&
                        thisLotteryItemStatus == otherLotteryItemStatus
            }
        } else {
            false
        }
    }

    override fun hashCode(): Int {
        var result = itemType.hashCode()
        result = 31 * result + (lottery?.hashCode() ?: 0)
        return result
    }
}
