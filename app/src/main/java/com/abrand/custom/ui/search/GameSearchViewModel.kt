package com.abrand.custom.ui.search

import androidx.lifecycle.LiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.data.entity.LoadedGames
import com.abrand.custom.network.GameSearchRepository
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException

class GameSearchViewModel : ViewModel() {
    private val repository = GameSearchRepository()
    val POPULAR_GAMES_LIMIT = 21
    private val LOAD_STEP = 21
    val gamesByNameLiveData: LiveData<LoadedGames>
    val popularGamesLiveData: SingleLiveEvent<LoadedGames>
    val apolloExceptionLiveData: LiveData<ApolloException?>

    init {
        gamesByNameLiveData = repository.gamesByNameLiveData
        popularGamesLiveData = repository.popularGamesLiveData
        apolloExceptionLiveData = repository.apolloExceptionLiveData
    }

    fun loadGamesByName(name: String, offset: Int) {
        repository.loadGamesByName(name, LOAD_STEP, offset, viewModelScope)
    }

    fun loadPopularGames() {
        repository.loadPopularGames(POPULAR_GAMES_LIMIT, viewModelScope)
    }
}
