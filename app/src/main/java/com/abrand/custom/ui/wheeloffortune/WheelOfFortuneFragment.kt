package com.abrand.custom.ui.wheeloffortune

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.WheelSector
import com.abrand.custom.databinding.FragmentWheelOfFortuneBinding
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.setHtmlText
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment
import com.abrand.custom.ui.views.WheelOfFortuneView

class WheelOfFortuneFragment : Fragment() {
    private var binding: FragmentWheelOfFortuneBinding? = null
    private val viewModel: WheelOfFortuneViewModel by viewModels()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentWheelOfFortuneBinding.inflate(inflater, container, false)

        setListeners()
        observeViewModel()

        return binding?.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        unlockToolbarClicks()
        binding = null
    }

    private fun setListeners() {
        binding?.tvRules?.setOnClickListener {
            showRulesDialog()
        }

        binding?.wheel?.listener = object : WheelOfFortuneView.Listener {
            override fun onPlayClick() {
                processSpinClick()
            }

            override fun onWheelRotationEnd() {
                if (<EMAIL>) {
                    viewModel.wheelSpinLiveData.value?.data?.let {
                        if (WheelSector.PrizeType.ZERO == it.type) {
                            showEmptySectorDialog()
                        } else {
                            showWinDialog(it)
                        }
                    }
                }
                unlockToolbarClicks()

                viewModel.onWheelEnd()
            }
        }
    }

    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) {
            if (it == false) {
                hideLoader()
                showContent()
            }
        }

        viewModel.wheelLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    val spinPriceValue = it.data?.spinPrice.toString()
                    val spinPriceSymbol = Settings.get().userCurrencySymbol
                    it.data?.sectors?.let { sectors -> binding?.wheel?.addSectors(sectors) }
                    binding?.tvDescription?.text = context?.getString(R.string.wheel_of_fortune_description, spinPriceValue, spinPriceSymbol)
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(it.failure)
                    }
                }
                else -> {}
            }
        }

        viewModel.freeSpinsLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    binding?.tvFreeSpins?.text = it.data.toString()
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(it.failure)
                    }
                }
                else -> {}
            }
        }

        viewModel.wheelSpinLiveData.observe(viewLifecycleOwner) {
            when (it.status) {
                Status.SUCCESS -> {
                    binding?.wheel?.winSector = it.data
                }
                Status.ERROR -> {
                    (activity as MainActivity).showMessage(it.error?.errorMessage ?: "")
                }
                Status.FAILURE -> {
                    activity?.runOnUiThread {
                        (activity as MainActivity).showConnectionIssueMessage(it.failure)
                    }
                }
                else -> {}
            }
        }
    }

    private fun hideLoader() {
        binding?.loader?.visibility = View.GONE
    }

    private fun showContent() {
        binding?.tvTitle?.visibility = View.VISIBLE
        binding?.tvDescription?.visibility = View.VISIBLE
        binding?.tvRules?.visibility = View.VISIBLE
        binding?.llFreeSpins?.visibility = View.VISIBLE
        binding?.wheel?.visibility = View.VISIBLE
    }

    fun isSoundEnable(): Boolean {
        return viewModel.isSoundEnable
    }

    fun setSoundEnable(soundEnable : Boolean) {
        viewModel.isSoundEnable = soundEnable
        binding?.wheel?.soundEnable(soundEnable)
    }

    private fun showRulesDialog() {
        val dialog = context?.let { Dialog(it) }
        dialog?.setContentView(R.layout.dialog_wheel_rules)
        dialog?.findViewById<View>(R.id.ib_close)
            ?.setOnClickListener { v: View? -> dialog.cancel() }

        val rulesText = viewModel.rulesLiveData.value?.data
        if (rulesText != null) {
            dialog?.findViewById<TextView>(R.id.tv_rules)?.setHtmlText(rulesText)
        }

        val dialogView = dialog?.window?.decorView
        dialogView?.setBackgroundResource(android.R.color.transparent)

        val lp = dialog?.window?.attributes
        lp?.dimAmount = 0.8f
        dialog?.window?.attributes = lp
        dialog?.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
        setDialogSize(dialog)

        dialog?.show()
    }

    private fun showWinDialog(winSector: WheelSector) {
        val dialog = context?.let { Dialog(it) }
        dialog?.setContentView(R.layout.dialog_wheel_win)
        dialog?.findViewById<View>(R.id.ib_close)
            ?.setOnClickListener { v: View? -> dialog.cancel() }

        val tvWinText = dialog?.findViewById<TextView>(R.id.tv_win_text)
        val btnSpin = dialog?.findViewById<Button>(R.id.btn_wheel_spin)

        if (WheelSector.PrizeType.RESPIN == winSector.type) {
            tvWinText?.text = getString(R.string.wheel_of_fortune_free_spin)
            btnSpin?.text = getString(R.string.wheel_of_fortune_spin_free)
        } else {
            tvWinText?.text = getString(R.string.wheel_of_fortune_win_prize, winSector.title)
            btnSpin?.text = getString(R.string.wheel_of_fortune_spin_again)
        }

        btnSpin?.setOnClickListener {
            dialog.cancel()
            processSpinClick()
        }

        val dialogView = dialog?.window?.decorView
        dialogView?.setBackgroundResource(android.R.color.transparent)
        setDialogSize(dialog)

        dialog?.show()
    }

    private fun showEmptySectorDialog() {
        val dialog = context?.let { Dialog(it) }
        dialog?.setContentView(R.layout.dialog_wheel_empty_sector)
        dialog?.findViewById<View>(R.id.ib_close)
            ?.setOnClickListener { v: View? -> dialog.cancel() }

        val btnSpin = dialog?.findViewById<Button>(R.id.btn_wheel_spin)
        btnSpin?.setOnClickListener {
            dialog.cancel()
            processSpinClick()
        }

        val dialogView = dialog?.window?.decorView
        dialogView?.setBackgroundResource(android.R.color.transparent)
        setDialogSize(dialog)

        dialog?.show()
    }

    fun processSpinClick() {
        val spinPriceValue = viewModel.wheelLiveData.value?.data?.spinPrice
        spinPriceValue?.let {
            val balance = (activity as? MainActivity)?.balance
            val freeSpins = viewModel.freeSpinsLiveData.value?.data?.toString()?.toInt() ?: 0
            if ((balance != null && balance >= spinPriceValue) || freeSpins > 0) {
                binding?.wheel?.doSpin()
                viewModel.onWheelStart()
                viewModel.wheelSpin()
                lockToolbarClicks()
            } else {
                (activity as? MainActivity)?.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
            }
        }
    }

    private fun setDialogSize(dialog: Dialog?) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            val startEndMargin =
                context?.resources?.getDimensionPixelSize(R.dimen.activity_horizontal_margin) ?: 0
            val dialogWidth = GeneralTools.getScreenWidth(context) - startEndMargin * 2
            dialog?.window?.setLayout(dialogWidth, ConstraintLayout.LayoutParams.WRAP_CONTENT)
        } else {
            dialog?.window?.setLayout(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT)
        }
    }

    private fun lockToolbarClicks() {
        (activity as? MainActivity)?.lockToolbarBtnsClick(true)
    }

    private fun unlockToolbarClicks() {
        (activity as? MainActivity)?.lockToolbarBtnsClick(false)
    }
}
