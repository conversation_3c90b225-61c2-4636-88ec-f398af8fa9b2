<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="8dp">

    <TextView
        android:id="@+id/tv_amount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="@color/white"
        android:textSize="@dimen/child_transaction_amount_text_size"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/transaction_id"
        android:textColor="#8DA1BD"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_amount" />

    <Button
        android:id="@+id/btn_question"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_btn_transaction_ask_question"
        android:minHeight="20dp"
        android:paddingStart="6dp"
        android:paddingEnd="6dp"
        android:text="@string/ask_question"
        android:textAllCaps="false"
        android:textColor="#D9FFFFFF"
        android:textSize="10sp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_id"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_id" />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_id" />

    <ImageView
        android:id="@+id/iv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_status"
        app:layout_constraintStart_toEndOf="@+id/tv_status"
        app:layout_constraintTop_toTopOf="@+id/tv_status" />

    <TextView
        android:id="@+id/tv_user_comment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="#8DA1BD"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_status" />

</androidx.constraintlayout.widget.ConstraintLayout>
