package com.abrand.custom.social

import android.app.Dialog
import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.webkit.WebView
import android.webkit.WebViewClient
import com.abrand.custom.R

class OkRuNetwork {

    fun showAuthWebView(context: Context, listener: Listener) {
        val authUrl = "https://connect.ok.ru/oauth/authorize?" +
                "client_id=" + context.getString(R.string.ok_ru_app_id) +
                "&scope=GET_EMAIL" +
                "&response_type=token" +
                "&redirect_uri=https://connect.ok.ru/oauth/success.html"
//                "&layout={layout}"
//                "&state={state}"

        val webView = WebView(context)
        val dialog = Dialog(context, android.R.style.Theme_Black_NoTitleBar_Fullscreen)
        dialog.setContentView(webView)

        webView.webViewClient = object : WebViewClient() {

            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                if (url != null && url.contains("access_token")) {
                    Log.d("OkRu", "success: $url")
                    val ref = url.split("#")
                    val refParameters = ref[1].split("&")
                    for (refParameter in refParameters) {
                        if (refParameter.contains("access_token")) {
                            val accessToken = refParameter.split("=")[1]
                            Log.d("OkRu", "accessToken: $accessToken")
                            listener.onAccessTokenReceived(accessToken)
                            dialog.cancel()
                        }
                    }
                }
            }
        }

        webView.loadUrl(authUrl)
        dialog.show()
    }

    interface Listener {
        fun onAccessTokenReceived(token: String)
    }
}
