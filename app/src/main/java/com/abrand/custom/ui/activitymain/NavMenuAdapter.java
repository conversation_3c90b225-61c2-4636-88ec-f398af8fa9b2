package com.abrand.custom.ui.activitymain;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffColorFilter;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseExpandableListAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import com.abrand.custom.R;
import com.abrand.custom.data.entity.ExpandedMenuModel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class NavMenuAdapter extends BaseExpandableListAdapter {
    private final String SELECTED_ITEM_COLOR     = "#005BEA";
    private final String NOT_SELECTED_ITEM_COLOR = "#000000";
    private final String TAG                     = "NavMenuAdapter";

    private final Context                                  context;
    private final List<ExpandedMenuModel>                  listDataHeader = new ArrayList<>();
    private final HashMap<ExpandedMenuModel, List<String>> listDataChild  = new HashMap<>();
    private       ExpandedMenuModel.MenuItem               selectedMenuItem;

    private final ExpandedMenuModel menuModelHome;
    private final ExpandedMenuModel menuModelPromotions;
    private final ExpandedMenuModel menuModelTournaments;
    private final ExpandedMenuModel menuModelGameRoom;
    private final ExpandedMenuModel menuModelCashBox;
    private final ExpandedMenuModel menuModelShop;
    private final ExpandedMenuModel menuModelLottery;
    private final ExpandedMenuModel menuModelWheelOfFortune;
    private final ExpandedMenuModel menuModelRules;
    private final ExpandedMenuModel menuModelNews;
    private final ExpandedMenuModel menuModelLoyaltyProgram;
    private final ExpandedMenuModel menuModelMessages;
    private final ExpandedMenuModel menuModelHallOfFame;
    private final ExpandedMenuModel menuModelExit;
    private final List<String>      cashChildren;
    private final int               smallMenuItemMargin;

    public NavMenuAdapter(Context context) {
        this.context = context;

        menuModelHome = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.HOME,
                context.getString(R.string.menu_home), R.drawable.ic_home);
        menuModelPromotions = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.PROMOTIONS,
                context.getString(R.string.menu_promotions), R.drawable.ic_promotions);
        menuModelTournaments = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.TOURNAMENTS,
                context.getString(R.string.menu_tournaments), R.drawable.ic_tournaments);
        menuModelGameRoom = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.GAME_ROOM,
                context.getString(R.string.menu_game_room), R.drawable.ic_game_room);
        menuModelCashBox = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.CASH_BOX,
                context.getString(R.string.menu_cash), R.drawable.ic_cash);
        menuModelShop = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.SHOP,
                context.getString(R.string.menu_shop), R.drawable.ic_shop);
        menuModelLottery = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.LOTTERY,
                context.getString(R.string.menu_lottery), R.drawable.ic_lottery);
        menuModelWheelOfFortune = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.WHEEL_OF_FORTUNE,
                context.getString(R.string.menu_wheel_of_fortune), R.drawable.ic_wheel_of_fortune);
        menuModelRules = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.RULES,
                context.getString(R.string.menu_rules));
        menuModelNews = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.NEWS,
                context.getString(R.string.menu_news));
        menuModelLoyaltyProgram = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.LOYALTY_PROGRAM,
                context.getString(R.string.menu_loyalty_program));
        menuModelMessages = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.MESSAGES, "Сообщения");
        menuModelHallOfFame = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.HALL_OF_FAME,
                context.getString(R.string.menu_hall_of_fame));
        menuModelExit = new ExpandedMenuModel(ExpandedMenuModel.MenuItem.EXIT,
                context.getString(R.string.menu_exit), R.drawable.ic_log_out);

        cashChildren = new ArrayList<>();
        cashChildren.add(context.getString(R.string.menu_charity));
        cashChildren.add(context.getString(R.string.menu_extract));
        cashChildren.add(context.getString(R.string.menu_history_transactions));

        smallMenuItemMargin = context.getResources().getDimensionPixelSize(R.dimen.small_menu_item_margin);
    }

    public void applyLoggedState() {
        listDataHeader.clear();
        listDataChild.clear();

        listDataHeader.add(menuModelHome);
        listDataHeader.add(menuModelPromotions);
        listDataHeader.add(menuModelTournaments);
        listDataHeader.add(menuModelGameRoom);
        listDataHeader.add(menuModelCashBox);
        listDataHeader.add(menuModelShop);
        listDataHeader.add(menuModelLottery);
        listDataHeader.add(menuModelWheelOfFortune);
        listDataHeader.add(menuModelRules);
        listDataHeader.add(menuModelNews);
        listDataHeader.add(menuModelMessages);
        listDataHeader.add(menuModelLoyaltyProgram);
        listDataHeader.add(menuModelHallOfFame);
        listDataHeader.add(menuModelExit);
        listDataChild.put(menuModelCashBox, cashChildren);
        notifyDataSetChanged();
    }

    public void applyNotLoggedState() {
        listDataHeader.clear();
        listDataChild.clear();

        listDataHeader.add(menuModelHome);
        listDataHeader.add(menuModelPromotions);
        listDataHeader.add(menuModelTournaments);
        listDataHeader.add(menuModelGameRoom);
        listDataHeader.add(menuModelLottery);
        listDataHeader.add(menuModelRules);
        listDataHeader.add(menuModelNews);
        listDataHeader.add(menuModelLoyaltyProgram);
        listDataHeader.add(menuModelHallOfFame);
        notifyDataSetChanged();
    }

    public void applyOrganicState() {
        listDataHeader.clear();
        listDataChild.clear();

        listDataHeader.add(menuModelHome);
        listDataHeader.add(menuModelGameRoom);
        listDataHeader.add(menuModelRules);
        notifyDataSetChanged();
    }

    public void setMenuItemSelected(ExpandedMenuModel.MenuItem selectedMenuItem) {
        this.selectedMenuItem = selectedMenuItem;
    }

    public ExpandedMenuModel.MenuItem getMenuItemByPosition(int position) {
        return listDataHeader.get(position).getMenuItem();
    }

    public ExpandedMenuModel.MenuItem getCashBoxMenuItemByPosition(int position) {
        if ( position == 0 ) {
            return ExpandedMenuModel.MenuItem.CHARITY;
        } else if ( position == 1 ) {
            return ExpandedMenuModel.MenuItem.EXTRACT;
        } else {
            return ExpandedMenuModel.MenuItem.HISTORY;
        }
    }

    public void setMessagesCounter(int count) {
        menuModelMessages.setCount(count);
        notifyDataSetChanged();
    }

    public void increaseMessagesCounter() {
        menuModelMessages.increaseCount();
        notifyDataSetChanged();
    }

    public void resetMessagesCounter() {
        menuModelMessages.resetCount();
        notifyDataSetChanged();
    }

    public void setPromotionsCounter(int count) {
        menuModelPromotions.setCount(count);
        notifyDataSetChanged();
    }

    public void setTournamentsCounter(int count) {
        menuModelTournaments.setCount(count);
        notifyDataSetChanged();
    }

    public void setLotteriesCounter(int count) {
        menuModelLottery.setCount(count);
        notifyDataSetChanged();
    }

    public int getMessagesCount() {
        return menuModelMessages.getCount();
    }

    @Override
    public int getGroupCount() {
        return this.listDataHeader.size();
    }

    @Override
    public int getChildrenCount(int groupPosition) {
        List<String> childrenList = this.listDataChild.get(this.listDataHeader.get(groupPosition));
        if ( childrenList == null ) {
            return 0;
        } else {
            return childrenList.size();
        }
    }

    @Override
    public Object getGroup(int groupPosition) {
        return this.listDataHeader.get(groupPosition);
    }

    @Override
    public Object getChild(int groupPosition, int childPosition) {
        return this.listDataChild.get(this.listDataHeader.get(groupPosition)).get(childPosition);
    }

    @Override
    public long getGroupId(int groupPosition) {
        return groupPosition;
    }

    @Override
    public long getChildId(int groupPosition, int childPosition) {
        return childPosition;
    }

    @Override
    public boolean hasStableIds() {
        return false;
    }

    @Override
    public View getGroupView(int groupPosition, boolean isExpanded, View convertView, ViewGroup parent) {
        ExpandedMenuModel menuItemModel = (ExpandedMenuModel) getGroup(groupPosition);
        if ( convertView == null ) {
            LayoutInflater inflater = (LayoutInflater) this.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflater.inflate(R.layout.item_menu_header, null);
        }
        TextView  tvTitle    = convertView.findViewById(R.id.tv_title);
        ImageView ivMenuIcon = convertView.findViewById(R.id.icon_menu);
        tvTitle.setText(menuItemModel.getTitle());
        ivMenuIcon.setImageResource(menuItemModel.getIconId());

        setDropDownImage(groupPosition, isExpanded, convertView);
        setItemBackground(groupPosition, isExpanded, convertView, menuItemModel);
        initTvTitle(menuItemModel, tvTitle);
        setMenuItemHeaderMargin(convertView, menuItemModel);

        ImageView ivSelectedMenu = convertView.findViewById(R.id.iv_selected_item);
        Drawable  drawable       = null;
        try {
            drawable = ContextCompat.getDrawable(context, menuItemModel.getIconId());
        } catch ( Resources.NotFoundException exception ) {
            //Log.e(TAG, "menu item drawable exception: " + exception.toString());
        }

        if ( selectedMenuItem == getMenuItemByPosition(groupPosition) ) {
            ivSelectedMenu.setVisibility(View.VISIBLE);
            tvTitle.setTextColor(Color.parseColor(SELECTED_ITEM_COLOR));
            applyItemColorToDrawable(menuItemModel.getMenuItem(), drawable, true);
        } else {
            ivSelectedMenu.setVisibility(View.INVISIBLE);
            tvTitle.setTextColor(Color.parseColor(NOT_SELECTED_ITEM_COLOR));
            applyItemColorToDrawable(menuItemModel.getMenuItem(), drawable, false);
        }
        ivMenuIcon.setImageDrawable(drawable);

        setCounter(convertView, menuItemModel);

        return convertView;
    }

    private void setItemBackground(int groupPosition, boolean isExpanded, View convertView,
                                   ExpandedMenuModel menuItemModel) {
        if ( (getChildrenCount(groupPosition) > 0 && isExpanded) ||
                ExpandedMenuModel.MenuItem.RULES.equals(menuItemModel.getMenuItem()) ||
                ExpandedMenuModel.MenuItem.NEWS.equals(menuItemModel.getMenuItem()) ||
                ExpandedMenuModel.MenuItem.MESSAGES.equals(menuItemModel.getMenuItem()) ||
                ExpandedMenuModel.MenuItem.LOYALTY_PROGRAM.equals(menuItemModel.getMenuItem()) ||
                (ExpandedMenuModel.MenuItem.HALL_OF_FAME.equals(menuItemModel.getMenuItem()) && !listDataHeader.contains(menuModelExit)) ) {
            convertView.setBackground(null);
        } else {
            convertView.setBackground(ContextCompat.getDrawable(context, R.drawable.menu_item_bg));
        }
    }

    private void setDropDownImage(int groupPosition, boolean isExpanded, View convertView) {
        ImageView ivDropDown = convertView.findViewById(R.id.iv_drop_down);
        if ( getChildrenCount(groupPosition) > 0 ) {
            if ( isExpanded ) {
                ivDropDown.setImageResource(R.drawable.ic_menu_group_up);
            } else {
                ivDropDown.setImageResource(R.drawable.ic_menu_group_down);
            }
        } else {
            ivDropDown.setImageResource(0);
        }
    }

    private void initTvTitle(ExpandedMenuModel menuItemModel, TextView tvTitle) {
        if ( ExpandedMenuModel.MenuItem.RULES.equals(menuItemModel.getMenuItem()) ||
                ExpandedMenuModel.MenuItem.NEWS.equals(menuItemModel.getMenuItem()) ||
                ExpandedMenuModel.MenuItem.MESSAGES.equals(menuItemModel.getMenuItem()) ||
                ExpandedMenuModel.MenuItem.LOYALTY_PROGRAM.equals(menuItemModel.getMenuItem()) ||
                ExpandedMenuModel.MenuItem.HALL_OF_FAME.equals(menuItemModel.getMenuItem())) {
            tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, context.getResources().getDimension(R.dimen.menu_child_item_text_size));
            tvTitle.setTypeface(null);
        } else {
            tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_PX, context.getResources().getDimension(R.dimen.menu_header_item_text_size));
            tvTitle.setTypeface(Typeface.create("sans-serif-medium", Typeface.NORMAL));
        }
    }

    private void setMenuItemHeaderMargin(View convertView, ExpandedMenuModel menuItemModel) {
        View                   menuItem   = convertView.findViewById(R.id.menu_item);
        ViewGroup.LayoutParams menuItemLp = menuItem.getLayoutParams();
        if ( ExpandedMenuModel.MenuItem.RULES.equals(menuItemModel.getMenuItem()) ) {
            if ( menuItemLp instanceof ViewGroup.MarginLayoutParams ) {
                ((ViewGroup.MarginLayoutParams) menuItemLp).topMargin = smallMenuItemMargin;
                ((ViewGroup.MarginLayoutParams) menuItemLp).bottomMargin = 0;
            }
        } else if ( ExpandedMenuModel.MenuItem.HALL_OF_FAME.equals(menuItemModel.getMenuItem()) ) {
            if ( menuItemLp instanceof ViewGroup.MarginLayoutParams ) {
                ((ViewGroup.MarginLayoutParams) menuItemLp).topMargin = 0;
                ((ViewGroup.MarginLayoutParams) menuItemLp).bottomMargin = smallMenuItemMargin;
            }
        } else {
            if ( menuItemLp instanceof ViewGroup.MarginLayoutParams ) {
                ((ViewGroup.MarginLayoutParams) menuItemLp).topMargin = 0;
                ((ViewGroup.MarginLayoutParams) menuItemLp).bottomMargin = 0;
            }
        }
    }

    public void applyItemColorToDrawable(ExpandedMenuModel.MenuItem menuItem, Drawable image, boolean isSelected) {
        if ( image != null ) {
            PorterDuffColorFilter porterDuffColorFilter = null;
            if ( isSelected ) {
                porterDuffColorFilter = new PorterDuffColorFilter(
                        Color.parseColor(SELECTED_ITEM_COLOR), PorterDuff.Mode.SRC_ATOP);
            } else {
                if ( ExpandedMenuModel.MenuItem.WHEEL_OF_FORTUNE != menuItem ) {
                    porterDuffColorFilter = new PorterDuffColorFilter(
                            Color.parseColor(NOT_SELECTED_ITEM_COLOR), PorterDuff.Mode.SRC_ATOP);
                }
            }
            if ( porterDuffColorFilter != null ) {
                image.setColorFilter(porterDuffColorFilter);
            }
        }
    }

    private void setCounter(View convertView, ExpandedMenuModel menuItemModel) {
        TextView tvCounter = convertView.findViewById(R.id.tv_counter);
        if ( menuItemModel.getCount() > 0 ) {
            tvCounter.setText(String.valueOf(menuItemModel.getCount()));
            tvCounter.setVisibility(View.VISIBLE);
        } else {
            tvCounter.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public View getChildView(int groupPosition, int childPosition, boolean isLastChild, View convertView, ViewGroup parent) {
        final String childText = (String) getChild(groupPosition, childPosition);

        if ( convertView == null ) {
            LayoutInflater inflater = (LayoutInflater) this.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflater.inflate(R.layout.item_menu_child, null);
        }

        TextView tvChildTitle = convertView.findViewById(R.id.tv_child_title);
        tvChildTitle.setText(childText);

        if ( isLastChild ) {
            convertView.setBackground(ContextCompat.getDrawable(context, R.drawable.menu_item_bg));
        } else {
            convertView.setBackground(null);
        }

        setMenuItemChildMargin(convertView, childText);

        return convertView;
    }

    private void setMenuItemChildMargin(View convertView, String childText) {
        View                   menuItem   = convertView.findViewById(R.id.menu_item);
        ViewGroup.LayoutParams menuItemLp = menuItem.getLayoutParams();
        if ( context.getString(R.string.menu_history_transactions).equals(childText) ) {
            if ( menuItemLp instanceof ViewGroup.MarginLayoutParams ) {
                ((ViewGroup.MarginLayoutParams) menuItemLp).bottomMargin = smallMenuItemMargin;
            }
        } else {
            if ( menuItemLp instanceof ViewGroup.MarginLayoutParams ) {
                ((ViewGroup.MarginLayoutParams) menuItemLp).bottomMargin = 0;
            }
        }
    }

    @Override
    public boolean isChildSelectable(int groupPosition, int childPosition) {
        return true;
    }
}
