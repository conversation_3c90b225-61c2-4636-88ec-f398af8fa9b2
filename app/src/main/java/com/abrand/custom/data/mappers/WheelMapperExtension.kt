package com.abrand.custom.data.mappers

import com.abrand.custom.GetFreeSpinsQuery
import com.abrand.custom.GetWheelFortuneRulesQuery
import com.abrand.custom.GetWheelQuery
import com.abrand.custom.WheelSpinMutation
import com.abrand.custom.data.entity.LocalWheel
import com.abrand.custom.data.entity.WheelSector

fun GetWheelQuery.WheelByDenomination.mapToLocalWheel(): LocalWheel {

    val sectors = mutableListOf<WheelSector>()
    if (this.sectors != null) {
        for (sector in this.sectors) {
            val title = sector?.prize?.title
            val type = sector?.prize?.type?.alias?.let { WheelSector.PrizeType.getByAlias(it) }
            val position = sector?.position
            sectors.add(WheelSector(position, title, type))
        }
    }

    return LocalWheel(this.id.toInt(), this.spinPrice.toString().toInt(), sectors)
}

fun List<GetFreeSpinsQuery.WheelUserFreeSpin?>?.mapToLocalWheelUserFreeSpin(): Int {
    return if (this != null && this.isNotEmpty()) {
        this[0]?.spinCount ?: 0
    } else {
        0
    }
}

fun WheelSpinMutation.WheelGameSpin.mapToWheelSector(): WheelSector {
    val type = this.prize.type.alias.let { WheelSector.PrizeType.getByAlias(it) }
    return WheelSector(this.position, this.prize.title, type)
}

fun List<GetWheelFortuneRulesQuery.TextBlock?>?.mapToWheelRules(): String {
    if (this != null && this.isNotEmpty()) {
        val rulesText = this[0]?.text
        if (!rulesText.isNullOrEmpty()) {
            return rulesText
        }
    }
    return ""
}
