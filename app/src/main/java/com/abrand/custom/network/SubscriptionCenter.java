package com.abrand.custom.network;

import androidx.annotation.NonNull;

import com.abrand.custom.OnlineUsersSubscription;
import com.abrand.custom.SubscribeBalanceSubscription;
import com.abrand.custom.SubscribeLoyaltyPointsSubscription;
import com.abrand.custom.SubscribeLoyaltyProgressSubscription;
import com.abrand.custom.SubscribeLoyaltyStatusSubscription;
import com.abrand.custom.SubscribeLoyaltyXOnPointsSubscription;
import com.abrand.custom.SubscribeMessagesSubscription;
import com.abrand.custom.SubscribeRealTimeNotificationSubscription;
import com.abrand.custom.SubscribeTournamentResultSubscription;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class SubscriptionCenter {
    private static volatile SubscriptionCenter instance;
    private final static int MAX_ERROR_COUNT = 3;

//    private Map<SubscriptionObserver, ApolloSubscriptionCall> observers;

    private SubscriptionCenter() {
//        this.observers = new HashMap<>();
    }

    public static SubscriptionCenter getInstance() {
        SubscriptionCenter localInstance = instance;

        if ( localInstance == null ) {
            synchronized (SubscriptionCenter.class) {
                localInstance = instance;
                if ( localInstance == null ) {
                    instance = localInstance = new SubscriptionCenter();
                }
            }
        }

        return localInstance;
    }

//    public void subscribe(@NonNull SubscriptionType type, SubscriptionObserver observer) {
//        if ( type == SubscriptionType.BALANCE ) {
//            //noinspection unchecked
//            subscribeOnChanges(new SubscribeBalanceSubscription(),
//                    (SubscriptionObserver<SubscribeBalanceSubscription.Data>) observer, 0);
//        } else if ( type == SubscriptionType.LOYALTY_POINTS ) {
//            //noinspection unchecked
//            subscribeOnChanges(new SubscribeLoyaltyPointsSubscription(),
//                    (SubscriptionObserver<SubscribeLoyaltyPointsSubscription.Data>) observer, 0);
//        } else if ( type == SubscriptionType.LOYALTY_PROGRESS ) {
//            //noinspection unchecked
//            subscribeOnChanges(new SubscribeLoyaltyProgressSubscription(),
//                    (SubscriptionObserver<SubscribeLoyaltyProgressSubscription.Data>) observer, 0);
//        } else if ( type == SubscriptionType.LOYALTY_STATUS ) {
//            //noinspection unchecked
//            subscribeOnChanges(new SubscribeLoyaltyStatusSubscription(),
//                    (SubscriptionObserver<SubscribeLoyaltyStatusSubscription.Data>) observer, 0);
//        } else if (type == SubscriptionType.LOYALTY_X_ON_POINTS) {
//            //noinspection unchecked
//            subscribeOnChanges(new SubscribeLoyaltyXOnPointsSubscription(),
//                    (SubscriptionObserver<SubscribeLoyaltyXOnPointsSubscription.Data>) observer, 0);
//        } else if ( type == SubscriptionType.REAL_TIME_MESSAGES ) {
//            //noinspection unchecked
//            subscribeOnChanges(new SubscribeRealTimeNotificationSubscription(),
//                    (SubscriptionObserver<SubscribeRealTimeNotificationSubscription.Data>) observer, 0);
//        } else if ( type == SubscriptionType.ONLINE_USERS ) {
//            //noinspection unchecked
//            subscribeOnChanges(new OnlineUsersSubscription(),
//                    (SubscriptionObserver<OnlineUsersSubscription.Data>) observer, 0);
//        } else if (type == SubscriptionType.MESSAGES) {
//            //noinspection unchecked
//            subscribeOnChanges(new SubscribeMessagesSubscription(),
//                    (SubscriptionObserver<SubscribeMessagesSubscription.Data>) observer, 0);
//        }
//    }

//    public void SubscribeTournament(String id, SubscriptionObserver observer){
//        //noinspection unchecked
//        subscribeOnChanges(new SubscribeTournamentResultSubscription(id),
//                (SubscriptionObserver<SubscribeTournamentResultSubscription.Data>) observer, 0);
//    }

    public void unSubscribeAll() {
//        Set<SubscriptionObserver> set = new HashSet<>(observers.keySet());

//        for ( SubscriptionObserver entry : set ) {
//            unSubscribe(entry);
//        }
    }

    public void unSubscribe(@NonNull SubscriptionObserver observer) {
//        cancel(observer);

//        observers.remove(observer);
    }

    public interface SubscriptionObserver<T> {
        void onUpdate(T data);

        void onFail();
    }

    public interface SubscriptionCallback<T> {
        void onUpdate(T data);

        void onFail(boolean tryRefresh);
    }

    public enum SubscriptionType {
        BALANCE, LOYALTY_POINTS, LOYALTY_PROGRESS, LOYALTY_STATUS, LOYALTY_X_ON_POINTS,
        REAL_TIME_MESSAGES, ONLINE_USERS, TOURNAMENT, MESSAGES, UNKNOWN
    }

//    private <T extends Operation.Data> void subscribeOnChanges(Subscription subscription,
//            SubscriptionObserver<T> subscriber, int errorCount) {
//        SubscriptionCallback<T> subscriptionCallback =
//                new SubscriptionCallback<T>() {
//                    @Override
//                    public void onUpdate(T data) {
//                        subscriber.onUpdate(data);
//                    }
//
//                    @Override
//                    public void onFail(boolean tryRefresh) {
//                        cancel(subscriber);
//                        if ( tryRefresh && observers.get(subscriber) != null && errorCount < 2 ) {
//                            subscribeOnChanges(subscription, subscriber, errorCount + 1);
//                        } else {
//                            subscriber.onFail();
//                        }
//                    }
//                };
//        cancel(subscriber);
//
//        ApolloSubscriptionCall<T> call =
//                ApolloProcessor.subscribe(subscription, subscriptionCallback);
//
//        observers.put(subscriber, call);
//    }

//    @SuppressWarnings("rawtypes")
//    private void cancel(SubscriptionObserver subscriber) {
//        ApolloSubscriptionCall call = observers.get(subscriber);
//
//        if ( call != null ) {
//            call.cancel();
//        }
//    }
}
