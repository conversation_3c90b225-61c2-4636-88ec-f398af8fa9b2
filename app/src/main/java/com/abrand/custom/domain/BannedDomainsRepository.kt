package com.abrand.custom.domain

import com.abrand.custom.data.entity.BannedDomainDTO
import com.abrand.custom.data.entity.appconfig.LocalConfig

interface BannedDomainsRepository {

    suspend fun saveBannedDomain(domain: BannedDomainDTO)

    suspend fun getBannedDomainsList(): List<BannedDomain>

    suspend fun deleteAllBannedDomains()

    suspend fun sendBannedDomains(
        bannedDomains: List<BannedDomain>,
        appConfig: LocalConfig
    ): BannedDomainResponse

    suspend fun saveLastFailedBannedDomainsRequestTimestamp(lastTime: Long)

    suspend fun getLastFailedBannedDomainsRequestTimestamp(): Long

//    suspend fun getReservedDomain(): String
}
