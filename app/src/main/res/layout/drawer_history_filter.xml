<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_drawer_filter"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF"
    android:orientation="vertical"
    android:paddingStart="@dimen/size_16"
    android:paddingEnd="@dimen/size_16">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/history_filter"
            android:textColor="#000000"
            android:textSize="24sp" />

        <Button
            android:id="@+id/btn_clear_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:background="@null"
            android:text="@string/filter_clear_all"
            android:textAllCaps="false"
            android:textColor="#0097EC"
            android:visibility="invisible" />
    </FrameLayout>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/filter_transaction_type"
        android:textColor="#000000"
        android:textSize="16sp"
        android:textStyle="bold" />

    <com.google.android.flexbox.FlexboxLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_16"
        app:flexWrap="wrap">

        <Button
            android:id="@+id/btn_type_all"
            style="@style/ButtonFilterHistorySelected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_all" />

        <Button
            android:id="@+id/btn_type_transaction_in"
            style="@style/ButtonFilterHistoryUnselected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_transaction_in" />

        <Button
            android:id="@+id/btn_type_transaction_out"
            style="@style/ButtonFilterHistoryUnselected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_transaction_out" />

        <Button
            android:id="@+id/btn_type_exchange_points"
            style="@style/ButtonFilterHistoryUnselected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_transaction_exchange_points" />

        <Button
            android:id="@+id/btn_type_bonuses_prises"
            style="@style/ButtonFilterHistoryUnselected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_transaction_bonuses_prises" />

        <Button
            android:id="@+id/btn_type_other"
            style="@style/ButtonFilterHistoryUnselected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_transaction_other" />

    </com.google.android.flexbox.FlexboxLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginTop="12dp"
        android:background="#1A000000" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/filter_status"
        android:textColor="#000000"
        android:textSize="16sp"
        android:textStyle="bold" />

    <com.google.android.flexbox.FlexboxLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_16"
        app:flexWrap="wrap">

        <Button
            android:id="@+id/btn_status_all"
            style="@style/ButtonFilterHistorySelected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_all" />

        <Button
            android:id="@+id/btn_status_new"
            style="@style/ButtonFilterHistoryUnselected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_transaction_new" />

        <Button
            android:id="@+id/btn_status_success"
            style="@style/ButtonFilterHistoryUnselected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_transaction_success" />

        <Button
            android:id="@+id/btn_status_user_cancelled"
            style="@style/ButtonFilterHistoryUnselected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_transaction_user_cancelled" />

        <Button
            android:id="@+id/btn_status_fail"
            style="@style/ButtonFilterHistoryUnselected"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:text="@string/filter_transaction_fail" />

    </com.google.android.flexbox.FlexboxLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginTop="12dp"
        android:background="#1A000000" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/filter_date_range"
        android:textColor="#000000"
        android:textSize="16sp"
        android:textStyle="bold" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_16"
        app:cardBackgroundColor="#FFFFFF">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_date_range"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_date_range"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/size_16"
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:text="@string/filter_all_time"
                android:textColor="#000000"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:src="@drawable/ic_calendar"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <Button
        android:id="@+id/btnApply"
        style="@style/ButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/size_16"
        android:text="@string/filter_apply"
        android:visibility="gone" />

</LinearLayout>
