query hello {
    hello
}

query getSupportPhone(
    $host: String!
) {
    siteMirror(
        host: $host,
        device: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    ) {
        supportPhone
    }
}

query getPage($path: String!) {
    page(
        path: $path,
        deviceInfo: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
    ) {
        contents
    }
}

query getHallOfFame($year: Int, $month: Int, $itemsLimit: Int) {
    hotHallOfFameV2 (
        rating: MOST_MAX_WIN,
        year: $year,
        month: $month,
        itemsLimit: $itemsLimit
    ) {
        places {
            top  {
                ... MaxWinHotRatingPlace
            }
        }
    }
}

query getSocialNetworks(
    $host: String!
) {
    siteMirror(
        device: {
            platformType: MOB,
            platform: NATIVE_APP,
            os: ANDROID
        }
        host: $host
    ) {
        socialNetworkForLogin
        socialNetworkForRegistration
    }
}

fragment MaxWinHotRatingPlace on MaxWinHotRatingPlace {
    position
    gameItem {
        iconMob
        url
    }
    user {
        id
        formattedUserName
        loyaltyStatus {
            title
        }
    }
    maxWinMoneyAmount
}

