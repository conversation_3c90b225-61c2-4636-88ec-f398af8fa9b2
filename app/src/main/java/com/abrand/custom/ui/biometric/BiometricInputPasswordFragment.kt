package com.abrand.custom.ui.biometric

import android.os.Build
import android.os.Bundle
import android.text.InputType
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.biometric.BiometricPrompt
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.Navigation
import com.abrand.custom.R
import com.abrand.custom.data.*
import com.abrand.custom.data.entity.AnalyticsEvent
import com.abrand.custom.data.entity.BiometricCredentials
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.databinding.FragmentBiometricInputPasswordBinding
import com.abrand.custom.interfaces.BiometricAuthListener
import com.abrand.custom.network.OkHttpProcessor
import com.abrand.custom.tools.BiometricUtil
import com.abrand.custom.tools.CryptographyUtil
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.ui.activitymain.MainActivity
import com.apollographql.apollo3.exception.ApolloException
import javax.crypto.Cipher

class BiometricInputPasswordFragment : Fragment() {
    private val TAG = "BiometricInputPasswordFragment"
    private var binding: FragmentBiometricInputPasswordBinding? = null
    private lateinit var viewModel: BiometricInputPasswordViewModel
    private var biometricPassword: String? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        val localBinding = FragmentBiometricInputPasswordBinding.inflate(inflater, container, false)
        binding = localBinding

        viewModel = ViewModelProvider(this).get(BiometricInputPasswordViewModel::class.java)
        observeViewModel()

        localBinding.etPassword.setInputType(InputType.TYPE_TEXT_VARIATION_WEB_PASSWORD)
        localBinding.etPassword.subscribeToUpdates { data, valid ->
            if (valid) {
                localBinding.etPassword.hideErrorMessage()
            }
        }
        localBinding.btnSave.setOnClickListener {
            biometricPassword = localBinding.etPassword.text
            val localPassword = biometricPassword
            if (localPassword != null && localPassword.length >= Constants.MIN_PASSWORD_LENGTH) {
                GeneralTools.hideKeyboard(activity)
                viewModel.checkPassword(localPassword)
            } else {
                localBinding.etPassword.showErrorMessage(getString(R.string.password_length_warning,
                    Constants.MIN_PASSWORD_LENGTH))
            }
        }

        localBinding.btnCancel.setOnClickListener {
            navigateUp()
        }

        return localBinding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun observeViewModel() {
        viewModel.checkPasswordLiveData.observe(viewLifecycleOwner,
            Observer<Resource<Boolean?, ServerError, ApolloException>> { (status, data, error, failure) ->
                when (status) {
                    Status.SUCCESS -> {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                            if (data == true) {
                                showBiometricPromptToEncrypt()
                            } else {
                                binding?.etPassword?.showErrorMessage(getString(R.string.wrong_password))
                            }
                        } else {
                            navigateUp()
                        }
                    }
                    Status.ERROR -> {
                        Toast.makeText(context, error?.errorMessage, Toast.LENGTH_SHORT).show()
                    }
                    Status.FAILURE -> {
                        (activity as MainActivity).showConnectionIssueMessage(failure)
                    }
                    else -> {
                        // TODO: decide what should u do in another cases
                    }
                }
            })
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun showBiometricPromptToEncrypt() {
        val cryptoObject = BiometricPrompt.CryptoObject(
            CryptographyUtil.getInitializedCipherForEncryption()
        )

        BiometricUtil.showBiometricPrompt(
            title = getString(R.string.biometric_password_title),
            description = getString(R.string.biometric_password_description),
            negativeButtonText = getString(R.string.biometric_password_negative_button),
            activity = activity as AppCompatActivity,
            listener = biometricAuthEncryptListener,
            cryptoObject = cryptoObject
        )
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private val biometricAuthEncryptListener = object : BiometricAuthListener {

        override fun onBiometricAuthenticationSuccess(result: BiometricPrompt.AuthenticationResult) {
            result.cryptoObject?.cipher?.let { chiper ->
                biometricPassword?.let { localBiometricPassword ->
                    if (localBiometricPassword.isNotEmpty()) {
                        encryptAndSave(localBiometricPassword, chiper)
                        navigateUp()
                    }
                }

            }
        }

        override fun onBiometricAuthenticationError(errorCode: Int, errorMessage: String) {
            Log.d(TAG, "Biometric error: $errorMessage")
            if (BiometricPrompt.ERROR_LOCKOUT == errorCode) {
                (activity as? MainActivity)?.showMessage(errorMessage)
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun encryptAndSave(plainTextMessage: String, cipher: Cipher) {
        val encryptedPassword = CryptographyUtil.encryptData(plainTextMessage, cipher)
        val email = Settings.get().loggedUserEmail
        Settings.get().biometricCredentials = BiometricCredentials(email, encryptedPassword)
        context?.let {
            val biometryAuthDataJson = JsonDataGenerator().getBiometryAuthDataJson(true,
                Settings.get().userId)
            OkHttpProcessor.sendAnalytics(it, AnalyticsEvent.BIOMETRY_AUTH, biometryAuthDataJson,
                null)
        }
    }

    private fun navigateUp() {
        view?.let {
            activity?.runOnUiThread { Navigation.findNavController(it).navigateUp() }
        }
    }

}
