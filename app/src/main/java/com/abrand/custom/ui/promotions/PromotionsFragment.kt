package com.abrand.custom.ui.promotions

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Button
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.Constants
import com.abrand.custom.data.PrizeUtil
import com.abrand.custom.data.Resource
import com.abrand.custom.data.Settings
import com.abrand.custom.data.Status
import com.abrand.custom.data.entity.Bonus
import com.abrand.custom.data.entity.BonusesResponse
import com.abrand.custom.data.entity.FastClickPaymentSystem
import com.abrand.custom.data.entity.PermanentPromotion
import com.abrand.custom.data.entity.PromotionItem
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.data.entity.UrlSource
import com.abrand.custom.data.entity.User
import com.abrand.custom.databinding.FragmentPromotionsBinding
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.readTextFromAsset
import com.abrand.custom.tools.setHtmlText
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment
import com.abrand.custom.ui.views.FastClickPaymentView
import com.abrand.custom.ui.views.PromoCodeField
import com.apollographql.apollo3.exception.ApolloException

class PromotionsFragment : Fragment(R.layout.fragment_promotions) {
    private lateinit var viewModel: PromotionsViewModel
    private lateinit var promotionsAdapter: PromotionsAdapter
    private lateinit var currencyCode: String
    private val TAG = "PromotionsFragment"
    private var infoDialog : Dialog? = null
    private var binding: FragmentPromotionsBinding? = null
    private var promotionsLayoutManager: LinearLayoutManager? = null
    private var promoCodeFocusedPosition = -1

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding = FragmentPromotionsBinding.bind(view)
        viewModel = ViewModelProviders.of(this).get(PromotionsViewModel::class.java)
        viewModel.bonusesLiveData.observe(viewLifecycleOwner, bonusesObserver)
        viewModel.bonusActivateLiveData.observe(viewLifecycleOwner, bonusActivateObserver)
        viewModel.bonusDeactivateLiveData.observe(viewLifecycleOwner, bonusDeactivateObserver)
        viewModel.fastPaymentRebillingLiveData.observe(viewLifecycleOwner, fastPaymentRebillingObserver)
        viewModel.fastPaymentUrlLiveData.observe(viewLifecycleOwner, fastPaymentUrlObserver)
        viewModel.tryBonusPromoCodeLiveData.observe(viewLifecycleOwner, tryBonusPromoCodeObserver)
        viewModel.serverErrorLiveData.observe(viewLifecycleOwner, serverErrorObserver)
        viewModel.apolloExceptionLiveData.observe(viewLifecycleOwner, apolloExceptionObserver)
        observeViewModel()

        showLoader()
        currencyCode = if (User.State.PLAYER == Settings.get().userState) {
            Settings.get().userCurrencyCode
        } else {
            getString(R.string.default_currency_code)
        }
        viewModel.getBonuses()

        promotionsLayoutManager = LinearLayoutManager(context)
        binding?.rvBonuses?.layoutManager = promotionsLayoutManager
        promotionsAdapter = PromotionsAdapter(currencyCode, (activity as MainActivity).fastClickPaymentSystem)
        promotionsAdapter.buttonsClickListener = promotionButtonsClickListener
        promotionsAdapter.footerListener = footerListener
        binding?.rvBonuses?.adapter = promotionsAdapter

        setupInsets()
        binding?.apply {
            val layoutParams: ViewGroup.LayoutParams = ivLoader.layoutParams

            if (layoutParams is ViewGroup.MarginLayoutParams) {
                layoutParams.topMargin = GeneralTools.getStatusBarHeight(context) + GeneralTools.getActionBarHeight(context)
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun setupInsets() {
        binding?.apply {
            ViewCompat.setOnApplyWindowInsetsListener(rvBonuses) { view, insets ->
                view.updatePadding(bottom = insets.systemWindowInsetBottom)

                if (insets.systemWindowInsetBottom > 150 && promoCodeFocusedPosition >= 0) {
                    scrollToFocusedPromoCodeCard(insets.systemWindowInsetBottom)
                }

                insets
            }
        }
    }

    private val bonusesObserver = Observer<BonusesResponse?> {
        val formattedPromotions = viewModel.getFormattedPromotions(it, getString(R.string.promotion_active_bonuses_title),
                getString(R.string.promotion_available_bonuses_title), getString(R.string.permanent_promotions_title))
        promotionsAdapter.setList(formattedPromotions)
        hideLoader()
    }

    private val bonusActivateObserver = Observer<Int?> {
        closeInfoDialog()
        showLoader()
        viewModel.getBonuses()
    }

    private val bonusDeactivateObserver = Observer<Boolean> {
        showLoader()
        viewModel.getBonuses()
    }

    private val fastPaymentRebillingObserver = Observer<Pair<Boolean, Int>> { isRebilling_BonusId_Pair ->
        if (isRebilling_BonusId_Pair.first) {
            promotionsAdapter.fastClickPaymentViewShowLoader(isRebilling_BonusId_Pair.second)
        } else {
            promotionsAdapter.fastClickPaymentViewShowSuccessfulRebillingMessage()
            infoDialogFastClickPaymentViewShowSuccessfulRebillingMessage()
        }
    }

    private val fastPaymentUrlObserver = Observer<String> { url: String? ->
        (activity as MainActivity).openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT, url)
    }

    private val tryBonusPromoCodeObserver = Observer<Resource<Boolean, Pair<Int, ServerError>, ApolloException>> {
        when (it.status) {
            Status.SUCCESS -> {
                closeInfoDialog()
                viewModel.getBonuses()
            }
            Status.ERROR -> {
                val errorMessage = if (PromotionsViewModel.INVALID_PROMOCODE == it.error?.second?.errorCode) {
                    context?.getString(R.string.invalid_promocode)
                } else {
                    it.error?.second?.errorMessage
                }

                val promotionId = it.error?.first
                if (promotionId != null && !errorMessage.isNullOrEmpty()) {
                    val adapterPosition = promotionsAdapter.getAdapterPosition(promotionId)
                    val permanentPromotionVH =
                        binding?.rvBonuses?.findViewHolderForAdapterPosition(adapterPosition) as? PromotionsAdapter.PermanentPromotionVH
                    permanentPromotionVH?.setError(errorMessage)
                }
                errorMessage?.let { error -> showInfoDialogPromoCodeError(error) }
            }
            Status.FAILURE -> {
                activity?.runOnUiThread {
                    (activity as MainActivity).showConnectionIssueMessage(it.failure)
                }
            }
            else -> {}
        }
    }

    private val serverErrorObserver = Observer<ServerError> {
        Toast.makeText(context, it.errorMessage, Toast.LENGTH_SHORT).show()
        hideLoader()
    }

    private val apolloExceptionObserver = Observer<ApolloException?> {
        (activity as MainActivity).showConnectionIssueMessage(it)
        hideLoader()
    }

    private fun observeViewModel() {
        viewModel.makeRebillLiveData.observe(viewLifecycleOwner,
            Observer<Resource<String?, ServerError, ApolloException>> { (status, data, error, failure) ->
                when (status) {
                    Status.SUCCESS -> {
                        data?.let { Log.d("FastClickPayment", it) }
                    }
                    Status.ERROR -> {
                        Toast.makeText(context, error?.errorMessage, Toast.LENGTH_SHORT).show()
                        promotionsAdapter.fastClickPaymentViewHideLoader()
                        infoDialogFastClickPaymentViewShowHideLoader()
                    }
                    Status.FAILURE -> {
                        promotionsAdapter.fastClickPaymentViewHideLoader()
                        infoDialogFastClickPaymentViewShowHideLoader()
                        (activity as MainActivity).showConnectionIssueMessage(failure)
                    }
                    else -> {
                        // TODO: decide what should u do in another cases
                    }
                }
            })
    }

    private val promotionButtonsClickListener =
            object : PromotionsAdapter.ButtonsClickListener {

                override fun onMainButtonClicked(bonusId: Int?, isBonusActivated: Boolean?, isEmailOrPhoneBonus: Boolean) {
                    if (isBonusActivated == null || !isBonusActivated) {
                        if (User.State.PLAYER == Settings.get().userState) {
                            bonusActivate(bonusId, "")
                        } else {
                            (activity as MainActivity).openEnterScreen(false)
                        }
                    } else {
                        if (isEmailOrPhoneBonus) {
                            (activity as MainActivity).openProfileScreen()
                        } else {
                            (activity as MainActivity).openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
                        }
                    }
                }

                override fun onPromoCodeEntered(bonusId: Int, promoCode: String) {
                    GeneralTools.hideKeyboard(activity)
                    if (User.State.PLAYER == Settings.get().userState) {
                        bonusActivate(bonusId, promoCode)
                    } else {
                        (activity as MainActivity).openEnterScreen(false)
                    }
                }

                override fun onPromoCodeFocused(adapterPosition: Int) {
                    promoCodeFocusedPosition = adapterPosition
                }

                override fun onInfoButtonClicked(promotion: PromotionItem) {
                    context?.let {
                        val bonus = promotion.bonus
                        val permanentPromotion = promotion.permanentPromotion
                        if (bonus != null) {
                            showBonusInfoDialog(it, bonus)
                        } else if (permanentPromotion != null) {
                            showPermanentPromotionInfoDialog(it, permanentPromotion)
                        }
                    }
                }

                override fun onDeactivatedButtonClicked(bonusId: Int, name: String, availableAfterDeactivation: Boolean) {
                    context?.let {
                        if (availableAfterDeactivation) {
                            viewModel.bonusDeactivate(bonusId.toString())
                        } else {
                            showDeactivationWarningDialog(it, bonusId, name)
                        }
                    }
                }

                override fun onFastPaymentPayClicked(bonusId: Int, isRebill: Boolean, amount: Int) {
                    if (isRebill) {
                        viewModel.makeRebill(bonusId, amount)
                    } else {
                        viewModel.getFastPaymentUrl(amount)
                    }
                }

                override fun onActionButtonClicked(url: String) {
                    (activity as MainActivity).processDeepLinkUrl(url, UrlSource.BONUS)
                }

                override fun onTryBonusPromoCode(promotionId: Int, promoCode: String) {
                    GeneralTools.hideKeyboard(activity)
                    if (User.State.PLAYER == Settings.get().userState) {
                        viewModel.tryBonusPromoCode(promotionId, promoCode)
                    } else {
                        openRegisterScreen()
                    }
                }
            }

    private val footerListener = object : FooterListener {
        override fun onClickPaymentSystems() {
            if (User.State.PLAYER == Settings.get().userState) {
                (activity as? MainActivity)?.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
            } else {
                (activity as? MainActivity)?.openEnterScreen(false)
            }
        }
    }

    private fun bonusActivate(bonusId: Int?, promoCode: String) {
        if (bonusId != null) {
            val bonusEvent: PromotionItem.BonusEvent? = promotionsAdapter.getBonusEvent(bonusId)
            val activeProgressiveCurrentStep = promotionsAdapter.getActiveProgressiveCurrentStep()
            if ((PromotionItem.BonusEvent.MIN_DEPOSIT == bonusEvent ||
                            PromotionItem.BonusEvent.END_ACTIVATION_TIME == bonusEvent ||
                            PromotionItem.BonusEvent.FIRST_DEPOSIT == bonusEvent) && activeProgressiveCurrentStep >= 2) {
                context?.let { showActivationWarningDialog(it, bonusId, promoCode) }
            } else {
                viewModel.bonusActivate(bonusId.toString(), promoCode)
            }
        } else {
            Log.e(TAG, "Can't activate bonus because bonus id is null")
        }
    }

    private fun showBonusInfoDialog(context: Context, bonus: Bonus) {
        infoDialog = Dialog(context, android.R.style.Theme_Black_NoTitleBar)
        infoDialog!!.setContentView(R.layout.dialog_bonus_info)

        infoDialog!!.findViewById<ImageButton>(R.id.ib_close).setOnClickListener {
            infoDialog!!.cancel()
        }
        
        val promotionName = bonus.name?.replace("</br>", "<br>")
        val tvTitle = infoDialog!!.findViewById<TextView>(R.id.tv_title)
        promotionName?.apply {
            tvTitle.setHtmlText(this)
        }

        infoDialog!!.findViewById<TextView>(R.id.tv_sub_title).text = getInfoDialogSubTitle(bonus)

        val rules = bonus.rules
        if (!rules.isNullOrEmpty()) {
            val wvRules = infoDialog?.findViewById<WebView>(R.id.wvRules)
            wvRules?.let {
                setHtmlRules(wvRules, rules)
            }
        }

        val btnActivate = infoDialog!!.findViewById<View>(R.id.btn_activate)
        val etPromoCode = infoDialog?.findViewById<PromoCodeField>(R.id.et_promo_code)
        if (bonus.isActivated == null || bonus.isActivated == false) {
            if (bonus.isPromoCodeRequired != null && bonus.isPromoCodeRequired!!) {
                etPromoCode?.visibility = View.VISIBLE
                etPromoCode?.listener = object : PromoCodeField.PromoCodeListener {
                    override fun onPromoCodeEntered(promoCode: String) {
                        if (User.State.PLAYER == Settings.get().userState) {
                            etPromoCode?.text?.let { bonusActivate(bonus.id, it) }
                        } else {
                            openRegisterScreen()
                            closeInfoDialog()
                        }
                    }
                }
            } else {
                btnActivate.visibility = View.VISIBLE
                btnActivate.setOnClickListener { v: View? ->
                    if (User.State.PLAYER == Settings.get().userState) {
                        bonusActivate(bonus.id, "")
                    } else {
                        openRegisterScreen()
                    }
                    infoDialog!!.cancel()
                }
            }
        }

        initDialogInfoPayments(infoDialog!!, bonus)
        val containerPrizes = infoDialog!!.findViewById<LinearLayout>(R.id.container_prizes)
        initDialogInfoPrizes(bonus, containerPrizes)

        infoDialog!!.show()
    }

    private fun showPermanentPromotionInfoDialog(context: Context, promotion: PermanentPromotion) {
        infoDialog = Dialog(context, android.R.style.Theme_Black_NoTitleBar)
        infoDialog?.setContentView(R.layout.dialog_bonus_info)

        infoDialog?.findViewById<ImageButton>(R.id.ib_close)?.setOnClickListener {
            infoDialog?.cancel()
        }

        val promotionName = promotion.name?.replace("</br>", "<br>")
        val tvTitle = infoDialog?.findViewById<TextView>(R.id.tv_title)
        promotionName?.apply {
            tvTitle?.setHtmlText(this)
        }

        val description = promotion.description
        if (description != null) {
            val wvRules = infoDialog?.findViewById<WebView>(R.id.wvRules)
            wvRules?.let { setHtmlRules(it, description) }
        }

        infoDialog?.findViewById<View>(R.id.tv_sub_title)?.visibility = View.GONE
        infoDialog?.findViewById<View>(R.id.fast_click_payment)?.visibility = View.GONE
        infoDialog?.findViewById<View>(R.id.btn_payment)?.visibility = View.GONE

        val btnAction = infoDialog?.findViewById<Button>(R.id.btn_activate)
        val etPromoCode = infoDialog?.findViewById<PromoCodeField>(R.id.et_promo_code)
        if (promotion.showInput == true) {
            etPromoCode?.visibility = View.VISIBLE
            etPromoCode?.listener = object : PromoCodeField.PromoCodeListener {
                override fun onPromoCodeEntered(promoCode: String) {
                    if (User.State.PLAYER == Settings.get().userState) {
                        viewModel.tryBonusPromoCode(-1, etPromoCode?.text.toString())
                    } else {
                        openRegisterScreen()
                        closeInfoDialog()
                    }
                }
            }
        } else {
            btnAction?.visibility = View.VISIBLE
            btnAction?.text = promotion.buttonName
            btnAction?.setOnClickListener {
                (activity as MainActivity).processDeepLinkUrl(ApoloConfig.getFullUrl(promotion.buttonUrl), UrlSource.BONUS)
                closeInfoDialog()
            }
        }

        infoDialog?.show()
    }

    private fun getInfoDialogSubTitle(bonus: Bonus) : String {
        val formattedDepositAmount = GeneralTools.formatBalance(Settings.get().userCurrencyCode, bonus.depositAmount)
        if ((bonus.isCalculatedByDepositSum != null && bonus.isCalculatedByDepositSum!!) ||
                bonus.bonusEvent == PromotionItem.BonusEvent.END_ACTIVATION_TIME) {
            return getString(R.string.on_amount_deposits_from, formattedDepositAmount)
        } else {
            return when (bonus.bonusEvent) {
                PromotionItem.BonusEvent.MIN_DEPOSIT -> getString(R.string.on_deposit_from, formattedDepositAmount)
                PromotionItem.BonusEvent.FIRST_DEPOSIT -> getString(R.string.first_deposit_from, formattedDepositAmount)
                PromotionItem.BonusEvent.EMAIL_CONFIRMATION -> getString(R.string.for_email_confirmation)
                PromotionItem.BonusEvent.PHONE_CONFIRMATION -> getString(R.string.for_phone_confirmation)
                else -> ""
            }
        }
    }

    private fun initDialogInfoPayments(dialog: Dialog, bonus: Bonus) {
        val fastClickPaymentSystem: FastClickPaymentSystem? = (activity as MainActivity).fastClickPaymentSystem
        val fastClickPaymentView: FastClickPaymentView = dialog.findViewById(R.id.fast_click_payment)
        val btnPayment: Button = dialog.findViewById(R.id.btn_payment)
        if (bonus.isActivated == null ||
                (bonus.isActivated == false)) {
            fastClickPaymentView.visibility = View.GONE
            btnPayment.visibility = View.GONE
        } else if (PromotionItem.BonusEvent.EMAIL_CONFIRMATION == bonus.bonusEvent ||
                PromotionItem.BonusEvent.PHONE_CONFIRMATION == bonus.bonusEvent) {
            fastClickPaymentView.visibility = View.GONE
            btnPayment.visibility = View.VISIBLE
            btnPayment.text = context?.getString(R.string._profile) ?: ""
            btnPayment.setOnClickListener {
                (activity as MainActivity).openProfileScreen()
                dialog.cancel()
            }
        } else if (fastClickPaymentSystem != null) {
            fastClickPaymentView.visibility = View.VISIBLE
            fastClickPaymentView.setButtonFastPaymentClickListener { isRebill: Boolean, amount: Int ->
                if (bonus.id != null && isRebill) {
                    viewModel.makeRebill(bonus.id!!, amount)
                    fastClickPaymentView.showLoader()
                } else {
                    viewModel.getFastPaymentUrl(amount)
                    dialog.dismiss()
                }
            }
            fastClickPaymentView.setFastClickPaymentSystem(fastClickPaymentSystem)
            btnPayment.visibility = View.GONE
        } else {
            fastClickPaymentView.visibility = View.GONE
            btnPayment.text = context?.getString(R.string._replenish) ?: ""
            btnPayment.visibility = View.VISIBLE
            btnPayment.setOnClickListener {
                (activity as MainActivity).openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
                dialog.cancel()
            }
        }
    }

    private fun initDialogInfoPrizes(bonus: Bonus, container: LinearLayout) {
        val prizesInfo = context?.let { PrizeUtil().getPrizesInfo(it, bonus) }
        prizesInfo?.forEachIndexed { index, element ->
            container.addView(initPrizeView(element.name, element.value, index % 2 == 0))
        }
    }

    private fun initPrizeView(title: String, value: String, greyBackground : Boolean): View {
        val inflater: LayoutInflater = context?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val prizeView: View = inflater.inflate(R.layout.item_prize, null)
        val tvTitle: TextView = prizeView.findViewById(R.id.tv_title)
        tvTitle.text = title
        val tvValue: TextView = prizeView.findViewById(R.id.tv_value)
        tvValue.text = value
        if (greyBackground && context != null) {
            prizeView.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.light_prize_item_bg))
        }
        return prizeView
    }

    private fun setHtmlRules(webViewRules: WebView, htmlRules: String) {
        val CSS_ASSET_FILE_NAME = "bonusRules.css"
        webViewRules.setBackgroundColor(Color.TRANSPARENT)
        webViewRules.settings.apply {
            javaScriptEnabled = true
        }
        webViewRules.webViewClient = object : WebViewClient() {

            override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
                (activity as MainActivity).processDeepLinkUrl(url, UrlSource.BONUS)
                closeInfoDialog()
                return true
            }

            @RequiresApi(Build.VERSION_CODES.KITKAT)
            override fun onPageFinished(view: WebView, url: String) {
                val css = context?.readTextFromAsset(CSS_ASSET_FILE_NAME)
                val js = "var style = document.createElement('style'); style.innerHTML = `$css`; document.head.appendChild(style);"
                webViewRules.evaluateJavascript(js, null)
                webViewRules.visibility = View.VISIBLE
                super.onPageFinished(view, url)
            }
        }

        webViewRules.loadDataWithBaseURL(ApoloConfig.BASE_URL, htmlRules, "text/html", "UTF-8", "")
    }

    private fun closeInfoDialog() {
        if (infoDialog != null && infoDialog?.isShowing == true) {
            infoDialog?.cancel();
        }
    }

    private fun showInfoDialogPromoCodeError(error: String) {
        if (infoDialog != null && infoDialog?.isShowing == true) {
            val etPromoCode = infoDialog?.findViewById<PromoCodeField>(R.id.et_promo_code)
            etPromoCode?.showErrorMessage(error)
        }
    }

    private fun infoDialogFastClickPaymentViewShowSuccessfulRebillingMessage() {
        if (infoDialog != null && infoDialog!!.isShowing) {
            val fastClickPaymentView: FastClickPaymentView = infoDialog!!.findViewById(R.id.fast_click_payment)
            fastClickPaymentView.showSuccessfulRebillingMessage()
        }
    }

    private fun infoDialogFastClickPaymentViewShowHideLoader() {
        if (infoDialog != null && infoDialog!!.isShowing) {
            val fastClickPaymentView: FastClickPaymentView = infoDialog!!.findViewById(R.id.fast_click_payment)
            fastClickPaymentView.hideLoader()
        }
    }

    private fun infoDialogUpdateFastClickPaymentSystem(fastClickPaymentSystem: FastClickPaymentSystem) {
        if (infoDialog != null && infoDialog!!.isShowing) {
            val fastClickPaymentView: FastClickPaymentView = infoDialog!!.findViewById(R.id.fast_click_payment)
            fastClickPaymentView.setFastClickPaymentSystem(fastClickPaymentSystem)
        }
    }

    private fun showDeactivationWarningDialog(context: Context, bonusId: Int, bonusName: String) {
        val builder = AlertDialog.Builder(context, R.style.DeactivateBonusAlertDialogStyle)
        builder.setCancelable(false)
        builder.setMessage(getString(R.string.deactivation_bonus_warning_no_available))

        builder.setPositiveButton(getString(R.string.deactivation_dialog_positive_button)) { dialog: DialogInterface?, which: Int ->
            viewModel.bonusDeactivate(bonusId.toString())
        }
        builder.setNegativeButton(getString(R.string.deactivation_dialog_negative_button)) { dialog: DialogInterface?, which: Int -> }

        builder.show()
    }

    private fun showActivationWarningDialog(context: Context, bonusId: Int, promoCode: String) {
        val builder = AlertDialog.Builder(context, R.style.DeactivateBonusAlertDialogStyle)
        builder.setCancelable(false)
        builder.setMessage(getString(R.string.deactivation_bonus_warning_no_available))

        builder.setPositiveButton(getString(R.string.deactivation_dialog_positive_button)) { dialog: DialogInterface?, which: Int ->
            viewModel.bonusActivate(bonusId.toString(), promoCode)
        }
        builder.setNegativeButton(getString(R.string.deactivation_dialog_negative_button)) { dialog: DialogInterface?, which: Int -> }

        builder.show()
    }

    private fun showLoader() {
        binding?.apply {
            ivLoader.visibility = View.VISIBLE
            val animation = AnimationUtils.loadAnimation(context, R.anim.rotation_loader)
            ivLoader.startAnimation(animation)
        }
    }

    private fun hideLoader() {
        binding?.apply {
            ivLoader.clearAnimation()
            ivLoader.visibility = View.GONE
        }
    }

    fun balanceUpdated() {
        if (viewModel.isRebilling) {
            viewModel.isRebilling = false
            promotionsAdapter.fastClickPaymentViewShowSuccessfulRebillingMessage()
            infoDialogFastClickPaymentViewShowSuccessfulRebillingMessage()
        }
    }

    fun updateFastClickPaymentSystem(fastClickPaymentSystem: FastClickPaymentSystem) {
        promotionsAdapter.updateFastClickPaymentSystem(fastClickPaymentSystem)
        infoDialogUpdateFastClickPaymentSystem(fastClickPaymentSystem)
        showLoader()
        viewModel.getBonuses()
    }

    fun updateBonuses() {
        showLoader()
        viewModel.getBonuses()
    }

    private fun openRegisterScreen() {
        if (activity != null && activity is MainActivity) {
            (activity as MainActivity).openEnterScreen(false, Constants.DEEP_LINK_BONUSES)
        }
    }

    private fun showMessage(message: String) {
        (activity as MainActivity).showMessage(message)
    }

    private fun scrollToFocusedPromoCodeCard(keyboardHeight: Int) {
        context?.apply {
            val offset = GeneralTools.getScreenHeight(this) +
                    GeneralTools.getNavBarHeight(this) -
                    keyboardHeight -
                    resources.getDimensionPixelSize(R.dimen.permanent_promotion_card_default_height)
            promotionsLayoutManager?.scrollToPositionWithOffset(promoCodeFocusedPosition, offset)
            promoCodeFocusedPosition = -1
        }
    }

}
