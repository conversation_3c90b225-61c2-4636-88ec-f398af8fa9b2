package com.abrand.custom;

import android.text.TextUtils;

import androidx.fragment.app.testing.FragmentScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.abrand.custom.data.entity.AuthResponse;
import com.abrand.custom.fragment.BaseTransaction;
import com.abrand.custom.interfaces.FastClickPaymentSystemTarget;
import com.abrand.custom.interfaces.GenericTarget;
import com.abrand.custom.interfaces.GetInitialViewerDataTarget;
import com.abrand.custom.interfaces.GetProfileTarget;
import com.abrand.custom.interfaces.GetUrlTarget;
import com.abrand.custom.interfaces.TransactionsTarget;
import com.abrand.custom.network.ApolloProcessor;
import com.abrand.custom.network.ApolloProcessorKt;
import com.abrand.custom.presenter.FieldsErrorsHolder;
import com.abrand.custom.type.PaymentUrlDirection;
import com.abrand.custom.ui.enter.EnterFragment;
import com.apollographql.apollo3.exception.ApolloException;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.List;

@RunWith(AndroidJUnit4.class)
public class ProfileInstrumentedTest {
    private final String[] error              = {null};
    private final int      RESPONSE_TIME      = 5_000;
    private final int      RESPONSE_TIME_LONG = 10_000;

    //***** For testing FastPaymentUrl must be attached cashbox payment and not attached card otherwise it's not available

    @Before
    public void setUp() {
//        FragmentScenario.launch(EnterFragment.class, null, R.style.AppTheme, null);
//
//        ApolloProcessor.getCaptchas(false, new GetCaptchaTarget() {
//            @Override
//            public void onSuccess(boolean isCaptchaEnabled, String captchaId, boolean isReCaptchaEnabled, String reCaptchaId) {
//                if ( isCaptchaEnabled ) {
//                    error[0] = "Captcha enabled, can't login";
//                } else if ( isReCaptchaEnabled ) {
//                    error[0] = "ReCaptcha enabled, can't login";
//                } else {
//                    login();
//                }
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = "getCaptchas: " + e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    private void login() {
        // TODO: 28.10.2022 rewrite to Kt version
//        final String email    = "<EMAIL>";
//        final String password = "qweqwe1";
//
//        ApolloProcessor.login(email, password, "", "",
//                "", new GenericTarget<AuthResponse>() {
//                    @Override
//                    public void onSuccess(@Nullable AuthResponse authResponse) {
//                        if ( TextUtils.isEmpty(userId) ) {
//                            error[0] = "success response but empty userId";
//                        }
//                    }
//
//                    @Override
//                    public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                        error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//                    }
//
//                    @Override
//                    public void onFailure(ApolloException e) {
//                        error[0] = e.toString();
//                    }
//                });
    }

    @Test
    public void getProfile() {
        ApolloProcessorKt.getProfile(new GetProfileTarget() {
            @Override
            public void onSuccess(GetProfileQuery.Profile profile) {
                if ( profile == null ) {
                    error[0] = "profile is empty";
                } else if ( TextUtils.isEmpty(profile.getUserName()) ) {
                    error[0] = "userName is empty";
                } else if ( TextUtils.isEmpty(profile.getEmail()) ) {
                    error[0] = "email is empty";
                }
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }

            @Override
            public void onViewerNull() {
                error[0] = "onViewerNull";
            }
        });

        waitResponseAndAssertFailIfError();
    }

    @Test
    public void getPaymentUrlIn() {
        ApolloProcessorKt.getPaymentUrl(PaymentUrlDirection.IN, "test_ref", new GetUrlTarget() {
            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                //NOP
            }

            @Override
            public void onSuccess(String url) {
                if ( TextUtils.isEmpty(url) ) {
                    error[0] = "payment url is empty";
                }
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }

            @Override
            public void onViewerNull() {
                error[0] = "onViewerNull";
            }
        });

        waitResponseAndAssertFailIfError();
    }

    @Test
    public void getPaymentUrlOut() {
        ApolloProcessorKt.getPaymentUrl(PaymentUrlDirection.OUT, "test_ref", new GetUrlTarget() {
            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {

            }

            @Override
            public void onSuccess(String url) {
                if ( TextUtils.isEmpty(url) ) {
                    error[0] = "payment url is empty";
                }
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }

            @Override
            public void onViewerNull() {
                error[0] = "onViewerNull";
            }
        });

        waitResponseAndAssertFailIfError();
    }

    @Test
    public void changeProfileName() {
        int    randomNumber = (int) (Math.random() * 100 + 1);
        String newUserName  = "user" + randomNumber;
        ApolloProcessorKt.userChangeProfile(newUserName, null, null, null, "",
                new GenericTarget<Boolean>() {
                    @Override
                    public void onSuccess(@Nullable Boolean isSuccess) {
                        if ( isSuccess ) {
                            checkProfileName(newUserName);
                        } else {
                            error[0] = "change profile name failed";
                        }
                    }

                    @Override
                    public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                        error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
                    }

                    @Override
                    public void onFailure(ApolloException e) {
                        error[0] = e.toString();
                    }
                });

        waitResponseAndAssertFailIfError();
    }

    public void checkProfileName(String newUserName) {
        ApolloProcessorKt.getProfile(new GetProfileTarget() {
            @Override
            public void onSuccess(GetProfileQuery.Profile profile) {
                if ( profile == null ) {
                    error[0] = "profile is empty";
                } else if ( TextUtils.isEmpty(profile.getUserName()) ) {
                    error[0] = "userName is empty";
                } else if ( !profile.getUserName().equals(newUserName) ) {
                    error[0] = "newUserName is incorrect";
                }
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }

            @Override
            public void onViewerNull() {
                error[0] = "onViewerNull";
            }
        });

        waitResponseAndAssertFailIfError();
    }

    @Test
    public void getBalanceAndMailAndFastClickPaymentSystemTest() {
        ApolloProcessorKt.getInitialViewerData(new GetInitialViewerDataTarget() {
            @Override
            public void onSuccess(@NotNull GetInitialViewerDataQuery.Viewer viewer) {
                if ( viewer.getWallet() == null ) {
                    error[0] = "wallet is empty";
                } else if ( viewer.getWallet().getCurrency() == null
                        || viewer.getWallet().getCurrency().getCode() == null
                        || viewer.getWallet().getCurrency().getSymbol() == null ) {
                    error[0] = "wallet currency is null";
                } else if ( viewer.getWallet().getBalance() == null ) {
                    error[0] = "wallet balance is empty";
                } else if ( viewer.getWallet().getRealBalance() == null ) {
                    error[0] = "wallet real balance is empty";
                } else if ( viewer.getWallet().getBonusBalance() == null ) {
                    error[0] = "wallet bonus balance is empty";
                } else if ( TextUtils.isEmpty(viewer.getProfile().getEmail()) ) {
                    error[0] = "email is empty";
                } else if ( viewer.getFastClickPaymentSystem() == null ) {
                    error[0] = "fastClickPaymentSystem is null";
                } else if ( viewer.getFastClickPaymentSystem().getCurrency() == null ) {
                    error[0] = "fastClickPaymentSystem currency is null";
                } else if ( viewer.getFastClickPaymentSystem().getMinAmount() == null ) {
                    error[0] = "fastClickPaymentSystem minAmount is null";
                } else if ( viewer.getFastClickPaymentSystem().getMaxAmount() == null ) {
                    error[0] = "fastClickPaymentSystem maxAmount is null";
                } else if ( viewer.getFastClickPaymentSystem().getDefaultAmount() == null ) {
                    error[0] = "fastClickPaymentSystem defaultAmount is null";
                }
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }

            @Override
            public void onViewerNull() {
                error[0] = "onViewerNull";
            }
        });

        waitResponseAndAssertFailIfError();
    }

    @Test
    public void getTransactionsAndBalanceTest() {
//        ApolloProcessor.getTransactionsAndBalance(new TransactionsTarget() {
//            @Override
//            public void onSuccess(@NotNull GetTransactionsAndBalanceQuery.Viewer viewer) {
//                BaseTransaction baseTransaction = ((GetTransactionsAndBalanceQuery.AsTransaction)
//                        viewer.transactions.get(0)).fragments().baseTransaction;
//                if ( viewer.wallet == null ) {
//                    error[0] = "wallet is empty";
//                } else if ( viewer.wallet.currency == null || viewer.wallet.currency.code == null ||
//                        viewer.wallet.currency.symbol == null ) {
//                    error[0] = "wallet currency is null";
//                } else if ( viewer.wallet.realBalance == null ) {
//                    error[0] = "wallet real balance is empty";
//                } else if ( viewer.transactions.isEmpty() ) {
//                    error[0] = "transactions is empty";
//                } else if ( baseTransaction.amount() == null ) {
//                    error[0] = "first transaction amount is empty";
//                } else if ( baseTransaction.currency() == null || baseTransaction.currency().code() == null ) {
//                    error[0] = "first transaction currency is null";
//                } else if ( baseTransaction.date() == null ) {
//                    error[0] = "first transaction date is empty";
//                } else if ( baseTransaction.direction() == null ) {
//                    error[0] = "first transaction direction is empty";
//                } else if ( baseTransaction.status() == null ) {
//                    error[0] = "first transaction status is empty";
//                }
//            }
//
//            @Override
//            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//
//            @Override
//            public void onViewerNull() {
//                error[0] = "onViewerNull";
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    @Test
    public void getFastClickPaymentSystemTest() {
        ApolloProcessorKt.getFastClickPaymentSystem(new FastClickPaymentSystemTarget() {
            @Override
            public void onSuccess(@NotNull GetFastClickPaymentSystemQuery.FastClickPaymentSystem fastClickPaymentSystem) {
                if ( fastClickPaymentSystem == null ) {
                    error[0] = "fastClickPaymentSystem is null";
                } else if ( fastClickPaymentSystem.getCurrency() == null ) {
                    error[0] = "fastClickPaymentSystem currency is null";
                } else if ( fastClickPaymentSystem.getMinAmount() == null ) {
                    error[0] = "fastClickPaymentSystem minAmount is null";
                } else if ( fastClickPaymentSystem.getMaxAmount() == null ) {
                    error[0] = "fastClickPaymentSystem maxAmount is null";
                } else if ( fastClickPaymentSystem.getDefaultAmount() == null ) {
                    error[0] = "fastClickPaymentSystem defaultAmount is null";
                }
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }

            @Override
            public void onViewerNull() {
                error[0] = "onViewerNull";
            }
        });

        waitResponseAndAssertFailIfError();
    }

    @Test
    public void getBonusesTest() {
//        ApolloProcessorKt.getBonuses(new GenericTarget<List<GetBonusesQuery.Item>>() {
//            @Override
//            public void onSuccess(@Nullable List<GetBonusesQuery.Item> bonuses) {
//                if ( bonuses.isEmpty() ) {
//                    error[0] = "bonuses list is empty";
//                }
//
//                for ( GetBonusesQuery.Item bonus : bonuses ) {
//                    if ( bonus.getId() == null ) {
//                        error[0] = "one of the bonuses does not have id";
//                    } else if ( bonus.getName() == null ) {
//                        error[0] = "one of the bonuses does not have name";
//                    } else if ( bonus.getDateEnd() == null ) {
//                        error[0] = "one of the bonuses does not have date end";
//                    }
//                }
//            }
//
//            @Override
//            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    @Test
    public void bonusActivateDeactivateTest() {
//        ApolloProcessorKt.getBonuses(new GenericTarget<List<GetBonusesQuery.Item>>() {
//            @Override
//            public void onSuccess(@Nullable List<GetBonusesQuery.Item> bonuses) {
//                if ( bonuses.isEmpty() ) {
//                    error[0] = "bonuses list is empty";
//                } else {
//                    GetBonusesQuery.Item notPromoBonus = getNotPromoBonus(bonuses);
//                    if ( notPromoBonus == null ) {
//                        error[0] = "not promo bonus not found";
//                    } else {
//                        bonusActivate(String.valueOf(notPromoBonus.getId()));
//                    }
//                }
//            }
//
//            @Override
//            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    private GetBonusesQuery.Item getNotPromoBonus(List<? extends GetBonusesQuery.Item> bonuses) {
//        for ( GetBonusesQuery.Item bonus : bonuses ) {
//            if ( bonus.isPromoCodeRequired != null && !bonus.isPromoCodeRequired ) {
//                return bonus;
//            }
//        }
        return null;
    }

    private void bonusActivate(String bonusId) {
        ApolloProcessorKt.bonusActivate(bonusId, null, new GenericTarget<Integer>() {
            @Override
            public void onSuccess(Integer bonusId) {
                bonusDeactivate(String.valueOf(bonusId));
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }
        });
    }

    private void bonusDeactivate(String bonusId) {
        ApolloProcessorKt.bonusDeactivate(bonusId, new GenericTarget<Boolean>() {
            @Override
            public void onSuccess(@Nullable Boolean isDeactivated) {
                
            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }
        });
    }

    @Test
    public void makeRebillTest() {
        ApolloProcessorKt.makeRebill(150, new GenericTarget<String>() {
            @Override
            public void onSuccess(@NotNull String response) {

            }

            @Override
            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
            }

            @Override
            public void onFailure(ApolloException e) {
                error[0] = e.toString();
            }
        });

        waitResponsLongeAndAssertFailIfError();
    }

    private void waitResponseAndAssertFailIfError() {
        try {
            Thread.sleep(RESPONSE_TIME);
            if ( !TextUtils.isEmpty(error[0]) ) {
                Assert.fail(error[0]);
            }
        } catch ( InterruptedException e ) {
            e.printStackTrace();
        }
    }

    private void waitResponsLongeAndAssertFailIfError() {
        try {
            Thread.sleep(RESPONSE_TIME_LONG);
            if ( !TextUtils.isEmpty(error[0]) ) {
                Assert.fail(error[0]);
            }
        } catch ( InterruptedException e ) {
            e.printStackTrace();
        }
    }

}
