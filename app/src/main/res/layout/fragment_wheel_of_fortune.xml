<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient"
    tools:context=".ui.wheeloffortune.WheelOfFortuneFragment">

    <com.abrand.custom.ui.views.LoaderAnchor
        android:id="@+id/loader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="96dp"
        android:text="@string/wheel_of_fortune_title"
        android:textColor="#FFFFFF"
        android:textSize="24sp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:textColor="#8DA1BD"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/ll_free_spins"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_rules"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/wheel_of_fortune_rules"
        android:textColor="#0097EC"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_description" />

    <LinearLayout
        android:id="@+id/ll_free_spins"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_free_spins"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_title">

        <TextView
            android:id="@+id/tv_free_spins"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="14dp"
            android:textColor="#FFFFFF"
            android:textSize="32sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="14dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/wheel_of_fortune_spins"
            android:textColor="#FFFFFF" />

    </LinearLayout>

    <com.abrand.custom.ui.views.WheelOfFortuneView
        android:id="@+id/wheel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_rules" />

</androidx.constraintlayout.widget.ConstraintLayout>
