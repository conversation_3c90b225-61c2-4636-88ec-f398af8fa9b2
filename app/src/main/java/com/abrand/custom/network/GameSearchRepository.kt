package com.abrand.custom.network

import androidx.lifecycle.MutableLiveData
import com.abrand.custom.data.entity.LoadedGames
import com.abrand.custom.data.entity.LocalGameItem
import com.abrand.custom.interfaces.LoadTarget
import com.abrand.custom.tools.SingleLiveEvent
import com.apollographql.apollo3.exception.ApolloException
import kotlinx.coroutines.CoroutineScope

class GameSearchRepository {
    val gamesByNameLiveData = MutableLiveData<LoadedGames>()
    val popularGamesLiveData = SingleLiveEvent<LoadedGames>()
    val apolloExceptionLiveData = SingleLiveEvent<ApolloException?>()

    fun loadGamesByName(name : String, limit: Int, offset: Int, scope: CoroutineScope) {

        ApolloProcessorKt.loadGamesByName(name, limit, offset, object : LoadTarget {
            override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                gamesByNameLiveData.postValue(LoadedGames(localGameItems, total, offset))
            }

            override fun onFailure(e: ApolloException) {
                apolloExceptionLiveData.postValue(e)
            }
        }, scope)
    }

    fun loadPopularGames(limit: Int, scope: CoroutineScope) {
        ApolloProcessorKt.loadPopularGames(limit, 0, null, null, object : LoadTarget{
            override fun onSuccess(localGameItems: MutableList<LocalGameItem>, total: Int, offset: Int) {
                popularGamesLiveData.postValue(LoadedGames(localGameItems, total, offset))
            }

            override fun onFailure(e: ApolloException) {
                apolloExceptionLiveData.postValue(e)
            }
        }, scope)
    }
}
