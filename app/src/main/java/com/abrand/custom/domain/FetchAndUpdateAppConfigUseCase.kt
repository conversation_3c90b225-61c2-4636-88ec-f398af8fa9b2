package com.abrand.custom.domain

import com.abrand.custom.data.datasources.CrashlyticsProvider
import com.abrand.custom.data.datasources.CrashlyticsProviderImpl
import com.abrand.custom.data.entity.AllDomainsBannedException
import com.abrand.custom.data.entity.appconfig.LocalConfig
import com.abrand.custom.data.repositories.ConfigRepository

class FetchAndUpdateAppConfigUseCase(
    private val configRepository: ConfigRepository = ConfigRepository,
    // add reserve domain provider
    private val crashlyticsProvider: CrashlyticsProvider = CrashlyticsProviderImpl(),
) {
    suspend operator fun invoke(appConfig: LocalConfig): <PERSON><PERSON><PERSON> {
        try {
            configRepository.fetchAppConfig(appConfig)
            return true
        } catch (e: AllDomainsBannedException) {
            crashlyticsProvider.log("Updater domains: ${appConfig.updaterDomains}\n Communication domains: ${appConfig.domains}")
            crashlyticsProvider.recordException(e)
            // process AllBannedDomainsException: add reserve domain and recall
            return false
        } catch (e: Exception) {
            throw e
        }
    }
}
