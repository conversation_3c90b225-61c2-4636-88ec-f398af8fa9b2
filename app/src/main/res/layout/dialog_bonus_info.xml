<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <ImageButton
        android:id="@+id/ib_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:padding="@dimen/size_16"
        android:src="@drawable/ic_promotion_info_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/sv_text"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="42dp"
        android:layout_marginBottom="30dp"
        app:layout_constraintBottom_toTopOf="@+id/bottom_barrier"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/promotion_info_margin_left"
                android:layout_marginRight="@dimen/promotion_info_margin_right"
                android:textColor="#FFFFFF"
                android:textSize="24sp" />

            <TextView
                android:id="@+id/tv_sub_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/promotion_info_margin_left"
                android:layout_marginTop="16dp"
                android:layout_marginRight="@dimen/promotion_info_margin_right"
                android:textColor="#FFFFFF"
                android:textSize="16sp" />

            <com.abrand.custom.ui.views.FastClickPaymentView
                android:id="@+id/fast_click_payment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/promotion_info_margin_left"
                android:layout_marginTop="@dimen/size_16"
                android:layout_marginRight="@dimen/promotion_info_margin_right"
                android:layout_marginBottom="@dimen/size_16" />

            <Button
                android:id="@+id/btn_payment"
                style="@style/ButtonStyle"
                android:layout_width="176dp"
                android:layout_height="48dp"
                android:layout_marginLeft="@dimen/promotion_info_margin_left"
                android:layout_marginTop="@dimen/size_16"
                android:layout_marginRight="@dimen/promotion_info_margin_right"
                android:layout_marginBottom="@dimen/size_16"
                android:background="@drawable/btn_promotion_bg"
                android:text="@string/_replenish"
                app:layout_constraintStart_toStartOf="parent"
                tools:visibility="gone" />

            <LinearLayout
                android:id="@+id/container_prizes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:orientation="vertical" />

            <WebView
                android:id="@+id/wvRules"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="16dp"
                android:scrollbars="none"
                android:visibility="gone" />

        </LinearLayout>

    </ScrollView>

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/bottom_barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="top"
        app:constraint_referenced_ids="et_promo_code,btn_activate" />

    <com.abrand.custom.ui.views.PromoCodeField
        android:id="@+id/et_promo_code"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/promotion_info_margin_left"
        android:layout_marginEnd="@dimen/promotion_info_margin_right"
        android:layout_marginBottom="30dp"
        android:visibility="gone"
        app:hint="@string/enter_promo_code"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <Button
        android:id="@+id/btn_activate"
        style="@style/ButtonBlueStyle"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginLeft="@dimen/promotion_info_margin_left"
        android:layout_marginRight="@dimen/promotion_info_margin_right"
        android:layout_marginBottom="30dp"
        android:text="@string/bonus_activate"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
