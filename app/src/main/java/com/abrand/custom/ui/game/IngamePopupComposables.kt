package com.abrand.custom.ui.game

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.runtime.Composable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.abrand.custom.R
import com.abrand.custom.ui.bonusbalances.RegularText

@Composable
fun PopupContent(
    id: Int?,
    value: String = "",
    onDismiss: () -> Unit,
) {
    val background = arrayOf(
        0.0f to Color(0xFFFFFFFF),
        1.0f to Color(0xFFE8F5FE)
    )
    val amount = remember { value }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(397.dp)
            .background(Color.Transparent),
        contentAlignment = Alignment.TopCenter
    ) {
        Box(
            modifier = Modifier
                .sizeIn(maxWidth = 397.dp)
                .fillMaxSize()
                .clip(RoundedCornerShape(10.dp))
                .background(brush = Brush.verticalGradient(colorStops = background))
                .padding(horizontal = 11.dp)
                .padding(top = 34.dp), contentAlignment = Alignment.TopCenter
        ) {
            Image(
                imageVector = ImageVector.vectorResource(R.drawable.candies),
                contentDescription = null,
                contentScale = ContentScale.None,
            )
        }

        Box(
            modifier = Modifier.fillMaxSize().padding(12.dp), contentAlignment = Alignment.TopEnd
        ) {
            Image(
                imageVector = ImageVector.vectorResource(R.drawable.ic_unknown_link_close),
                contentDescription = null,
                contentScale = ContentScale.None,
                modifier = Modifier.size(24.dp).clickable {
                    onDismiss()
                }
            )
        }

        Box(
            modifier = Modifier.fillMaxSize()
                .padding(horizontal = 32.dp)
                .padding(top = 40.dp),
            contentAlignment = Alignment.TopCenter
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                RegularText(modifier = Modifier,
                    text = stringResource(R.string.bonus_balance_won_title),
                    fontSize = 24.sp,
                    lineHeight = 32.sp,
                    color = Color.Black,
                )
                Spacer(modifier = Modifier.size(20.dp))
                Image(
                    painterResource(R.drawable.present),
                    contentDescription = null,
                    contentScale = ContentScale.Fit,
                    modifier = Modifier.size(110.dp, 116.dp)
                )
                Text(
                    textAlign = TextAlign.Center,
                    text = buildAnnotatedString {
                        withStyle(style = SpanStyle(
                            color = Color.Black,
                            fontFamily = FontFamily(Font(R.font.roboto_regular, FontWeight.Normal)),
                            fontSize = 12.sp,
                        )
                        ) {
                            append(stringResource(R.string.bonus_balance_won_content_part1))
                        }
                        withStyle(style = SpanStyle(
                            color = Color(0xFF0097EC),
                            fontSize = 16.sp,
                            fontFamily = FontFamily(Font(R.font.roboto_medium, FontWeight.SemiBold))
                        )) {
                            append("\n$amount")
                        }
                        withStyle(style = SpanStyle(
                            color = Color.Black,
                            fontFamily = FontFamily(Font(R.font.roboto_regular, FontWeight.Normal)),
                            fontSize = 12.sp,
                        )
                        ) {
                            append(stringResource(R.string.bonus_balance_won_content_part2))
                        }
                    },
                    lineHeight = 20.sp,
                )
                Spacer(modifier = Modifier.size(20.dp))
                Box(
                    modifier = Modifier
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = ripple(),
                        ) {
                            onDismiss()
                        }
                        .heightIn(min = 48.dp)
                        .widthIn(min = 280.dp, max = 280.dp)
                        .shadow(8.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFF6ED200),
                                    Color(0xFF008E00),
                                )
                            )
                        )
                        .padding(horizontal = 9.dp, vertical = 4.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = stringResource(R.string.bonus_balance_won_ok_button_text),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        lineHeight = 24.sp,
                        color = Color.White,
                        style = TextStyle(
                            shadow = Shadow(
                                color = Color(0x33000000),
                                offset = Offset(0.0f, 2.0f)
                            ),
                            fontSize = 14.sp,
                            fontFamily = FontFamily(Font(R.font.roboto_medium, FontWeight.Medium)),
                            fontWeight = FontWeight.Medium,
                        ),
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun DialogPreview() {
    PopupContent(42, "78900 ₽", {})
}
