package com.abrand.custom.data

class AffDataGenerator {

    companion object {
        private const val adjustIdKey = "adjust_id"

        fun getAffData(adjustId: String?): String? {
            val formattedConfigAffData = getFormattedConfigAffData()
            return if (!adjustId.isNullOrEmpty() && formattedConfigAffData.isNotEmpty()) {
                "$adjustIdKey=$adjustId&$formattedConfigAffData"
            } else if (!adjustId.isNullOrEmpty()) {
                "$adjustIdKey=$adjustId"
            } else if (formattedConfigAffData.isNotEmpty()) {
                formattedConfigAffData
            } else {
                null
            }
        }

        private fun getFormattedConfigAffData(): String {
            val stringBuilder = java.lang.StringBuilder()
            val configAffData = Settings.get().localConfig?.affData

            configAffData?.let {
                for ((key, value) in configAffData) {
                    if (stringBuilder.isNotEmpty()) {
                        stringBuilder.append("&")
                    }
                    stringBuilder.append("$key=$value")
                }
            }

            return stringBuilder.toString()
        }
    }
}
