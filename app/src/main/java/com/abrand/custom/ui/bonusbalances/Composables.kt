package com.abrand.custom.ui.bonusbalances

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.PlainTooltip
import androidx.compose.material3.Text
import androidx.compose.material3.TooltipBox
import androidx.compose.material3.TooltipDefaults
import androidx.compose.material3.rememberTooltipState
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.abrand.custom.R
import com.abrand.custom.ui.profile.ActiveIndicator
import com.abrand.custom.ui.profile.CountdownTimer
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.util.Locale

@Composable
fun BonusBalanceCardContainer(
    item: BonusBalanceUi,
    onEvent: (BonusBalancesUiEvent) -> Unit,
    scrollToStart: () -> Job,
    getActiveBonusAmount: () -> String?,
    activateBtnOnSeparateRow: Boolean = false
) {
    var cardState: CardState by remember { mutableStateOf(CardState.Front) }
    var prevCardState: CardState by remember { mutableStateOf(CardState.Front) }
    var size by remember { mutableStateOf(IntSize.Zero) }
    val onDeleteClick = {
        prevCardState = cardState
        cardState = CardState.Delete
    }
    val activateEvent = {
        onEvent(BonusBalancesUiEvent.ActivateBonusBalance(item.id))
        scrollToStart.invoke()
        cardState = CardState.Front
    }
    val deleteEvent = {
        onEvent(BonusBalancesUiEvent.DeleteBonusBalance(item.id))
        cardState = CardState.Front
    }
    val onActivateClick = {
        val activeAmount = getActiveBonusAmount.invoke()

        if (activeAmount != null) {
            prevCardState = cardState
            cardState = CardState.Activate(activeAmount)
        } else {
            activateEvent.invoke()
        }
    }
    val rotation = animateFloatAsState(
        targetValue = cardState.angle,
        animationSpec = tween(500)
    )

    Box(
        modifier = Modifier.graphicsLayer {
            rotationY = rotation.value
            cameraDistance = 12f * density
        }
    ) {
        if (
            (cardState == CardState.Delete || prevCardState == CardState.Delete) && rotation.value > 90f
        ) {
            val width = with(LocalDensity.current) { size.width.toDp() }
            val height = with(LocalDensity.current) { size.height.toDp() }
            DeleteBonusBalanceCard(
                modifier = Modifier
                    .size(width, height)
                    .graphicsLayer { rotationY = 180f },
                cancelAction = {
                    prevCardState = cardState
                    cardState = CardState.Front
                },
                deleteAction = deleteEvent
            )
        } else if (
            (cardState is CardState.Activate || prevCardState is CardState.Activate) && rotation.value > 90f
        ) {
            val width = with(LocalDensity.current) { size.width.toDp() }
            val height = with(LocalDensity.current) { size.height.toDp() }
            ActivateBonusBalanceCard(
                modifier = Modifier
                    .size(width, height)
                    .graphicsLayer { rotationY = 180f },
                cancelAction = {
                    prevCardState = cardState
                    cardState = CardState.Front
                },
                activateAction = activateEvent,
                activeBonusAmount = (cardState as? CardState.Activate)?.activeBonusAmount
                    ?: (prevCardState as? CardState.Activate)?.activeBonusAmount
                    ?: ""
            )
        } else {
            BonusBalanceCard(
                Modifier.onGloballyPositioned { coordinates -> size = coordinates.size },
                item,
                onDeleteClick, onActivateClick,
                activateBtnOnSeparateRow,
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BonusBalanceCard(
    modifier: Modifier = Modifier,
    item: BonusBalanceUi,
    onDelete: () -> Unit,
    onActivate: () -> Unit,
    activateBtnOnSeparateRow: Boolean = false,
) {
    val activeBG = arrayOf(
        0.0f to Color(0xFF4582DB),
        1.0f to Color(0xFF2D4871)
    )
    val inactiveBG = arrayOf(
        0.0f to Color(0xFF53A59E),
        1.0f to Color(0xFF11705B)
    )
    val background = if (item.isActive) { activeBG } else { inactiveBG }
    val tooltipState = rememberTooltipState()
    val scope = rememberCoroutineScope()

    Box(
        modifier = modifier
            .defaultMinSize(minHeight = 212.dp)
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(brush = Brush.verticalGradient(colorStops = background))
            .padding(16.dp)
    ) {
        Column {
            Row(modifier = Modifier.padding(bottom = 12.dp)) {
                Image(imageVector = ImageVector.vectorResource(R.drawable.trash),
                    contentDescription = null,
                    modifier = Modifier.clickable {
                        onDelete.invoke()
                    },
                )
                item.wageringMaxTransferAmount?.let { wageringMaxTransferAmount ->
                    TooltipBox(
                        positionProvider = TooltipDefaults.rememberPlainTooltipPositionProvider(),
                        tooltip = {
                            PlainTooltip(
                                caretSize = TooltipDefaults.caretSize,
                                containerColor = Color(0XFF55BEF9),
                            ) {
                                RegularText(
                                    modifier = Modifier.padding(8.dp),
                                    text = "Макс трансфер:\n$wageringMaxTransferAmount",
                                    fontSize = 12.sp, lineHeight = 20.sp,
                                    color = Color(0XFFFFFFFF),
                                )
                            }
                        },
                        state = tooltipState
                    ) {
                        Image(imageVector = ImageVector.vectorResource(R.drawable.info),
                            null,
                            Modifier
                                .clickable { scope.launch { tooltipState.show() } }
                                .padding(start = 8.dp)
                        )
                    }
                }
                Spacer(Modifier.weight(1f))
                ActiveIndicator(item.isActive)
            }
            MediumText(
                modifier = Modifier.padding(bottom = 12.dp),
                text = item.amount,
                fontSize = 16.sp, lineHeight = 24.sp,
            )
            Row {
                RegularText(
                    text = item.wageringCurrent,
                    fontSize = 14.sp, lineHeight = 24.sp,
                    color = Color(0XFFFDBB2C),
                )
                Spacer(Modifier.weight(1f))
                RegularText(
                    text = item.wageringTarget,
                    fontSize = 14.sp, lineHeight = 24.sp,
                )
            }
            LinearProgressIndicator(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(2.dp))
                    .padding(bottom = 4.dp),
                progress = { item.wageringCompletePercent.toFloat() },
                trackColor = Color(0x29FFFFFF),
                color = Color(0x99FFFFFF),
                gapSize = 0.dp,
                strokeCap = StrokeCap.Square,
                drawStopIndicator = {}
            )
            Text(
                text = buildAnnotatedString {
                    withStyle(style = SpanStyle(
                        color = if (item.wager != null && item.wager != 0) Color(0xCCFFFFFF) else Color.Transparent,
                        fontFamily = FontFamily(Font(R.font.roboto_regular, FontWeight.Normal))
                    )
                    ) {
                        append("Вейджер: ")
                    }
                    withStyle(style = SpanStyle(
                        color = if (item.wager != null && item.wager != 0) Color.White else Color.Transparent,
                        fontFamily = FontFamily(Font(R.font.roboto_medium, FontWeight.Medium))
                    )
                    ) {
                        append("X${item.wager}")
                    }
                },
                fontSize = 12.sp,
                lineHeight = 20.sp
            )

            Row(
                modifier = Modifier.padding(top = 12.dp),
                verticalAlignment = Alignment.Bottom,
            ) {
                Column {
                    MediumText(
                        text = "До завершения",
                        fontSize = 12.sp,
                        lineHeight = 24.sp,
                        color = Color(0xCCFFFFFF)
                    )
                    CountdownTimer(item.wageringExpiredAt) { time ->
                        MediumText(
                            text = if (time == null || time.isNegative()) {
                                "--/--"
                            } else if (time.inWholeDays >= 1) {
                                time.toComponents { days, hours, _, _, _ ->
                                    "${pluralStringResource(R.plurals.days, days.toInt(), days.toInt())} $hours ч"
                                }
                            } else {
                                time.toComponents { _, hours, minutes, seconds, _ ->
                                    String.format(
                                        Locale.getDefault(),
                                        "%02d:%02d:%02d",
                                        hours,
                                        minutes,
                                        seconds
                                    )
                                }
                            },
                            fontSize = 14.sp,
                            lineHeight = 24.sp,
                            color = Color.White
                        )
                    }
                }
                Spacer(Modifier.weight(1f))
                if (!item.isActive && !activateBtnOnSeparateRow) {
                    Box(
                        modifier = Modifier
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = ripple(),
                                true
                            ) {
                                onActivate.invoke()
                            }
                            .defaultMinSize(minWidth = 112.dp, minHeight = 32.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .background(
                                brush = Brush.verticalGradient(
                                    colors = listOf(
                                        Color(0xFF00C6FB),
                                        Color(0xFF005BEA)
                                    )
                                )
                            )
                            .padding(horizontal = 9.dp, vertical = 4.dp),
                        contentAlignment = Alignment.Center,
                    ) {
                        Text(
                            text = "Активировать",
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            lineHeight = 24.sp,
                            color = Color.White,
                            style = TextStyle(
                                shadow = Shadow(
                                    color = Color(0x33000000),
                                    offset = Offset(0.0f, 2.0f)
                                ),
                                fontSize = 14.sp,
                                fontFamily = FontFamily(Font(R.font.roboto_medium, FontWeight.Medium)),
                                fontWeight = FontWeight.Medium,
                            ),
                        )
                    }
                }
            }

            if (activateBtnOnSeparateRow && !item.isActive) {
                Box(
                    modifier = Modifier
                        .padding(top = 8.dp)
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = ripple(),
                            true
                        ) {
                            onActivate.invoke()
                        }
                        .defaultMinSize(minWidth = 112.dp, minHeight = 32.dp)
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(4.dp))
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFF00C6FB),
                                    Color(0xFF005BEA)
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = "Активировать",
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        lineHeight = 24.sp,
                        color = Color.White,
                        style = TextStyle(
                            shadow = Shadow(
                                color = Color(0x33000000),
                                offset = Offset(0.0f, 2.0f)
                            ),
                            fontSize = 14.sp,
                            fontFamily = FontFamily(Font(R.font.roboto_medium, FontWeight.Medium)),
                            fontWeight = FontWeight.Medium,
                        ),
                    )
                }
            }
        }
    }
}

@Composable
fun DeleteBonusBalanceCard(
    modifier: Modifier = Modifier,
    cancelAction: () -> Unit = {},
    deleteAction: () -> Unit = {},
) {
    DialogCard(
        modifier = modifier,
        title = "Вы точно хотите удалить бонус?",
        description = "После удаления баланс бонуса невозможно восстановить.",
        leftBtnText = "Нет",
        leftBtnAction = cancelAction,
        rightBtnText = "Да",
        rightBtnAction = deleteAction,
    )
}

@Composable
fun ActivateBonusBalanceCard(
    modifier: Modifier = Modifier,
    cancelAction: () -> Unit = {},
    activateAction: () -> Unit = {},
    activeBonusAmount: String,
) {
    DialogCard(
        modifier = modifier,
        title = "У вас есть активный бонус\n$activeBonusAmount",
        description = "Баланс активного бонуса будет автоматически обнулён.",
        leftBtnText = "Ок",
        rightBtnText = "Отмена",
        leftBtnAction = activateAction,
        rightBtnAction = cancelAction,
    )
}

@Composable
fun DialogCard(
    modifier: Modifier = Modifier,
    title: String,
    description: String,
    leftBtnText: String,
    leftBtnAction: () -> Unit,
    rightBtnText: String,
    rightBtnAction: () -> Unit,
) {
    val background = arrayOf(
        0.0f to Color(0xFF886B7F),
        1.0f to Color(0xFF6E5276)
    )

    Box(
        modifier = modifier
            .defaultMinSize(minHeight = 212.dp)
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(brush = Brush.verticalGradient(colorStops = background))
            .padding(16.dp), contentAlignment = Alignment.Center,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            MediumText(
                text = title,
                fontSize = 16.sp,
                lineHeight = 24.sp,
                textAlign = TextAlign.Center,
            )
            Spacer(Modifier.height(8.dp))
            RegularText(
                text = description,
                fontSize = 12.sp,
                lineHeight = 20.sp,
                textAlign = TextAlign.Center,
            )
            Spacer(Modifier.weight(1f))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceAround
            ) {
                Box(
                    modifier = Modifier
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = ripple(),
                            true
                        ) {
                            leftBtnAction.invoke()
                        }
                        .heightIn(min = 32.dp)
                        .widthIn(min = 92.dp, max = 240.dp)
                        .weight(1f)
                        .shadow(8.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFF6ED200),
                                    Color(0xFF008E00),
                                )
                            )
                        )
                        .padding(horizontal = 9.dp, vertical = 4.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = leftBtnText,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        lineHeight = 24.sp,
                        color = Color.White,
                        style = TextStyle(
                            shadow = Shadow(
                                color = Color(0x33000000),
                                offset = Offset(0.0f, 2.0f)
                            ),
                            fontSize = 14.sp,
                            fontFamily = FontFamily(Font(R.font.roboto_medium, FontWeight.Medium)),
                            fontWeight = FontWeight.Medium,
                        ),
                    )
                }
                Spacer(Modifier.width(8.dp))
                Box(
                    modifier = Modifier
                        .clickable(
                            interactionSource = remember { MutableInteractionSource() },
                            indication = ripple(),
                            true
                        ) {
                            rightBtnAction.invoke()
                        }
                        .heightIn(min = 32.dp)
                        .widthIn(min = 92.dp, max = 240.dp)
                        .weight(1f)
                        .shadow(8.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    Color(0xFF6ED200),
                                    Color(0xFF008E00),
                                )
                            )
                        )
                        .padding(horizontal = 9.dp, vertical = 4.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = rightBtnText,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        lineHeight = 24.sp,
                        color = Color.White,
                        style = TextStyle(
                            shadow = Shadow(
                                color = Color(0x33000000),
                                offset = Offset(0.0f, 2.0f)
                            ),
                            fontSize = 14.sp,
                            fontFamily = FontFamily(Font(R.font.roboto_medium, FontWeight.Medium)),
                            fontWeight = FontWeight.Medium,
                        ),
                    )
                }
            }
        }
    }
}

@Composable
fun NoBonusesView(modifier: Modifier = Modifier, openPromotions: () -> Unit) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            RegularText(
                modifier = Modifier,
                text = "У вас нет бонусов",
                fontSize = 14.sp,
                lineHeight = 24.sp,
            )
            Spacer(Modifier.height(16.dp))
            AvailablePromotions(openPromotions = openPromotions)
        }
    }
}

@Composable
fun AvailablePromotions(modifier: Modifier = Modifier, openPromotions: () -> Unit) {
    Box(
        modifier = modifier
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = ripple(),
                true
            ) {
                openPromotions.invoke()
            }
            .defaultMinSize(minWidth = 134.dp, minHeight = 32.dp)
            .clip(RoundedCornerShape(4.dp))
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF00C6FB),
                        Color(0xFF005BEA)
                    )
                )
            )
            .padding(horizontal = 9.dp, vertical = 4.dp),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = stringResource(R.string.bonus_balances_available_promo_btn_text),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            lineHeight = 24.sp,
            color = Color.White,
            style = TextStyle(
                shadow = Shadow(
                    color = Color(0x33000000),
                    offset = Offset(0.0f, 2.0f)
                ),
                fontSize = 14.sp,
                fontFamily = FontFamily(Font(R.font.roboto_medium, FontWeight.Medium)),
                fontWeight = FontWeight.Medium,
            ),
        )
    }
}

@Composable
fun RegularText(
    modifier: Modifier = Modifier,
    text: String,
    fontSize: TextUnit = TextUnit.Unspecified,
    lineHeight: TextUnit = TextUnit.Unspecified,
    color: Color = Color.White,
    textAlign: TextAlign = TextAlign.Unspecified,
) {
    Text(
        modifier = modifier,
        text = text,
        fontSize = fontSize,
        lineHeight = lineHeight,
        color = color,
        fontFamily = FontFamily(Font(R.font.roboto_regular, FontWeight.Normal)),
        textAlign = textAlign,
    )
}

@Composable
fun MediumText(
    modifier: Modifier = Modifier,
    text: String,
    fontSize: TextUnit = TextUnit.Unspecified,
    lineHeight: TextUnit = TextUnit.Unspecified,
    color: Color = Color.White,
    textAlign: TextAlign = TextAlign.Unspecified
) {
    Text(
        modifier = modifier,
        text = text,
        fontSize = fontSize,
        lineHeight = lineHeight,
        color = color,
        fontFamily = FontFamily(Font(R.font.roboto_medium, FontWeight.Medium)),
        textAlign = textAlign,
    )
}

sealed class CardState(val angle: Float) {
    data object Front: CardState(0f)
    data object Delete: CardState(180f)
    data class Activate(val activeBonusAmount: String): CardState(180f)
}
