package com.abrand.custom.ui.loyaltyprogram

import android.content.Context
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.GetLoyaltyStatusesQuery
import com.abrand.custom.R
import com.abrand.custom.data.entity.PrizePrivilegeItem
import com.abrand.custom.tools.GeneralTools

class LoyaltyProgramAdapter(var currentLoyaltyStatusId: Int, var currentProgress: Int) : RecyclerView.Adapter<LoyaltyProgramAdapter.LoyaltyProgramVH>() {
    var items = mutableListOf<GetLoyaltyStatusesQuery.LoyaltyStatus>()

    fun setList(newList: MutableList<GetLoyaltyStatusesQuery.LoyaltyStatus>) {
        this.items = newList
        notifyDataSetChanged()
    }

    fun updateProgress(currentProgress : Int) {
        this.currentProgress = currentProgress
        notifyDataSetChanged()
    }

    fun updateLoyaltyStatusId(currentLoyaltyStatusId: Int) {
        this.currentLoyaltyStatusId = currentLoyaltyStatusId
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LoyaltyProgramVH {
        val inflater = LayoutInflater.from(parent.context)
        return LoyaltyProgramVH(inflater, parent)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: LoyaltyProgramVH, position: Int) {
        val loyaltyStatus = items[position]
        holder.bind(loyaltyStatus, position)
    }

    private fun initPrivileges(context: Context, holder: LoyaltyProgramVH,
                               loyaltyStatus: GetLoyaltyStatusesQuery.LoyaltyStatus) {
        holder.rvPrizesPrivileges.visibility = View.VISIBLE
        val prizesPrivileges = mutableListOf<PrizePrivilegeItem>()
        prizesPrivileges.add(PrizePrivilegeItem(PrizePrivilegeItem.ItemType.PRIVILEGE, loyaltyStatus.exchange_rate))
        if (loyaltyStatus.privileges != null) {
            for (privilege in loyaltyStatus.privileges) {
                if (privilege?.description != null) {
                    prizesPrivileges.add(PrizePrivilegeItem(PrizePrivilegeItem.ItemType.PRIVILEGE, privilege?.description!!))
                }
            }
        }

        val isCurrentLoyaltyStatus = loyaltyStatus.id == currentLoyaltyStatusId
        holder.rvPrizesPrivileges.layoutManager = LinearLayoutManager(context)
        holder.rvPrizesPrivileges.adapter = PrizesPrivilegesAdapter(prizesPrivileges, isCurrentLoyaltyStatus)
    }

    private fun initPrivileges(context: Context, holder: LoyaltyProgramVH,
                                loyaltyStatus: GetLoyaltyStatusesQuery.LoyaltyStatus, privileges: Array<String>) {
        holder.rvPrizesPrivileges.visibility = View.VISIBLE
        val prizesPrivileges = mutableListOf<PrizePrivilegeItem>()
        for (privilege in privileges) {
            prizesPrivileges.add(PrizePrivilegeItem(PrizePrivilegeItem.ItemType.PRIVILEGE, privilege))
        }

        val isCurrentLoyaltyStatus = loyaltyStatus.id == currentLoyaltyStatusId
        holder.rvPrizesPrivileges.layoutManager = LinearLayoutManager(context)
        holder.rvPrizesPrivileges.adapter = PrizesPrivilegesAdapter(prizesPrivileges, isCurrentLoyaltyStatus)
    }

    inner class LoyaltyProgramVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_loyalty_program, parent, false)) {
        private var ivLoyaltyStatus = itemView.findViewById<ImageView>(R.id.iv_loyalty_status)
        private var tvTitle = itemView.findViewById<TextView>(R.id.tv_title)
        private var tvPrizes = itemView.findViewById<TextView>(R.id.tv_prizes)
        private var tvExperiencePoints = itemView.findViewById<TextView>(R.id.tv_experience_points)
        var rvPrizesPrivileges: RecyclerView = itemView.findViewById(R.id.rv_prizes_privileges)
        private var progressBar: ProgressBar = itemView.findViewById(R.id.progress_bar)
        private var progressBarTop: ProgressBar = itemView.findViewById(R.id.progress_bar_top)

        fun bind(loyaltyStatus: GetLoyaltyStatusesQuery.LoyaltyStatus, position: Int) {
            ivLoyaltyStatus.setImageDrawable(GeneralTools.getLoyaltyStatusDrawable(itemView.context,
                    loyaltyStatus.id, currentLoyaltyStatusId >= loyaltyStatus.id))
            tvTitle.text = loyaltyStatus.title
            if (loyaltyStatus.startPoints > 0) {
                tvExperiencePoints.text = Html.fromHtml(itemView.context.resources.getString(R.string.experience_points,
                        GeneralTools.formatNumber(loyaltyStatus.startPoints.toString())))
            } else {
                tvExperiencePoints.text = itemView.context.resources.getString(R.string.without_experience)
            }

            when {
                loyaltyStatus.id < currentLoyaltyStatusId -> {
                    tvTitle.setTextColor(ContextCompat.getColor(itemView.context, R.color.loyalty_status_old_title))
                    progressBar.progress = 100
                }
                loyaltyStatus.id == currentLoyaltyStatusId -> {
                    tvTitle.setTextColor(ContextCompat.getColor(itemView.context, R.color.loyalty_status_current_title))
                    progressBar.progress = currentProgress
                }
                else -> {
                    tvTitle.setTextColor(ContextCompat.getColor(itemView.context, R.color.loyalty_status_future_title))
                    progressBar.progress = 0
                }
            }

            setPrizesTitle(loyaltyStatus.prizes)
            hideLastProgressBar(loyaltyStatus.id)
            hideFirstTopProgressBar(loyaltyStatus.id)
            setTopProgressBarProgress(loyaltyStatus.id)
            setProgressBarHeight(loyaltyStatus.id)

//            initPrivileges(itemView.context, this, loyaltyStatus) //will be used in the future
            val privileges = when (loyaltyStatus.id) {
                1 -> itemView.context.resources.getStringArray(R.array.loyalty_privileges_1)
                2 -> itemView.context.resources.getStringArray(R.array.loyalty_privileges_2)
                3 -> itemView.context.resources.getStringArray(R.array.loyalty_privileges_3)
                4 -> itemView.context.resources.getStringArray(R.array.loyalty_privileges_4)
                5 -> itemView.context.resources.getStringArray(R.array.loyalty_privileges_5)
                6 -> itemView.context.resources.getStringArray(R.array.loyalty_privileges_6)
                else -> itemView.context.resources.getStringArray(R.array.loyalty_privileges_7)
            }
            initPrivileges(itemView.context, this, loyaltyStatus, privileges)
        }

        private fun setPrizesTitle(prizes: List<GetLoyaltyStatusesQuery.Prize?>?) {
            if (prizes != null && prizes.isNotEmpty()) {
                var prizePoints = ""
                var prizeMoney = 0.0
                var prizeCurrencySymbol = ""

                for (prize in prizes) {
                    if (prize?.asLoyaltyPointsPrizeType is GetLoyaltyStatusesQuery.AsLoyaltyPointsPrizeType) {
                        prizePoints = prize.asLoyaltyPointsPrizeType.count.toString()
                    } else if (prize?.asLoyaltyMoneyPrizeType is GetLoyaltyStatusesQuery.AsLoyaltyMoneyPrizeType) {
                        prizeMoney = prize.asLoyaltyMoneyPrizeType.amount.toString().toDouble()
                        prizeCurrencySymbol = prize.asLoyaltyMoneyPrizeType.currency.symbol
                    }
                }

                tvPrizes.visibility = View.VISIBLE
                if (prizePoints.isNotEmpty() && prizeMoney != 0.0) {
                    tvPrizes.text = Html.fromHtml(itemView.context.resources.getString(R.string.prize_points_money,
                            prizePoints, prizeMoney.toInt(), prizeCurrencySymbol))
                } else if (prizePoints.isNotEmpty()) {
                    tvPrizes.text = Html.fromHtml(itemView.context.resources.getString(R.string.prize_points, prizePoints))
                } else if (prizeMoney != 0.0) {
                    tvPrizes.text = Html.fromHtml(itemView.context.resources.getString(R.string.prize_money, prizeMoney.toInt(), prizeCurrencySymbol))
                } else {
                    tvPrizes.visibility = View.GONE
                }
            } else {
                tvPrizes.visibility = View.GONE
            }
        }

        private fun hideLastProgressBar(loyaltyStatusId: Int) {
            if (loyaltyStatusId == itemCount) {
                progressBar.visibility = View.GONE
            } else {
                progressBar.visibility = View.VISIBLE
            }
        }

        private fun hideFirstTopProgressBar(loyaltyStatusId: Int) {
            if (loyaltyStatusId == 1) {
                progressBarTop.visibility = View.GONE
            } else {
                progressBarTop.visibility = View.VISIBLE
            }
        }

        private fun setTopProgressBarProgress(loyaltyStatusId: Int) {
            if (loyaltyStatusId > currentLoyaltyStatusId) {
                progressBarTop.progress = 0
            } else {
                progressBarTop.progress = 100
            }
        }

        private fun setProgressBarHeight(loyaltyStatusId: Int) {
            val params = progressBar.layoutParams
            params.height = 0
            progressBar.layoutParams = params
        }
    }
}
