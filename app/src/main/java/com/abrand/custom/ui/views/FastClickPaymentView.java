package com.abrand.custom.ui.views;

import android.content.Context;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;

import com.abrand.custom.R;
import com.abrand.custom.data.ApoloConfig;
import com.abrand.custom.data.entity.FastClickPaymentSystem;
import com.abrand.custom.tools.GeneralTools;
import com.squareup.picasso.Picasso;

public class FastClickPaymentView extends ConstraintLayout {
    private final String                         TAG                            = "FastClickPaymentView";
    private       Context                        context;
    protected     ViewGroup                      rootView;
    private       EditText                       etFastPaymentAmount;
    private       TextView                       tvFastPaymentCurrency;
    private       TextView                       tvFastPaymentRequisite;
    private       ImageView                      ivFastPaymentLogo;
    private       Button                         btnFastPayment;
    private       View                           containerFastPaymentLoader;
    private       ImageView                      ivFastPaymentCloseSuccessfulTransaction;
    private       ImageView                      ivFastPaymentLoader;
    private       TextView                       tvFastPaymentSuccessfulTransaction;
    private       ButtonFastPaymentClickListener buttonFastPaymentClickListener;
    private final int                            SUCCESSFUL_REBILL_MESSAGE_TIME = 4000;
    private final int                            FAST_PAYMENT_LOADER_TIME = 15_000;
    private       FastClickPaymentSystem         fastClickPaymentSystem;
    private       String                         minDepositWarning;
    private       String                         maxDepositWarning;
    private       Handler                        fastPaymentLoaderHandler = new Handler();

    public FastClickPaymentView(@NonNull Context context) {
        super(context);
        init(context);
    }

    public FastClickPaymentView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public FastClickPaymentView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        this.context = context;
        rootView = inflate(context, R.layout.view_fast_click_payment, this).findViewById(R.id.root);
        etFastPaymentAmount = rootView.findViewById(R.id.et_fast_payment_amount);
        tvFastPaymentCurrency = rootView.findViewById(R.id.tv_fast_payment_currency);
        tvFastPaymentRequisite = rootView.findViewById(R.id.tv_fast_payment_requisite);
        ivFastPaymentLogo = rootView.findViewById(R.id.iv_fast_payment_logo);
        View containerFastPaymentAmount = rootView.findViewById(R.id.container_fast_payment_amount);
        containerFastPaymentAmount.setOnClickListener(v -> {
            etFastPaymentAmount.requestFocus();
            etFastPaymentAmount.setSelection(etFastPaymentAmount.getText().length());
            GeneralTools.showKeyboard(context, etFastPaymentAmount);
        });
        btnFastPayment = rootView.findViewById(R.id.btn_fast_payment);
        containerFastPaymentLoader = rootView.findViewById(R.id.container_fast_payment_loader);
        ivFastPaymentCloseSuccessfulTransaction = rootView.findViewById(R.id.iv_fast_payment_close_successful_transaction);
        ivFastPaymentCloseSuccessfulTransaction.setOnClickListener(v -> containerFastPaymentLoader.setVisibility(View.INVISIBLE));
        ivFastPaymentLoader = rootView.findViewById(R.id.iv_loader);
        tvFastPaymentSuccessfulTransaction = rootView.findViewById(R.id.tv_fast_payment_successful_transaction);
    }

    public void setFastClickPaymentSystem(FastClickPaymentSystem fastClickPaymentSystem) {
        setFastClickPaymentSystem(fastClickPaymentSystem, null);
    }

    /***
     *
     * @param fastClickPaymentSystem
     * @param defaultAmount custom default amount to be filled in editText field,
     *                      must be greater that fastClickPaymentSystem min amount
     *                      and less than fastClickPaymentSystem max amount
     *                      or will be ignored
     */

    public void setFastClickPaymentSystem(FastClickPaymentSystem fastClickPaymentSystem,
                                          @Nullable String defaultAmount) {
        final char space                = ' ';
        final int  REQUISITE_CHARACTERS = 4;
        this.fastClickPaymentSystem = fastClickPaymentSystem;

        try {
            if ( defaultAmount != null ) {
                double customAmount = Double.parseDouble(defaultAmount);
                //noinspection ConstantConditions
                double minAmount = Double.parseDouble(fastClickPaymentSystem.getMinAmount().toString());
                //noinspection ConstantConditions
                double maxAmount = Double.parseDouble(fastClickPaymentSystem.getMaxAmount().toString());

                if ( customAmount < minAmount || customAmount > maxAmount ) {
                    defaultAmount = null;
                } else {
                    defaultAmount = String.valueOf((int) customAmount);
                }
            }
        } catch ( NumberFormatException | NullPointerException e ) {
            // error parse custom amount
            defaultAmount = null;
        }

        String formattedMinAmount = GeneralTools.formatBalance(fastClickPaymentSystem.getCurrency().getCode(),
                Double.parseDouble(fastClickPaymentSystem.getMinAmount().toString()));
        this.minDepositWarning = context.getString(R.string.min_deposit_warning, formattedMinAmount);
        
        String formattedMaxAmount = GeneralTools.formatBalance(fastClickPaymentSystem.getCurrency().getCode(),
                Double.parseDouble(fastClickPaymentSystem.getMaxAmount().toString()));
        this.maxDepositWarning = context.getString(R.string.max_deposit_warning, formattedMaxAmount);

        etFastPaymentAmount.removeTextChangedListener(fastPaymentAmountTextWatcher);
        etFastPaymentAmount.addTextChangedListener(fastPaymentAmountTextWatcher);
        etFastPaymentAmount.setText(defaultAmount != null ? defaultAmount : fastClickPaymentSystem.getDefaultAmount().toString().split("\\.")[0]);

        tvFastPaymentCurrency.setText(fastClickPaymentSystem.getCurrency().getSymbol());
        String requisite = fastClickPaymentSystem.getRequisite()
                .substring(fastClickPaymentSystem.getRequisite().length() - REQUISITE_CHARACTERS);
        tvFastPaymentRequisite.setText(requisite);
        if ( !TextUtils.isEmpty(fastClickPaymentSystem.getLogo()) ) {
            Picasso.get().load(ApoloConfig.getFullUrl(fastClickPaymentSystem.getLogo())).into(ivFastPaymentLogo);
        } else {
            Log.e(TAG, "FastClickPaymentSystem logo is empty");
        }


        btnFastPayment.setOnClickListener(v -> {
            String input = GeneralTools.removeSpaces(etFastPaymentAmount.getText().toString());
            Integer amount = Integer.valueOf(input);
            etFastPaymentAmount.clearFocus();
            if ( fastClickPaymentSystem.isRebill() ) {
                GeneralTools.hideKeyboard(context, etFastPaymentAmount);
                btnFastPayment.setEnabled(false);
            }

            if ( buttonFastPaymentClickListener != null ) {
                buttonFastPaymentClickListener.onClick(fastClickPaymentSystem.isRebill(), amount);
            }
        });
    }

    protected TextWatcher fastPaymentAmountTextWatcher = new TextWatcher() {

        @Override
        public void afterTextChanged(Editable s) {
            processFastPaymentAmountInput(etFastPaymentAmount, btnFastPayment, this);
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }
    };

    private void processFastPaymentAmountInput(EditText etFastPaymentAmount, Button btnFastPayment,
                                         TextWatcher textWatcher) {
        final char space          = ' ';
        final int  SPACE_POSITION = 3;
        String     input          = GeneralTools.removeSpaces(etFastPaymentAmount.getText().toString());

        try {
            long amount    = Long.parseLong(input);
            int  minAmount = (int) Double.parseDouble(fastClickPaymentSystem.getMinAmount().toString());
            int  maxAmount = (int) Double.parseDouble(fastClickPaymentSystem.getMaxAmount().toString());

            if ( amount < minAmount ) {
                btnFastPayment.setText(minDepositWarning);
                btnFastPayment.setEnabled(false);
                btnFastPayment.setBackground(ContextCompat.getDrawable(context, R.drawable.btn_bg_fast_payment_disable));
            } else if ( amount > maxAmount) {
                btnFastPayment.setText(maxDepositWarning);
                btnFastPayment.setEnabled(false);
                btnFastPayment.setBackground(ContextCompat.getDrawable(context, R.drawable.btn_bg_fast_payment_disable));
            } else {
                btnFastPayment.setText(context.getString(R.string.to_pay));
                btnFastPayment.setEnabled(true);
                btnFastPayment.setBackground(ContextCompat.getDrawable(context, R.drawable.btn_bg_enable));
            }
        } catch ( NumberFormatException e ) {
            Log.d("FastClickPayment", e.toString());
            btnFastPayment.setText(minDepositWarning); // block refill button if parse error
            btnFastPayment.setEnabled(false);
            btnFastPayment.setBackground(ContextCompat.getDrawable(context, R.drawable.btn_bg_fast_payment_disable));
        }

        StringBuilder inputBuilder = new StringBuilder(input);
        for ( int i = inputBuilder.length() - SPACE_POSITION; i > 0; i -= SPACE_POSITION ) {
            inputBuilder.insert(i, space);
        }

        etFastPaymentAmount.removeTextChangedListener(textWatcher);
        etFastPaymentAmount.setText(inputBuilder.toString());
        etFastPaymentAmount.addTextChangedListener(textWatcher);
        etFastPaymentAmount.setSelection(inputBuilder.length());
    }

    public void setButtonFastPaymentClickListener(ButtonFastPaymentClickListener buttonFastPaymentClickListener) {
        this.buttonFastPaymentClickListener = buttonFastPaymentClickListener;
    }

    public void showLoader() {
        containerFastPaymentLoader.setVisibility(View.VISIBLE);
        ivFastPaymentCloseSuccessfulTransaction.setVisibility(View.INVISIBLE);
        Animation animation = AnimationUtils.loadAnimation(context, R.anim.rotation_loader);
        ivFastPaymentLoader.startAnimation(animation);
        tvFastPaymentSuccessfulTransaction.setVisibility(View.INVISIBLE);

        fastPaymentLoaderHandler.postDelayed(() -> {
            hideLoader();
            btnFastPayment.setEnabled(true);
        }, FAST_PAYMENT_LOADER_TIME);
    }

    public void hideLoader() {
        fastPaymentLoaderHandler.removeCallbacksAndMessages(null);
        ivFastPaymentLoader.clearAnimation();
        ivFastPaymentLoader.setVisibility(View.INVISIBLE);
        containerFastPaymentLoader.setVisibility(View.INVISIBLE);
    }

    public void showSuccessfulRebillingMessage() {
        fastPaymentLoaderHandler.removeCallbacksAndMessages(null);
        ivFastPaymentLoader.clearAnimation();
        ivFastPaymentLoader.setVisibility(View.INVISIBLE);
        tvFastPaymentSuccessfulTransaction.setVisibility(View.VISIBLE);
        ivFastPaymentCloseSuccessfulTransaction.setVisibility(View.VISIBLE);

        final Handler handler = new Handler();
        handler.postDelayed(() -> {
            tvFastPaymentSuccessfulTransaction.setVisibility(View.INVISIBLE);
            ivFastPaymentCloseSuccessfulTransaction.setVisibility(View.INVISIBLE);
            containerFastPaymentLoader.setVisibility(View.INVISIBLE);
        }, SUCCESSFUL_REBILL_MESSAGE_TIME);
    }

    public void hideKeyboard() {
        GeneralTools.hideKeyboard(context, etFastPaymentAmount);
    }

    public interface ButtonFastPaymentClickListener {
        void onClick(boolean isRebill, Integer amount);
    }
}
