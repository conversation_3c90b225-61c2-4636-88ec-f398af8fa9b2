package com.abrand.custom.domain

import com.abrand.custom.data.repositories.ConfigRepository
import com.abrand.custom.data.repositories.SettingsRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class PrepareAppUseCase(
    private val settingsRepository: SettingsRepository = SettingsRepository,
    private val configRepository: ConfigRepository = ConfigRepository,
    private val fetchConfigUseCase: FetchAndUpdateAppConfigUseCase = FetchAndUpdateAppConfigUseCase(),
    private val dispatcher: CoroutineDispatcher = Dispatchers.Default,
) {
    suspend operator fun invoke() = withContext(dispatcher) {
        val localConfig = settingsRepository.getLocalConfig()
        val isNeedFetchConfig = localConfig.hash != null && !configRepository.isConfigDataFetched()

        if (isNeedFetchConfig) {
            fetchConfigUseCase(localConfig).also { isFetched ->
                if (isFetched) configRepository.setConfigDataFetched()
            }
        }
    }
}
