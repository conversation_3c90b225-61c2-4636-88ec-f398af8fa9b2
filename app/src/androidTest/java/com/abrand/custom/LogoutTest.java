package com.abrand.custom;

import android.text.TextUtils;

import androidx.fragment.app.testing.FragmentScenario;

import com.abrand.custom.data.entity.AuthResponse;
import com.abrand.custom.interfaces.GenericTarget;
import com.abrand.custom.network.ApolloProcessor;
import com.abrand.custom.presenter.FieldsErrorsHolder;
import com.abrand.custom.ui.enter.EnterFragment;
import com.apollographql.apollo3.exception.ApolloException;

import org.jetbrains.annotations.Nullable;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class LogoutTest {
    private final String[] error         = {null};
    private final int      RESPONSE_TIME = 5_000;

    @Before
    public void setUp() {
//        FragmentScenario.launch(EnterFragment.class, null, R.style.AppTheme, null);

//        ApolloProcessor.getCaptchas(false, new GetCaptchaTarget() {
//            @Override
//            public void onSuccess(boolean isCaptchaEnabled, String captchaId, boolean isReCaptchaEnabled, String reCaptchaId) {
//                if ( isCaptchaEnabled ) {
//                    error[0] = "Captcha enabled, can't login";
//                } else if ( isReCaptchaEnabled ) {
//                    error[0] = "ReCaptcha enabled, can't login";
//                } else {
//                    login();
//                }
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = "getCaptchas: " + e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    private void login() {
        // TODO: 28.10.2022 rewrite to Kt version
//        final String email    = "<EMAIL>";
//        final String password = "qweqwe1";
//
//        ApolloProcessor.login(email, password, "", "",
//                "", new GenericTarget<AuthResponse>() {
//                    @Override
//                    public void onSuccess(@Nullable AuthResponse authResponse) {
//                        if ( TextUtils.isEmpty(authResponse.getUserId()) ) {
//                            error[0] = "success response but empty userId";
//                        }
//                    }
//
//                    @Override
//                    public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                        error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//                    }
//
//                    @Override
//                    public void onFailure(ApolloException e) {
//                        error[0] = e.toString();
//                    }
//                });
    }

    @Test
    public void logoutTest() {
        // TODO: 01.11.2022 rewrite to Kt version
//        ApolloProcessor.logout(new GenericTarget<Boolean>() {
//            @Override
//            public void onSuccess(@Nullable Boolean isSuccess) {
//                if ( !isSuccess ) {
//                    error[0] = "logout isSuccess: " + isSuccess;
//                }
//            }
//
//            @Override
//            public void onError(String errorMessage, String errorCode, FieldsErrorsHolder fieldsErrors) {
//                error[0] = "errorMessage: " + errorMessage + " | errorCode: " + errorCode;
//            }
//
//            @Override
//            public void onFailure(ApolloException e) {
//                error[0] = e.toString();
//            }
//        });
//
//        waitResponseAndAssertFailIfError();
    }

    private void waitResponseAndAssertFailIfError() {
        try {
            Thread.sleep(RESPONSE_TIME);
            if ( !TextUtils.isEmpty(error[0]) ) {
                Assert.fail(error[0]);
            }
        } catch ( InterruptedException e ) {
            e.printStackTrace();
        }
    }
}
