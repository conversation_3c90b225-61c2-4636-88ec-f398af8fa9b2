<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_root"
    android:layout_width="match_parent"
    android:layout_height="@dimen/lottery_completed_card_height"
    android:layout_marginLeft="@dimen/lottery_card_side_margin"
    android:layout_marginTop="@dimen/size_16"
    android:layout_marginRight="@dimen/lottery_card_side_margin"
    android:background="@drawable/tournament_default_bg">

    <ImageView
        android:id="@+id/iv_lottery_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:alpha="0.5" />

    <TextView
        android:id="@+id/tv_lottery_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="@dimen/size_16"
        android:gravity="start"
        android:textColor="@color/white"
        android:textFontWeight="500"
        android:textSize="18sp"
        app:layout_constraintEnd_toStartOf="@+id/tv_lottery_month"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_lottery_day"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textColor="@color/white"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/tv_lottery_month"
        app:layout_constraintStart_toStartOf="@+id/tv_lottery_month"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_lottery_month"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_lottery_day" />

    <TextView
        android:id="@+id/tv_lottery_prize"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />

    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginEnd="24dp"
        android:layout_marginBottom="16dp"
        android:scaleType="center"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
