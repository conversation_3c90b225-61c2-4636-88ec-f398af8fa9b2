query getBonusBalances {
    viewer {
        wallet {
            bonusBalances {
                id,
                wager,
                isActive,
                amount,
                wageringTarget,
                wageringCurrent,
                wageringExpiredAt,
                wageringMaxTransferAmount,
                wageringCompletePercent,
            }
        }
    }
}

query getBonusBalancesCount {
    viewer {
        wallet {
            bonusBalances {
                id,
            }
        }
    }
}

mutation activateBonusBalance(
    $balanceId: Int!
) {
    walletBonusBalanceActivate(balanceId: $balanceId)
}

mutation deleteBonusBalance(
    $balanceId: Int!
) {
    walletBonusBalanceReset(balanceId: $balanceId)
}

subscription subscribeBonusBalanceWon {
    bonusBalanceWageringWon {
        balanceId,
        amount,
    }
}
