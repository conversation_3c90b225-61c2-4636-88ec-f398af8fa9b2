package com.abrand.custom.tools

import kotlinx.datetime.Clock
import java.security.MessageDigest
import java.util.*
import kotlin.text.Charsets.UTF_8

class UrlEncoder(seed: Long = Clock.System.now().toEpochMilliseconds()) {

    private val random: Random = Random(seed)

    fun encodeParam(key: String, value: String): String {
        return key + generateHash() + "=" + value
    }

    fun encodeParam(key: String): String {
        return key + generateHash() + "=" + generateHash()
    }

    fun encode(vararg params: String, urlPath: String? = null): String {
        val path = StringBuilder()

        if (urlPath != null) {
            path.append(urlPath).append('/')
        } else {
            val size = random.nextInt(4)
            for (i in 0 until size) {
                path.append(generateHash()).append('/')
            }
        }
        path.append('?')
        for (i in params.indices) {
            if (i != 0) path.append('&')
            path.append(params[i])
        }

        val additionalParams = random.nextInt(4)
        for (i in 0 until additionalParams) {
            path.append('&')
            path.append(generateHash())
            path.append('=')
            path.append(generateHash())
        }
        return path.toString()
    }


    private fun generateHash(): String {
        return random.nextInt(100).toString() + md5(Math.random().toString() + "").substring(
            0,
            5 + random.nextInt(10)
        )
    }

    companion object {

        fun md5(str: String): String {
            return hashString(str).toHex()
        }

        private fun ByteArray.toHex() = joinToString(separator = "") { byte -> "%02x".format(byte) }

        private fun hashString(str: String, algorithm: String = "MD5"): ByteArray {
            return MessageDigest.getInstance(algorithm).digest(str.toByteArray(UTF_8))
        }
    }
}
