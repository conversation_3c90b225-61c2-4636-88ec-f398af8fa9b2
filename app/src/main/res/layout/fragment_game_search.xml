<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/clRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient">

    <ImageView
        android:id="@+id/ivSearch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginLeft="16dp"
        android:layout_marginTop="16dp"
        android:background="@null"
        android:src="@drawable/ic_search_white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/etSearch"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginLeft="16dp"
        android:background="@null"
        android:imeOptions="actionDone"
        android:inputType="text"
        android:maxLines="1"
        app:layout_constraintEnd_toStartOf="@+id/ibCloseSearch"
        app:layout_constraintStart_toEndOf="@+id/ivSearch"
        app:layout_constraintTop_toTopOf="@+id/ivSearch" />

    <ImageView
        android:id="@+id/ibCloseSearch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginRight="16dp"
        android:src="@drawable/ic_close_white"
        app:layout_constraintBottom_toBottomOf="@+id/ivSearch"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivSearch" />

    <View
        android:id="@+id/separator"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="10dp"
        android:background="#8DA1BD"
        app:layout_constraintTop_toBottomOf="@+id/ivSearch" />

    <LinearLayout
        android:id="@+id/container_titles"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@+id/separator">

        <TextView
            android:id="@+id/tvNoGamesFound"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="30dp"
            android:layout_marginEnd="@dimen/size_16"
            android:text="@string/no_games_found_by_name"
            android:textAllCaps="true"
            android:textColor="#FFFFFF"
            android:textSize="20sp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvPopularGame"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/size_16"
            android:layout_marginTop="30dp"
            android:layout_marginEnd="@dimen/size_16"
            android:text="@string/popular_games"
            android:textColor="#FFFFFF"
            android:textSize="20sp"
            android:visibility="visible" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvGames"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/container_titles" />

</androidx.constraintlayout.widget.ConstraintLayout>


