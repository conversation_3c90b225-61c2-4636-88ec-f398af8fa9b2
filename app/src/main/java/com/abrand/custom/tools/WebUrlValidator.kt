package com.abrand.custom.tools

import java.util.regex.Pattern

object WebUrlValidator {
    private const val REGEX =
        "((http|https)://)(www.)?[a-zA-Z0-9@:%.\\-_+~#?&/=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%._+~#?&/=]*)"
    private val WEB_URL_PATTERN: Pattern = Pattern.compile(REGEX)

    fun isValidUrl(url: String?): Bo<PERSON>an {
        if (url.isNullOrEmpty()) return false
        return try {
            val trimmedUrl = url.trim()
            WEB_URL_PATTERN.matcher(trimmedUrl).matches()
        } catch (e: Exception) {
            false
        }
    }
}
