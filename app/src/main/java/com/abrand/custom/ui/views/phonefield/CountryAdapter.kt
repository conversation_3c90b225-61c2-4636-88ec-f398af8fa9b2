package com.abrand.custom.ui.views.phonefield

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.entity.Country
import com.abrand.custom.tools.GeneralTools
import java.util.*

class CountryAdapter(private var countries: List<Country>,
                     private val countrySelectionListener: CountrySelectionListener) :
        RecyclerView.Adapter<CountryAdapter.CountryViewHolder>() {
    private val TAG = "CountryAdapter";

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CountryViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return CountryViewHolder(inflater, parent)
    }

    override fun onBindViewHolder(holder: CountryViewHolder, position: Int) {
        val country = countries[position]
        holder.ivCountryFlag.setImageDrawable(GeneralTools.getDrawableByCountryCode(holder.itemView.context, country.isoCode))
        val locale = Locale("", country.isoCode)
        holder.tvCountryName.text = locale.displayCountry
        val phoneCode = country.phoneCode
        holder.tvCountryCode.text = phoneCode
        holder.itemView.setOnClickListener { countrySelectionListener.onSelected(country) }
    }

    override fun getItemCount() = countries.size

    fun setList(countries: List<Country>) {
        this.countries = countries
        notifyDataSetChanged()
    }

    class CountryViewHolder(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_country, parent, false)) {
        var ivCountryFlag: ImageView = itemView.findViewById(R.id.iv_country_flag)
        var tvCountryName: TextView = itemView.findViewById(R.id.tv_country_name)
        var tvCountryCode: TextView = itemView.findViewById(R.id.tv_country_code)
    }

    interface CountrySelectionListener {
        fun onSelected(country: Country)
    }
}