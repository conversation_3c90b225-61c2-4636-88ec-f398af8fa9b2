package com.abrand.custom.ui.game

import android.content.res.Configuration
import android.graphics.drawable.BitmapDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.GridLayoutManager.SpanSizeLookup
import com.abrand.custom.R
import com.abrand.custom.data.entity.GameListItem
import com.abrand.custom.data.entity.Screen
import com.abrand.custom.data.entity.SessionDataHolder
import com.abrand.custom.databinding.FragmentInGameMenuSearchBinding
import com.abrand.custom.tools.EditTextDebounce
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.tools.afterTextChanged
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.home.GamesAdapter
import com.abrand.custom.ui.interfaces.GameClickListener
import com.apollographql.apollo3.exception.ApolloException

class InGameMenuSearchFragment : Fragment() {
    private var binding: FragmentInGameMenuSearchBinding? = null
    private val viewModel: InGameMenuSearchViewModel by viewModels()
    private var gamesAdapter: GamesAdapter? = null
    private val MIN_SEARCH_LENGHT = 2
    private val SEARCH_DELAY_MILLIS = 1000
    private var popularGameThumbs: List<GameListItem> = listOf()
    private var currentSearch = ""

    companion object {
        const val TAG = "SearchFragment"
        const val GAMES_COLS_COUNT_PORTRAIT = 2
        const val GAMES_COLS_COUNT_LANDSCAPE = 4
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View {
        val localBinding = FragmentInGameMenuSearchBinding.inflate(inflater, container, false)
        binding = localBinding

        val isPortrait = resources.configuration.orientation == Configuration.ORIENTATION_PORTRAIT
        val screen = if (isPortrait) Screen.INGAME_MENU_SEARCH_PORTRAIT else Screen.INGAME_MENU_SEARCH_LANDSCAPE
        val spanCount = if (isPortrait) GAMES_COLS_COUNT_PORTRAIT else GAMES_COLS_COUNT_LANDSCAPE

        gamesAdapter = GamesAdapter(
            GeneralTools.getGameIconSize(activity, screen),
            clickListener, null, loadMoreClickListener,
            screen, context,
            InGameMenuSearchViewModel.GAMES_BY_NAME_LIMIT
        )

        localBinding.rvGames.layoutManager = GridLayoutManager(context, spanCount).apply {
            spanSizeLookup = object : SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    val viewType = gamesAdapter?.getItemViewType(position)
                    return if (viewType == GameListItem.ItemType.GAME_LOAD.id) spanCount else 1
                }
            }
        }

        localBinding.rvGames.adapter = gamesAdapter

        initEditTextSearch()

        observeViewModel()

        return localBinding.root
    }

    override fun onDestroyView() {
        binding = null
        super.onDestroyView()
    }

    private fun observeViewModel() {
        viewModel.gamesByNameLiveData.observe(viewLifecycleOwner) {
            gamesAdapter?.setCurrentTotalGames(it.total)

            if (it.localGameItems.isEmpty() && it.offset == 0) {
                showContainerNoGamesFound()
                if (popularGameThumbs.isEmpty()) {
                    viewModel.loadPopularGames()
                } else {
                    gamesAdapter?.setList(popularGameThumbs)
                }
            } else {
                if (it.offset == 0) {
                    val newList = mutableListOf<GameListItem>()
                    for (game in it.localGameItems) {
                        newList.add(GameListItem(GameListItem.ItemType.GAME, game))
                    }
                    gamesAdapter?.setList(newList)
                    binding?.rvGames?.smoothScrollToPosition(0)
                } else if (it.localGameItems.isNotEmpty()) {
                    val newList = mutableListOf<GameListItem>()
                    for (game in it.localGameItems) {
                        newList.add(GameListItem(GameListItem.ItemType.GAME, game))
                    }
                    gamesAdapter?.appendList(newList)
                }
            }
            gamesAdapter?.setGameLoadingState(false)
        }

        viewModel.popularGamesLiveData.observe(viewLifecycleOwner) {
            gamesAdapter?.setCurrentTotalGames(InGameMenuSearchViewModel.POPULAR_GAMES_LIMIT)

            val newList = mutableListOf<GameListItem>()
            for (game in it.localGameItems) {
                newList.add(GameListItem(GameListItem.ItemType.GAME, game))
            }
            gamesAdapter?.setList(newList)
            popularGameThumbs = newList

            gamesAdapter?.setGameLoadingState(false)
            binding?.rvGames?.smoothScrollToPosition(0)
        }

        viewModel.apolloExceptionLiveData.observe(viewLifecycleOwner) { exception: ApolloException? ->
            Toast.makeText(context, exception.toString(), Toast.LENGTH_LONG).show()
        }
    }

    private val clickListener = GameClickListener { game, itemView ->
        if (game != null) {
            GeneralTools.hideKeyboard(context, binding?.etSearch)
            val dataHolder = SessionDataHolder.getInstance()
            dataHolder.currentGameItem = game
            val gameIcon = itemView?.findViewById<ImageView>(R.id.iv_item_game_image)
            if (gameIcon?.drawable is BitmapDrawable) {
                dataHolder.currentGameImage = (gameIcon.drawable as BitmapDrawable).bitmap
            }
            (activity as MainActivity).showPregame(SessionDataHolder.getInstance().currentGameItem, null)
        }
    }

    private fun showContainerNoGamesFound() {
        binding?.containerNoGamesFound?.visibility = View.VISIBLE
        binding?.tvNothingFoundDescription?.text = getString(R.string.nothing_found_description,
            binding?.etSearch?.text)
        titlePopularGamesVisibility(true)
    }

    private fun titlePopularGamesVisibility(isVisible: Boolean) {
        val visibility: Int = if (isVisible) {
            View.VISIBLE
        } else {
            View.GONE
        }

        binding?.tvPopularTitle?.visibility = visibility
    }

    private fun initEditTextSearch() {
        binding?.etSearch?.post { initSearchInput() }
        binding?.etSearch?.afterTextChanged {
            binding?.containerNoGamesFound?.visibility = View.GONE
            if (it.length < MIN_SEARCH_LENGHT) {
                return@afterTextChanged
            }
            gamesAdapter?.setList(ArrayList<GameListItem>(0))
            titlePopularGamesVisibility(false)
            gamesAdapter?.setGameLoadingState(true)
        }

        binding?.etSearch?.let {
            EditTextDebounce.create(it, SEARCH_DELAY_MILLIS).watch { result ->
                if (result.length >= MIN_SEARCH_LENGHT) {
                    gamesAdapter?.gameCount?.let { offset ->
                        viewModel.loadGamesByName(result, offset)
                    }
                } else {
                    titlePopularGamesVisibility(true)
                    if (popularGameThumbs.isEmpty()) {
                        viewModel.loadPopularGames()
                    } else {
                        gamesAdapter?.setCurrentTotalGames(InGameMenuSearchViewModel.POPULAR_GAMES_LIMIT)
                        gamesAdapter?.setList(popularGameThumbs)
                    }
                }
                currentSearch = result
            }
        }
    }

    private fun initSearchInput() {
        // not use requestFocusFromTouch() because NoConnection's (screen) update button in focus after screen open
        binding?.etSearch?.requestFocus()
        GeneralTools.showKeyboard(context, binding?.etSearch)
    }

    private val loadMoreClickListener = GamesAdapter.LoadMoreClickListener {
        gamesAdapter?.gameCount?.let { viewModel.loadGamesByName(currentSearch, it) }
    }

}
