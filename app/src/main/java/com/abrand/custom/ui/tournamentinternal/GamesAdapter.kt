package com.abrand.custom.ui.tournamentinternal

import android.text.TextUtils
import android.view.LayoutInflater
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.ApoloConfig
import com.abrand.custom.data.entity.LocalGameItem
import com.abrand.custom.data.entity.Screen
import com.abrand.custom.tools.CircleTransform
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.ui.interfaces.GameClickListener
import com.squareup.picasso.Picasso

class GamesAdapter(private val data: List<LocalGameItem>, listener: GameClickListener) :
        RecyclerView.Adapter<GamesAdapter.GameViewHolder>() {
    lateinit var localGameItem: LocalGameItem
    private var clickListener: GameClickListener = listener

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GameViewHolder {
        val inflater = LayoutInflater.from(parent.context)
        return GameViewHolder(inflater, parent)
    }

    override fun onBindViewHolder(holder: GameViewHolder, position: Int) {
        localGameItem = data[position]
        val animation = AnimationUtils
                .loadAnimation(holder.itemView.context, R.anim.rotation_loader)
        holder.ivItemGameImage.setImageResource(R.drawable.ic_preload)
        holder.ivItemGameImage.startAnimation(animation)

        if (!TextUtils.isEmpty(localGameItem.mobileIcon)) {
            val iconCornerRadius: Int = holder.itemView.context.resources.getDimensionPixelSize(R.dimen.game_icon_corner_radius)
            Picasso.get().load(ApoloConfig.getFullUrl(localGameItem.mobileIcon))
                    .resize(GeneralTools.getGameIconSize(holder.itemView.context, Screen.TOURNAMENT),
                        GeneralTools.getGameIconSize(holder.itemView.context, Screen.TOURNAMENT))
                    .transform(CircleTransform(iconCornerRadius))
                    .into(holder.ivItemGameImage)
            holder.ivItemGameImage.clearAnimation()
        } else {
            val iconCornerRadius: Int = holder.itemView.context.resources.getDimensionPixelSize(R.dimen.game_icon_corner_radius)
            Picasso.get().load(R.drawable.default_thumb_rounded)
                    .resize(GeneralTools.getGameIconSize(holder.itemView.context, Screen.TOURNAMENT),
                        GeneralTools.getGameIconSize(holder.itemView.context, Screen.TOURNAMENT))
                    .transform(CircleTransform(iconCornerRadius))
                    .into(holder.ivItemGameImage)
            holder.ivItemGameImage.clearAnimation()
        }
        holder.tvItemGameName.text = localGameItem.name
        holder.localGameItem = localGameItem
        holder.itemView.setOnClickListener { view ->
            holder.localGameItem?.let { holderGameItem ->
                clickListener.onGameClick(holderGameItem, view)
            }
        }
    }

    override fun getItemCount() = data.size

    class GameViewHolder(inflater: LayoutInflater, parent: ViewGroup) :
        RecyclerView.ViewHolder(inflater.inflate(R.layout.item_game, parent, false)) {
        var tvItemGameName: TextView = itemView.findViewById(R.id.tv_item_game_name)
        var ivItemGameImage: ImageView = itemView.findViewById(R.id.iv_item_game_image)
        var localGameItem: LocalGameItem? = null
    }
}
