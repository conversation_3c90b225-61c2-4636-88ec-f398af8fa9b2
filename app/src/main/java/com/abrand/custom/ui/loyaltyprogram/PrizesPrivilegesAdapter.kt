package com.abrand.custom.ui.loyaltyprogram

import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.abrand.custom.R
import com.abrand.custom.data.entity.PrizePrivilegeItem

class PrizesPrivilegesAdapter(private var items: List<PrizePrivilegeItem>, private val isCurrent: Boolean) :
        RecyclerView.Adapter<PrizesPrivilegesAdapter.PrizesPrivilegesVH>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PrizesPrivilegesVH {
        val inflater = LayoutInflater.from(parent.context)
        return PrizesPrivilegesVH(inflater, parent)
    }

    override fun getItemCount() = items.size

    override fun onBindViewHolder(holder: PrizesPrivilegesVH, position: Int) {
        val item = items[position]
        holder.tvTitle.text = item.text

        if (isCurrent) {
            holder.tvTitle.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.loyalty_current_prize_privilege_title))
            holder.ivStatus.setImageDrawable(ContextCompat.getDrawable(holder.itemView.context, R.drawable.ic_privilege_received))
        } else if (PrizePrivilegeItem.ItemType.PRIZE == item.type) {
            holder.tvTitle.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.loyalty_future_prize_title))
            holder.ivStatus.setImageDrawable(ContextCompat.getDrawable(holder.itemView.context, R.drawable.ic_privilege_next))
        } else {
            holder.tvTitle.setTextColor(ContextCompat.getColor(holder.itemView.context, R.color.loyalty_future_privilege_title))
            holder.ivStatus.setImageDrawable(ContextCompat.getDrawable(holder.itemView.context, R.drawable.ic_privilege_next))
        }
    }

    class PrizesPrivilegesVH(inflater: LayoutInflater, parent: ViewGroup) :
            RecyclerView.ViewHolder(inflater.inflate(R.layout.item_prizes_privileges, parent, false)) {
        var tvTitle = itemView.findViewById<TextView>(R.id.tv_title)
        var ivStatus = itemView.findViewById<ImageView>(R.id.iv_status)
    }
}
