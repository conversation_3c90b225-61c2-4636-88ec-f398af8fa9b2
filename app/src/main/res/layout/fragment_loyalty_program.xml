<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.loyaltyprogram.LoyaltyProgramFragment">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="90dp"
            android:paddingLeft="@dimen/size_16"
            android:text="@string/loyalty_program_title"
            android:textColor="#FFFFFF"
            android:textFontWeight="300"
            android:textSize="24sp" />

        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="23dp"
            android:lineSpacingExtra="6dp"
            android:paddingLeft="@dimen/size_16"
            android:paddingRight="@dimen/size_16"
            android:text="@string/loyalty_program_description"
            android:textColor="#B2E3FF"
            android:textFontWeight="700"
            android:textSize="12sp" />

        <Button
            android:id="@+id/btnExchangePoints"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/size_16"
            android:layout_marginTop="32dp"
            android:background="@drawable/more_btn_bg"
            android:foreground="?attr/selectableItemBackground"
            android:paddingLeft="22dp"
            android:paddingTop="12dp"
            android:paddingRight="22dp"
            android:paddingBottom="12dp"
            android:text="Обменять баллы"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvLoyaltyProgram"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="29dp"
            android:layout_marginBottom="@dimen/not_organic_user_footer_padding_bottom"
            android:clipToPadding="false"
            android:overScrollMode="never" />

    </LinearLayout>
</androidx.core.widget.NestedScrollView>
