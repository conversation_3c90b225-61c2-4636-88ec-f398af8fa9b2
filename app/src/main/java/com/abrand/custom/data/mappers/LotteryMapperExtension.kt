package com.abrand.custom.data.mappers

import com.abrand.custom.BuyLotteryTicketsMutation
import com.abrand.custom.BuyLotteryTicketsPackageMutation
import com.abrand.custom.GetHomeAdditionalBlocksQuery
import com.abrand.custom.GetLotteriesQuery
import com.abrand.custom.data.entity.lottery.*
import com.abrand.custom.fragment.FragmentFullLotteryItem
import com.abrand.custom.type.LotteryRedemption
import com.abrand.custom.fragment.FragmentLotteriesItem
import com.abrand.custom.type.LotteryStatus

fun List<GetLotteriesQuery.Item?>?.mapToListLottery(): List<LocalLottery> {
    val result = arrayListOf<LocalLottery>()

    if (this != null) {
        for (item in this) {
            if (item != null) {
                val localLottery = item.asStandardLottery?.fragments?.fragmentLotteriesItem?.mapToLocalLottery()
                if (localLottery != null) {
                    result.add(localLottery)
                }
            }
        }
    }

    return result
}

fun FragmentLotteriesItem.mapToLocalLottery(): LocalLottery {
    val prizesSum = if (this.prizesSum is String) {
        this.prizesSum.toDouble()
    } else {
        0.0
    }

    return LocalLottery(
        this.id,
        this.name,
        this.status.mapToLocalLotteryStatus(),
        prizesSum,
        this.prizeFundByString,
        this.startAt,
        this.finishAt,
        this.bannerMobile
    )
}

fun LotteryStatus.mapToLocalLotteryStatus(): LocalLotteryStatus {
    return when (this) {
        LotteryStatus.COMING -> LocalLotteryStatus.COMING
        LotteryStatus.IN_PROCESS -> LocalLotteryStatus.IN_PROCESS
        LotteryStatus.COMPLETED -> LocalLotteryStatus.COMPLETED
        LotteryStatus.NEW -> LocalLotteryStatus.NEW
        LotteryStatus.UNKNOWN__ -> LocalLotteryStatus.UNKNOWN
    }
}

fun FragmentFullLotteryItem.mapToLocalLottery(): LocalLottery {
    val prizesSum = if (this.prizesSum is String) {
        this.prizesSum.toDouble()
    } else {
        0.0
    }

    val ticketPrice = if (this.ticketPrice is String) {
        this.ticketPrice.toDouble()
    } else {
        0.0
    }

    return LocalLottery(
        this.id,
        this.name,
        this.status.mapToLocalLotteryStatus(),
        prizesSum,
        this.prizeFundByString,
        this.startAt,
        this.finishAt,
        this.bannerMobile,
        this.textMobile,
        this.redemptionType?.toLocalRedemptionType(),
        ticketPrice,
        this.prizes.mapToLocalPrizes(),
        this.winners.mapToLocalWinners(),
        this.userTicketsBatch?.mapToLocalLotteryTickets(),
        this.ticketsPackageSettings.mapToLocalTicketPackages()
    )
}

fun LotteryRedemption.toLocalRedemptionType(): LocalLottery.RedemptionType {
    return if (this == LotteryRedemption.REDEMPTION) {
        LocalLottery.RedemptionType.REDEMPTION
    } else {
        LocalLottery.RedemptionType.NO_REDEMPTION
    }
}

fun List<FragmentFullLotteryItem.Prize?>?.mapToLocalPrizes(): MutableList<LocalLotteryPrize> {
    val localPrizes = mutableListOf<LocalLotteryPrize>()
    if (this != null) {
        for (prize in this) {
            if (prize != null) {
                val prizeSum = if (prize.sum is String) {
                    prize.sum.toDouble()
                } else {
                    0.0
                }
                val localPrize = LocalLotteryPrize(prize.place, prizeSum)
                localPrizes.add(localPrize)
            }
        }
    }
    return localPrizes
}

fun List<FragmentFullLotteryItem.Winner?>?.mapToLocalWinners(): MutableList<LocalLotteryWinner> {
    val localWinners = mutableListOf<LocalLotteryWinner>()

    if (this != null) {
        for (winner in this) {
            if (winner != null) {
                val prizeSum = if (winner.prize.sum is String) {
                    winner.prize.sum.toDouble()
                } else {
                    0.0
                }
                localWinners.add(
                    LocalLotteryWinner(
                        winner.id,
                        winner.distributed,
                        winner.prize.place,
                        winner.user?.id,
                        winner.user?.formattedUserName,
                        winner.ticketId,
                        winner.isGold,
                        prizeSum,
                        winner.prize.prize
                    )
                )
            }
        }
    }

    return localWinners
}

fun FragmentFullLotteryItem.UserTicketsBatch.mapToLocalLotteryTickets() : MutableList<LocalLotteryTicket> {
    val localTickets = mutableListOf<LocalLotteryTicket>()

    if (this.items != null) {
        for (ticket in this.items) {
            if (ticket != null) {
                localTickets.add(LocalLotteryTicket(ticket.id, ticket.isGold))
            }
        }
    }

    return localTickets
}

fun List<BuyLotteryTicketsMutation.BuyLotteryTicket?>?.mapToLocalLotteryTickets() : MutableList<LocalLotteryTicket> {
    val localTickets = mutableListOf<LocalLotteryTicket>()

    if (this != null) {
        for (ticket in this) {
            if (ticket != null) {
                localTickets.add(LocalLotteryTicket(ticket.id, ticket.isGold))
            }
        }
    }

    return localTickets
}

fun List<FragmentFullLotteryItem.TicketsPackageSetting?>?.mapToLocalTicketPackages() : MutableList<LocalLotteryTicketPackage> {
    val localTicketPackages = mutableListOf<LocalLotteryTicketPackage>()

    if (this != null) {
        for (ticketPackage in this) {
            if (ticketPackage != null) {

                val priceWithDiscount = if (ticketPackage.priceWithDiscount is String) {
                    ticketPackage.priceWithDiscount.toDouble()
                } else {
                    0.0
                }

                val fullPrice = if (ticketPackage.fullPrice is String) {
                    ticketPackage.fullPrice.toDouble()
                } else {
                    0.0
                }

                localTicketPackages.add(
                    LocalLotteryTicketPackage(
                        ticketPackage.ticketCount, ticketPackage.discount,
                        priceWithDiscount, fullPrice
                    )
                )
            }
        }
    }

    return localTicketPackages
}

fun List<BuyLotteryTicketsPackageMutation.BuyLotteryTicketsPackage?>?.mapToLocalTickets(): MutableList<LocalLotteryTicket> {
    val localTickets = mutableListOf<LocalLotteryTicket>()

    if (this != null) {
        for (ticket in this) {
            if (ticket != null) {
                localTickets.add(LocalLotteryTicket(ticket.id, ticket.isGold))
            }
        }
    }

    return localTickets
}

fun List<GetHomeAdditionalBlocksQuery.Item1?>.mapToFirstLocalLottery(): LocalLottery? {
    return if (this.isNotEmpty()) {
        this[0]?.asStandardLottery?.fragments?.fragmentLotteriesItem?.mapToLocalLottery()
    } else {
        null
    }
}
