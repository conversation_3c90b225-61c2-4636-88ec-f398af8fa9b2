package com.abrand.custom.ui.loyaltyprogram

import android.os.Bundle
import android.text.Html
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.User
import com.abrand.custom.databinding.FragmentLoyaltyProgramBinding
import com.abrand.custom.ui.activitymain.MainActivity

class LoyaltyProgramFragment : Fragment(R.layout.fragment_loyalty_program) {
    private lateinit var viewModel: LoyaltyProgramViewModel
    private lateinit var adapter: LoyaltyProgramAdapter
    private var binding: FragmentLoyaltyProgramBinding? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding = FragmentLoyaltyProgramBinding.bind(view)
        viewModel = ViewModelProviders.of(this).get(LoyaltyProgramViewModel::class.java)

        setupInsets()
        observeViewModel()

        if (User.State.PLAYER == Settings.get().userState) {
            applyLoggedState()
        } else {
            applyNotLoggedState()
        }

        binding?.tvDescription?.setText(Html.fromHtml(getString(R.string.loyalty_program_description)), TextView.BufferType.SPANNABLE)
        binding?.rvLoyaltyProgram?.layoutManager = LinearLayoutManager(context)
        val mainActivity = activity as MainActivity
        adapter = LoyaltyProgramAdapter(mainActivity.loyaltyStatusId, mainActivity.loyaltyProgressPercent)
        binding?.rvLoyaltyProgram?.adapter = adapter
        viewModel.getLoyaltyStatuses()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun setupInsets() {
        binding?.apply {
            ViewCompat.setOnApplyWindowInsetsListener(tvTitle) { view, insets ->
                view.updatePadding(top = insets.systemWindowInsetTop)
                insets
            }
            ViewCompat.setOnApplyWindowInsetsListener(rvLoyaltyProgram) { view, insets ->
                view.updatePadding(bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }

    private fun observeViewModel() {
        viewModel.loyaltyStatusesLiveData.observe(viewLifecycleOwner, Observer {
            adapter.setList(it?.toMutableList() ?: mutableListOf())
        })

        viewModel.serverErrorLiveData.observe(viewLifecycleOwner, Observer {
            Toast.makeText(context, it.errorMessage, Toast.LENGTH_SHORT).show()
        })

        viewModel.apolloExceptionLiveData.observe(viewLifecycleOwner, Observer {
            (activity as MainActivity).showConnectionIssueMessage(it)
        })
    }

    private fun applyLoggedState() {
        binding?.apply {
            btnExchangePoints.visibility = View.VISIBLE
            btnExchangePoints.setOnClickListener {
                (activity as MainActivity).openExchangePointsScreen()
            }
        }
    }

    private fun applyNotLoggedState() {
        binding?.btnExchangePoints?.visibility = View.GONE
    }

    fun updateProgress(currentProgress : Int) {
        adapter.updateProgress(currentProgress)
    }

    fun updateLoyaltyStatusId(currentLoyaltyStatusId: Int) {
        adapter.updateLoyaltyStatusId(currentLoyaltyStatusId)
    }

}
