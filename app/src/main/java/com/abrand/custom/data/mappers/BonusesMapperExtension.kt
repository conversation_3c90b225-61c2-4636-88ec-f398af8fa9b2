package com.abrand.custom.data.mappers

import com.abrand.custom.GetBonusRefundQuery
import com.abrand.custom.GetBonusesQuery
import com.abrand.custom.data.entity.*
import com.abrand.custom.fragment.FragmentBonusPrize

fun GetBonusesQuery.Data.mapToBonusesResponse(): BonusesResponse {

    val bonuses = this.bonuses?.items?.filterNotNull() ?: emptyList()
    val localBonuses = mutableListOf<Bonus>()
    for (bonus in bonuses) {
        localBonuses.add(bonus.mapToLocalBonus())
    }

    val promotions = this.promotions?.filterNotNull() ?: emptyList()
    val localPermanentPromotions = mutableListOf<PermanentPromotion>()
    for (promotion in promotions) {
        localPermanentPromotions.add(promotion.mapToLocalPermanentPromotion())
    }

    return BonusesResponse(localBonuses, localPermanentPromotions)
}

fun GetBonusesQuery.Promotion.mapToLocalPermanentPromotion(): PermanentPromotion {
    return PermanentPromotion(
        this.id,
        this.name,
        this.description,
        this.showInput,
        this.imgCharacter,
        this.imgMob,
        this.imgPattern,
        this.buttonName,
        this.buttonUrl,
        this.moveToAvailable
    )
}

fun GetBonusesQuery.Item.mapToLocalBonus(): Bonus {
    val prizes: ArrayList<FragmentBonusPrize> = ArrayList()
    if (this.prizes != null) {
        for (prize in this.prizes) {
            prize?.fragments?.fragmentBonusPrize?.let {
                prizes.add(it)
            }
//                prize?.fragments?.fragmentBonusPrize?.asBonusFreeSpinsPrize?.let {
//                    prizes.add(it)
//                }
//                prize?.fragments?.fragmentBonusPrize?.asBonusGiftSpinsPrize?.let {
//                    prizes.add(it)
//                }
//                prize?.fragments?.fragmentBonusPrize?.asBonusPointsPrize?.let {
//                    prizes.add(it)
//                }
//                prize?.fragments?.fragmentBonusPrize?.asBonusX2Prize?.let {
//                    prizes.add(it)
//                }
//                prize?.fragments?.fragmentBonusPrize?.asBonusX3Prize?.let {
//                    prizes.add(it)
//                }
//                prize?.fragments?.fragmentBonusPrize?.asBonusLotteryTicketsPrize?.let {
//                    prizes.add(it)
//                }
//                prize?.fragments?.fragmentBonusPrize?.asBonusWheelFortuneSpinPrize?.let {
//                    prizes.add(it)
//                }
//                prize?.fragments?.fragmentBonusPrize?.asBonusTalismanPrize?.let {
//                    prizes.add(it)
//                }
        }
    }
    return Bonus(
        id = this.id,
        name = this.name,
        depositAmount = this.depositAmount,
        buttonName = this.buttonName,
        imgCharacter = this.imgCharacter,
        imgPattern = this.imgPattern,
        isActivated = this.isActivated,
        isDeactivated = this.isDeactivated,
        rules = this.rules,
        isProgressive = this.isProgressive,
        progressiveCurrentStep = this.progressiveCurrentStep,
        paidSumOfDeposits = this.paidSumOfDeposits,
        isPromoCodeRequired = this.isPromoCodeRequired,
        availableAfterDeactivation = this.availableAfterDeactivation,
        dateEnd = this.dateEnd,
        secondsToEnd = this.asBonusListTimer?.secondsToEnd?.toInt(),
        bonusEvent = this.bonusEvent?.let { PromotionItem.BonusEvent.getByValue(it) },
        isCalculatedByDepositSum = this.isCalculatedByDepositSum,
        prizes = prizes
    )
}

fun GetBonusRefundQuery.Wallet.mapToBonusRefund(): BonusRefund {
    val bonusBetSum: Double = this.bonusBetSum.toString().toDouble()
    val bonusRefundSum: Double = this.bonusRefundSum.toString().toDouble()
    val bonusPercentToRefund: Double = this.bonusPercentToRefund.toString().toDouble()
    return BonusRefund(bonusBetSum, bonusRefundSum, bonusPercentToRefund)
}
