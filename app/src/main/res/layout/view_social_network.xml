<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/social_login_btn_width"
    android:layout_height="@dimen/social_login_btn_height">

    <ImageView
        android:id="@+id/iv_social_network"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="@dimen/social_icon_padding"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
