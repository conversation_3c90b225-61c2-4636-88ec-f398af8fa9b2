package com.abrand.custom.ui.views

import android.content.Context
import android.util.AttributeSet
import com.abrand.custom.R

class BonusWageringDrawerView : BonusWageringView {

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr)

    override fun getLayoutId(): Int {
        return R.layout.view_bonus_wagering_drawer
    }

    override fun isHideProgressForReset(): <PERSON><PERSON><PERSON> {
        return false
    }
}
