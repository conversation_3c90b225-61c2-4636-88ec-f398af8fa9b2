package com.abrand.custom.tools

import android.app.Activity
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.net.Uri
import android.os.Build
import androidx.appcompat.app.AlertDialog
import com.abrand.custom.R

object NotificationUtil {

    private const val REQUEST_APP_SETTINGS = 102

    fun createNotificationChannel(context: Context) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val channelId = context.getString(R.string.general_notification_channel_id)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                context.getString(R.string.general_notification_channel_name),
                NotificationManager.IMPORTANCE_HIGH
            )
            notificationManager.createNotificationChannel(channel)
        }
    }

    fun showNotificationsPermissionDeniedDialog(activity: Activity) {
        val builder = AlertDialog.Builder(activity)
        builder.setCancelable(false)
        val message: String = activity.getString(R.string.notifications_permission_denied_message)
        builder.setMessage(message)
            .setPositiveButton(R.string.go_to_settings) { dialog: DialogInterface?, id: Int ->
                openApplicationSettings(activity)
            }.show()
    }

    private fun openApplicationSettings(activity: Activity) {
        val appSettingsIntent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
            Uri.parse("package:" + activity.packageName))
        activity.startActivityForResult(appSettingsIntent, REQUEST_APP_SETTINGS)
    }
}
