package com.abrand.custom.ui.news

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.core.view.ViewCompat
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.abrand.custom.R
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.NewsItem
import com.abrand.custom.data.entity.User
import com.abrand.custom.databinding.FragmentNewsBinding
import com.abrand.custom.interfaces.FooterListener
import com.abrand.custom.tools.GeneralTools
import com.abrand.custom.ui.activitymain.MainActivity
import com.abrand.custom.ui.payments.PaymentsFragment

class NewsFragment : Fragment(R.layout.fragment_news) {
    private lateinit var viewModel: NewsViewModel
    private lateinit var newsAdapter: NewsAdapter
    private var binding: FragmentNewsBinding? = null

    companion object {
        const val LOAD_STEP = 5
        const val CARD_NEWS_NUM = 2
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Use it here because onActivityCreated(savedInstanceState: Bundle?) is deprecated
        arguments?.let {
            (it.getSerializable(NewsInternalFragment.NEWS_ITEM_KEY) as? NewsItem)?.let {newsItem ->
                if ( activity?.lifecycle?.currentState?.isAtLeast(Lifecycle.State.STARTED) == true ) {
                    (activity as MainActivity).openNewsInternalScreen(newsItem)
                }
            }
        }

        binding = FragmentNewsBinding.bind(view)
        binding?.rvNews?.layoutManager = LinearLayoutManager(context)
        newsAdapter = NewsAdapter(getNewsCardWidth(), getNewsCardHeight())
        newsAdapter.newsClickListener = newsClickListener
        newsAdapter.footerListener = footerListener
        binding?.rvNews?.adapter = newsAdapter

        viewModel = ViewModelProvider(this).get(NewsViewModel::class.java)
        viewModel.getNews(0, LOAD_STEP)

        observeViewModel()
        setupInsets()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding = null
    }

    private fun observeViewModel() {
        viewModel.newsLiveData.observe(viewLifecycleOwner, Observer {
            newsAdapter.currentTotalNews = it.total
            val newsItems = mutableListOf<NewsItem>()
            for ((index, localNews) in it.newsList.withIndex()) {
                if (it.offset == 0 && index < CARD_NEWS_NUM) {
                    newsItems.add(NewsItem(NewsItem.ItemType.CARD_NEWS, localNews))
                } else {
                    newsItems.add(NewsItem(NewsItem.ItemType.SMALL_NEWS, localNews))
                }
            }
            if (it.offset == 0) {
                newsAdapter.setList(newsItems)
            } else {
                newsAdapter.appendList(newsItems)
            }
            newsAdapter.setGameLoadingState(false)
        })

        viewModel.serverErrorLiveData.observe(viewLifecycleOwner, Observer {
            Toast.makeText(context, it.errorMessage, Toast.LENGTH_SHORT).show()
            newsAdapter.setGameLoadingState(false)
        })

        viewModel.apolloExceptionLiveData.observe(viewLifecycleOwner, Observer {
            newsAdapter.setGameLoadingState(false)
            (activity as MainActivity).showConnectionIssueMessage(it)
        })
    }

    private fun setupInsets() {
        binding?.apply {
            ViewCompat.setOnApplyWindowInsetsListener(rvNews) { view, insets ->
                view.updatePadding(top = insets.systemWindowInsetTop, bottom = insets.systemWindowInsetBottom)
                insets
            }
        }
    }

    private val newsClickListener = object : NewsAdapter.NewsClickListener {
        override fun onNewsClicked(newsItem: NewsItem) {
            (activity as MainActivity).openNewsInternalScreen(newsItem)
        }

        override fun onLoadMoreClicked() {
            newsAdapter.setGameLoadingState(true)
            viewModel.getNews(newsAdapter.getNewsCount(), LOAD_STEP)
        }
    }

    private val footerListener = object : FooterListener {
        override fun onClickPaymentSystems() {
            if (User.State.PLAYER == Settings.get().userState) {
                (activity as? MainActivity)?.openPaymentsScreen(PaymentsFragment.Screen.DEPOSIT)
            } else {
                (activity as? MainActivity)?.openEnterScreen(false)
            }
        }
    }

    private fun getNewsCardWidth(): Int {
        val screenWidth = GeneralTools.getScreenWidth(context)
        return screenWidth - 2 * (context?.resources?.getDimensionPixelSize(R.dimen.news_card_side_margin)
                ?: 0)
    }

    private fun getNewsCardHeight() : Int {
        return context?.resources?.getDimensionPixelSize(R.dimen.news_card_height) ?: 0
    }
}
