<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/news_small_first_top_margin"
    android:layout_marginBottom="@dimen/news_small_first_top_margin">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="6dp"
        android:paddingStart="@dimen/activity_horizontal_margin"
        android:paddingEnd="@dimen/activity_horizontal_margin"
        android:text="@string/tournaments_ended_title"
        android:textColor="#FFFFFF"
        android:textFontWeight="400"
        android:textSize="24sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>