package com.abrand.custom.ui.halloffame

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.TextView
import com.abrand.custom.R

class MonthArrayAdapter(context: Context, resource: Int, objects: MutableList<String>) :
    ArrayAdapter<String>(context, resource, objects) {
    var selectedPosition = 0

    override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
        var view = convertView
        val text: TextView

        if (selectedPosition == position) {
            view = LayoutInflater.from(context)
                .inflate(R.layout.item_hall_of_fame_spinner_open_selected, parent, false)
        } else if (convertView == null) {
            view = LayoutInflater.from(context)
                .inflate(R.layout.item_hall_of_fame_spinner_open_unselected, parent, false)
        }

        try {
            text = view as TextView
        } catch (e: ClassCastException) {
            Log.e("ArrayAdapter", "You must supply a resource ID for a TextView")
            throw IllegalStateException("ArrayAdapter requires the resource ID to be a TextView", e)
        }

        val item = getItem(position)
        if (item is CharSequence) {
            text.text = item
        }

        return view
    }
}
