package com.abrand.custom.ui.wheeloffortune

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.abrand.custom.data.Resource
import com.abrand.custom.data.Settings
import com.abrand.custom.data.entity.LocalWheel
import com.abrand.custom.data.entity.ServerError
import com.abrand.custom.data.entity.User
import com.abrand.custom.data.entity.WheelSector
import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.network.ApolloProcessorKt
import com.abrand.custom.network.retrySubscription
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException
import com.apollographql.apollo3.exception.ApolloNetworkException
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class WheelOfFortuneViewModel: ViewModel() {

    private val _wheelLiveData = MutableLiveData<Resource<LocalWheel, ServerError, ApolloException>>()
    val wheelLiveData: LiveData<Resource<LocalWheel, ServerError, ApolloException>> = _wheelLiveData

    private val _freeSpinsLiveData = MutableLiveData<Resource<Int, ServerError, ApolloException>>()
    val freeSpinsLiveData: LiveData<Resource<Int, ServerError, ApolloException>> = _freeSpinsLiveData

    private val _rulesLiveData = MutableLiveData<Resource<String, ServerError, ApolloException>>()
    val rulesLiveData: LiveData<Resource<String, ServerError, ApolloException>> = _rulesLiveData

    private val _wheelSpinLiveData = MutableLiveData<Resource<WheelSector, ServerError, ApolloException>>()
    val wheelSpinLiveData: LiveData<Resource<WheelSector, ServerError, ApolloException>> = _wheelSpinLiveData

    private val _isLoading = MutableLiveData(true)
    val isLoading: LiveData<Boolean> = _isLoading

    var isSoundEnable = true
    private var freeSpinsSubscriptionJob: Job? = null
    private val exceptionHandler = CoroutineExceptionHandler { _, e -> Log.e(TAG, e.toString()) }
    private val dispatcher = Dispatchers.Default + exceptionHandler
    private var tempFreeSpins: Int? = 0
    private var isWheelRotating = false

    init {
        getWheel()
        getFreeSpins()
        getWheelRules()
        freeSpinsSubscribe()
    }

    private fun getWheel() {
        val denomination = 1
        ApolloProcessorKt.getWheel(Settings.get().userCurrencyCode, denomination, object : GenericTarget<LocalWheel> {

            override fun onSuccess(t: LocalWheel?) {
                _wheelLiveData.postValue(Resource.success(t))
                if (freeSpinsLiveData.value?.data != null) {
                    _isLoading.postValue(false)
                }
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _wheelLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _wheelLiveData.postValue(Resource.failure(e))
            }

        })
    }

    private fun getFreeSpins() {
        ApolloProcessorKt.getFreeSpins(object : GenericTarget<Int> {

            override fun onSuccess(t: Int?) {
                _freeSpinsLiveData.postValue(Resource.success(t))
                if (wheelLiveData.value?.data != null) {
                    _isLoading.postValue(false)
                }
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _freeSpinsLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _freeSpinsLiveData.postValue(Resource.failure(e))
            }

        })
    }

    private fun getWheelRules() {
        val wheelRulesTextBlockId = "wheel_fortune_rules"
        ApolloProcessorKt.getWheelRules(wheelRulesTextBlockId, object : GenericTarget<String> {

            override fun onSuccess(t: String?) {
                _rulesLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _rulesLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _rulesLiveData.postValue(Resource.failure(e))
            }
        })
    }

    fun wheelSpin() {
        val wheelId = wheelLiveData.value?.data?.id
        if (wheelId == null) {
            Log.d("WheelOfFortuneViewModel", "wheelId is null")
            return
        }

        ApolloProcessorKt.wheelSpin(wheelId, object : GenericTarget<WheelSector> {

            override fun onSuccess(t: WheelSector?) {
                _wheelSpinLiveData.postValue(Resource.success(t))
            }

            override fun onError(errorMessage: String, errorCode: String, fieldsErrors: FieldsErrorsHolder?) {
                _wheelSpinLiveData.postValue(Resource.error(ServerError(errorMessage, errorCode, fieldsErrors)))
            }

            override fun onFailure(e: ApolloException) {
                _wheelSpinLiveData.postValue(Resource.failure(e))
            }

        })
    }

    private fun freeSpinsSubscribe() {
        val userState = Settings.get().userState

        freeSpinsSubscriptionJob?.cancel()
        if (User.State.PLAYER != userState) {
            return
        }

        freeSpinsSubscriptionJob = viewModelScope.launch(dispatcher) {
            try {
                ApolloProcessorKt.subscribeFreeSpins().retrySubscription().collect {
                    val response = it.data?.wofUserFreeSpinsUpdated
                    response?.let { responseInner ->
                        val freeSpins = if (responseInner.isEmpty()) {
                            0
                        } else {
                            responseInner[0]?.spinCount ?: 0
                        }

                        if (isWheelRotating) {
                            tempFreeSpins = freeSpins
                        } else {
                            _freeSpinsLiveData.postValue(Resource.success(freeSpins))
                        }
                    }
                }
            } catch (e: ApolloNetworkException) {
                val crashlytics = FirebaseCrashlytics.getInstance()
                crashlytics.setCustomKey("subscription", "freeSpinsSubscribe")
                crashlytics.recordException(e)
            }
        }
    }

    fun onWheelStart() {
        isWheelRotating = true
        decreaseFreeSpins()
    }

    fun onWheelEnd() {
        isWheelRotating = false
        tempFreeSpins?.let {
            _freeSpinsLiveData.postValue(Resource.success(it))
        }
        tempFreeSpins = null
    }

    private fun decreaseFreeSpins() {
        val freeSpins = freeSpinsLiveData.value?.data
        freeSpins?.let {
            if (freeSpins > 0) {
                _freeSpinsLiveData.postValue(Resource.success(freeSpins - 1))
            }
        }
    }

    companion object {
        private const val TAG = "WheelOfFortuneViewModel"
    }
}
