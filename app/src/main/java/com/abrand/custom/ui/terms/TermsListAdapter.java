package com.abrand.custom.ui.terms;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.abrand.custom.R;
import com.abrand.custom.data.Settings;
import com.abrand.custom.data.entity.RuleItem;
import com.abrand.custom.data.entity.RuleItem.ItemType;
import com.abrand.custom.data.entity.User;
import com.abrand.custom.data.repositories.YearRepository;
import com.abrand.custom.tools.GeneralTools;

import java.util.ArrayList;
import java.util.List;

class TermsListAdapter extends RecyclerView.Adapter {
    private List<RuleItem> items = new ArrayList<>();

    TermsListAdapter(Context context) {
        items.add(new RuleItem(ItemType.CATEGORY, context.getString(R.string.category_1)));
        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i1), "1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i1_1), "1.1"));

        items.add(new RuleItem(ItemType.CATEGORY, context.getString(R.string.category_2)));
        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i2), "2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i2_1), "2.1"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i3), "3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i3_1), "3.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i3_2), "3.2"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i4), "4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i4_1), "4.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i4_2), "4.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i4_3), "4.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i4_4), "4.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i4_5), "4.5"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i5), "5"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i5_1), "5.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i5_2), "5.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i5_3), "5.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i5_4), "5.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i5_5), "5.5"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i5_5_1), "5.5.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i5_5_2), "5.5.2"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i5_5_3), "5.5.3"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i6), "6"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i6_1), "6.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i6_1_1), "6.1.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i6_1_2), "6.1.2"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i6_1_3), "6.1.3"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i6_1_4), "6.1.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i6_2), "6.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i6_3), "6.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i6_4), "6.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i6_5), "6.5"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i6_5_1), "6.5.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i6_5_2), "6.5.2"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i6_5_3), "6.5.3"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i6_5_4), "6.5.4"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i7), "7"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i7_1), "7.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i7_2), "7.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i7_3), "7.3"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i8), "8"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_1), "8.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_2), "8.2"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i8_2_1), "8.2.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i8_2_2), "8.2.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_3), "8.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_4), "8.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_5), "8.5"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_6), "8.6"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_7), "8.7"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_8), "8.8"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_9), "8.9"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_10), "8.10"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i8_10_1), "8.10.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i8_10_2), "8.10.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_11), "8.11"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i8_11_1), "8.11.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i8_11_2), "8.11.2"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i8_11_3), "8.11.3"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i8_11_4), "8.11.4"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i8_11_5), "8.11.5"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_12), "8.12"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_13), "8.13"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_14), "8.14"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_15), "8.15"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_16), "8.16"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_17), "8.17"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_18), "8.18"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_19), "8.19"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_20), "8.20"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i8_21), "8.21"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i9), "9"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i9_1), "9.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i9_2), "9.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i9_3), "9.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i9_4), "9.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i9_5), "9.5"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i10), "10"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i10_1), "10.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i10_1_1), "10.1.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i10_1_2), "10.1.2"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i10_1_3), "10.1.3"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i10_1_4), "10.1.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i10_2), "10.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i10_3), "10.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i10_4), "10.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i10_5), "10.5"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i10_6), "10.6"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i10_7), "10.7"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i11), "11"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i11_1), "11.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i11_2), "11.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i11_3), "11.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i11_4), "11.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i11_5), "11.5"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i11_6), "11.6"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i12), "12"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i12_1), "12.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i12_1_1), "12.1.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i12_1_2), "12.1.2"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i12_1_3), "12.1.3"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i12_1_4), "12.1.4"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i12_1_5), "12.1.5"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i12_1_6), "12.1.6"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i12_1_7), "12.1.7"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i12_2), "12.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i12_3), "12.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i12_4), "12.4"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i13), "13"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i13_1), "13.1"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i14), "14"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i14_1), "14.1"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i15), "15"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i15_1), "15.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i15_2), "15.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i15_3), "15.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i15_4), "15.4"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i16), "16"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i16_1), "16.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i16_2), "16.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i16_3), "16.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i16_4), "16.4"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i17), "17"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i17_1), "17.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i17_2), "17.2"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i17_2_1), "17.2.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i17_2_2), "17.2.2"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i17_2_3), "17.2.3"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i17_2_4), "17.2.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i17_3), "17.3"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i17_3_1), "17.3.1"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i17_3_2), "17.3.2"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i17_3_3), "17.3.3"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i17_3_4), "17.3.4"));
        items.add(new RuleItem(ItemType.THIRD_LEVEL, context.getString(R.string.i17_4), "17.4"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i18), "18"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i18_1), "18.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i18_2), "18.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i18_3), "18.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i18_4), "18.4"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i19), "19"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i19_1), "19.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i19_2), "19.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i19_3), "19.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i19_4), "19.4"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i20), "20"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i20_1), "20.1"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i21), "21"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i21_1), "21.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i21_2), "21.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i21_3), "21.3"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i22), "22"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i22_1), "22.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i22_2), "22.2"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i23), "23"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i23_1), "23.1"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i24), "24"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i24_1), "24.1"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i25), "25"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i25_1), "25.1"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i26), "26"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i26_1), "26.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i26_2), "26.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i26_3), "26.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i26_4), "26.4"));
        items.add(new RuleItem(ItemType.EXCHANGE_TABLE));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i26_5), "26.5"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i26_6), "26.6"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i26_7), "26.7"));

        items.add(new RuleItem(ItemType.FIRST_LEVEL, context.getString(R.string.i27), "27"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_1), "27.1"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_2), "27.2"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_3), "27.3"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_4), "27.4"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_5), "27.5"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_6), "27.6"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_7), "27.7"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_8), "27.8"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_9), "27.9"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_10), "27.10"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_11), "27.11"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_12), "27.12"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_13), "27.13"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_14), "27.14"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_15), "27.15"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_16), "27.16"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_17), "27.17"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_18), "27.18"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_19), "27.19"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_20), "27.20"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_21), "27.21"));
        items.add(new RuleItem(ItemType.SECOND_LEVEL, context.getString(R.string.i27_22), "27.22"));

        items.add(new RuleItem(ItemType.FOOTER));
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view;
        if ( ItemType.CATEGORY.getId() == viewType ) {
            view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rules_category, parent, false);
            return new CategoryViewHolder(view);
        } else if ( ItemType.FIRST_LEVEL.getId() == viewType ) {
            view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rules_first_level, parent, false);
            return new FirstLevelViewHolder(view);
        } else if ( ItemType.SECOND_LEVEL.getId() == viewType ) {
            view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rules_second_level, parent, false);
            return new SecondLevelViewHolder(view);
        } else if ( ItemType.THIRD_LEVEL.getId() == viewType ) {
            view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rules_third_level, parent, false);
            return new ThirdLevelViewHolder(view);
        } else if(ItemType.EXCHANGE_TABLE.getId() == viewType) {
            view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rules_exchange_table, parent, false);
            return new ExchangeTableViewHolder(view);
        } else {
            view = LayoutInflater.from(parent.getContext()).inflate(R.layout.view_footer, parent, false);
            return new FooterViewHolder(view);
        }
    }

    @Override
    public int getItemViewType(int position) {
        return items.get(position).getItemType().getId();
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if ( holder instanceof CategoryViewHolder ) {
            ((CategoryViewHolder) holder).category.setText(items.get(position).getText());
        } else if ( holder instanceof FirstLevelViewHolder ) {
            ((FirstLevelViewHolder) holder).number.setText(items.get(position).getNumber());
            ((FirstLevelViewHolder) holder).text.setText(items.get(position).getText());
        } else if ( holder instanceof SecondLevelViewHolder ) {
            ((SecondLevelViewHolder) holder).number.setText(items.get(position).getNumber());
            ((SecondLevelViewHolder) holder).text.setText(items.get(position).getText());
        } else if ( holder instanceof ThirdLevelViewHolder ) {
            ((ThirdLevelViewHolder) holder).number.setText(items.get(position).getNumber());
            ((ThirdLevelViewHolder) holder).text.setText(items.get(position).getText());
        }

        if ( position == 0 ) {
            int padding = GeneralTools.getActionBarHeight(holder.itemView.getContext());
            holder.itemView.setPadding(
                    holder.itemView.getPaddingLeft(),
                    padding,
                    holder.itemView.getPaddingRight(),
                    holder.itemView.getPaddingBottom()
            );
        } else {
            holder.itemView.setPadding(
                    holder.itemView.getPaddingLeft(),
                    0,
                    holder.itemView.getPaddingRight(),
                    holder.itemView.getPaddingBottom()
            );
        }
    }

    public static class CategoryViewHolder extends RecyclerView.ViewHolder {
        public TextView category;

        public CategoryViewHolder(View v) {
            super(v);
            category = v.findViewById(R.id.tv_category);
        }
    }

    public static class FirstLevelViewHolder extends RecyclerView.ViewHolder {
        public TextView number;
        public TextView text;

        public FirstLevelViewHolder(View v) {
            super(v);
            number = v.findViewById(R.id.tv_number);
            text = v.findViewById(R.id.tv_text);
        }
    }

    public static class SecondLevelViewHolder extends RecyclerView.ViewHolder {
        public TextView number;
        public TextView text;

        public SecondLevelViewHolder(View v) {
            super(v);
            number = v.findViewById(R.id.tv_number);
            text = v.findViewById(R.id.tv_text);
        }
    }

    public static class ThirdLevelViewHolder extends RecyclerView.ViewHolder {
        public TextView number;
        public TextView text;

        public ThirdLevelViewHolder(View v) {
            super(v);
            number = v.findViewById(R.id.tv_number);
            text = v.findViewById(R.id.tv_text);
        }
    }

    public static class ExchangeTableViewHolder extends RecyclerView.ViewHolder {

        public ExchangeTableViewHolder(View v) {
            super(v);
        }
    }

    public class FooterViewHolder extends RecyclerView.ViewHolder {
        FooterViewHolder(@NonNull View itemView) {
            super(itemView);
            ConstraintLayout footerEmblems = itemView.findViewById(R.id.l_footer_emblems);
            if ( User.State.ORGANIC.equals(Settings.get().getUserState()) ) {
                footerEmblems.setVisibility(View.GONE);
            } else {
                footerEmblems.setVisibility(View.VISIBLE);
            }
            TextView tvYear = itemView.findViewById(R.id.tv_copyright_year);
            if ( tvYear != null ) {
                tvYear.setText(tvYear.getContext().getString(
                        R.string.app_copyright_year, YearRepository.INSTANCE.getCurrentYear())
                );
            }
        }
    }

}
