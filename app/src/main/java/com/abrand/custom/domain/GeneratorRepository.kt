package com.abrand.custom.domain


interface GeneratorRepository {

    suspend fun initCustomer(
        urlParams: String,
        uuid: String,
        refCode: String,
        downloadCode: String,
        domains: List<String>
    ): String

    suspend fun check(
        urlParams: String,
        domains: List<String>
    ): CheckResponse

    suspend fun getInstalled(
        urlParams: String,
        domains: List<String>
    ): String

    suspend fun sendAnalytics(url: String)

    fun addParametersToUrl(url: String, paramsMap: Map<String, String>): String
}
