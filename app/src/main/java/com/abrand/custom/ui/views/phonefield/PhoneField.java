package com.abrand.custom.ui.views.phonefield;

import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.telephony.PhoneNumberUtils;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.View;
import android.view.Window;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.abrand.custom.R;
import com.abrand.custom.data.entity.Country;
import com.abrand.custom.tools.GeneralTools;
import com.abrand.custom.ui.views.textfield.TextField;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class Phone<PERSON>ield extends TextField {
    private       ImageView             ivArrowDropDown;
    private       ImageView             ivCountryFlag;
    private       TextView              tvCountryPhoneCode;
    private       List<Country>         countries        = new ArrayList<>();
    private       CountryChangeListener countryChangeListener;
    private final int                   MIN_PHONE_LENGTH = 7;

    public PhoneField(Context context) {
        super(context);
        init(context);
    }

    public PhoneField(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public PhoneField(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        ivArrowDropDown = rootView.findViewById(R.id.iv_arrow_dropdown);
        ivCountryFlag = rootView.findViewById(R.id.iv_country_flag);
        tvCountryPhoneCode = rootView.findViewById(R.id.country_phone_code);

        View countryContainer = rootView.findViewById(R.id.country_container);
        countryContainer.setOnClickListener(v -> {
            if ( isFieldEditable() ) {
                showCountrySelectionDialog(context);
            }
        });
    }

    public String getPhone() {
        return !TextUtils.isEmpty(getText()) ? getCountryPhoneCode() + GeneralTools.removeSpaces(getText()) : "";
    }

    private String getCountryPhoneCode() {
        return tvCountryPhoneCode.getText().toString();
    }

    public void setPhone(String countryCode, String phone) {
        tvCountryPhoneCode.setText(countryCode);
        setText(phone);
    }

    @Override
    public int getLayoutId() {
        return R.layout.view_phone_field;
    }

    public void setCountryChangeListener(CountryChangeListener countryChangeListener) {
        this.countryChangeListener = countryChangeListener;
    }

    @Override
    protected void setUpFocusManagement(Context context) {
        editText.setOnFocusChangeListener((v, hasFocus) -> {
            if ( !hasFocus ) {
                GeneralTools.hideKeyboard(context, editText);
            }
        });
    }

    @Override
    public void setFieldNotEditable() {
        super.setFieldNotEditable();
        setHint(getContext().getString(R.string.phone_number_not_editable_hint));
        ivArrowDropDown.setImageResource(R.drawable.ic_arrow_dropdown_white);
        tvCountryPhoneCode.setTextColor(TEXT_NOT_EDITABLE_COLOR);
    }

    private void showCountrySelectionDialog(Context context) {
        Dialog dialog = new Dialog(context);
        dialog.setContentView(R.layout.dialog_country_selection);
        dialog.show();

        Window window = dialog.getWindow();
        if ( window != null ) {
            window.setLayout(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
        }

        if ( countries.isEmpty() ) {
            countries = GeneralTools.getCountries(context);
        }
        Collections.sort(countries, (country1, country2) -> country1.getName().compareTo(country2.getName()));
        RecyclerView rvCountries = dialog.findViewById(R.id.rv_countries);
        rvCountries.setLayoutManager(new LinearLayoutManager(context));
        CountryAdapter countryAdapter = new CountryAdapter(countries,
                country -> {
                    setUserCountry(context, country);
                    dialog.cancel();
                });
        rvCountries.setAdapter(countryAdapter);
        initEditTextSearch(dialog, countryAdapter);
    }

    private void initEditTextSearch(Dialog dialog, CountryAdapter countryAdapter) {
        EditText etSearch = dialog.findViewById(R.id.et_search);
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String input = s.toString();
                if ( !TextUtils.isEmpty(input) ) {
                    String firstCharacter = input.substring(0, 1);
                    if ( Character.isLetter(firstCharacter.charAt(0)) ) {
                        //якщо перший символ буква шукаємо по назвах
                        countryAdapter.setList(getCountriesBySearchName(input));
                    } else if ( "+".equals(firstCharacter) || Character.isDigit(firstCharacter.charAt(0)) ) {
                        //якщо перший символ + або цифра шукаємо по кодах
                        countryAdapter.setList(getCountriesBySearchCode(input));
                    } else {
                        countryAdapter.setList(new ArrayList<>());
                    }
                } else {
                    countryAdapter.setList(countries);
                }
            }
        });

        dialog.findViewById(R.id.btn_search_clear).setOnClickListener(v -> {
            Editable etSearchEditable = etSearch.getText();
            if ( etSearchEditable.toString().isEmpty() ) {
                dialog.cancel();
            } else {
                etSearch.getText().clear();
            }
        });
    }

    private List<Country> getCountriesBySearchName(String input) {
        List<Country> result = new ArrayList<>();
        for ( Country country : countries ) {
            if ( country.getName().toLowerCase().contains(input.toLowerCase()) ) {
                result.add(country);
            }
        }
        return result;
    }

    private List<Country> getCountriesBySearchCode(String input) {
        List<Country> result = new ArrayList<>();
        for ( Country country : countries ) {
            if ( country.getPhoneCode().contains(input) ) {
                result.add(country);
            }
        }
        return result;
    }

    public void setUserCountry(Context context, Country selectedCountry) {
        if ( countryChangeListener != null ) {
            countryChangeListener.onCountryChanged(selectedCountry.getIsoCode());
        }

        Drawable flagDrawable = GeneralTools.getDrawableByCountryCode(context, selectedCountry.getIsoCode());
        ivCountryFlag.setImageDrawable(flagDrawable);
        tvCountryPhoneCode.setText(selectedCountry.getPhoneCode());
    }

    public boolean isPhoneNumberValid() {
        return TextUtils.isEmpty(getText()) ||
                (getPhone().length() >= MIN_PHONE_LENGTH && PhoneNumberUtils.isGlobalPhoneNumber(getPhone()));
    }

    public interface CountryChangeListener {
        void onCountryChanged(String countryISO);
    }
}
