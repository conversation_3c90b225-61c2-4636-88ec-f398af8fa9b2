package com.abrand.custom.data.entity

class GameRoomItem {
    val itemViewType: ItemViewType
    var gameItem: LocalGameItem? = null

    constructor(itemViewType: ItemViewType) {
        this.itemViewType = itemViewType
    }

    constructor(itemViewType: ItemViewType, localGameItem: LocalGameItem) {
        this.itemViewType = itemViewType
        this.gameItem = localGameItem
    }

    enum class ItemViewType(val id: Int) {
        HEADER(0), GAME(1), FOOTER(2), GAME_LOAD(3)
    }

    fun isTheSame(other: Any?): Boolean {
        return if (other is GameRoomItem) {
            if (ItemViewType.HEADER == this.itemViewType && ItemViewType.HEADER == other.itemViewType) {
                true
            } else if (ItemViewType.FOOTER == this.itemViewType && ItemViewType.FOOTER == other.itemViewType) {
                true
            } else if (ItemViewType.GAME_LOAD == this.itemViewType && ItemViewType.GAME_LOAD == other.itemViewType) {
                true
            } else {
                val thisGameItem = this.gameItem
                val otherGameItem = other.gameItem
                val thisGameItemId = thisGameItem?.id
                val otherGameItemId = otherGameItem?.id

                thisGameItemId != null && otherGameItemId != null && thisGameItemId == otherGameItemId
            }
        } else {
            false
        }
    }

    override fun equals(other: Any?): Boolean {
        return if (other is GameRoomItem) {
            if (ItemViewType.HEADER == this.itemViewType && ItemViewType.HEADER == other.itemViewType) {
                true
            } else if (ItemViewType.FOOTER == this.itemViewType && ItemViewType.FOOTER == other.itemViewType) {
                true
            } else if (ItemViewType.GAME_LOAD == this.itemViewType && ItemViewType.GAME_LOAD == other.itemViewType) {
                false
            } else {
                val thisGameItem = this.gameItem
                val otherGameItem = other.gameItem
                val thisGameItemId = thisGameItem?.id
                val otherGameItemId = otherGameItem?.id

                thisGameItemId != null && otherGameItemId != null && thisGameItemId == otherGameItemId &&
                        thisGameItem.isFavorite == otherGameItem.isFavorite &&
                        thisGameItem.tournamentId == otherGameItem.tournamentId
            }
        } else {
            false
        }
    }

    override fun hashCode(): Int {
        return super.hashCode()
    }
}
