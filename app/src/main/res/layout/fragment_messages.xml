<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_messages"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/container_no_messages"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:src="@drawable/ic_inbox" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/no_meessages"
            android:textColor="#8DA1BD"
            android:textSize="18sp" />

    </androidx.appcompat.widget.LinearLayoutCompat>

    <Button
        android:id="@+id/btn_bottom"
        android:layout_width="match_parent"
        android:layout_height="@dimen/btn_mark_all_height"
        android:background="@drawable/bg_messages_bottom_btn"
        android:text="@string/mark_all_as_read"
        android:textAllCaps="false"
        android:textColor="#0097EC"
        android:textSize="14sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
