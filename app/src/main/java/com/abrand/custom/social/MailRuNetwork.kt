package com.abrand.custom.social

import android.app.Dialog
import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import com.abrand.custom.R

class MailRuNetwork {

    fun showAuthWebView(context: Context, listener: Listener) {
        val authUrl = "https://connect.mail.ru/oauth/authorize?client_id=" +
                context.getString(R.string.mail_ru_app_id) + "&response_type=token&" +
                "redirect_uri=http%3A%2F%2Fconnect.mail.ru%2Foauth%2Fsuccess.html"

        val webView = WebView(context)
        val dialog = Dialog(context, android.R.style.Theme_Black_NoTitleBar_Fullscreen)
        dialog.setContentView(webView)
        webView.webViewClient = object : WebViewClient() {

            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
//                val successUrl = "http://connect.mail.ru/oauth/success.html#" +
//                        "refresh_token=b45529ac9bf6b32be761975c043ef9e3&" +
//                        "access_token=b6442ed12223a7d0b459916b8ea03ce5&" +
//                        "token_type=bearer"
                if (url != null && url.contains("oauth/success.html")) {
                    val ref = url.split("#")
                    val refParameters = ref[1].split("&")
                    for (refParameter in refParameters) {
                        if (refParameter.contains("access_token")) {
                            val accessToken = refParameter.split("=")[1]
                            Log.d("MailRu", "accessToken: $accessToken")
                            listener.onAccessTokenReceived(accessToken)
                            dialog.cancel()
                        }
                    }
                }
            }
        }
        webView.loadUrl(authUrl)
        dialog.show()
    }

    interface Listener {
        fun onAccessTokenReceived(token: String)
    }

}
