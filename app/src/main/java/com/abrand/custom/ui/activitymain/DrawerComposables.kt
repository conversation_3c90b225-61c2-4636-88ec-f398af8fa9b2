package com.abrand.custom.ui.activitymain

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.abrand.custom.R

@Composable
fun NumberInRing(
    viewModel: MainViewModelKt = viewModel(),
    modifier: Modifier = Modifier,
    ringColor: Color = Color(0x66FFFFFF),
    textColor: Color = Color.White,
    ringSize: Dp = 12.dp,
    expandedWidth: Dp = 18.dp,
    fontSize: TextUnit = 9.sp,
    lineHeight: TextUnit = 12.sp,
    strokeWidth: Dp = 1.dp
) {
    val bonusesCount by viewModel.balanceBonusesCount.collectAsState()
    if (bonusesCount < 1) return

//    var offsetY by remember { mutableStateOf(0.dp) }
//    val density = LocalDensity.current
    val width = if (bonusesCount > 9) expandedWidth else ringSize
    val offsetX = if (bonusesCount > 9) 0.dp else (-0.5).dp

    Box(
        modifier = modifier
            .size(width, ringSize)
            .border(width = strokeWidth, color = ringColor, shape = CircleShape)
            .clip(CircleShape)
            .padding(strokeWidth),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = bonusesCount.toString(),
            color = textColor,
            lineHeight = lineHeight,
            textAlign = TextAlign.Unspecified,
            maxLines = 1,
            modifier = Modifier.offset(x = offsetX, y = (-0.5).dp),
            style = TextStyle(
                shadow = Shadow(
                    color = Color(0x33000000),
                    offset = Offset(0.0f, 2.0f)
                ),
                fontSize = fontSize,
                fontFamily = FontFamily(Font(R.font.roboto_medium, FontWeight.Medium)),
                fontWeight = FontWeight.Medium,
            ),
//            onTextLayout = { layoutResult ->
//                val textHeightPx = layoutResult.size.height.toFloat()
//                val containerHeightPx = with(density) { ringSize.toPx() }
//                val zero = with(density) {0.5.dp.toPx()}
//                println("textHeightPx $textHeightPx")
//                println("containerHeightPx $containerHeightPx")
//                println("0,5.dp in px: ${(zero)}")
//
//                val computedOffsetPx = (textHeightPx - containerHeightPx) / 2f
//                offsetY = with(density) { (computedOffsetPx).toDp() }
//            },
        )
    }
}

//@Preview
//@Composable
//fun RingPreview() {
//    NumberInRing(8)
//}
