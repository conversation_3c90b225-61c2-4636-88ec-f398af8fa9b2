package com.abrand.custom.network

import com.abrand.custom.interfaces.GenericTarget
import com.abrand.custom.presenter.FieldsErrorsHolder
import com.apollographql.apollo3.exception.ApolloException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers

class PushTokenRepository {

    fun updateMobileAppSubscription(pushSubscriptionData: String, pushUpdateListener: PushUpdateListener) {
        val scope = CoroutineScope(Dispatchers.IO)

        ApolloProcessorKt.updateMobileAppSubscription(pushSubscriptionData, object : GenericTarget<Boolean> {
            override fun onSuccess(t: <PERSON><PERSON><PERSON>?) {
                pushUpdateListener.onSuccess()
            }

            override fun onFailure(e: ApolloException?) {
                pushUpdateListener.onFail()
            }

            override fun onError(errorMessage: String?, errorCode: String?, fieldsErrors: FieldsErrorsHolder?) {
                pushUpdateListener.onFail()
            }
        }, scope)
    }

    interface PushUpdateListener {
        fun onSuccess()
        fun onFail()
    }
}
