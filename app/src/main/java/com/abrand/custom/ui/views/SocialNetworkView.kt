package com.abrand.custom.ui.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.animation.AnimationUtils
import android.widget.FrameLayout
import com.abrand.custom.R
import com.abrand.custom.data.entity.SocialNetwork
import com.abrand.custom.databinding.ViewSocialNetworkBinding

class SocialNetworkView: FrameLayout {
    private var binding: ViewSocialNetworkBinding? = null
    private var socialNetwork: SocialNetwork? = null
    var listener: Listener? = null

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int) :
            super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {
        binding = ViewSocialNetworkBinding.inflate(LayoutInflater.from(context), this, true)
        binding?.ivSocialNetwork?.setOnClickListener {
            listener?.onIconClicked()
        }
    }

    fun getSocialNetwork(): SocialNetwork? {
        return socialNetwork
    }

    fun setSocialNetwork(socialNetwork: SocialNetwork) {
        this.socialNetwork = socialNetwork
        binding?.ivSocialNetwork?.setImageResource(socialNetwork.icon)
    }

    fun showLoader() {
        binding?.ivSocialNetwork?.isClickable = false
        binding?.ivSocialNetwork?.setImageResource(R.drawable.ic_preload)
        val animation = AnimationUtils.loadAnimation(context, R.anim.rotation_loader)
        binding?.ivSocialNetwork?.startAnimation(animation)
    }

    fun hideLoader() {
        binding?.ivSocialNetwork?.isClickable = true
        binding?.ivSocialNetwork?.clearAnimation()
        socialNetwork?.icon?.let { binding?.ivSocialNetwork?.setImageResource(it) }
    }

    interface Listener {
        fun onIconClicked()
    }

}
